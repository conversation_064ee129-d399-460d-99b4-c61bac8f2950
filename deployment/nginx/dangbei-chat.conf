# 当贝AI聊天界面 - Nginx配置文件
# 支持静态文件服务、API代理、缓存优化和安全配置

# 上游后端API服务器
upstream dangbei_api {
    server localhost:3000;
    # 如果有多个API服务器，可以添加负载均衡
    # server localhost:3001 backup;
    keepalive 32;
}

# HTTP服务器配置（重定向到HTTPS）
server {
    listen 80;
    server_name chat.yourdomain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS服务器配置
server {
    listen 443 ssl http2;
    server_name chat.yourdomain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/dangbei-chat.crt;
    ssl_certificate_key /etc/ssl/private/dangbei-chat.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 静态文件根目录
    root /var/www/dangbei-chat;
    index index.html;
    
    # 字符编码
    charset utf-8;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self' wss: https:; font-src 'self' cdn.jsdelivr.net;" always;
    
    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 静态资源缓存配置
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # 启用ETag
        etag on;
        
        # 预压缩文件支持
        gzip_static on;
        
        # 跨域资源共享（如果需要）
        add_header Access-Control-Allow-Origin "*";
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
    
    # API代理配置
    location /api/ {
        proxy_pass http://dangbei_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }
    
    # SPA路由支持（所有路径都返回index.html）
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    # 访问日志
    access_log /var/log/nginx/dangbei-chat-access.log combined;
    error_log /var/log/nginx/dangbei-chat-error.log warn;
}

# 本地开发配置
server {
    listen 8080;
    server_name localhost;
    
    root /path/to/dangbei-provider/public/chat;
    index index.html;
    
    # 基本配置
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 开发环境日志
    access_log /var/log/nginx/dangbei-chat-dev.log;
    error_log /var/log/nginx/dangbei-chat-dev-error.log;
}
