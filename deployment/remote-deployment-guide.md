# 当贝AI聊天界面远程部署方案

## 🌐 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   构建服务器     │    │   Web服务器     │    │   API服务器     │
│                │    │                │    │                │
│ • 代码仓库      │───▶│ • 静态文件      │───▶│ • 后端API       │
│ • 构建脚本      │    │ • Nginx/Caddy  │    │ • 数据库        │
│ • CI/CD        │    │ • SSL证书       │    │ • 业务逻辑      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 自动化部署脚本

### 1. 构建脚本
```bash
#!/bin/bash
# build-and-deploy.sh - 构建和部署脚本

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_NAME="dangbei-chat"
BUILD_DIR="/tmp/${PROJECT_NAME}-build"
STATIC_FILES_DIR="public/chat"
REMOTE_USER="deploy"
WEB_SERVER="web.yourdomain.com"
API_SERVER="api.yourdomain.com"
DEPLOY_PATH="/var/www/dangbei-chat"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 清理构建目录
log_info "清理构建目录..."
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"

# 2. 克隆代码
log_info "克隆代码仓库..."
git clone https://git.atjog.com/aier/dangbei-provider.git "$BUILD_DIR"
cd "$BUILD_DIR"

# 3. 检查代码版本
COMMIT_HASH=$(git rev-parse --short HEAD)
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
log_info "当前版本: $BRANCH_NAME ($COMMIT_HASH)"

# 4. 验证静态文件
log_info "验证静态文件..."
if [ ! -d "$STATIC_FILES_DIR" ]; then
    log_error "静态文件目录不存在: $STATIC_FILES_DIR"
    exit 1
fi

if [ ! -f "$STATIC_FILES_DIR/index.html" ]; then
    log_error "主页文件不存在: $STATIC_FILES_DIR/index.html"
    exit 1
fi

# 5. 优化静态文件
log_info "优化静态文件..."

# 压缩CSS文件
find "$STATIC_FILES_DIR" -name "*.css" -exec sh -c '
    for file; do
        echo "压缩CSS: $file"
        # 使用csso或其他CSS压缩工具
        # csso "$file" --output "$file.min"
        # mv "$file.min" "$file"
    done
' sh {} +

# 压缩JS文件
find "$STATIC_FILES_DIR" -name "*.js" -exec sh -c '
    for file; do
        echo "压缩JS: $file"
        # 使用terser或其他JS压缩工具
        # terser "$file" --compress --mangle --output "$file.min"
        # mv "$file.min" "$file"
    done
' sh {} +

# 6. 创建部署包
log_info "创建部署包..."
DEPLOY_PACKAGE="${PROJECT_NAME}-${COMMIT_HASH}.tar.gz"
tar -czf "/tmp/$DEPLOY_PACKAGE" -C "$STATIC_FILES_DIR" .

# 7. 上传到Web服务器
log_info "上传到Web服务器..."
scp "/tmp/$DEPLOY_PACKAGE" "${REMOTE_USER}@${WEB_SERVER}:/tmp/"

# 8. 远程部署
log_info "执行远程部署..."
ssh "${REMOTE_USER}@${WEB_SERVER}" << EOF
    set -e
    
    # 备份当前版本
    if [ -d "$DEPLOY_PATH" ]; then
        sudo cp -r "$DEPLOY_PATH" "${DEPLOY_PATH}.backup.\$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 创建部署目录
    sudo mkdir -p "$DEPLOY_PATH"
    
    # 解压新版本
    cd "$DEPLOY_PATH"
    sudo tar -xzf "/tmp/$DEPLOY_PACKAGE" .
    
    # 设置权限
    sudo chown -R www-data:www-data "$DEPLOY_PATH"
    sudo chmod -R 755 "$DEPLOY_PATH"
    
    # 重新加载Web服务器
    sudo systemctl reload nginx || sudo systemctl reload caddy
    
    # 清理临时文件
    rm -f "/tmp/$DEPLOY_PACKAGE"
    
    echo "部署完成: $COMMIT_HASH"
EOF

# 9. 验证部署
log_info "验证部署..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://${WEB_SERVER}/")
if [ "$HTTP_STATUS" = "200" ]; then
    log_info "部署验证成功 (HTTP $HTTP_STATUS)"
else
    log_error "部署验证失败 (HTTP $HTTP_STATUS)"
    exit 1
fi

# 10. 清理本地文件
log_info "清理本地文件..."
rm -rf "$BUILD_DIR"
rm -f "/tmp/$DEPLOY_PACKAGE"

log_info "部署完成！"
log_info "访问地址: https://${WEB_SERVER}/"
log_info "版本信息: $BRANCH_NAME ($COMMIT_HASH)"
```

### 2. 回滚脚本
```bash
#!/bin/bash
# rollback.sh - 回滚脚本

REMOTE_USER="deploy"
WEB_SERVER="web.yourdomain.com"
DEPLOY_PATH="/var/www/dangbei-chat"

# 列出可用的备份
ssh "${REMOTE_USER}@${WEB_SERVER}" "ls -la ${DEPLOY_PATH}.backup.*"

echo "请输入要回滚到的备份目录名："
read BACKUP_DIR

# 执行回滚
ssh "${REMOTE_USER}@${WEB_SERVER}" << EOF
    if [ -d "$BACKUP_DIR" ]; then
        sudo rm -rf "$DEPLOY_PATH"
        sudo cp -r "$BACKUP_DIR" "$DEPLOY_PATH"
        sudo chown -R www-data:www-data "$DEPLOY_PATH"
        sudo systemctl reload nginx
        echo "回滚完成"
    else
        echo "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
EOF
```

## 🔄 CI/CD 集成

### 1. GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy Dangbei Chat Interface

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Validate static files
      run: |
        if [ ! -f "public/chat/index.html" ]; then
          echo "Missing index.html"
          exit 1
        fi
        
    - name: Optimize files
      run: |
        # 安装优化工具
        npm install -g csso-cli terser
        
        # 压缩CSS
        find public/chat -name "*.css" -exec csso {} --output {} \;
        
        # 压缩JS
        find public/chat -name "*.js" -exec terser {} --compress --mangle --output {} \;
        
    - name: Create deployment package
      run: |
        cd public/chat
        tar -czf ../../dangbei-chat-${{ github.sha }}.tar.gz .
        
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.WEB_SERVER_HOST }}
        username: ${{ secrets.WEB_SERVER_USER }}
        key: ${{ secrets.WEB_SERVER_SSH_KEY }}
        script: |
          # 备份当前版本
          sudo cp -r /var/www/dangbei-chat /var/www/dangbei-chat.backup.$(date +%Y%m%d_%H%M%S)
          
          # 部署新版本
          cd /var/www/dangbei-chat
          sudo tar -xzf /tmp/dangbei-chat-${{ github.sha }}.tar.gz .
          sudo chown -R www-data:www-data .
          sudo systemctl reload nginx
          
    - name: Verify deployment
      run: |
        sleep 10
        curl -f https://${{ secrets.WEB_SERVER_HOST }}/ || exit 1
```

### 2. GitLab CI
```yaml
# .gitlab-ci.yml
stages:
  - validate
  - build
  - deploy

variables:
  DEPLOY_PATH: "/var/www/dangbei-chat"

validate:
  stage: validate
  script:
    - test -f public/chat/index.html
    - test -d public/chat/css
    - test -d public/chat/js

build:
  stage: build
  script:
    - cd public/chat
    - tar -czf ../../dangbei-chat-${CI_COMMIT_SHA}.tar.gz .
  artifacts:
    paths:
      - dangbei-chat-${CI_COMMIT_SHA}.tar.gz
    expire_in: 1 hour

deploy:
  stage: deploy
  script:
    - scp dangbei-chat-${CI_COMMIT_SHA}.tar.gz deploy@${WEB_SERVER}:/tmp/
    - ssh deploy@${WEB_SERVER} "
        sudo cp -r ${DEPLOY_PATH} ${DEPLOY_PATH}.backup.$(date +%Y%m%d_%H%M%S) &&
        cd ${DEPLOY_PATH} &&
        sudo tar -xzf /tmp/dangbei-chat-${CI_COMMIT_SHA}.tar.gz . &&
        sudo chown -R www-data:www-data . &&
        sudo systemctl reload nginx
      "
  only:
    - main
    - production
```

## 🔐 安全配置

### 1. SSH密钥配置
```bash
# 在构建服务器上生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "deploy@build-server"

# 将公钥添加到Web服务器
ssh-copy-id <EMAIL>

# 配置SSH客户端
cat >> ~/.ssh/config << EOF
Host web-server
    HostName web.yourdomain.com
    User deploy
    IdentityFile ~/.ssh/id_rsa
    StrictHostKeyChecking no
EOF
```

### 2. 部署用户权限
```bash
# 在Web服务器上创建部署用户
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG sudo deploy

# 配置sudo权限（无密码）
echo "deploy ALL=(ALL) NOPASSWD: /bin/systemctl reload nginx, /bin/systemctl reload caddy, /bin/chown, /bin/chmod, /bin/cp, /bin/rm, /bin/mkdir, /bin/tar" | sudo tee /etc/sudoers.d/deploy
```

## 📊 监控和通知

### 1. 部署状态监控
```bash
#!/bin/bash
# monitor-deployment.sh

WEB_SERVER="web.yourdomain.com"
WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# 检查服务状态
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://${WEB_SERVER}/")
RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" "https://${WEB_SERVER}/")

# 发送通知
if [ "$HTTP_STATUS" = "200" ]; then
    MESSAGE="✅ 部署成功 - ${WEB_SERVER} (响应时间: ${RESPONSE_TIME}s)"
else
    MESSAGE="❌ 部署失败 - ${WEB_SERVER} (HTTP状态: ${HTTP_STATUS})"
fi

curl -X POST -H 'Content-type: application/json' \
    --data "{\"text\":\"$MESSAGE\"}" \
    "$WEBHOOK_URL"
```

### 2. 健康检查
```bash
#!/bin/bash
# health-check.sh

WEB_SERVER="web.yourdomain.com"
API_SERVER="api.yourdomain.com"

# 检查前端
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://${WEB_SERVER}/")

# 检查API
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://${API_SERVER}/api/health")

echo "前端状态: $FRONTEND_STATUS"
echo "API状态: $API_STATUS"

if [ "$FRONTEND_STATUS" != "200" ] || [ "$API_STATUS" != "200" ]; then
    echo "健康检查失败，触发告警..."
    # 发送告警通知
fi
```

## 🔧 故障排除

### 1. 常见部署问题
```bash
# 检查SSH连接
ssh -v <EMAIL>

# 检查磁盘空间
ssh <EMAIL> "df -h"

# 检查权限问题
ssh <EMAIL> "ls -la /var/www/"

# 检查服务状态
ssh <EMAIL> "sudo systemctl status nginx"
```

### 2. 日志分析
```bash
# 查看部署日志
tail -f /var/log/deploy.log

# 查看Web服务器日志
ssh <EMAIL> "sudo tail -f /var/log/nginx/error.log"

# 查看系统日志
ssh <EMAIL> "sudo journalctl -u nginx -f"
```

通过以上配置，您可以实现完全自动化的远程部署流程，确保代码从构建到生产环境的无缝部署！
