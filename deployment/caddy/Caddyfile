# 当贝AI聊天界面 - Caddy配置文件
# 支持静态文件服务、API代理和HTTPS自动证书

# 本地开发配置
localhost:8080 {
    # 静态文件根目录
    root * /path/to/dangbei-provider/public/chat
    
    # 默认首页
    try_files {path} {path}/ /index.html
    
    # 静态文件缓存策略
    @static {
        path *.css *.js *.png *.jpg *.jpeg *.gif *.svg *.ico *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"
    
    # HTML文件不缓存
    @html {
        path *.html
    }
    header @html Cache-Control "no-cache, no-store, must-revalidate"
    
    # API代理到后端服务器
    reverse_proxy /api/* http://localhost:3000
    
    # 启用压缩
    encode gzip
    
    # 安全头设置
    header {
        X-Content-Type-Options nosniff
        X-Frame-Options DENY
        X-XSS-Protection "1; mode=block"
        Referrer-Policy strict-origin-when-cross-origin
    }
    
    # 日志记录
    log {
        output file /var/log/caddy/dangbei-chat.log
        format json
    }
}

# 生产环境配置（带域名和HTTPS）
chat.yourdomain.com {
    # 静态文件根目录
    root * /var/www/dangbei-chat
    
    # 默认首页和SPA路由支持
    try_files {path} {path}/ /index.html
    
    # 静态资源缓存
    @static {
        path *.css *.js *.png *.jpg *.jpeg *.gif *.svg *.ico *.woff *.woff2
    }
    header @static {
        Cache-Control "public, max-age=31536000, immutable"
        ETag off
    }
    
    # HTML文件缓存策略
    @html {
        path *.html
    }
    header @html {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma no-cache
        Expires 0
    }
    
    # API代理到后端服务器
    reverse_proxy /api/* https://api.yourdomain.com {
        header_up Host {upstream_hostport}
        header_up X-Real-IP {remote_host}
        header_up X-Forwarded-For {remote_host}
        header_up X-Forwarded-Proto {scheme}
    }
    
    # 启用压缩
    encode {
        gzip 6
        minimum_length 1024
    }
    
    # 安全头设置
    header {
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        X-XSS-Protection "1; mode=block"
        Referrer-Policy strict-origin-when-cross-origin
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self' wss: https:; font-src 'self' cdn.jsdelivr.net;"
    }
    
    # 错误页面
    handle_errors {
        @404 {
            expression {http.error.status_code} == 404
        }
        rewrite @404 /index.html
        file_server
    }
    
    # 访问日志
    log {
        output file /var/log/caddy/dangbei-chat-access.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
        level INFO
    }
}

# 全局配置
{
    # 自动HTTPS
    auto_https on
    
    # 邮箱用于Let's Encrypt证书
    email <EMAIL>
    
    # 管理端点
    admin localhost:2019
}
