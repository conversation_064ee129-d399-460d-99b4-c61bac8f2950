# 当贝AI聊天界面本地静态部署指南

## 🎯 部署概述

当贝AI聊天界面支持完全静态部署，前端和后端可以分离部署在不同的服务器上。

## 📁 文件准备

### 1. 复制静态文件
```bash
# 创建部署目录
sudo mkdir -p /var/www/dangbei-chat

# 复制聊天界面文件
sudo cp -r /path/to/dangbei-provider/public/chat/* /var/www/dangbei-chat/

# 设置文件权限
sudo chown -R www-data:www-data /var/www/dangbei-chat
sudo chmod -R 755 /var/www/dangbei-chat
```

### 2. 验证文件结构
```bash
# 检查文件结构
ls -la /var/www/dangbei-chat/
# 应该包含：
# - index.html
# - css/
# - js/
# - assets/
```

## 🔧 Caddy 部署

### 1. 安装 Caddy
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install caddy

# CentOS/RHEL
sudo yum install yum-plugin-copr
sudo yum copr enable @caddy/caddy
sudo yum install caddy
```

### 2. 配置 Caddy
```bash
# 复制配置文件
sudo cp deployment/caddy/Caddyfile /etc/caddy/Caddyfile

# 修改配置中的路径
sudo sed -i 's|/path/to/dangbei-provider/public/chat|/var/www/dangbei-chat|g' /etc/caddy/Caddyfile

# 验证配置
sudo caddy validate --config /etc/caddy/Caddyfile
```

### 3. 启动 Caddy
```bash
# 启动服务
sudo systemctl start caddy
sudo systemctl enable caddy

# 检查状态
sudo systemctl status caddy

# 查看日志
sudo journalctl -u caddy -f
```

### 4. 测试访问
```bash
# 本地测试
curl -I http://localhost:8080

# 检查响应头
curl -I http://localhost:8080/index.html
```

## 🌐 Nginx 部署

### 1. 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx

# 或使用官方仓库获取最新版本
sudo yum install epel-release
sudo yum install nginx
```

### 2. 配置 Nginx
```bash
# 复制配置文件
sudo cp deployment/nginx/dangbei-chat.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/dangbei-chat.conf /etc/nginx/sites-enabled/

# 或者对于CentOS/RHEL
sudo cp deployment/nginx/dangbei-chat.conf /etc/nginx/conf.d/

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

### 3. 启动 Nginx
```bash
# 启动服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查状态
sudo systemctl status nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 4. 防火墙配置
```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 'Nginx Full'
sudo ufw allow 8080

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 🔐 SSL证书配置

### 1. Let's Encrypt (免费证书)
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx  # Ubuntu/Debian
sudo yum install certbot python3-certbot-nginx  # CentOS/RHEL

# 获取证书
sudo certbot --nginx -d chat.yourdomain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 自签名证书（开发环境）
```bash
# 创建证书目录
sudo mkdir -p /etc/ssl/private /etc/ssl/certs

# 生成私钥
sudo openssl genrsa -out /etc/ssl/private/dangbei-chat.key 2048

# 生成证书
sudo openssl req -new -x509 -key /etc/ssl/private/dangbei-chat.key \
    -out /etc/ssl/certs/dangbei-chat.crt -days 365 \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=Dangbei/CN=localhost"

# 设置权限
sudo chmod 600 /etc/ssl/private/dangbei-chat.key
sudo chmod 644 /etc/ssl/certs/dangbei-chat.crt
```

## 🔍 故障排除

### 1. 常见问题

**问题：页面无法加载**
```bash
# 检查文件权限
ls -la /var/www/dangbei-chat/
sudo chown -R www-data:www-data /var/www/dangbei-chat/

# 检查SELinux（CentOS/RHEL）
sudo setsebool -P httpd_can_network_connect 1
sudo restorecon -R /var/www/dangbei-chat/
```

**问题：API请求失败**
```bash
# 检查后端服务状态
curl http://localhost:3000/api/health

# 检查代理配置
sudo nginx -t
sudo systemctl reload nginx
```

**问题：静态资源404**
```bash
# 检查文件路径
find /var/www/dangbei-chat/ -name "*.css" -o -name "*.js"

# 检查Nginx配置
sudo nginx -T | grep -A 10 "location.*\.(css|js)"
```

### 2. 性能优化

**启用HTTP/2**
```bash
# 确保Nginx支持HTTP/2
nginx -V 2>&1 | grep -o with-http_v2_module

# 在配置中添加 http2
listen 443 ssl http2;
```

**启用Brotli压缩**
```bash
# 安装Brotli模块
sudo apt install nginx-module-brotli

# 在nginx.conf中添加
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;
```

## 📊 监控和日志

### 1. 日志分析
```bash
# 实时查看访问日志
sudo tail -f /var/log/nginx/dangbei-chat-access.log

# 分析错误日志
sudo grep "error" /var/log/nginx/dangbei-chat-error.log

# 统计访问量
sudo awk '{print $1}' /var/log/nginx/dangbei-chat-access.log | sort | uniq -c | sort -nr
```

### 2. 性能监控
```bash
# 检查Nginx状态
curl http://localhost/nginx_status

# 检查系统资源
htop
iostat -x 1
```

## ✅ 验证部署

### 1. 功能测试
```bash
# 测试主页加载
curl -I http://localhost:8080/

# 测试静态资源
curl -I http://localhost:8080/css/main.css

# 测试API代理
curl http://localhost:8080/api/health
```

### 2. 性能测试
```bash
# 使用ab进行压力测试
ab -n 1000 -c 10 http://localhost:8080/

# 使用curl测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/
```

部署完成后，访问 http://localhost:8080 即可使用当贝AI聊天界面！
