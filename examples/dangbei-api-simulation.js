/**
 * 当贝AI API调用模拟示例
 * 
 * 这个示例展示了如何使用WebAssembly签名模块来模拟当贝AI的实际API调用
 * 包括完整的请求头生成、签名计算和API调用流程
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const { quickSign, UnifiedSignature } = require('../src/wasm/unified-signature');

/**
 * 生成随机nonce字符串
 * 模拟原始JavaScript中的随机数生成
 * 
 * @returns {string} 17位随机字符串
 */
function generateRandomNonce() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
  let result = '';
  for (let i = 0; i < 17; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成设备ID
 * 模拟当贝AI的设备标识生成
 * 
 * @returns {string} 设备ID
 */
function generateDeviceId() {
  const hash1 = Math.random().toString(36).substr(2, 16);
  const hash2 = Math.random().toString(36).substr(2, 16);
  const suffix = Math.random().toString(36).substr(2, 12);
  return `${hash1}${hash2}_${suffix}`;
}

/**
 * 创建对话API调用示例
 * 模拟 /ai-search/conversationApi/v1/batch/create 接口
 */
async function createConversationExample() {
  console.log('🎯 模拟创建对话API调用');
  console.log('=' .repeat(50));

  // 准备API请求数据
  const requestData = {
    conversationList: [{
      metaData: {
        chatModelConfig: {},
        superAgentPath: "/chat"
      },
      shareId: "",
      isAnonymous: false,
      source: ""
    }]
  };

  // 生成签名参数
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = generateRandomNonce();
  const deviceId = generateDeviceId();
  const requestBody = JSON.stringify(requestData);

  console.log('📋 请求参数:');
  console.log(`   时间戳: ${timestamp}`);
  console.log(`   随机数: ${nonce}`);
  console.log(`   设备ID: ${deviceId}`);
  console.log(`   请求体: ${requestBody}`);

  try {
    // 使用WebAssembly签名模块生成签名
    console.log('\n🔐 生成签名...');
    const signature = await quickSign(requestBody, `${timestamp}:${nonce}`);
    console.log(`   签名结果: ${signature}`);

    // 构建完整的请求头
    const headers = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'Connection': 'keep-alive',
      'Origin': 'https://ai.dangbei.com',
      'Referer': 'https://ai.dangbei.com/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0',
      'appType': '6',
      'appVersion': '1.1.17-22',
      'client-ver': '1.0.2',
      'content-type': 'application/json',
      'deviceId': deviceId,
      'lang': 'zh',
      'nonce': nonce,
      'sign': signature,
      'timestamp': timestamp.toString(),
      'token': ''
    };

    console.log('\n📤 完整请求信息:');
    console.log('   URL: https://ai-api.dangbei.net/ai-search/conversationApi/v1/batch/create');
    console.log('   Method: POST');
    console.log('   Headers:', JSON.stringify(headers, null, 4));

    // 这里可以使用 fetch 或 axios 发送实际请求
    console.log('\n💡 发送请求示例代码:');
    console.log(`
const response = await fetch('https://ai-api.dangbei.net/ai-search/conversationApi/v1/batch/create', {
  method: 'POST',
  headers: ${JSON.stringify(headers, null, 2)},
  body: '${requestBody}'
});

const result = await response.json();
console.log('API响应:', result);
    `);

  } catch (error) {
    console.error('❌ 签名生成失败:', error.message);
  }
}

/**
 * 发送消息API调用示例
 * 模拟 /ai-search/chatApi/v2/chat 接口
 */
async function sendMessageExample() {
  console.log('\n🎯 模拟发送消息API调用');
  console.log('=' .repeat(50));

  // 准备API请求数据
  const requestData = {
    stream: true,
    botCode: "AI_SEARCH",
    conversationId: "363022964585267589",
    question: "你好，请介绍一下你自己",
    model: "doubao-1_6-thinking",
    chatOption: {
      searchKnowledge: false,
      searchAllKnowledge: false,
      searchSharedKnowledge: false
    },
    knowledgeList: [],
    anonymousKey: "",
    uuid: "363022965811450053",
    chatId: "363022965811450053",
    files: [],
    reference: [],
    role: "user",
    status: "local",
    content: "你好，请介绍一下你自己",
    userAction: "",
    agentId: ""
  };

  // 生成签名参数
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = generateRandomNonce();
  const deviceId = generateDeviceId();
  const requestBody = JSON.stringify(requestData);

  console.log('📋 请求参数:');
  console.log(`   对话ID: ${requestData.conversationId}`);
  console.log(`   问题: ${requestData.question}`);
  console.log(`   模型: ${requestData.model}`);
  console.log(`   时间戳: ${timestamp}`);
  console.log(`   随机数: ${nonce}`);

  try {
    // 生成签名
    console.log('\n🔐 生成签名...');
    const signature = await quickSign(requestBody, `${timestamp}:${nonce}`);
    console.log(`   签名结果: ${signature}`);

    // 构建请求头
    const headers = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'Connection': 'keep-alive',
      'Origin': 'https://ai.dangbei.com',
      'Referer': 'https://ai.dangbei.com/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0',
      'appType': '6',
      'appVersion': '1.1.17-22',
      'client-ver': '1.0.2',
      'content-type': 'application/json',
      'deviceId': deviceId,
      'lang': 'zh',
      'nonce': nonce,
      'sign': signature,
      'timestamp': timestamp.toString(),
      'token': ''
    };

    console.log('\n📤 完整请求信息:');
    console.log('   URL: https://ai-api.dangbei.net/ai-search/chatApi/v2/chat');
    console.log('   Method: POST');
    console.log('   Headers:', JSON.stringify(headers, null, 4));

    console.log('\n💡 SSE流式响应处理示例:');
    console.log(`
const EventSource = require('eventsource');

const eventSource = new EventSource('https://ai-api.dangbei.net/ai-search/chatApi/v2/chat', {
  method: 'POST',
  headers: ${JSON.stringify(headers, null, 2)},
  body: '${requestBody}'
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'conversation.message.delta') {
    process.stdout.write(data.content);
  } else if (data.type === 'conversation.chat.completed') {
    console.log('\\n对话完成');
    eventSource.close();
  }
};

eventSource.onerror = function(error) {
  console.error('SSE错误:', error);
};
    `);

  } catch (error) {
    console.error('❌ 签名生成失败:', error.message);
  }
}

/**
 * 生成ID API调用示例
 * 模拟 /ai-search/commonApi/v1/generateId 接口
 */
async function generateIdExample() {
  console.log('\n🎯 模拟生成ID API调用');
  console.log('=' .repeat(50));

  // 准备API请求数据
  const requestData = {
    timestamp: Date.now()
  };

  // 生成签名参数
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = generateRandomNonce();
  const deviceId = generateDeviceId();
  const requestBody = JSON.stringify(requestData);

  console.log('📋 请求参数:');
  console.log(`   时间戳: ${timestamp}`);
  console.log(`   随机数: ${nonce}`);
  console.log(`   请求体: ${requestBody}`);

  try {
    // 生成签名
    console.log('\n🔐 生成签名...');
    const signature = await quickSign(requestBody, `${timestamp}:${nonce}`);
    console.log(`   签名结果: ${signature}`);

    // 构建请求头
    const headers = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'Connection': 'keep-alive',
      'Origin': 'https://ai.dangbei.com',
      'Referer': 'https://ai.dangbei.com/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0',
      'appType': '6',
      'appVersion': '1.1.17-22',
      'client-ver': '1.0.2',
      'content-type': 'application/json',
      'deviceId': deviceId,
      'lang': 'zh',
      'nonce': nonce,
      'sign': signature,
      'timestamp': timestamp.toString(),
      'token': ''
    };

    console.log('\n📤 完整请求信息:');
    console.log('   URL: https://ai-api.dangbei.net/ai-search/commonApi/v1/generateId');
    console.log('   Method: POST');
    console.log('   Headers:', JSON.stringify(headers, null, 4));

  } catch (error) {
    console.error('❌ 签名生成失败:', error.message);
  }
}

/**
 * 性能测试示例
 * 测试签名生成的性能表现
 */
async function performanceTestExample() {
  console.log('\n🎯 签名性能测试');
  console.log('=' .repeat(50));

  const signer = new UnifiedSignature({
    preferWasm: false, // 使用备用实现
    enableFallback: true,
    debug: false
  });

  try {
    await signer.initialize();

    const iterations = 100;
    console.log(`🏃 开始性能测试 (${iterations} 次迭代)...`);

    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      const timestamp = Math.floor(Date.now() / 1000);
      const nonce = generateRandomNonce();
      const requestData = { test: `data_${i}`, timestamp };
      const requestBody = JSON.stringify(requestData);
      
      await signer.generateSignature(requestBody, `${timestamp}:${nonce}`);
    }

    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    const throughput = (iterations / totalTime) * 1000;

    console.log('📊 性能测试结果:');
    console.log(`   总时间: ${totalTime}ms`);
    console.log(`   平均时间: ${avgTime.toFixed(2)}ms/次`);
    console.log(`   吞吐量: ${throughput.toFixed(2)} 次/秒`);

    // 内置性能测试
    const perfResults = await signer.performanceTest(50);
    console.log('   内置测试结果:', perfResults);

  } catch (error) {
    console.error('❌ 性能测试失败:', error.message);
  } finally {
    signer.destroy();
  }
}

/**
 * 主函数
 * 运行所有示例
 */
async function main() {
  console.log('🚀 当贝AI WebAssembly 签名模块 - API调用模拟示例');
  console.log('基于对原始JavaScript代码的逆向分析实现');
  console.log('');

  try {
    // 运行各种API调用示例
    await createConversationExample();
    await sendMessageExample();
    await generateIdExample();
    await performanceTestExample();

    console.log('\n🎉 所有示例运行完成！');
    console.log('\n💡 使用提示:');
    console.log('   1. 复制上述请求信息到你的HTTP客户端');
    console.log('   2. 根据需要修改请求参数');
    console.log('   3. 发送请求到当贝AI API服务器');
    console.log('   4. 处理返回的响应数据');

  } catch (error) {
    console.error('💥 示例运行失败:', error.message);
    process.exit(1);
  }
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  createConversationExample,
  sendMessageExample,
  generateIdExample,
  performanceTestExample,
  generateRandomNonce,
  generateDeviceId
};
