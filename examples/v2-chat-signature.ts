/**
 * v2/chat 签名计算示例脚本
 * 
 * 依据 docs/WASM_SIGNATURE_EMULATOR.md 与 docs/WASM_V2_SIGNATURE_IMPLEMENTATION.md 描述，
 * 使用 WasmSignatureEmulator.getSignV2 按真实 WASM 调用模式计算签名：
 *   get_sign(requestDataPtr, requestDataLen, urlPathPtr, urlPathLen)
 *
 * 关键中文调试日志与详细中文注释已包含，便于排查与核对。
 */

import { WasmSignatureEmulator } from '../src/utils/wasm-signature-emulator';
import { createHash } from 'crypto';

// 提供的参数（与真实抓包一致）
// a = 16, o = 548, n = 1115144, i = 1114120, t = '/chatApi/v2/chat'
const urlPath = '/chatApi/v2/chat';
const requestData =
  '{"stream":true,"botCode":"AI_SEARCH","conversationId":"364120755967492485","question":"使用Agno框架进行多Agent协作时如何进行共享上下文？","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"364270491870957765","chatId":"364270491870957765","files":[],"reference":[],"role":"user","status":"local","content":"使用Agno框架进行多Agent协作时如何进行共享上下文？","userAction":"","agentId":""}';

function main() {
  // 开启 debug 以输出中文调试日志
  const emulator = new WasmSignatureEmulator({ debug: true, strategy: 'hybrid', secretKey: 'dangbei_ai_v2_secret_2024' });

  console.log('🔧 签名输入校验');
  console.log('  - URL 路径:', urlPath);
  console.log('  - URL 字节长度(期望 a=16):', Buffer.byteLength(urlPath, 'utf8'));
  console.log('  - 请求数据字节长度(期望 o=548):', Buffer.byteLength(requestData, 'utf8'));

  // 使用 v2 专用接口：getSignV2(requestData, urlPath)
  const result = emulator.getSignV2(requestData, urlPath);

  // 进一步分析差异：尝试还原 get_sign 内部可能的合并串
  const lenHash = createHash('md5').update(`${Buffer.byteLength(requestData, 'utf8')}:${Buffer.byteLength(urlPath, 'utf8')}`).digest('hex');
  const dataHash = createHash('md5').update(requestData + urlPath).digest('hex');
  const try1 = createHash('md5').update(lenHash + dataHash).digest('hex').toUpperCase();
  const try2 = createHash('md5').update(lenHash + ':' + dataHash).digest('hex').toUpperCase();
  const try3 = createHash('md5').update(requestData + ':' + urlPath).digest('hex').toUpperCase();
  const try4 = createHash('md5').update(requestData + urlPath).digest('hex').toUpperCase();

  console.log('🧪 尝试组合签名(初步)', { lenHash, dataHash, try1, try2, try3, try4 });

  console.log('✅ v2/chat 签名生成成功');
  console.log('  - 最终签名(signature):', result.signature);
  console.log('  - 时间戳(timestamp):', result.timestamp);
  console.log('  - 策略(strategy):', result.strategy);
  if (result.debug) {
    console.log('  - 调试(algorithm):', result.debug.algorithm);
    console.log('  - 调试(signString):', result.debug.signString);
  }

  // 使用抓包中的 timestamp 与 nonce 进行更多候选组合测试
  const timestamp = 1755834473;
  const nonce = 'nhwYHmTJgwTiQtchXghtA';
  const version = 'v2';

  const lenReq = Buffer.byteLength(requestData, 'utf8');
  const lenPath = Buffer.byteLength(urlPath, 'utf8');
  const lstr = `${lenReq}:${lenPath}`;
  const lhash = createHash('md5').update(lstr).digest('hex');
  const dhash = createHash('md5').update(requestData + urlPath).digest('hex');
  const dhashU = dhash.toUpperCase();
  const lhashU = lhash.toUpperCase();
  const method = 'POST';
  const norm = `${method} ${urlPath}`;
  const nhash = createHash('md5').update(norm).digest('hex');

  // 指针与长度（来自抓包）
  const nPtr = 1114120; // request ptr
  const iPtr = 1114672; // path ptr
  const oLen = lenReq;  // 548
  const aLen = lenPath; // 16

  const sep = [ '', ':', '|', ',', ';', '#', '&' ];


  function md5buf(buf: Buffer) {
    return createHash('md5').update(buf).digest('hex').toUpperCase();
  }

  function u32le(n: number) {
    const b = Buffer.allocUnsafe(4);
    b.writeUInt32LE(n >>> 0, 0);
    return b;
  }

  const bodyBytes = Buffer.from(requestData, 'utf8');
  const pathBytes = Buffer.from(urlPath, 'utf8');

  const payload1 = Buffer.concat([u32le(lenReq), u32le(lenPath), bodyBytes, pathBytes]);
  const payload2 = Buffer.concat([u32le(nPtr), u32le(lenReq), u32le(iPtr), u32le(lenPath), bodyBytes, pathBytes]);
  const payload3 = Buffer.concat([u32le(nPtr), u32le(iPtr), u32le(lenReq), u32le(lenPath), bodyBytes, pathBytes]);
  const payload4 = Buffer.concat([u32le(lenReq), u32le(lenPath), u32le(nPtr), u32le(iPtr), bodyBytes, pathBytes]);
  const payload5 = Buffer.concat([u32le(lenReq), bodyBytes, u32le(lenPath), pathBytes]);

  const ptrLenCandidates = {
    'md5(u32le(o),u32le(a),body,path)': md5buf(payload1),
    'md5(u32le(n),u32le(o),u32le(i),u32le(a),body,path)': md5buf(payload2),
    'md5(u32le(n),u32le(i),u32le(o),u32le(a),body,path)': md5buf(payload3),
    'md5(u32le(o),u32le(a),u32le(n),u32le(i),body,path)': md5buf(payload4),
    'md5(u32le(o),body,u32le(a),path)': md5buf(payload5),
  };

  console.log('🧪 指针/长度原始字节候选', ptrLenCandidates);

  const hb = createHash('md5').update(requestData).digest('hex');
  const hp = createHash('md5').update(urlPath).digest('hex');
  const HB = hb.toUpperCase();
  const HP = hp.toUpperCase();

  const tries: Record<string, string> = {
    // 仅 body/path 相关组合（无时间/nonce）
    'md5(body)': createHash('md5').update(requestData).digest('hex').toUpperCase(),
    'md5(path)': createHash('md5').update(urlPath).digest('hex').toUpperCase(),
    'md5(body+path)': createHash('md5').update(requestData + urlPath).digest('hex').toUpperCase(),
    'md5(path+body)': createHash('md5').update(urlPath + requestData).digest('hex').toUpperCase(),
    'md5(md5(body)+path)': createHash('md5').update(createHash('md5').update(requestData).digest('hex') + urlPath).digest('hex').toUpperCase(),
    'md5(md5(body)+\":\"+path)': createHash('md5').update(createHash('md5').update(requestData).digest('hex') + ':' + urlPath).digest('hex').toUpperCase(),
    'md5(md5(body)+md5(path))': createHash('md5').update(createHash('md5').update(requestData).digest('hex') + createHash('md5').update(urlPath).digest('hex')).digest('hex').toUpperCase(),

    // 长度与数据哈希混合（注意：lstr 为 `${lenReq}:${lenPath}`）
    'md5(lenHash+dataHash)': createHash('md5').update(lhash + dhash).digest('hex').toUpperCase(),
    'md5(lenHash:+:dataHash)': createHash('md5').update(lhash + ':' + dhash).digest('hex').toUpperCase(),
    'md5(lenStr:+:dataHash)': createHash('md5').update(lstr + ':' + dhash).digest('hex').toUpperCase(),
    'md5(lenStr+dataHash)': createHash('md5').update(lstr + dhash).digest('hex').toUpperCase(),
    'md5(lenStr:dataHashUpper)': createHash('md5').update(lstr + ':' + dhash.toUpperCase()).digest('hex').toUpperCase(),
    'md5(lenStr+dataHashUpper)': createHash('md5').update(lstr + dhash.toUpperCase()).digest('hex').toUpperCase(),
    'md5(lenReq:lenPath:md5(body))': createHash('md5').update(`${lstr}:${createHash('md5').update(requestData).digest('hex')}`).digest('hex').toUpperCase(),
    'md5(md5(body+path)+lenStr)': createHash('md5').update(createHash('md5').update(requestData + urlPath).digest('hex') + lstr).digest('hex').toUpperCase(),
    'md5(lenStr+md5(body+path))': createHash('md5').update(lstr + createHash('md5').update(requestData + urlPath).digest('hex')).digest('hex').toUpperCase(),
    'md5(md5(body)+lenStr+md5(path))': createHash('md5').update(createHash('md5').update(requestData).digest('hex') + lstr + createHash('md5').update(urlPath).digest('hex')).digest('hex').toUpperCase(),

    // 指针与长度增强
    'md5(n:o:i:a)': createHash('md5').update(`${nPtr}:${oLen}:${iPtr}:${aLen}`).digest('hex').toUpperCase(),
    'md5(n+o+i+a)': createHash('md5').update(`${nPtr}${oLen}${iPtr}${aLen}`).digest('hex').toUpperCase(),
    'md5(o:a:md5(body+path))': createHash('md5').update(`${oLen}:${aLen}:${dhash}`).digest('hex').toUpperCase(),
    'md5(md5(body+path):o:a)': createHash('md5').update(`${dhash}:${oLen}:${aLen}`).digest('hex').toUpperCase(),
    'md5(o+a+md5(body+path))': createHash('md5').update(`${oLen}${aLen}${dhash}`).digest('hex').toUpperCase(),

    // 含时间/nonce 的组合
    'len:data:ts:nonce': createHash('md5').update(`${lstr}:${dhash}:${timestamp}:${nonce}`).digest('hex').toUpperCase(),
    'len:data:ts+nonce': createHash('md5').update(`${lstr}:${dhash}:${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'len+data+ts+nonce': createHash('md5').update(`${lstr}${dhash}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'data:ts:nonce': createHash('md5').update(`${dhash}:${timestamp}:${nonce}`).digest('hex').toUpperCase(),
    'data+ts+nonce': createHash('md5').update(`${dhash}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'DATA+ts+nonce': createHash('md5').update(`${dhashU}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'LEN+DATA+ts+nonce': createHash('md5').update(`${lhashU}${dhashU}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'len:data:ts:nonce:v2': createHash('md5').update(`${lstr}:${dhash}:${timestamp}:${nonce}:${version}`).digest('hex').toUpperCase(),
    'body+path+ts+nonce': createHash('md5').update(`${requestData}${urlPath}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'path+body+ts+nonce': createHash('md5').update(`${urlPath}${requestData}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'norm+ts+nonce': createHash('md5').update(`${norm}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'norm:ts:nonce': createHash('md5').update(`${norm}:${timestamp}:${nonce}`).digest('hex').toUpperCase(),
    'nhash+ts+nonce': createHash('md5').update(`${nhash}${timestamp}${nonce}`).digest('hex').toUpperCase(),
    'lenhash:datahash -> md5 -> +ts+nonce': createHash('md5').update(createHash('md5').update(`${lhash}${dhash}`).digest('hex') + `${timestamp}${nonce}`).digest('hex').toUpperCase(),
  };

  // 动态扩展：不同分隔符组合（lenStr ? dataHash ? ts ? nonce）
  for (const s1 of sep) {
    for (const s2 of sep) {
      for (const s3 of sep) {
        tries[`len${s1}data${s2}ts${s3}nonce`] = createHash('md5').update(`${lstr}${s1}${dhash}${s2}${timestamp}${s3}${nonce}`).digest('hex').toUpperCase();
        tries[`data${s1}len${s2}ts${s3}nonce`] = createHash('md5').update(`${dhash}${s1}${lstr}${s2}${timestamp}${s3}${nonce}`).digest('hex').toUpperCase();
        tries[`o${s1}a${s2}ts${s3}nonce`] = createHash('md5').update(`${oLen}${s1}${aLen}${s2}${timestamp}${s3}${nonce}`).digest('hex').toUpperCase();
        tries[`o${s1}a${s2}lenstr${s3}nonce`] = createHash('md5').update(`${oLen}${s1}${aLen}${s2}${lstr}${s3}${nonce}`).digest('hex').toUpperCase();
      }
    }
  }

  const expectedSign = '63334E59ECA2FC0ABDBC3BFF6CD4A30D';
  let matched = false;
  for (const [name, sig] of Object.entries(tries)) {
    if (sig === expectedSign) {
      console.log('✅ 找到匹配组合:', name, sig);
      matched = true;
    }
  }
  if (!matched) {
    console.warn('⚠️ 暂未匹配到期望签名，候选如下:');
    console.log(tries);
  }

  // 断言与抓包期望签名一致（如不一致则提示）
  if (result.signature !== expectedSign) {
    console.warn('⚠️ 与期望签名不一致', { expectedSign, actual: result.signature });
  } else {
    console.log('✅ 与期望签名一致');
  }
}

main();

