/**
 * WASM签名算法集成示例
 * 展示如何在DangbeiProvider中使用新的WASM模拟签名算法
 */

import { DangbeiProvider } from '../src/providers/dangbei-provider';
import { WasmSignatureEmulator } from '../src/utils/wasm-signature-emulator';
import { SignatureV2Utils } from '../src/utils/signature-v2';
import { runWasmSignatureTests, compareSignatureStrategies } from '../src/utils/wasm-signature-test';

/**
 * 基础集成示例
 */
async function basicIntegrationExample(): Promise<void> {
  console.log('🚀 基础WASM签名集成示例\n');
  
  // 创建当贝AI Provider实例
  const provider = new DangbeiProvider({
    deviceId: 'example_device_' + Date.now(),
    debug: true,
    // 其他配置...
  });

  try {
    // 测试v2聊天接口调用
    console.log('📞 调用v2聊天接口...');
    
    const response = await provider.chat({
      messages: [
        { role: 'user', content: '你好，请介绍一下你自己' }
      ],
      stream: false
    });

    console.log('✅ 聊天响应:', response);
    
  } catch (error) {
    console.error('❌ 聊天调用失败:', error);
    
    // 如果签名验证失败，可以尝试不同的策略
    console.log('🔄 尝试使用不同的签名策略...');
    await tryDifferentSignatureStrategies();
  }
}

/**
 * 尝试不同的签名策略
 */
async function tryDifferentSignatureStrategies(): Promise<void> {
  const strategies = ['standard', 'enhanced', 'hybrid'] as const;
  
  for (const strategy of strategies) {
    console.log(`\n🧪 测试策略: ${strategy.toUpperCase()}`);
    
    try {
      // 创建使用特定策略的WASM模拟器
      const emulator = new WasmSignatureEmulator({
        debug: true,
        strategy,
        secretKey: `dangbei_ai_${strategy}_2024`
      });
      
      // 模拟签名生成
      const testSignature = emulator.getSign(
        '{"test": "data"}',
        `${Math.floor(Date.now() / 1000)}:test_nonce`
      );
      
      console.log(`   生成的签名: ${testSignature.signature}`);
      console.log(`   使用的策略: ${testSignature.strategy}`);
      
    } catch (error) {
      console.error(`   ❌ 策略 ${strategy} 失败:`, error);
    }
  }
}

/**
 * 高级集成示例 - 自定义签名处理
 */
async function advancedIntegrationExample(): Promise<void> {
  console.log('\n🔧 高级WASM签名集成示例\n');
  
  // 创建自定义的WASM模拟器
  const customEmulator = new WasmSignatureEmulator({
    debug: true,
    strategy: 'hybrid',
    timeOffset: 0, // 时间偏移量
    secretKey: 'custom_secret_key_2024'
  });
  
  // 测试不同类型的请求
  const testCases = [
    {
      name: 'GET请求测试',
      requestData: 'param1=value1&param2=value2',
      timestampNonce: `${Math.floor(Date.now() / 1000)}:get_test_nonce`
    },
    {
      name: 'POST请求测试',
      requestData: JSON.stringify({
        conversationList: [{
          metaData: { chatModelConfig: {}, superAgentPath: "/chat" },
          shareId: "",
          isAnonymous: false,
          source: ""
        }]
      }),
      timestampNonce: `${Math.floor(Date.now() / 1000)}:post_test_nonce`
    },
    {
      name: '空数据测试',
      requestData: '',
      timestampNonce: `${Math.floor(Date.now() / 1000)}:empty_test_nonce`
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}:`);
    
    try {
      const result = customEmulator.getSign(
        testCase.requestData,
        testCase.timestampNonce
      );
      
      console.log(`   签名: ${result.signature}`);
      console.log(`   时间戳: ${result.timestamp}`);
      
      if (result.debug) {
        console.log(`   算法: ${result.debug.algorithm}`);
        console.log(`   数据长度: ${result.debug.normalizedData.length} 字符`);
      }
      
    } catch (error) {
      console.error(`   ❌ 失败:`, error);
    }
    
    console.log('');
  }
}

/**
 * 错误处理和降级示例
 */
async function errorHandlingExample(): Promise<void> {
  console.log('🛡️ 错误处理和降级示例\n');
  
  // 模拟一个可能失败的签名生成场景
  const testParams = {
    timestamp: Math.floor(Date.now() / 1000),
    nonce: 'error_test_nonce',
    deviceId: 'error_test_device',
    method: 'POST' as const,
    url: '/ai-search/chatApi/v2/chat',
    data: { test: 'error handling' }
  };
  
  try {
    // 尝试使用WASM模拟算法
    console.log('🔐 尝试WASM模拟算法...');
    const wasmSignature = SignatureV2Utils.generateV2Signature(testParams);
    console.log(`✅ WASM签名成功: ${wasmSignature}`);
    
  } catch (wasmError) {
    console.warn(`⚠️ WASM算法失败: ${wasmError}`);
    
    try {
      // 降级到传统算法
      console.log('🔄 降级到传统算法...');
      
      // 这里可以实现传统的签名算法作为备用方案
      const fallbackSignature = generateFallbackSignature(testParams);
      console.log(`✅ 降级签名成功: ${fallbackSignature}`);
      
    } catch (fallbackError) {
      console.error(`❌ 所有签名算法都失败: ${fallbackError}`);
      
      // 最后的降级方案：使用本地缓存或离线模式
      console.log('🔄 启用离线模式...');
      handleOfflineMode();
    }
  }
}

/**
 * 生成降级签名（简化版本）
 */
function generateFallbackSignature(params: any): string {
  const crypto = require('crypto');
  const signString = `${params.timestamp}${params.nonce}${params.deviceId}`;
  return crypto.createHash('md5').update(signString).digest('hex').toUpperCase();
}

/**
 * 处理离线模式
 */
function handleOfflineMode(): void {
  console.log('📱 离线模式已启用');
  console.log('   - 使用本地缓存的响应');
  console.log('   - 队列化请求以便后续重试');
  console.log('   - 提供基础的AI功能');
}

/**
 * 性能监控示例
 */
async function performanceMonitoringExample(): Promise<void> {
  console.log('\n📊 性能监控示例\n');
  
  const emulator = new WasmSignatureEmulator({
    debug: false,
    strategy: 'hybrid'
  });
  
  // 监控签名生成性能
  const iterations = 100;
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    emulator.getSign(
      `{"iteration": ${i}, "data": "performance test"}`,
      `${Math.floor(Date.now() / 1000)}:perf_${i}`
    );
    
    const endTime = Date.now();
    times.push(endTime - startTime);
  }
  
  // 计算统计信息
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  
  console.log(`📈 性能统计 (${iterations} 次迭代):`);
  console.log(`   平均时间: ${avgTime.toFixed(2)}ms`);
  console.log(`   最小时间: ${minTime}ms`);
  console.log(`   最大时间: ${maxTime}ms`);
  console.log(`   吞吐量: ${(1000 / avgTime).toFixed(0)} 次/秒`);
}

/**
 * 主函数 - 运行所有示例
 */
async function main(): Promise<void> {
  console.log('🎯 WASM签名算法集成示例\n');
  console.log('=' .repeat(60));
  
  try {
    // 1. 运行WASM签名测试
    console.log('\n1️⃣ 运行WASM签名测试');
    runWasmSignatureTests();
    
    // 2. 基础集成示例
    console.log('\n2️⃣ 基础集成示例');
    await basicIntegrationExample();
    
    // 3. 高级集成示例
    console.log('\n3️⃣ 高级集成示例');
    await advancedIntegrationExample();
    
    // 4. 错误处理示例
    console.log('\n4️⃣ 错误处理示例');
    await errorHandlingExample();
    
    // 5. 性能监控示例
    console.log('\n5️⃣ 性能监控示例');
    await performanceMonitoringExample();
    
    // 6. 策略比较
    console.log('\n6️⃣ 策略比较示例');
    compareSignatureStrategies({
      timestamp: Math.floor(Date.now() / 1000),
      nonce: 'strategy_compare_nonce',
      deviceId: 'strategy_compare_device',
      method: 'POST',
      url: '/api/test',
      data: { test: 'strategy comparison' }
    });
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  }
  
  console.log('\n✅ 所有示例运行完成');
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(console.error);
}

export {
  basicIntegrationExample,
  advancedIntegrationExample,
  errorHandlingExample,
  performanceMonitoringExample,
  main as runAllExamples
};
