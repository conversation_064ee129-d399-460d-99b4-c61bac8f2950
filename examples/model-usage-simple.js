/**
 * 当贝AI模型调用简单示例 (JavaScript版本)
 * 基于 models.json 中的模型列表，演示如何调用不同的AI模型
 */

const { DangbeiProvider } = require('../dist');

/**
 * 支持的AI模型列表（基于 models.json）
 */
const MODELS = {
  // 推荐的推理模型
  DEEPSEEK_R1: 'deepseek',                    // DeepSeek-R1最新版 - 逻辑推理专家
  DOUBAO_1_6: 'doubao-1_6-thinking',         // 豆包-1.6 - 创作推理增强
  GLM_4_5: 'glm-4-5',                        // GLM-4.5 - 智谱旗舰模型
  
  // 高效响应模型
  DEEPSEEK_V3: 'deepseek-v3',                // DeepSeek-V3 - 代码专家
  KIMI_K2: 'kimi-k2-0711-preview',          // Kimi K2 - Agent任务专家
  QWEN_PLUS: 'qwen-plus',                    // 通义Plus - 问题解析专家
};

/**
 * 基础模型调用示例
 */
async function basicExample() {
  console.log('=== 基础模型调用示例 ===\n');

  // 创建Provider实例
  const provider = new DangbeiProvider({
    debug: true,
    timeout: 60000
  });

  try {
    // 1. 快速聊天 - 使用默认模型
    console.log('1. 快速聊天（默认模型）...');
    const quickResponse = await provider.quickChat('你好，请介绍一下你自己');
    console.log('回复:', quickResponse.content.substring(0, 200) + '...\n');

    // 2. 创建对话并使用指定模型
    console.log('2. 创建对话并使用DeepSeek-R1模型...');
    const conversation = await provider.createConversation();
    
    const logicResponse = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '请分析：为什么1+1=2？请从数学逻辑角度详细解释',
      model: MODELS.DEEPSEEK_R1  // 使用DeepSeek-R1进行逻辑推理
    });
    console.log('DeepSeek-R1回复:', logicResponse.substring(0, 300) + '...\n');

    // 3. 使用代码专家模型
    console.log('3. 使用DeepSeek-V3进行代码分析...');
    const codeResponse = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '请解释这段Python代码的功能并优化：\n```python\ndef sum_list(lst):\n    total = 0\n    for i in lst:\n        total = total + i\n    return total\n```',
      model: MODELS.DEEPSEEK_V3  // 使用DeepSeek-V3分析代码
    });
    console.log('DeepSeek-V3回复:', codeResponse.substring(0, 300) + '...\n');

  } catch (error) {
    console.error('调用失败:', error.message);
  }
}

/**
 * 流式响应示例
 */
async function streamingExample() {
  console.log('=== 流式响应示例 ===\n');

  const provider = new DangbeiProvider({ debug: false });

  try {
    console.log('使用GLM-4.5进行创意写作（实时显示）:');
    console.log('问题: 写一个关于AI与人类合作的短故事\n');
    console.log('AI回复: ');

    // 使用流式回调实时显示内容
    await provider.quickChat('请写一个关于AI与人类合作的短故事，要求情节生动有趣', {
      onMessage: (content) => {
        process.stdout.write(content);  // 实时显示内容
      },
      onComplete: () => {
        console.log('\n\n✅ 故事创作完成！\n');
      },
      onError: (error) => {
        console.error('\n❌ 创作失败:', error.message);
      }
    });

  } catch (error) {
    console.error('流式调用失败:', error.message);
  }
}

/**
 * 多模型对比示例
 */
async function comparisonExample() {
  console.log('=== 多模型对比示例 ===\n');

  const provider = new DangbeiProvider({ debug: false });

  try {
    const conversation = await provider.createConversation();
    const question = '请解释什么是区块链技术，并说明其主要应用场景';

    console.log(`测试问题: ${question}\n`);
    console.log('=' .repeat(60));

    // 对比不同模型的回答
    const modelsToTest = [
      { name: 'DeepSeek-R1（推理专家）', value: MODELS.DEEPSEEK_R1 },
      { name: '豆包-1.6（创作增强）', value: MODELS.DOUBAO_1_6 },
      { name: '通义Plus（问题解析）', value: MODELS.QWEN_PLUS }
    ];

    for (const model of modelsToTest) {
      console.log(`\n🤖 ${model.name}:`);
      console.log('-' .repeat(40));
      
      try {
        const startTime = Date.now();
        const response = await provider.chatSync({
          conversationId: conversation.conversationId,
          question: question,
          model: model.value
        });
        const duration = Date.now() - startTime;
        
        console.log(response.substring(0, 300) + '...');
        console.log(`\n⏱️ 响应时间: ${duration}ms`);
        
      } catch (error) {
        console.log(`❌ 调用失败: ${error.message}`);
      }
      
      console.log('=' .repeat(60));
    }

  } catch (error) {
    console.error('对比测试失败:', error.message);
  }
}

/**
 * 高级配置示例
 */
async function advancedExample() {
  console.log('=== 高级配置示例 ===\n');

  const provider = new DangbeiProvider({ debug: true });

  try {
    const conversation = await provider.createConversation();

    // 使用高级配置选项
    console.log('使用高级配置调用模型（启用知识搜索）...');
    
    const response = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '请分析2024年人工智能技术的最新发展趋势，并提供相关资料',
      model: MODELS.GLM_4_5,
      chatOption: {
        searchKnowledge: true,        // 启用知识搜索
        searchAllKnowledge: false,    // 不搜索所有知识库
        searchSharedKnowledge: true   // 启用共享知识库
      }
    });

    console.log('高级配置回复:', response.substring(0, 400) + '...\n');

  } catch (error) {
    console.error('高级配置调用失败:', error.message);
  }
}

/**
 * 显示所有支持的模型
 */
function showSupportedModels() {
  console.log('=== 支持的AI模型列表 ===\n');
  
  console.log('🧠 推理模型（适合逻辑分析、数学推理）:');
  console.log('• DeepSeek-R1最新版 (deepseek) - 逻辑推理与深度分析专家 🌟推荐');
  console.log('• 豆包-1.6 (doubao-1_6-thinking) - 创作、推理、数学增强 🌟推荐');
  console.log('• GLM-4.5 (glm-4-5) - 智谱旗舰模型，支持思考模式切换');
  console.log('• 通义3-235B (qwen3-235b-a22b) - 国内首个混合推理模型');
  console.log('• MiniMax-M1 (MiniMax-M1) - 80K思维链 x 1M输入');
  console.log('• 通义QwQ (qwq-plus) - 善解难题，精准表达');
  console.log('• 豆包-1.5 (doubao-thinking) - 专精数理编程\n');

  console.log('⚡ 高效模型（适合快速响应、代码分析）:');
  console.log('• DeepSeek-V3 (deepseek-v3) - 代码专家，响应极快');
  console.log('• Kimi K2 (kimi-k2-0711-preview) - Agent任务专家');
  console.log('• GLM-4-Plus (glm-4-plus) - 智谱高智能旗舰');
  console.log('• 豆包 (doubao) - 字节全能AI');
  console.log('• 通义Plus (qwen-plus) - 问题解析专家');
  console.log('• Kimi (moonshot-v1-32k) - 高效问题解析');
  console.log('• 通义Long (qwen-long) - 超长上下文处理');
  console.log('• 文心4.5 (ernie-4.5-turbo-32k) - 各领域复杂任务\n');
}

/**
 * 主函数
 */
async function main() {
  try {
    // 显示支持的模型
    showSupportedModels();
    
    // 运行基础示例
    await basicExample();
    
    // 等待避免请求过频
    console.log('等待2秒...\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行流式示例
    await streamingExample();
    
    console.log('等待2秒...\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行对比示例
    await comparisonExample();
    
    console.log('等待2秒...\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行高级配置示例
    await advancedExample();
    
    console.log('🎉 所有示例执行完成！');
    
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  MODELS,
  basicExample,
  streamingExample,
  comparisonExample,
  advancedExample,
  showSupportedModels
};
