/**
 * 当贝AI WebAssembly 签名模块使用示例
 * 
 * 这个示例展示了如何在 Node.js 中使用当贝AI的 WebAssembly 签名算法
 * 基于对原始 JavaScript 代码的逆向分析实现
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const DangbeiSignatureWasm = require('../src/wasm/dangbei-signature-wasm');
const path = require('path');

/**
 * 主要示例函数
 * 演示如何使用 WebAssembly 签名模块
 */
async function main() {
  console.log('🎯 当贝AI WebAssembly 签名模块使用示例');
  console.log('=' .repeat(50));

  // 创建签名器实例
  const signer = new DangbeiSignatureWasm();

  try {
    // 初始化 WebAssembly 模块
    console.log('\n📦 步骤1: 初始化 WebAssembly 模块');
    const wasmPath = path.join(__dirname, '../sign_bg.wasm');
    await signer.initialize(wasmPath);

    // 准备测试数据
    console.log('\n📝 步骤2: 准备测试数据');
    const testCases = [
      {
        name: '基础签名测试',
        param1: 'test_data_1',
        param2: 'test_nonce_123'
      },
      {
        name: 'JSON数据签名测试',
        param1: '{"question":"你好","model":"kimi-k2-0711-preview"}',
        param2: `${Math.floor(Date.now() / 1000)}:random_nonce_${Math.random().toString(36).substr(2, 9)}`
      },
      {
        name: '空字符串测试',
        param1: '',
        param2: 'empty_test'
      },
      {
        name: '长字符串测试',
        param1: 'a'.repeat(1000),
        param2: 'long_string_test'
      }
    ];

    // 执行签名测试
    console.log('\n🔐 步骤3: 执行签名生成测试');
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\n--- 测试 ${i + 1}: ${testCase.name} ---`);
      
      try {
        const signature = signer.generateSignature(testCase.param1, testCase.param2);
        
        console.log(`✅ 签名生成成功`);
        console.log(`📥 参数1: ${testCase.param1.length > 50 ? testCase.param1.substring(0, 50) + '...' : testCase.param1}`);
        console.log(`📥 参数2: ${testCase.param2}`);
        console.log(`🔐 签名结果: ${signature}`);
        
      } catch (error) {
        console.log(`❌ 签名生成失败: ${error.message}`);
      }
    }

    // 性能测试
    console.log('\n⚡ 步骤4: 性能测试');
    await performanceTest(signer);

    // 模拟实际API调用场景
    console.log('\n🌐 步骤5: 模拟实际API调用场景');
    await simulateApiCall(signer);

  } catch (error) {
    console.error(`❌ 示例执行失败: ${error.message}`);
    console.error(error.stack);
  } finally {
    // 清理资源
    console.log('\n🧹 步骤6: 清理资源');
    signer.destroy();
    console.log('✅ 示例执行完成');
  }
}

/**
 * 性能测试函数
 * 测试签名生成的性能表现
 * 
 * @param {DangbeiSignatureWasm} signer - 签名器实例
 */
async function performanceTest(signer) {
  const iterations = 100;
  const testData1 = '{"test": "performance"}';
  const testData2 = 'performance_nonce';

  console.log(`🏃 开始性能测试 (${iterations} 次迭代)...`);
  
  const startTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    try {
      signer.generateSignature(testData1, `${testData2}_${i}`);
    } catch (error) {
      console.log(`⚠️ 第 ${i + 1} 次迭代失败: ${error.message}`);
    }
  }
  
  const endTime = Date.now();
  const totalTime = endTime - startTime;
  const avgTime = totalTime / iterations;
  
  console.log(`📊 性能测试结果:`);
  console.log(`   总时间: ${totalTime}ms`);
  console.log(`   平均时间: ${avgTime.toFixed(2)}ms/次`);
  console.log(`   吞吐量: ${(1000 / avgTime).toFixed(2)} 次/秒`);
}

/**
 * 模拟实际API调用场景
 * 展示如何在实际应用中使用签名功能
 * 
 * @param {DangbeiSignatureWasm} signer - 签名器实例
 */
async function simulateApiCall(signer) {
  console.log('🎭 模拟当贝AI API调用场景...');

  // 模拟API请求参数
  const apiScenarios = [
    {
      name: '创建对话',
      endpoint: '/ai-search/conversationApi/v1/batch/create',
      method: 'POST',
      data: {
        conversationList: [{
          metaData: { chatModelConfig: {}, superAgentPath: "/chat" },
          shareId: "",
          isAnonymous: false,
          source: ""
        }]
      }
    },
    {
      name: '发送消息',
      endpoint: '/ai-search/chatApi/v2/chat',
      method: 'POST',
      data: {
        stream: true,
        botCode: "AI_SEARCH",
        conversationId: "363022964585267589",
        question: "你好!",
        model: "doubao-1_6-thinking",
        chatOption: {
          searchKnowledge: false,
          searchAllKnowledge: false,
          searchSharedKnowledge: false
        },
        knowledgeList: [],
        anonymousKey: "",
        uuid: "363022965811450053",
        chatId: "363022965811450053",
        files: [],
        reference: [],
        role: "user",
        status: "local",
        content: "你好!",
        userAction: "",
        agentId: ""
      }
    },
    {
      name: '生成ID',
      endpoint: '/ai-search/commonApi/v1/generateId',
      method: 'POST',
      data: {
        timestamp: Date.now()
      }
    }
  ];

  for (const scenario of apiScenarios) {
    console.log(`\n--- ${scenario.name} ---`);
    
    try {
      // 生成时间戳和随机数
      const timestamp = Math.floor(Date.now() / 1000);
      const nonce = generateRandomNonce();
      
      // 准备签名数据
      const requestBody = JSON.stringify(scenario.data);
      const signatureInput = `${timestamp}${requestBody}${nonce}`;
      
      console.log(`🌐 API端点: ${scenario.endpoint}`);
      console.log(`📅 时间戳: ${timestamp}`);
      console.log(`🎲 随机数: ${nonce}`);
      console.log(`📦 请求体: ${requestBody.substring(0, 100)}${requestBody.length > 100 ? '...' : ''}`);
      
      // 使用 WebAssembly 生成签名
      const signature = signer.generateSignature(requestBody, `${timestamp}:${nonce}`);
      
      console.log(`🔐 生成的签名: ${signature}`);
      
      // 模拟完整的请求头
      const headers = {
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'appType': '6',
        'appVersion': '1.1.17-22',
        'client-ver': '1.0.2',
        'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
        'lang': 'zh',
        'nonce': nonce,
        'sign': signature,
        'timestamp': timestamp.toString(),
        'token': ''
      };
      
      console.log(`📋 完整请求头:`, JSON.stringify(headers, null, 2));
      
    } catch (error) {
      console.log(`❌ ${scenario.name} 签名生成失败: ${error.message}`);
    }
  }
}

/**
 * 生成随机nonce字符串
 * 
 * @returns {string} 随机nonce
 */
function generateRandomNonce() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
  let result = '';
  for (let i = 0; i < 17; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 错误处理包装器
 */
function handleError(error) {
  console.error('\n💥 发生错误:');
  console.error(`   消息: ${error.message}`);
  console.error(`   堆栈: ${error.stack}`);
  
  // 提供故障排除建议
  console.log('\n🔧 故障排除建议:');
  console.log('   1. 确保 sign_bg.wasm 文件存在且路径正确');
  console.log('   2. 检查 Node.js 版本是否支持 WebAssembly');
  console.log('   3. 验证 WASM 文件是否完整且未损坏');
  console.log('   4. 查看详细错误日志以获取更多信息');
}

// 运行示例
if (require.main === module) {
  main().catch(handleError);
}

module.exports = {
  main,
  performanceTest,
  simulateApiCall,
  generateRandomNonce
};
