/**
 * WASM v2签名算法演示
 * 展示如何使用新的v2签名算法生成当贝AI接口签名
 */

import { WasmSignatureEmulator } from '../src/utils/wasm-signature-emulator';
import { SignatureV2Utils } from '../src/utils/signature-v2';

/**
 * 演示基础的v2签名生成
 */
function demonstrateBasicV2Signature(): void {
  console.log('🔐 基础v2签名生成演示');
  console.log('=' .repeat(50));

  // 创建WASM签名模拟器
  const emulator = new WasmSignatureEmulator({
    debug: true,
    strategy: 'hybrid',
    secretKey: 'dangbei_ai_demo_2024'
  });

  // 真实的v2/chat接口参数
  const requestData = JSON.stringify({
    stream: true,
    botCode: 'AI_SEARCH',
    conversationId: '364120755967492485',
    question: '使用Agno框架进行多Agent协作时如何进行共享上下文？',
    model: 'kimi-k2-0711-preview',
    chatOption: {
      searchKnowledge: false,
      searchAllKnowledge: false,
      searchSharedKnowledge: false
    },
    knowledgeList: [],
    anonymousKey: '',
    uuid: '364133138684711109',
    chatId: '364133138684711109',
    files: [],
    reference: [],
    role: 'user',
    status: 'local',
    content: '使用Agno框架进行多Agent协作时如何进行共享上下文？',
    userAction: '',
    agentId: ''
  });

  const urlPath = '/chatApi/v2/chat';

  console.log('📊 输入参数:');
  console.log(`  - 请求数据长度: ${requestData.length} 字节`);
  console.log(`  - URL路径: ${urlPath}`);
  console.log(`  - URL路径长度: ${urlPath.length} 字节`);

  // 生成签名
  const result = emulator.getSignV2(requestData, urlPath);

  console.log('\n✅ 签名生成结果:');
  console.log(`  - 签名: ${result.signature}`);
  console.log(`  - 策略: ${result.strategy}`);
  console.log(`  - 时间戳: ${result.timestamp}`);

  if (result.debug) {
    console.log('\n🔍 调试信息:');
    console.log(`  - 算法: ${result.debug.algorithm}`);
    console.log(`  - 规范化数据: ${result.debug.normalizedData}`);
  }
}

/**
 * 演示SignatureV2Utils集成使用
 */
function demonstrateSignatureV2Utils(): void {
  console.log('\n🔧 SignatureV2Utils集成演示');
  console.log('=' .repeat(50));

  // 构建签名参数
  const signatureParams = {
    timestamp: Math.floor(Date.now() / 1000),
    nonce: 'demo_nonce_' + Math.random().toString(36).substring(7),
    deviceId: 'demo_device_12345',
    method: 'POST' as const,
    url: '/ai-search/chatApi/v2/chat',
    data: {
      stream: true,
      botCode: 'AI_SEARCH',
      question: '你好，请介绍一下当贝AI的功能',
      model: 'kimi-k2-0711-preview',
      chatOption: {
        searchKnowledge: false,
        searchAllKnowledge: false,
        searchSharedKnowledge: false
      }
    }
  };

  console.log('📊 签名参数:');
  console.log(`  - 时间戳: ${signatureParams.timestamp}`);
  console.log(`  - 随机数: ${signatureParams.nonce}`);
  console.log(`  - 设备ID: ${signatureParams.deviceId}`);
  console.log(`  - 方法: ${signatureParams.method}`);
  console.log(`  - URL: ${signatureParams.url}`);

  // 生成签名
  const signature = SignatureV2Utils.generateV2Signature(signatureParams);

  console.log('\n✅ 生成的签名:');
  console.log(`  - 签名值: ${signature}`);
  console.log(`  - 签名长度: ${signature.length} 字符`);
  console.log(`  - 格式验证: ${/^[A-F0-9]{32}$/.test(signature) ? '✅ 有效' : '❌ 无效'}`);
}

/**
 * 演示不同策略的签名对比
 */
function demonstrateStrategyComparison(): void {
  console.log('\n🔄 不同策略签名对比');
  console.log('=' .repeat(50));

  const testData = {
    requestData: '{"question":"测试问题","model":"kimi-k2-0711-preview"}',
    urlPath: '/chatApi/v2/chat'
  };

  const strategies = ['standard', 'enhanced', 'hybrid'] as const;

  console.log('📊 测试数据:');
  console.log(`  - 请求数据: ${testData.requestData}`);
  console.log(`  - URL路径: ${testData.urlPath}`);

  console.log('\n🔐 不同策略生成的签名:');

  for (const strategy of strategies) {
    const emulator = new WasmSignatureEmulator({
      debug: false,
      strategy,
      secretKey: `dangbei_ai_${strategy}_2024`
    });

    const result = emulator.getSignV2(testData.requestData, testData.urlPath);

    console.log(`  - ${strategy.toUpperCase().padEnd(8)}: ${result.signature}`);
  }
}

/**
 * 演示性能测试
 */
function demonstratePerformanceTest(): void {
  console.log('\n⚡ 性能测试演示');
  console.log('=' .repeat(50));

  const emulator = new WasmSignatureEmulator({
    debug: false,
    strategy: 'hybrid'
  });

  const testData = {
    requestData: '{"stream":true,"botCode":"AI_SEARCH","question":"性能测试"}',
    urlPath: '/chatApi/v2/chat'
  };

  const iterations = 1000;
  console.log(`📊 执行 ${iterations} 次签名生成...`);

  const startTime = Date.now();

  for (let i = 0; i < iterations; i++) {
    emulator.getSignV2(testData.requestData, testData.urlPath);
  }

  const endTime = Date.now();
  const duration = endTime - startTime;
  const avgTime = duration / iterations;
  const throughput = Math.round(iterations / (duration / 1000));

  console.log('\n📈 性能指标:');
  console.log(`  - 总耗时: ${duration}ms`);
  console.log(`  - 平均耗时: ${avgTime.toFixed(3)}ms`);
  console.log(`  - 吞吐量: ${throughput.toLocaleString()} 次/秒`);
}

/**
 * 主演示函数
 */
function main(): void {
  console.log('🚀 WASM v2签名算法演示程序');
  console.log('基于真实WASM调用模式的签名生成');
  console.log('=' .repeat(80));

  try {
    // 基础签名演示
    demonstrateBasicV2Signature();

    // 集成使用演示
    demonstrateSignatureV2Utils();

    // 策略对比演示
    demonstrateStrategyComparison();

    // 性能测试演示
    demonstratePerformanceTest();

    console.log('\n🎉 演示完成！');
    console.log('新的v2签名算法已成功实现，可以用于当贝AI接口调用。');

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  main();
}

export {
  demonstrateBasicV2Signature,
  demonstrateSignatureV2Utils,
  demonstrateStrategyComparison,
  demonstratePerformanceTest
};
