/**
 * 高级使用示例
 * 演示当贝AI Provider的高级功能和自定义配置
 */

import { 
  DangbeiProvider, 
  HttpClient, 
  ConversationService, 
  ChatService,
  DeviceUtils,
  SignatureUtils,
  ChatCallbacks,
  DeviceConfig
} from '../src';

async function advancedUsageExample(): Promise<void> {
  console.log('=== 当贝AI Provider 高级使用示例 ===\n');

  // 1. 自定义设备配置
  console.log('1. 创建自定义设备配置...');
  const customDeviceConfig: Partial<DeviceConfig> = {
    deviceId: DeviceUtils.generateDeviceId('custom-seed-123'),
    lang: 'en',
    appVersion: '2.0.0-custom',
    userAgent: 'CustomApp/2.0.0 (Custom Platform)'
  };

  const provider = new DangbeiProvider({
    deviceConfig: customDeviceConfig,
    debug: true,
    timeout: 45000,
    retries: 5
  });

  console.log('自定义设备配置:', provider.getDeviceConfig());

  try {
    // 2. 使用底层服务
    console.log('\n2. 使用底层服务...');
    
    // 创建自定义HTTP客户端
    const httpClient = new HttpClient(customDeviceConfig);
    const conversationService = new ConversationService(httpClient);
    const chatService = new ChatService(httpClient);

    // 使用底层服务创建对话
    const conversation = await conversationService.createConversation({
      superAgentPath: '/advanced-chat',
      isAnonymous: false,
      source: 'advanced-example'
    });
    console.log('通过底层服务创建的对话ID:', conversation.conversationId);

    // 3. 批量ID生成
    console.log('\n3. 批量生成ID...');
    const ids = await Promise.all([
      provider.generateId(),
      provider.generateId(),
      provider.generateId()
    ]);
    console.log('批量生成的ID:', ids);

    // 4. 多轮对话示例
    console.log('\n4. 多轮对话示例...');
    const questions = [
      '什么是深度学习？',
      '深度学习和机器学习有什么区别？',
      '请举一个深度学习的实际应用例子'
    ];

    for (let i = 0; i < questions.length; i++) {
      console.log(`\n第${i + 1}轮对话:`);
      console.log('问题:', questions[i]);
      
      const response = await provider.chatSync({
        conversationId: conversation.conversationId,
        question: questions[i],
        model: 'doubao-1_6-thinking'
      });
      
      console.log('回答:', response.substring(0, 150) + '...');
    }

    // 5. 并发聊天示例
    console.log('\n5. 并发聊天示例...');
    const concurrentQuestions = [
      '介绍一下Python编程语言',
      '什么是云计算？',
      '区块链技术的原理是什么？'
    ];

    const concurrentPromises = concurrentQuestions.map(async (question, index) => {
      // 为每个并发聊天创建独立的对话
      const conv = await provider.createConversation();
      const response = await provider.chatSync({
        conversationId: conv.conversationId,
        question
      });
      return {
        index: index + 1,
        question,
        response: response.substring(0, 100) + '...'
      };
    });

    const concurrentResults = await Promise.all(concurrentPromises);
    concurrentResults.forEach(result => {
      console.log(`并发聊天${result.index}:`);
      console.log('问题:', result.question);
      console.log('回答:', result.response);
      console.log('---');
    });

    // 6. 流式聊天with进度跟踪
    console.log('\n6. 带进度跟踪的流式聊天...');
    let messageCount = 0;
    let totalLength = 0;
    const startTime = Date.now();

    const progressCallbacks: ChatCallbacks = {
      onMessage: (content, data) => {
        messageCount++;
        totalLength += content.length;
        
        // 每10个消息片段显示一次进度
        if (messageCount % 10 === 0) {
          const elapsed = Date.now() - startTime;
          console.log(`\n进度: ${messageCount}个片段, ${totalLength}字符, ${elapsed}ms`);
        }
        
        process.stdout.write(content);
      },
      onComplete: (data) => {
        const elapsed = Date.now() - startTime;
        console.log(`\n\n流式聊天完成:`);
        console.log(`- 总片段数: ${messageCount}`);
        console.log(`- 总字符数: ${totalLength}`);
        console.log(`- 总耗时: ${elapsed}ms`);
        console.log(`- 平均速度: ${(totalLength / elapsed * 1000).toFixed(2)} 字符/秒`);
      },
      onError: (error) => {
        console.error('\n流式聊天错误:', error);
      }
    };

    await provider.chat({
      conversationId: conversation.conversationId,
      question: '请详细介绍人工智能在医疗领域的应用，包括具体的技术和案例',
      callbacks: progressCallbacks
    });

    // 7. 错误恢复示例
    console.log('\n\n7. 错误恢复示例...');
    try {
      // 故意使用无效的对话ID
      await provider.chat({
        conversationId: 'invalid-conversation-id',
        question: '这应该会失败'
      });
    } catch (error) {
      console.log('捕获到预期的错误，尝试恢复...');
      
      // 创建新对话并重试
      const newConversation = await provider.createConversation();
      const recoveryResponse = await provider.chatSync({
        conversationId: newConversation.conversationId,
        question: '错误恢复后的测试消息'
      });
      console.log('错误恢复成功:', recoveryResponse.substring(0, 100) + '...');
    }

    // 8. 性能监控
    console.log('\n8. 性能监控示例...');
    const performanceTest = async () => {
      const testStart = Date.now();
      const testConversation = await provider.createConversation();
      const createTime = Date.now() - testStart;

      const chatStart = Date.now();
      await provider.chatSync({
        conversationId: testConversation.conversationId,
        question: '简单测试问题'
      });
      const chatTime = Date.now() - chatStart;

      return { createTime, chatTime };
    };

    const { createTime, chatTime } = await performanceTest();
    console.log('性能指标:');
    console.log(`- 对话创建耗时: ${createTime}ms`);
    console.log(`- 聊天响应耗时: ${chatTime}ms`);

  } catch (error) {
    console.error('\n❌ 高级示例执行失败:', error);
  } finally {
    // 清理资源
    console.log('\n9. 清理资源...');
    provider.destroy();
    console.log('所有资源已清理完成');
  }
}

// 工具函数示例
function demonstrateUtilities(): void {
  console.log('\n=== 工具函数示例 ===');

  // 签名工具示例
  console.log('\n签名工具示例:');
  const timestamp = SignatureUtils.getTimestamp();
  const nonce = SignatureUtils.generateNonce(20);
  const signature = SignatureUtils.generateSignature({
    timestamp,
    nonce,
    deviceId: 'example-device-id',
    data: { test: 'data' }
  });
  
  console.log('时间戳:', timestamp);
  console.log('随机字符串:', nonce);
  console.log('生成的签名:', signature);
  console.log('签名验证:', SignatureUtils.verifySignature(signature, {
    timestamp,
    nonce,
    deviceId: 'example-device-id',
    data: { test: 'data' }
  }));

  // 设备工具示例
  console.log('\n设备工具示例:');
  const deviceId = DeviceUtils.generateDeviceId('test-seed');
  const isValid = DeviceUtils.isValidDeviceId(deviceId);
  const defaultConfig = DeviceUtils.createDefaultDeviceConfig();
  
  console.log('生成的设备ID:', deviceId);
  console.log('设备ID有效性:', isValid);
  console.log('默认设备配置:', defaultConfig);
}

// 运行示例
if (require.main === module) {
  Promise.resolve()
    .then(() => demonstrateUtilities())
    .then(() => advancedUsageExample())
    .then(() => {
      console.log('\n✅ 高级示例执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 高级示例执行失败:', error);
      process.exit(1);
    });
}

export { advancedUsageExample, demonstrateUtilities };
