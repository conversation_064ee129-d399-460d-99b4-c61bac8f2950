/**
 * 当贝AI模型调用示例
 * 基于 models.json 中列举的模型列表，演示如何调用不同的AI模型
 */

import { DangbeiProvider, ChatCallbacks, ChatOptions } from '../src';

/**
 * 支持的AI模型列表（基于 models.json）
 */
const SUPPORTED_MODELS = {
  // 推理模型（支持深度思考）
  DEEPSEEK_R1: 'deepseek',                    // DeepSeek-R1最新版 - 专注逻辑推理与深度分析
  DOUBAO_1_6: 'doubao-1_6-thinking',         // 豆包-1.6 - 创作、推理、数学大幅增强
  GLM_4_5: 'glm-4-5',                        // GLM-4.5 - 智谱最新旗舰模型
  QWEN3_235B: 'qwen3-235b-a22b',             // 通义3-235B - 国内首个混合推理模型
  MINIMAX_M1: 'MiniMax-M1',                  // MiniMax-M1 - 80K思维链 x 1M输入
  QWQ_PLUS: 'qwq-plus',                      // 通义QwQ - 善解难题，精准表达
  DOUBAO_1_5_THINKING: 'doubao-thinking',    // 豆包-1.5 - 推理模型，专精数理编程

  // 高效模型（响应快速）
  DEEPSEEK_V3: 'deepseek-v3',                // DeepSeek-V3 - 轻量高效，响应极快
  KIMI_K2: 'kimi-k2-0711-preview',          // Kimi K2 - 更强代码能力
  GLM_4_PLUS: 'glm-4-plus',                  // GLM-4-Plus - 智谱最强高智能旗舰模型
  DOUBAO: 'doubao',                          // 豆包 - 字节全能AI
  QWEN_PLUS: 'qwen-plus',                    // 通义Plus - 复杂问题速解专家
  KIMI: 'moonshot-v1-32k',                   // Kimi - 高效问题解析者
  QWEN_LONG: 'qwen-long',                    // 通义Long - 超长上下文处理
  ERNIE_4_5: 'ernie-4.5-turbo-32k'          // 文心4.5 - 广泛适用于各领域复杂任务
};

/**
 * 模型特性说明
 */
const MODEL_DESCRIPTIONS = {
  [SUPPORTED_MODELS.DEEPSEEK_R1]: {
    name: 'DeepSeek-R1最新版',
    description: '专注逻辑推理与深度分析，擅长解决复杂问题，提供精准决策支持',
    features: ['深度思考', '联网搜索'],
    recommended: true,
    category: '推理模型'
  },
  [SUPPORTED_MODELS.DOUBAO_1_6]: {
    name: '豆包-1.6',
    description: '豆包最新推理模型，创作、推理、数学大幅增强',
    features: ['深度思考', '联网搜索'],
    recommended: true,
    category: '推理模型'
  },
  [SUPPORTED_MODELS.GLM_4_5]: {
    name: 'GLM-4.5',
    description: '智谱最新旗舰模型，支持思考模式切换，综合能力达到开源模型的SOTA水平',
    features: ['深度思考', '联网搜索'],
    recommended: false,
    category: '推理模型'
  },
  [SUPPORTED_MODELS.DEEPSEEK_V3]: {
    name: 'DeepSeek-V3',
    description: '轻量高效，响应极快。擅长代码，可高效解析代码与图表',
    features: ['联网搜索'],
    recommended: false,
    category: '高效模型'
  },
  [SUPPORTED_MODELS.KIMI_K2]: {
    name: 'Kimi K2',
    description: '具备更强代码能力、更擅长通用Agent任务',
    features: ['联网搜索'],
    recommended: false,
    category: '高效模型'
  }
};

/**
 * 基础模型调用示例
 */
async function basicModelUsageExample(): Promise<void> {
  console.log('=== 当贝AI模型调用基础示例 ===\n');

  // 创建Provider实例
  const provider = new DangbeiProvider({
    debug: true,
    timeout: 60000  // 推理模型可能需要更长时间
  });

  try {
    // 创建对话
    console.log('1. 创建对话会话...');
    const conversation = await provider.createConversation({
      source: 'model-usage-example'
    });
    console.log(`对话ID: ${conversation.conversationId}\n`);

    // 使用默认模型（豆包-1.6）进行对话
    console.log('2. 使用默认模型进行对话...');
    const defaultResponse = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '请简单介绍一下你自己，包括你的能力和特点'
    });
    console.log('默认模型回复:', defaultResponse.substring(0, 200) + '...\n');

    // 使用DeepSeek-R1进行逻辑推理
    console.log('3. 使用DeepSeek-R1进行逻辑推理...');
    const deepseekResponse = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '请分析以下逻辑问题：如果所有的猫都是动物，所有的动物都需要食物，那么所有的猫都需要食物吗？请详细说明推理过程。',
      model: SUPPORTED_MODELS.DEEPSEEK_R1
    });
    console.log('DeepSeek-R1回复:', deepseekResponse.substring(0, 300) + '...\n');

    // 使用DeepSeek-V3进行代码分析
    console.log('4. 使用DeepSeek-V3进行代码分析...');
    const codeAnalysisResponse = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '请分析这段JavaScript代码的功能：\n```javascript\nfunction fibonacci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n-1) + fibonacci(n-2);\n}\n```',
      model: SUPPORTED_MODELS.DEEPSEEK_V3
    });
    console.log('DeepSeek-V3回复:', codeAnalysisResponse.substring(0, 300) + '...\n');

  } catch (error) {
    console.error('模型调用失败:', error);
  }
}

/**
 * 流式响应模型调用示例
 */
async function streamingModelUsageExample(): Promise<void> {
  console.log('=== 流式响应模型调用示例 ===\n');

  const provider = new DangbeiProvider({
    debug: true
  });

  try {
    // 创建对话
    const conversation = await provider.createConversation();
    console.log(`对话ID: ${conversation.conversationId}\n`);

    // 定义流式回调函数
    const callbacks: ChatCallbacks = {
      onMessage: (content: string, data: any) => {
        // 实时显示AI回复内容
        process.stdout.write(content);
      },
      onComplete: (data: any) => {
        console.log('\n\n✅ 对话完成\n');
      },
      onError: (error: Error) => {
        console.error('\n❌ 对话出错:', error.message);
      }
    };

    // 使用GLM-4.5进行创意写作
    console.log('使用GLM-4.5进行创意写作（流式响应）:');
    console.log('问题: 请写一首关于人工智能的现代诗\n');
    console.log('AI回复: ');
    
    await provider.chat({
      conversationId: conversation.conversationId,
      question: '请写一首关于人工智能的现代诗，要求有深度和美感',
      model: SUPPORTED_MODELS.GLM_4_5,
      callbacks
    });

  } catch (error) {
    console.error('流式模型调用失败:', error);
  }
}

/**
 * 多模型对比示例
 */
async function multiModelComparisonExample(): Promise<void> {
  console.log('=== 多模型对比示例 ===\n');

  const provider = new DangbeiProvider({
    debug: false  // 关闭调试日志以便更清晰地看到对比结果
  });

  try {
    // 创建对话
    const conversation = await provider.createConversation();
    
    // 测试问题
    const testQuestion = '请解释什么是机器学习，并举一个实际应用的例子';
    
    // 要对比的模型列表
    const modelsToCompare = [
      { name: 'DeepSeek-R1', value: SUPPORTED_MODELS.DEEPSEEK_R1 },
      { name: '豆包-1.6', value: SUPPORTED_MODELS.DOUBAO_1_6 },
      { name: 'DeepSeek-V3', value: SUPPORTED_MODELS.DEEPSEEK_V3 },
      { name: 'GLM-4.5', value: SUPPORTED_MODELS.GLM_4_5 }
    ];

    console.log(`测试问题: ${testQuestion}\n`);
    console.log('=' .repeat(80));

    // 依次使用不同模型回答同一个问题
    for (const model of modelsToCompare) {
      console.log(`\n🤖 ${model.name} (${model.value}) 的回答:`);
      console.log('-' .repeat(50));
      
      try {
        const startTime = Date.now();
        const response = await provider.chatSync({
          conversationId: conversation.conversationId,
          question: testQuestion,
          model: model.value
        });
        const endTime = Date.now();
        
        console.log(response.substring(0, 400) + '...');
        console.log(`\n⏱️ 响应时间: ${endTime - startTime}ms`);
        
      } catch (error) {
        console.log(`❌ 调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
      
      console.log('=' .repeat(80));
    }

  } catch (error) {
    console.error('多模型对比失败:', error);
  }
}

/**
 * 高级模型配置示例
 */
async function advancedModelConfigExample(): Promise<void> {
  console.log('=== 高级模型配置示例 ===\n');

  const provider = new DangbeiProvider({
    debug: true
  });

  try {
    // 创建对话
    const conversation = await provider.createConversation();

    // 使用高级配置选项
    const advancedChatOptions: ChatOptions = {
      conversationId: conversation.conversationId,
      question: '请帮我分析当前AI技术发展趋势，并提供一些相关的最新资料',
      model: SUPPORTED_MODELS.QWEN3_235B,  // 使用通义3-235B模型
      botCode: 'AI_SEARCH',
      chatOption: {
        searchKnowledge: true,        // 启用知识搜索
        searchAllKnowledge: false,    // 不搜索所有知识库
        searchSharedKnowledge: true   // 启用共享知识库搜索
      }
    };

    console.log('使用高级配置调用通义3-235B模型...');
    console.log('配置: 启用知识搜索 + 共享知识库');
    
    const response = await provider.chatSync(advancedChatOptions);
    console.log('\n模型回复:', response.substring(0, 500) + '...\n');

  } catch (error) {
    console.error('高级模型配置调用失败:', error);
  }
}

/**
 * 显示所有支持的模型信息
 */
function displaySupportedModels(): void {
  console.log('=== 支持的AI模型列表 ===\n');
  
  const categories = ['推理模型', '高效模型'];
  
  categories.forEach(category => {
    console.log(`📂 ${category}:`);
    console.log('-' .repeat(40));
    
    Object.entries(MODEL_DESCRIPTIONS).forEach(([modelValue, info]) => {
      if (info.category === category) {
        const recommendedTag = info.recommended ? ' 🌟推荐' : '';
        console.log(`• ${info.name}${recommendedTag}`);
        console.log(`  模型值: ${modelValue}`);
        console.log(`  描述: ${info.description}`);
        console.log(`  功能: ${info.features.join(', ')}`);
        console.log('');
      }
    });
  });
}

/**
 * 主函数 - 运行所有示例
 */
async function main(): Promise<void> {
  try {
    // 显示支持的模型列表
    displaySupportedModels();
    
    // 运行基础示例
    await basicModelUsageExample();
    
    // 等待一段时间避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行流式响应示例
    await streamingModelUsageExample();
    
    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行多模型对比示例
    await multiModelComparisonExample();
    
    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行高级配置示例
    await advancedModelConfigExample();
    
    console.log('\n🎉 所有模型调用示例执行完成！');
    
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main().catch(console.error);
}

// 导出示例函数供其他模块使用
export {
  SUPPORTED_MODELS,
  MODEL_DESCRIPTIONS,
  basicModelUsageExample,
  streamingModelUsageExample,
  multiModelComparisonExample,
  advancedModelConfigExample,
  displaySupportedModels
};
