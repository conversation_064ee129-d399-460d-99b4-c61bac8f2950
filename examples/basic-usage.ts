/**
 * 基础使用示例
 * 演示当贝AI Provider的基本功能
 */

import { DangbeiProvider, ChatCallbacks, DangbeiApiError, ErrorType } from '../src';

async function basicUsageExample(): Promise<void> {
  console.log('=== 当贝AI Provider 基础使用示例 ===\n');

  // 1. 创建Provider实例
  console.log('1. 创建Provider实例...');
  const provider = new DangbeiProvider({
    debug: true,
    timeout: 30000,
    retries: 3
  });

  try {
    // 2. 检查服务可用性
    console.log('\n2. 检查服务可用性...');
    const isAvailable = await provider.checkServiceAvailability();
    console.log('服务可用性:', isAvailable);

    // 3. 获取设备配置
    console.log('\n3. 设备配置信息:');
    const deviceConfig = provider.getDeviceConfig();
    console.log('设备ID:', deviceConfig.deviceId);
    console.log('应用版本:', deviceConfig.appVersion);
    console.log('语言设置:', deviceConfig.lang);

    // 4. 生成ID
    console.log('\n4. 生成唯一ID...');
    const id1 = await provider.generateId();
    const id2 = await provider.generateId();
    console.log('生成的ID1:', id1);
    console.log('生成的ID2:', id2);

    // 5. 创建对话
    console.log('\n5. 创建新对话...');
    const conversation = await provider.createConversation({
      superAgentPath: '/chat',
      isAnonymous: false,
      source: 'example'
    });
    console.log('对话创建成功:');
    console.log('- 对话ID:', conversation.conversationId);
    console.log('- 标题:', conversation.title);
    console.log('- 类型:', conversation.conversationType);

    // 6. 发送聊天消息（同步方式）
    console.log('\n6. 发送聊天消息（同步方式）...');
    const syncResponse = await provider.chatSync({
      conversationId: conversation.conversationId,
      question: '你好，请简单介绍一下自己',
      model: 'doubao-1_6-thinking'
    });
    console.log('同步响应:', syncResponse.substring(0, 100) + '...');

    // 7. 发送聊天消息（流式方式）
    console.log('\n7. 发送聊天消息（流式方式）...');
    let streamContent = '';
    
    const callbacks: ChatCallbacks = {
      onMessage: (content, data) => {
        streamContent += content;
        process.stdout.write(content); // 实时输出
      },
      onComplete: (data) => {
        console.log('\n流式聊天完成:');
        console.log('- 消息ID:', data.id);
        console.log('- 对话ID:', data.conversation_id);
        console.log('- 总内容长度:', streamContent.length);
      },
      onError: (error) => {
        console.error('流式聊天错误:', error);
      }
    };

    await provider.chat({
      conversationId: conversation.conversationId,
      question: '请详细解释一下人工智能的基本概念',
      callbacks
    });

    // 8. 快速聊天（自动创建对话）
    console.log('\n\n8. 快速聊天示例...');
    const quickResponse = await provider.quickChat('什么是机器学习？');
    console.log('快速聊天响应:');
    console.log('- 内容长度:', quickResponse.content.length);
    console.log('- 消息ID:', quickResponse.messageId);
    console.log('- 对话ID:', quickResponse.conversationId);

    // 9. 获取服务状态
    console.log('\n9. 服务状态信息:');
    const status = provider.getStatus();
    console.log('- 设备ID:', status.deviceId);
    console.log('- 聊天连接状态:', status.chatStatus.isConnected);
    console.log('- 连接就绪状态:', status.chatStatus.readyState);
    console.log('- 服务可用:', status.serviceAvailable);

  } catch (error) {
    console.error('\n❌ 示例执行失败:');
    
    if (error instanceof DangbeiApiError) {
      console.error('错误类型:', error.type);
      console.error('错误代码:', error.code);
      console.error('错误消息:', error.message);
      console.error('请求ID:', error.requestId);
      
      // 根据错误类型提供建议
      switch (error.type) {
        case ErrorType.NETWORK_ERROR:
          console.error('建议: 检查网络连接或稍后重试');
          break;
        case ErrorType.API_ERROR:
          console.error('建议: 检查API参数或联系技术支持');
          break;
        case ErrorType.PARAMETER_ERROR:
          console.error('建议: 检查输入参数是否正确');
          break;
        case ErrorType.TIMEOUT_ERROR:
          console.error('建议: 增加超时时间或稍后重试');
          break;
        default:
          console.error('建议: 查看详细错误信息或联系技术支持');
      }
    } else {
      console.error('未知错误:', error);
    }
  } finally {
    // 10. 清理资源
    console.log('\n10. 清理资源...');
    provider.destroy();
    console.log('Provider实例已销毁');
  }
}

// 运行示例
if (require.main === module) {
  basicUsageExample()
    .then(() => {
      console.log('\n✅ 示例执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 示例执行失败:', error);
      process.exit(1);
    });
}

export { basicUsageExample };
