/**
 * 当贝 AI /v2/chat 接口签名调试 Hook 脚本集合
 * 
 * 使用方法：
 * 1. 打开 ai.dangbei.com 聊天页面
 * 2. 打开浏览器控制台
 * 3. 依次粘贴需要的 Hook 函数并执行
 * 4. 触发 /v2/chat 请求，观察控制台输出
 * 
 * 注意：这些脚本仅用于调试分析，不会修改实际请求
 */

// ==================== 1. CryptoJS.MD5 劫持 ====================

/**
 * 劫持 CryptoJS.MD5 直接调用
 * 用于捕获 MD5 计算的输入参数和输出结果
 */
function installMd5Hook() {
  function decodeArg(m) {
    try {
      if (typeof m === 'string') return m;
      if (m && m.sigBytes != null && m.words) {
        try { return CryptoJS.enc.Utf8.stringify(m); } 
        catch { return CryptoJS.enc.Hex.stringify(m); }
      }
      return JSON.stringify(m);
    } catch {
      try { return String(m); } catch { return '[无法显示参数]'; }
    }
  }

  function patch() {
    if (!window.CryptoJS || !CryptoJS.MD5) return false;
    if (CryptoJS.MD5.__aug_patched__) return true;

    const orig = CryptoJS.MD5;
    CryptoJS.MD5 = function (...args) {
      try {
        const inputReadable = args.map(decodeArg);
        console.log('[签名调试] 调用 CryptoJS.MD5 入参(可读):', inputReadable);
        console.log('[签名调试] 调用 CryptoJS.MD5 原始参数:', args);
        console.trace('[签名调试] 调用堆栈');
      } catch (e) {
        console.warn('[签名调试] 打印入参失败:', e);
      }
      const res = orig.apply(this, args);
      try {
        console.log('[签名调试] CryptoJS.MD5 输出(HEX):', res.toString());
      } catch {}
      return res;
    };
    CryptoJS.MD5.__aug_patched__ = true;
    console.log('[签名调试] CryptoJS.MD5 已劫持');
    return true;
  }

  if (!patch()) {
    const id = setInterval(() => { if (patch()) clearInterval(id); }, 100);
  }
}

// ==================== 2. CryptoJS.algo.MD5 流式 API 劫持 ====================

/**
 * 劫持 CryptoJS.algo.MD5 的 create/update/finalize 方法
 * 用于捕获分段更新的 MD5 计算
 */
function installMd5AlgoHook() {
  if (!window.CryptoJS || !CryptoJS.algo || !CryptoJS.algo.MD5) {
    return console.warn('[签名调试] 未发现 CryptoJS.algo.MD5');
  }
  const P = CryptoJS.algo.MD5.prototype;
  if (P.__aug_patched__) return console.log('[签名调试] MD5 原型已劫持');

  const toStr = (m) => {
    try {
      if (typeof m === 'string') return m;
      if (m && m.sigBytes != null && m.words) {
        try { return CryptoJS.enc.Utf8.stringify(m); } catch { return CryptoJS.enc.Hex.stringify(m); }
      }
      return JSON.stringify(m);
    } catch { return String(m); }
  };

  const origUpdate = P.update;
  const origFinalize = P.finalize;

  P.update = function (messageUpdate) {
    try { this.__aug_buf__ = (this.__aug_buf__ || '') + toStr(messageUpdate); } catch {}
    return origUpdate.call(this, messageUpdate);
  };

  P.finalize = function (messageUpdate) {
    if (messageUpdate !== undefined) {
      try { this.__aug_buf__ = (this.__aug_buf__ || '') + toStr(messageUpdate); } catch {}
    }
    const res = origFinalize.call(this, messageUpdate);
    try {
      console.log('[签名调试] MD5.create() 累计输入(可读):', this.__aug_buf__);
      console.log('[签名调试] MD5.create() 输出(HEX):', res.toString());
      console.trace('[签名调试] MD5.create() 调用堆栈');
    } catch {}
    this.__aug_buf__ = '';
    return res;
  };

  P.__aug_patched__ = true;
  console.log('[签名调试] CryptoJS.algo.MD5 原型已劫持');
}

// ==================== 3. Headers 设置劫持 ====================

/**
 * 劫持 Headers.set/append 方法
 * 用于捕获请求头的设置，特别是 sign、nonce、timestamp
 */
function installHeadersHook() {
  const P = Headers.prototype;
  ['set', 'append'].forEach(fnName => {
    const orig = P[fnName];
    if (!orig || orig.__aug_patched__) return;
    
    P[fnName] = function(key, value) {
      const keyLower = String(key).toLowerCase();
      if (['sign', 'nonce', 'timestamp', 'deviceid', 'apptype', 'appversion', 'client-ver'].includes(keyLower)) {
        console.log(`[签名调试] 设置请求头: ${key} = ${value}`);
        if (keyLower === 'sign') {
          console.log('[签名调试] 设置 sign 头的调用堆栈');
          console.trace();
        }
      }
      return orig.call(this, key, value);
    };
    P[fnName].__aug_patched__ = true;
  });
  console.log('[签名调试] Headers.set/append 已劫持');
}

// ==================== 4. Fetch 请求劫持 ====================

/**
 * 劫持 fetch 方法
 * 用于捕获 /v2/chat 相关的请求
 */
function installFetchHook() {
  if (window.fetch.__aug_patched__) return;
  
  const origFetch = window.fetch;
  window.fetch = function(input, init) {
    const url = typeof input === 'string' ? input : input.url;
    if (url && url.includes('/v2/chat')) {
      console.log('[签名调试] fetch 准备发起 /v2/chat 请求:', url, init);
      console.log('[签名调试] fetch 发起调用堆栈');
      console.trace();
    }
    return origFetch.apply(this, arguments);
  };
  window.fetch.__aug_patched__ = true;
  console.log('[签名调试] fetch 已劫持');
}

// ==================== 5. 32位HEX转大写劫持 ====================

/**
 * 劫持 String.prototype.toUpperCase
 * 专门捕获 32位十六进制字符串转大写的操作（疑似 MD5 输出）
 */
function installUpperCaseHook() {
  const orig = String.prototype.toUpperCase;
  if (orig.__aug_patched__) return;
  
  String.prototype.toUpperCase = function() {
    const result = orig.apply(this, arguments);
    // 检查是否为 32位十六进制转大写（MD5 特征）
    if (/^[a-f0-9]{32}$/.test(this) && /^[A-F0-9]{32}$/.test(result)) {
      console.log('[签名调试] 发现32位HEX转大写:', this, '→', result);
      console.trace('[签名调试] toUpperCase 调用堆栈');
    }
    return result;
  };
  String.prototype.toUpperCase.__aug_patched__ = true;
  console.log('[签名调试] String.prototype.toUpperCase 已劫持(过滤32位hex)');
}

// ==================== 6. 随机数生成劫持 ====================

/**
 * 劫持 crypto.getRandomValues
 * 用于观察随机数的生成，可能与 nonce 相关
 */
function installRandomHook() {
  if (!window.crypto || !crypto.getRandomValues) {
    return console.warn('[签名调试] 无 getRandomValues');
  }
  if (crypto.getRandomValues.__aug_patched__) return;
  
  const orig = crypto.getRandomValues;
  crypto.getRandomValues = function(typedArray) {
    const result = orig.call(this, typedArray);
    console.log('[签名调试] getRandomValues ->', typedArray);
    return result;
  };
  crypto.getRandomValues.__aug_patched__ = true;
  console.log('[签名调试] crypto.getRandomValues 已劫持');
}

// ==================== 7. 断点调试辅助 ====================

/**
 * 在设置 sign 头时触发断点
 * 用于暂停执行，检查调用栈和作用域变量
 */
function installSignBreakpoint() {
  const P = Headers.prototype;
  ['set', 'append'].forEach(fnName => {
    const orig = P[fnName];
    if (!orig || orig.__aug_breakpoint__) return;
    
    P[fnName] = function(key, value) {
      if (String(key).toLowerCase() === 'sign') {
        console.log('[签名调试] 即将设置 sign =', value);
        debugger; // 在此处暂停，可以检查调用栈和变量
      }
      return orig.call(this, key, value);
    };
    P[fnName].__aug_breakpoint__ = true;
  });
  console.log('[签名调试] 已在 Headers.set/append(sign) 注入断点');
}

// ==================== 8. 一键安装所有 Hook ====================

/**
 * 一键安装所有调试 Hook
 * 推荐使用此函数进行完整的调试环境设置
 */
function installAllHooks() {
  console.log('[签名调试] 开始安装所有调试 Hook...');
  
  installMd5Hook();
  installMd5AlgoHook();
  installHeadersHook();
  installFetchHook();
  installUpperCaseHook();
  installRandomHook();
  
  console.log('[签名调试] 所有 Hook 安装完成！');
  console.log('[签名调试] 现在可以触发 /v2/chat 请求进行调试');
}

// ==================== 使用说明 ====================

console.log(`
=== 当贝 AI 签名调试工具 ===

使用方法：
1. 执行 installAllHooks() 安装所有调试 Hook
2. 或单独执行需要的 Hook 函数
3. 触发 /v2/chat 请求
4. 观察控制台输出的调试信息

可用函数：
- installAllHooks()          // 一键安装所有 Hook
- installMd5Hook()           // 劫持 CryptoJS.MD5
- installMd5AlgoHook()       // 劫持 MD5 流式 API
- installHeadersHook()       // 劫持请求头设置
- installFetchHook()         // 劫持 fetch 请求
- installUpperCaseHook()     // 劫持 32位HEX转大写
- installRandomHook()        // 劫持随机数生成
- installSignBreakpoint()    // 在设置 sign 时断点

注意：如需断点调试，请使用 installSignBreakpoint()
`);
