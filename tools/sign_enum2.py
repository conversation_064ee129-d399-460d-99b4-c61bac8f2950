#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当贝 AI /v2/chat 接口签名算法枚举验证脚本（扩展版）

功能：
- 使用 3 个真实样例数据
- 穷举更多组合：路径变体、ts/nonce 顺序、headers 位置、body 多种表达（含子集）、连接符、可选 method、可选 secret
- 总计测试约 134,400 种组合
- 输出中文调试信息

使用方法：
python3 tools/sign_enum2.py

作者：AI Assistant
创建时间：2025-01-21
"""
import hashlib, json
from itertools import product

# ========== 工具函数 ==========
md5u = lambda s: hashlib.md5(s.encode('utf-8')).hexdigest().upper()

def stable_json(raw: str) -> str:
    return json.dumps(json.loads(raw), ensure_ascii=False, separators=(',', ':'), sort_keys=True)

# Body 变体
def body_variants(raw: str):
    st = stable_json(raw)
    o = json.loads(raw)
    # 常用子集
    subA_keys = ['conversationId','question','model','uuid','chatId']
    subB_keys = subA_keys + ['content']
    subC_keys = subA_keys + ['content','botCode','stream']
    def subset_concat(keys):
        return ''.join(str(o.get(k,'')) for k in keys)
    def subset_kv_sorted(keys):
        items = [(k, o.get(k,'')) for k in keys]
        items.sort(key=lambda x:x[0])
        return '&'.join(f"{k}={v}" for k,v in items)
    return {
        'raw': raw,
        'stable': st,
        'md5_raw': hashlib.md5(raw.encode()).hexdigest().upper(),
        'md5_stable': hashlib.md5(st.encode()).hexdigest().upper(),
        'subA_concat': subset_concat(subA_keys),
        'subB_concat': subset_concat(subB_keys),
        'subA_kv_sorted': subset_kv_sorted(subA_keys),
        'subB_kv_sorted': subset_kv_sorted(subB_keys),
        'subC_concat': subset_concat(subC_keys),
    }

# ========== 样例 ==========
SAMPLES = [
    {
        'url_full': 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'host': 'ai-api.dangbei.net',
        'host_path': 'ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'path_full': '/ai-search/chatApi/v2/chat',
        'path_noslash': 'ai-search/chatApi/v2/chat',
        'path_short': '/chatApi/v2/chat',
        'timestamp': '1755595831',
        'nonce': 'h2KBbwoYj64N1jtUeJKCg',
        'sign': '7FCC1FA9DF55FCAE780C86860464146D',
        'headers': {
            'appType': '6',
            'appVersion': '1.1.19-1',
            'client-ver': '1.0.2',
            'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
            'lang': 'zh',
            'content-type': 'application/json',
        },
        'method': 'POST',
        'body': '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363769493893157061","question":"[这里不能返回多个字段组成的字典吗 for n, a in records]","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363770791349322117","chatId":"363770791349322117","files":[],"reference":[],"role":"user","status":"local","content":"[这里不能返回多个字段组成的字典吗 for n, a in records]","userAction":"","agentId":""}'
    },
    {
        'url_full': 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'host': 'ai-api.dangbei.net',
        'host_path': 'ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'path_full': '/ai-search/chatApi/v2/chat',
        'path_noslash': 'ai-search/chatApi/v2/chat',
        'path_short': '/chatApi/v2/chat',
        'timestamp': '1755596028',
        'nonce': 'dCxp-tRqd39NUepVg3Nts',
        'sign': '6C82DFE53F2D42166211409B71DCDC2F',
        'headers': {
            'appType': '6',
            'appVersion': '1.1.19-1',
            'client-ver': '1.0.2',
            'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
            'lang': 'zh',
            'content-type': 'application/json',
        },
        'method': 'POST',
        'body': '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363769493893157061","question":"records 是一个字典列表, 有多个 key, 怎么输出其中的两个 key 组成的列表","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363771205009805509","chatId":"363771205009805509","files":[],"reference":[],"role":"user","status":"local","content":"records 是一个字典列表, 有多个 key, 怎么输出其中的两个 key 组成的列表","userAction":"","agentId":""}'
    },
    {
        'url_full': 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'host': 'ai-api.dangbei.net',
        'host_path': 'ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'path_full': '/ai-search/chatApi/v2/chat',
        'path_noslash': 'ai-search/chatApi/v2/chat',
        'path_short': '/chatApi/v2/chat',
        'timestamp': '1755596071',
        'nonce': '_PZP1zdLBcECVpeymJHtW',
        'sign': '7284235F3A62F15C1E52E09C3BAA4DF5',
        'headers': {
            'appType': '6',
            'appVersion': '1.1.19-1',
            'client-ver': '1.0.2',
            'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
            'lang': 'zh',
            'content-type': 'application/json',
        },
        'method': 'POST',
        'body': '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363769493893157061","question":"如果records是一个嵌套字典列表呢，怎么输出其中两个key组成的列表？","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363771295053123781","chatId":"363771295053123781","files":[],"reference":[],"role":"user","status":"local","content":"如果records是一个嵌套字典列表呢，怎么输出其中两个key组成的列表？","userAction":"","agentId":""}'
    },
]

for s in SAMPLES:
    s['bv'] = body_variants(s['body'])

# 组合参数
PATHS = ['','path_full','path_short','url_full','host','host_path','path_noslash']
TS_NONCE_ORDER = ['TS_NONCE','NONCE_TS']
HEADERS_BLOCK = ['NONE','VALUES_FIXED','VALUES_SORTED']
INCLUDE_METHOD = [False, True]
JOINERS = ['', '&', '|', ':']
BODY_USE = ['NONE','raw','stable','md5_raw','md5_stable','subA_concat','subB_concat','subC_concat','subA_kv_sorted','subB_kv_sorted']
SECRETS = ['', 'dangbei', 'ai', 'dbai', 'secret']
SECRET_POS = ['NONE','SUFFIX','PREFIX','SURROUND']

# headers 固定顺序/排序顺序
HEADER_KEYS_FIXED = ['appType','appVersion','client-ver','deviceId','lang','content-type']

# headers 放置位置：在 ts/nonce 前还是后，或在 body 前后
HEADERS_POS = ['AFTER_TS_NONCE_BEFORE_BODY', 'AFTER_BODY']


def headers_block(s, mode: str) -> str:
    if mode == 'NONE':
        return ''
    h = s['headers']
    if mode == 'VALUES_FIXED':
        return ''.join(h[k] for k in HEADER_KEYS_FIXED)
    if mode == 'VALUES_SORTED':
        keys = sorted(HEADER_KEYS_FIXED)
        return ''.join(h[k] for k in keys)
    return ''


def build_concat_raw(s, path_key, ts_nonce_order, headers_mode, headers_pos, include_method, body_use, joiner):
    parts = []
    if include_method:
        parts.append(s['method'])
    if path_key:
        parts.append(str(s.get(path_key,'')))
    if ts_nonce_order == 'TS_NONCE':
        tn = [s['timestamp'], s['nonce']]
    else:
        tn = [s['nonce'], s['timestamp']]
    parts.extend(tn)
    hb = headers_block(s, headers_mode)
    bb = ''
    if body_use != 'NONE':
        bb = s['bv'][body_use]
    if headers_mode != 'NONE':
        if headers_pos == 'AFTER_TS_NONCE_BEFORE_BODY':
            parts.append(hb)
            if body_use != 'NONE':
                parts.append(bb)
        else: # AFTER_BODY
            if body_use != 'NONE':
                parts.append(bb)
            parts.append(hb)
    else:
        if body_use != 'NONE':
            parts.append(bb)
    raw = joiner.join(parts)
    return raw


def apply_secret(raw: str, secret: str, pos: str) -> str:
    if pos == 'NONE' or secret == '':
        return raw
    if pos == 'SUFFIX':
        return raw + secret
    if pos == 'PREFIX':
        return secret + raw
    if pos == 'SURROUND':
        return secret + raw + secret
    return raw


def main():
    print('[调试] 开始扩展枚举验证...')
    hits = []
    total = 0
    for path_key, tsn, hmode, hpos, inc_m, bodyu, joiner, secret, spos in product(
        PATHS, TS_NONCE_ORDER, HEADERS_BLOCK, HEADERS_POS, INCLUDE_METHOD, BODY_USE, JOINERS, SECRETS, SECRET_POS
    ):
        total += 1
        rule = {
            'mode':'CONCAT_EXT', 'path':path_key, 'ts_nonce':tsn, 'headers':hmode, 'headers_pos':hpos,
            'include_method':inc_m, 'body':bodyu, 'joiner':joiner, 'secret':secret, 'secret_pos':spos
        }
        ok_all = True
        per = []
        for s in SAMPLES:
            raw = build_concat_raw(s, path_key, tsn, hmode, hpos, inc_m, bodyu, joiner)
            raw2 = apply_secret(raw, secret, spos)
            calc = md5u(raw2)
            per.append({'raw': raw2, 'calc': calc, 'ok': calc==s['sign']})
            if calc != s['sign']:
                ok_all = False
                break
        if ok_all:
            hits.append((rule, per))
            # 可以打印首个命中提前终止以提速（暂不提前终止，继续搜集所有命中）
    print(f"[调试] 枚举组合尝试数量（上界）: {total}")
    print(f"[结果] 命中规则数: {len(hits)}")
    if hits:
        for i,(rule, per) in enumerate(hits,1):
            print(f"=== 命中 #{i} ===")
            print(json.dumps(rule, ensure_ascii=False, separators=(',',':'), sort_keys=True))
            for j,ps in enumerate(per,1):
                print(f"  - 样例{j}: {ps['calc']} (一致)")
                print("    原串片段:", ps['raw'][:60],'...',ps['raw'][-60:])
    else:
        print('[结论] 扩展枚举仍未命中。高度怀疑签名包含未掌握的 secret/salt 或不同的参数拼接（例如包含某些子字段的排序规则、或 URL 编码/转义参与）。')
        print('[建议] 下一步在运行时 hook 生成签名前的原串，或扩大 secret 候选集合。')

if __name__ == '__main__':
    main()

