# 当贝 AI /v2/chat 接口签名算法分析报告

## 概述

本报告记录了对当贝 AI 平台 `/v2/chat` 接口签名算法的深入分析过程，包括静态代码分析、动态调试、枚举验证等多种方法的应用。

## 接口基本信息

- **接口地址**: `https://ai-api.dangbei.net/ai-search/chatApi/v2/chat`
- **请求方法**: POST
- **Content-Type**: application/json
- **签名字段**: sign（32位大写十六进制，疑似MD5）

## 关键请求头字段

```
appType: 6
appVersion: 1.1.19-1
client-ver: 1.0.2
deviceId: eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm
lang: zh
nonce: [随机字符串，含大小写字母、数字、短横线]
timestamp: [10位秒级时间戳]
sign: [32位大写十六进制字符串]
```

## 分析过程

### 1. 静态代码分析

通过分析 Next.js 打包产物 `ai.dangbei.com/_next/static/chunks/`：

- **模块定位**: Chat 相关 API 调用统一封装在模块 ID 35476 中
- **导出方法**: uSU、q7N、EoR、lAK、fSX、boi 等混淆方法名
- **加密库**: 发现 CryptoJS SHA256 和 TripleDES，但用途与 chat 签名无直接关联
- **结论**: 签名逻辑不在可见的静态 chunk 中，可能在运行时动态加载或服务端处理

### 2. 样例数据收集

收集了三个真实的 /v2/chat 请求样例：

#### 样例 1
- timestamp: 1755595831
- nonce: h2KBbwoYj64N1jtUeJKCg
- sign: 7FCC1FA9DF55FCAE780C86860464146D

#### 样例 2
- timestamp: 1755596028
- nonce: dCxp-tRqd39NUepVg3Nts
- sign: 6C82DFE53F2D42166211409B71DCDC2F

#### 样例 3
- timestamp: 1755596071
- nonce: _PZP1zdLBcECVpeymJHtW
- sign: 7284235F3A62F15C1E52E09C3BAA4DF5

### 3. 签名特征分析

- **长度**: 32位十六进制 → 高概率为 MD5 算法
- **变化规律**: nonce/timestamp/body 变化时 sign 必变
- **固定字段**: deviceId、appType 等固定字段不变

### 4. 枚举验证结果

创建了两个版本的枚举验证脚本：
- **基础版**: 测试了多种路径格式、body 处理方式、headers 组合
- **扩展版**: 穷举了 134,400 种组合，包括连接符、顺序、编码等
- **结果**: 所有枚举均未命中，说明存在未掌握的关键要素

### 5. 运行时调试

通过浏览器控制台注入 Hook 脚本：

#### 成功捕获的信息
- 签名设置堆栈：定位到 `_app-04ec0e01f4390115.js` 和 `3536-845aec1084a70bcb.js`
- 请求发起流程：从 onClick → API 封装 → headers 设置
- 随机数生成：观察到 6字节和32字节的 getRandomValues 调用

#### 关键发现
- 未捕获到任何 CryptoJS.MD5 或常见哈希函数调用
- 说明签名可能使用自实现的轻量级 MD5 算法
- 或签名在更早阶段计算完成，仅在此处设置头部

## 推测的签名算法候选

基于分析，最可能的签名拼接规则包括：

1. **简单拼接**: `MD5(timestamp + nonce + md5(body))`
2. **路径参与**: `MD5(path + timestamp + nonce + md5(body))`
3. **头部参与**: `MD5(path + timestamp + nonce + headers + md5(body))`
4. **方法参与**: `MD5(POST + path + timestamp + nonce + md5(body))`
5. **密钥参与**: `MD5(原串 + secret)` 或 `HMAC-MD5(原串, secret)`

## 技术难点

1. **代码混淆**: Webpack 打包后的代码高度混淆，变量名和函数名不可读
2. **模块分离**: 关键签名逻辑可能在未包含的 chunk 中
3. **自实现算法**: 可能使用非标准库的自写 MD5 实现
4. **密钥隐藏**: 如存在 secret，可能通过运行时注入或混淆隐藏

## 下一步建议

1. **深入断点调试**: 在 `_app-*.js` 的签名设置处设置断点，查看上一帧作用域变量
2. **Hook 增强**: 使用 32位HEX转大写的 Hook 捕获可能的自实现 MD5
3. **源码获取**: 尝试获取 sourcemap 或包含模块 35476 的完整 chunk
4. **服务端分析**: 考虑从服务端角度分析签名验证逻辑

## 工具脚本

本次分析过程中开发了多个调试和验证工具：

- `sign_enum.py`: 基础签名算法枚举验证脚本
- `sign_enum2.py`: 扩展签名算法枚举验证脚本
- 浏览器 Hook 脚本集合：用于运行时调试和数据捕获

详细使用方法请参考各工具的说明文档。

## 结论

当贝 AI /v2/chat 接口的签名算法具有以下特点：
- 使用 MD5 算法生成 32位大写十六进制签名
- 参与签名的字段包括 timestamp、nonce，可能还包括 path、headers、body 的 MD5
- 可能存在固定的 secret/salt 参与签名
- 签名实现可能使用自写的轻量级 MD5 算法，不依赖常见的加密库

要完全破解该签名算法，需要通过运行时调试获取签名函数的原始输入参数，或获取包含签名逻辑的完整源码。
