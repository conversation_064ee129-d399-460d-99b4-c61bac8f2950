#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当贝 AI /v2/chat 接口签名算法枚举验证脚本（基础版）

功能：
- 包含三个真实样例数据
- 穷举多种签名拼接规则组合
- 对每条规则计算 MD5（大写）并与样例 sign 对比
- 输出所有命中的规则与详细调试信息（中文）

使用方法：
python3 tools/sign_enum.py

作者：AI Assistant
创建时间：2025-01-21
"""

import hashlib
import json
from itertools import product


def md5_upper(s: str) -> str:
    return hashlib.md5(s.encode('utf-8')).hexdigest().upper()


def stable_json_str(raw_json_str: str) -> str:
    obj = json.loads(raw_json_str)
    # 稳定序列化：按 key 排序 + 无空白
    return json.dumps(obj, ensure_ascii=False, separators=(",", ":"), sort_keys=True)


def prepare_body_variants(raw_json_str: str):
    """
    返回多种 body 表示：
    - raw: 原始 JSON 字符串
    - stable: 稳定序列化字符串
    - md5(raw) lower/upper
    - md5(stable) lower/upper
    """
    stable = stable_json_str(raw_json_str)
    md5_raw_lower = hashlib.md5(raw_json_str.encode('utf-8')).hexdigest()
    md5_raw_upper = md5_raw_lower.upper()
    md5_stable_lower = hashlib.md5(stable.encode('utf-8')).hexdigest()
    md5_stable_upper = md5_stable_lower.upper()
    return {
        'raw': raw_json_str,
        'stable': stable,
        'md5_raw_lower': md5_raw_lower,
        'md5_raw_upper': md5_raw_upper,
        'md5_stable_lower': md5_stable_lower,
        'md5_stable_upper': md5_stable_upper,
    }


# 三个样例数据（保持与抓包一致）
SAMPLES = [
    {
        'url_full': 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'path_full': '/ai-search/chatApi/v2/chat',
        'path_short': '/chatApi/v2/chat',
        'timestamp': '1755595831',
        'nonce': 'h2KBbwoYj64N1jtUeJKCg',
        'sign': '7FCC1FA9DF55FCAE780C86860464146D',
        'headers': {
            'appType': '6',
            'appVersion': '1.1.19-1',
            'client-ver': '1.0.2',
            'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
            'lang': 'zh',
        },
        'method': 'POST',
        'body': '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363769493893157061","question":"[这里不能返回多个字段组成的字典吗 for n, a in records]","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363770791349322117","chatId":"363770791349322117","files":[],"reference":[],"role":"user","status":"local","content":"[这里不能返回多个字段组成的字典吗 for n, a in records]","userAction":"","agentId":""}'
    },
    {
        'url_full': 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'path_full': '/ai-search/chatApi/v2/chat',
        'path_short': '/chatApi/v2/chat',
        'timestamp': '1755596028',
        'nonce': 'dCxp-tRqd39NUepVg3Nts',
        'sign': '6C82DFE53F2D42166211409B71DCDC2F',
        'headers': {
            'appType': '6',
            'appVersion': '1.1.19-1',
            'client-ver': '1.0.2',
            'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
            'lang': 'zh',
        },
        'method': 'POST',
        'body': '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363769493893157061","question":"records 是一个字典列表, 有多个 key, 怎么输出其中的两个 key 组成的列表","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363771205009805509","chatId":"363771205009805509","files":[],"reference":[],"role":"user","status":"local","content":"records 是一个字典列表, 有多个 key, 怎么输出其中的两个 key 组成的列表","userAction":"","agentId": ""}'
    },
    {
        'url_full': 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat',
        'path_full': '/ai-search/chatApi/v2/chat',
        'path_short': '/chatApi/v2/chat',
        'timestamp': '1755596071',
        'nonce': '_PZP1zdLBcECVpeymJHtW',
        'sign': '7284235F3A62F15C1E52E09C3BAA4DF5',
        'headers': {
            'appType': '6',
            'appVersion': '1.1.19-1',
            'client-ver': '1.0.2',
            'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
            'lang': 'zh',
        },
        'method': 'POST',
        'body': '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363769493893157061","question":"如果records是一个嵌套字典列表呢，怎么输出其中两个key组成的列表？","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363771295053123781","chatId":"363771295053123781","files":[],"reference":[],"role":"user","status":"local","content":"如果records是一个嵌套字典列表呢，怎么输出其中两个key组成的列表？","userAction":"","agentId": ""}'
    },
]

# 预计算每个样例的 body 多种变体
for s in SAMPLES:
    s['body_vars'] = prepare_body_variants(s['body'])

# 路径变体
PATH_VARIANTS = ['NONE', 'PATH_FULL', 'PATH_SHORT', 'URL_FULL']

# 方法变体
METHODS = ['NONE', 'POST']

# 头部字段固定有序列表与按 key 排序
HEADER_KEYS_FIXED = ['appType', 'appVersion', 'client-ver', 'deviceId', 'lang']

# Secret 候选（包含空字符串与一些常见值）
SECRETS = [
    '', 'ai', 'ai_web', 'dangbei', 'dangbei_ai', 'ai-search', 'chatApi',
    'db', 'dbai', 'secret', 'salt'
]

# Secret 放置位置
SECRET_POS = ['NONE', 'SUFFIX', 'PREFIX', 'SURROUND']


def get_path_value(s, which: str) -> str:
    if which == 'PATH_FULL':
        return s['path_full']
    if which == 'PATH_SHORT':
        return s['path_short']
    if which == 'URL_FULL':
        return s['url_full']
    return ''


def build_headers_block(s, mode: str) -> str:
    """返回简单拼接的 headers 值块"""
    h = s['headers']
    if mode == 'NONE':
        return ''
    if mode == 'VALUES_FIXED':
        return ''.join([h[k] for k in HEADER_KEYS_FIXED])
    if mode == 'VALUES_SORTED':
        keys = sorted(HEADER_KEYS_FIXED)
        return ''.join([h[k] for k in keys])
    return ''


def build_kv_string(s, keys, order: str, body_choice: str = None) -> str:
    """构造 key=value&... 的串，不 URL 编码"""
    kv = []
    data = {}
    h = s['headers']
    data.update(h)
    data['timestamp'] = s['timestamp']
    data['nonce'] = s['nonce']

    if body_choice:
        bv = s['body_vars']
        if body_choice == 'body_raw':
            data['body'] = bv['raw']
        elif body_choice == 'body_stable':
            data['body'] = bv['stable']
        elif body_choice == 'bodyMd5_raw_lower':
            data['bodyMd5'] = bv['md5_raw_lower']
        elif body_choice == 'bodyMd5_raw_upper':
            data['bodyMd5'] = bv['md5_raw_upper']
        elif body_choice == 'bodyMd5_stable_lower':
            data['bodyMd5'] = bv['md5_stable_lower']
        elif body_choice == 'bodyMd5_stable_upper':
            data['bodyMd5'] = bv['md5_stable_upper']

    sel_keys = list(keys)
    if order == 'SORTED':
        sel_keys = sorted(sel_keys)

    for k in sel_keys:
        if k in data:
            kv.append(f"{k}={data[k]}")
    return '&'.join(kv)


def apply_secret(raw: str, secret: str, pos: str) -> str:
    if pos == 'NONE' or not secret:
        return raw
    if pos == 'SUFFIX':
        return raw + secret
    if pos == 'PREFIX':
        return secret + raw
    if pos == 'SURROUND':
        return secret + raw + secret
    return raw


def rule_eval_concat(samples):
    """模式 A：简单拼接（method? + path? + timestamp + nonce + headers? + body? + secret?）"""
    results = []
    headers_modes = ['NONE', 'VALUES_FIXED', 'VALUES_SORTED']
    body_modes = ['NONE', 'md5_raw_lower', 'md5_raw_upper', 'md5_stable_lower', 'md5_stable_upper', 'raw', 'stable']

    for method, pathv, hmode, bmode, secret, spos in product(METHODS, PATH_VARIANTS, headers_modes, body_modes, SECRETS, SECRET_POS):
        rule_desc = {
            'mode': 'CONCAT', 'method': method, 'path': pathv,
            'headers': hmode, 'body': bmode, 'secret': secret, 'secret_pos': spos,
        }
        matched_all = True
        per_sample = []
        for s in samples:
            comp = []
            # method
            m = s['method'] if method == 'POST' else ''
            comp.append(m)
            # path
            comp.append(get_path_value(s, pathv))
            # timestamp + nonce
            comp.append(s['timestamp'])
            comp.append(s['nonce'])
            # headers block
            comp.append(build_headers_block(s, hmode))
            # body block
            bv = s['body_vars']
            if bmode == 'NONE':
                bb = ''
            else:
                bb = bv[bmode] if bmode in bv else ''
            comp.append(bb)

            raw = ''.join(comp)
            raw = apply_secret(raw, secret, spos)
            sign = md5_upper(raw)
            ok = (sign == s['sign'])
            per_sample.append({'raw': raw, 'calc': sign, 'ok': ok})
            if not ok:
                matched_all = False
                break
        if matched_all:
            results.append((rule_desc, per_sample))
    return results


def rule_eval_kv(samples):
    """模式 B：key=value&... + 可选 method/path/secret 外围包裹"""
    results = []
    # 不同的 key 集合
    key_sets = {
        'SET_A': ['appType', 'appVersion', 'client-ver', 'deviceId', 'lang', 'nonce', 'timestamp'],
        'SET_B': ['appType', 'appVersion', 'client-ver', 'deviceId', 'lang', 'nonce', 'timestamp', 'bodyMd5'],
        'SET_C': ['appType', 'appVersion', 'client-ver', 'deviceId', 'lang', 'nonce', 'timestamp', 'body'],
        'SET_D': ['nonce', 'timestamp'],
        'SET_E': ['nonce', 'timestamp', 'bodyMd5'],
    }
    orders = ['FIXED', 'SORTED']
    # body 在 kv 中的选择
    kv_body_choices = [None, 'body_raw', 'body_stable', 'bodyMd5_raw_lower', 'bodyMd5_raw_upper', 'bodyMd5_stable_lower', 'bodyMd5_stable_upper']

    # 外围包裹方式
    wrappers = [
        ('KV_ONLY', lambda s, kv, path, method: kv),
        ('PATH+KV', lambda s, kv, path, method: path + kv),
        ('METHOD+PATH+KV', lambda s, kv, path, method: method + path + kv),
        ('METHOD+KV', lambda s, kv, path, method: method + kv),
    ]

    for pathv, (wrap_name, wrap_fn), key_set_name, order, kv_body, secret, spos, methodv in product(
        PATH_VARIANTS, wrappers, key_sets.keys(), orders, kv_body_choices, SECRETS, SECRET_POS, METHODS
    ):
        # 如果 key_set 不包含 body/bodyMd5，而 kv_body 被选择了 body 相关，则跳过不一致组合
        if kv_body and 'body' not in key_set_name and 'bodyMd5' not in key_set_name:
            continue
        rule_desc = {
            'mode': 'KV', 'wrapper': wrap_name, 'path': pathv,
            'keys': key_set_name, 'order': order, 'kv_body': kv_body,
            'secret': secret, 'secret_pos': spos, 'method': methodv,
        }
        matched_all = True
        per_sample = []
        for s in samples:
            path = get_path_value(s, pathv)
            method = s['method'] if methodv == 'POST' else ''
            # 根据 key_set 构造 kv 字符串
            keys = key_sets[key_set_name]
            kv = build_kv_string(s, keys, order, kv_body)
            raw = wrap_fn(s, kv, path, method)
            raw = apply_secret(raw, secret, spos)
            sign = md5_upper(raw)
            ok = (sign == s['sign'])
            per_sample.append({'kv': kv, 'raw': raw, 'calc': sign, 'ok': ok})
            if not ok:
                matched_all = False
                break
        if matched_all:
            results.append((rule_desc, per_sample))
    return results


def main():
    print("[调试] 样例数量:", len(SAMPLES))
    # 运行两大类规则
    concat_hits = rule_eval_concat(SAMPLES)
    kv_hits = rule_eval_kv(SAMPLES)

    total_hits = len(concat_hits) + len(kv_hits)
    print(f"[调试] 命中规则总数: {total_hits} (CONCAT={len(concat_hits)}, KV={len(kv_hits)})")

    if total_hits:
        print("\n[结果] 命中规则详情如下：\n")
        def pretty_rule(d):
            return json.dumps(d, ensure_ascii=False, separators=(",", ":"), sort_keys=True)
        idx = 0
        for cat, hits in [('CONCAT', concat_hits), ('KV', kv_hits)]:
            for rule, samples in hits:
                idx += 1
                print(f"=== 命中规则 #{idx} 类型={cat} ===")
                print(pretty_rule(rule))
                for i, ps in enumerate(samples, 1):
                    print(f"  - 样例{i} 计算签名: {ps['calc']} (与实际一致)")
                    # 打印关键信息，避免过长
                    if cat == 'CONCAT':
                        print("    原始拼接串片段(前后各50):", ps['raw'][:50], '...', ps['raw'][-50:])
                    else:
                        print("    kv 串(前后各80):", ps['kv'][:80], '...', ps['kv'][-80:])
                        print("    包裹后原串(前后各50):", ps['raw'][:50], '...', ps['raw'][-50:])
                print()
    else:
        print("[结果] 未找到可同时匹配三条样例的规则。高概率存在未包含的 secret/salt 或不同的拼接规则。")


if __name__ == '__main__':
    main()

