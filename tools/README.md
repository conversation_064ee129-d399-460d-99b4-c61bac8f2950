# 当贝 AI /v2/chat 接口签名分析工具集

本目录包含了对当贝 AI 平台 `/v2/chat` 接口签名算法进行深入分析的完整工具集合。

## 文件说明

### 📋 分析报告
- **`dangbei_v2_chat_signature_analysis.md`** - 完整的签名算法分析报告，包含分析过程、发现和结论

### 🔧 调试工具
- **`browser_debug_hooks.js`** - 浏览器调试 Hook 脚本集合，用于运行时拦截和分析
- **`random_analysis_hooks.js`** - 专门用于分析随机数与签名关系的 Hook 脚本

### 🧮 验证脚本
- **`sign_enum.py`** - 基础版签名算法枚举验证脚本
- **`sign_enum2.py`** - 扩展版签名算法枚举验证脚本（134,400+ 组合）

## 快速开始

### 1. 浏览器调试

在 ai.dangbei.com 聊天页面的控制台中执行：

```javascript
// 方法一：加载完整调试工具
// 复制 browser_debug_hooks.js 的内容到控制台，然后执行：
installAllHooks();

// 方法二：仅加载随机数分析工具
// 复制 random_analysis_hooks.js 的内容到控制台，然后执行：
installRandomAnalysis();
```

### 2. 本地枚举验证

```bash
# 基础版枚举验证
python3 tools/sign_enum.py

# 扩展版枚举验证（更多组合）
python3 tools/sign_enum2.py
```

## 工具功能详解

### 浏览器调试 Hook

#### 核心功能
- **CryptoJS.MD5 劫持** - 捕获 MD5 计算的输入和输出
- **Headers 设置劫持** - 监控请求头的设置过程
- **Fetch 请求劫持** - 拦截 /v2/chat 相关请求
- **32位HEX转大写劫持** - 捕获可能的 MD5 输出特征
- **随机数生成劫持** - 监控 crypto.getRandomValues 调用

#### 使用示例
```javascript
// 安装所有调试 Hook
installAllHooks();

// 或单独安装特定 Hook
installMd5Hook();           // MD5 调用劫持
installHeadersHook();       // 请求头劫持
installSignBreakpoint();    // 在设置 sign 时断点
```

### 随机数分析工具

专门用于分析 `crypto.getRandomValues` 生成的随机数与 nonce/sign 的关系：

```javascript
// 安装随机数分析工具
installRandomAnalysis();

// 手动分析特定值
manualAnalyze('h2KBbwoYj64N1jtUeJKCg', 'nonce');
manualAnalyze('7FCC1FA9DF55FCAE780C86860464146D', 'sign');
```

### 枚举验证脚本

#### 基础版 (sign_enum.py)
- 测试常见的签名拼接规则
- 包含路径、方法、头部、body 的多种组合
- 支持不同的 secret/salt 位置

#### 扩展版 (sign_enum2.py)
- 测试 134,400+ 种组合
- 包含更多路径变体、连接符、顺序组合
- 支持 body 子集拼接和 key=value 格式

## 已知发现

### 签名特征
- **长度**: 32位大写十六进制（疑似 MD5）
- **变化规律**: nonce/timestamp/body 变化时 sign 必变
- **固定字段**: deviceId、appType 等保持不变

### 技术挑战
- 代码高度混淆，变量名不可读
- 关键签名逻辑可能在未包含的 chunk 中
- 可能使用自实现的轻量级 MD5 算法
- 未捕获到标准加密库的调用

### 调试线索
- 签名设置堆栈定位到 `_app-04ec0e01f4390115.js` 和 `3536-845aec1084a70bcb.js`
- 观察到 6字节和32字节的随机数生成
- 未发现随机数与 nonce/sign 的直接映射关系

## 下一步建议

1. **深入断点调试**
   - 在 `_app-*.js` 的签名设置处设置断点
   - 查看上一帧作用域中的变量
   - 寻找签名拼接的原始字符串

2. **源码获取**
   - 尝试获取 sourcemap 文件
   - 寻找包含模块 35476 的完整 chunk

3. **服务端分析**
   - 从服务端角度分析签名验证逻辑
   - 寻找可能的签名算法文档或实现

## 注意事项

- 所有工具仅用于学习和研究目的
- 调试 Hook 不会修改实际的请求数据
- 建议在开发环境中使用，避免影响正常使用
- 部分 Hook 可能会产生大量日志输出

## 贡献

如果你发现了新的线索或改进了分析方法，欢迎更新相关文档和工具。

---

**创建时间**: 2025-01-21  
**作者**: AI Assistant  
**版本**: 1.0
