#!/usr/bin/env node

/**
 * 开发服务器启动脚本
 * 解决 ts-node --watch 兼容性问题
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动当贝AI Provider开发服务器...');

// 检查 TypeScript 文件是否存在
const serverFile = path.join(__dirname, 'src/server/index.ts');
if (!fs.existsSync(serverFile)) {
  console.error('❌ 找不到服务器文件:', serverFile);
  process.exit(1);
}

// 启动 ts-node
const child = spawn('npx', ['ts-node', 'src/server/index.ts'], {
  stdio: 'inherit',
  cwd: __dirname,
  env: {
    ...process.env,
    NODE_ENV: 'development'
  }
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error.message);
  process.exit(1);
});

child.on('exit', (code) => {
  if (code !== 0) {
    console.error(`❌ 服务器退出，代码: ${code}`);
    process.exit(code);
  }
});

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  child.kill('SIGTERM');
});
