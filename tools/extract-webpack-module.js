#!/usr/bin/env node
/**
 * 轻量模块提取脚本：根据 webpack chunk 中的模块 ID 抽取模块源码片段
 * 用法：node tools/extract-webpack-module.js <chunk_file> <module_id>
 * 示例：node tools/extract-webpack-module.js ai.dangbei.com/_next/static/chunks/pages/_app-72ae859153e99355.js 35476
 * 注意：该脚本通过正则定位 ",<digits>:" 作为下一个模块的边界，可能存在少量误差，但足够辅助分析
 */
const fs = require('fs');
const path = process.argv[2];
const modId = process.argv[3];
if (!path || !modId) {
  console.error('用法: node tools/extract-webpack-module.js <chunk_file> <module_id>');
  process.exit(1);
}
const content = fs.readFileSync(path, 'utf8');
const startToken = `${modId}:`;
const startIdx = content.indexOf(startToken);
if (startIdx === -1) {
  console.error(`未找到模块ID ${modId}`);
  process.exit(2);
}
// 从 startIdx 往后找下一个 ",<digits>:" 作为结束边界
const tail = content.slice(startIdx + startToken.length);
const m = tail.match(/,\d+:/);
let endIdx;
if (m) {
  endIdx = startIdx + startToken.length + m.index;
} else {
  // 未找到下一个模块，取到文件末尾
  endIdx = content.length;
}
const snippet = content.slice(startIdx, endIdx);
console.log(`// ==== MODULE ${modId} BEGIN ====`);
console.log(snippet);
console.log(`\n// ==== MODULE ${modId} END ====`);

