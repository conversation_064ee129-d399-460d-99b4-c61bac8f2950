#!/usr/bin/env node

/**
 * 简化的开发服务器启动脚本
 * 直接使用编译后的代码或回退到测试服务器
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动开发服务器...');

// 检查是否有编译后的代码
const distServer = path.join(__dirname, 'dist/server/index.js');
const srcServer = path.join(__dirname, 'src/server/index.ts');
const testServer = path.join(__dirname, 'test-server.js');

let command, args, serverType;

if (fs.existsSync(distServer)) {
  // 使用编译后的代码
  command = 'node';
  args = ['dist/server/index.js'];
  serverType = '生产模式 (编译后代码)';
} else if (fs.existsSync(srcServer)) {
  // 尝试使用 ts-node
  command = 'npx';
  args = ['ts-node', 'src/server/index.ts'];
  serverType = '开发模式 (TypeScript)';
} else if (fs.existsSync(testServer)) {
  // 回退到测试服务器
  command = 'node';
  args = ['test-server.js'];
  serverType = '测试模式';
} else {
  console.error('❌ 找不到可用的服务器文件');
  process.exit(1);
}

console.log(`📡 使用 ${serverType}`);
console.log(`🔧 命令: ${command} ${args.join(' ')}`);

// 设置环境变量
const env = {
  ...process.env,
  NODE_ENV: 'development',
  PORT: process.env.PORT || '3000'
};

// 启动服务器
const child = spawn(command, args, {
  stdio: 'inherit',
  cwd: __dirname,
  env: env
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error.message);
  
  // 如果 ts-node 失败，尝试编译后启动
  if (command === 'npx' && args[0] === 'ts-node') {
    console.log('🔄 ts-node 失败，尝试编译后启动...');
    
    const buildChild = spawn('npm', ['run', 'build'], {
      stdio: 'inherit',
      cwd: __dirname
    });
    
    buildChild.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ 编译成功，启动编译后的服务器...');
        const prodChild = spawn('node', ['dist/server/index.js'], {
          stdio: 'inherit',
          cwd: __dirname,
          env: env
        });
        
        prodChild.on('error', (prodError) => {
          console.error('❌ 编译后启动也失败:', prodError.message);
          console.log('🔄 回退到测试服务器...');
          
          if (fs.existsSync(testServer)) {
            spawn('node', ['test-server.js'], {
              stdio: 'inherit',
              cwd: __dirname,
              env: env
            });
          } else {
            console.error('❌ 所有启动方式都失败了');
            process.exit(1);
          }
        });
      } else {
        console.error('❌ 编译失败');
        process.exit(1);
      }
    });
  } else {
    process.exit(1);
  }
});

child.on('exit', (code) => {
  if (code !== 0) {
    console.error(`❌ 服务器退出，代码: ${code}`);
  }
});

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  child.kill('SIGTERM');
});
