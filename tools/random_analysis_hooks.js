/**
 * 随机数与签名关联分析 Hook 脚本
 * 
 * 用于分析 crypto.getRandomValues 生成的随机数与 nonce/sign 的关系
 * 
 * 使用方法：
 * 1. 在浏览器控制台依次执行 recordRandomValues() 和 analyzeNonceSignRelation()
 * 2. 触发 /v2/chat 请求
 * 3. 观察控制台输出的关联分析结果
 */

// ==================== 随机数记录 Hook ====================

/**
 * 记录最近的 getRandomValues 调用
 * 保留最近 50 条记录，包含时间戳、长度、十六进制、字节数组、调用堆栈
 */
function recordRandomValues() {
  if (!window.crypto || !crypto.getRandomValues) {
    return console.warn('[调试] 无 getRandomValues');
  }
  if (crypto.getRandomValues.__aug_record__) return;
  
  const log = [];
  const toHex = (u8) => Array.from(u8).map(b => b.toString(16).padStart(2, '0')).join('');
  const orig = crypto.getRandomValues;
  
  crypto.getRandomValues = function(typedArray) {
    const result = orig.call(this, typedArray);
    try {
      const hex = toHex(typedArray);
      const item = {
        ts: Date.now(),
        len: typedArray.length,
        hex: hex,
        bytes: typedArray.slice(),
        stack: (new Error()).stack
      };
      log.push(item);
      if (log.length > 50) log.shift();
      window.__randomValues__ = log; // 暴露给全局
      console.log('[签名调试] getRandomValues', {
        len: item.len,
        hex: hex.slice(0, 64) + (hex.length > 64 ? '...' : '')
      });
    } catch(e) {
      console.warn('[签名调试] 记录随机值失败:', e);
    }
    return result;
  };
  
  crypto.getRandomValues.__aug_record__ = true;
  console.log('[签名调试] 已 Hook getRandomValues，并记录最近 50 条至 window.__randomValues__');
}

// ==================== nonce/sign 关联分析 Hook ====================

/**
 * 分析 nonce 和 sign 与最近随机数的关系
 * 检查是否存在直接的 Base64URL 或 Hex 映射关系
 */
function analyzeNonceSignRelation() {
  // Base64URL 编码函数
  const base64url = (bytes) => {
    let bin = String.fromCharCode.apply(null, bytes);
    let b64 = btoa(bin).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    return b64;
  };
  
  // 忽略大小写比较
  const equalsIgnoreCase = (a, b) => String(a).toUpperCase() === String(b).toUpperCase();
  
  const P = Headers.prototype;
  const wrapHeaderMethod = (fnName) => {
    const orig = P[fnName];
    if (!orig || orig.__aug_analyze__) return;
    
    P[fnName] = function(k, v) {
      const key = String(k).toLowerCase();
      if (key === 'nonce' || key === 'sign') {
        console.log(`[签名调试] 设置请求头: ${key} =`, v);
        
        // 与最近随机进行比对
        const recentRandom = (window.__randomValues__ || []).slice(-10);
        recentRandom.forEach((item, i) => {
          try {
            const bytes = new Uint8Array(item.bytes);
            const hex = item.hex.toUpperCase();
            const b64u = base64url(bytes);
            
            if (key === 'nonce') {
              if (v === b64u) {
                console.log(`[签名调试] ✓ nonce 与 __randomValues__[-${recentRandom.length - i}] 的 Base64URL 完全一致`);
                console.log(`[签名调试] 匹配的随机数长度: ${item.len} 字节`);
              }
            }
            
            if (key === 'sign') {
              if (equalsIgnoreCase(v, hex)) {
                console.log(`[签名调试] ✓ sign 与 __randomValues__[-${recentRandom.length - i}] 的 Hex(Upper) 完全一致`);
                console.log(`[签名调试] 匹配的随机数长度: ${item.len} 字节`);
              }
            }
          } catch(e) {
            console.warn('[签名调试] 比对随机数失败:', e);
          }
        });
        
        // 输出最近的随机数记录供参考
        if (window.__randomValues__ && window.__randomValues__.length > 0) {
          console.log('[签名调试] 最近 getRandomValues 记录 (window.__randomValues__):', 
            window.__randomValues__.slice(-5).map(item => ({
              len: item.len,
              hex: item.hex.slice(0, 32) + (item.hex.length > 32 ? '...' : ''),
              ts: new Date(item.ts).toLocaleTimeString()
            }))
          );
        }
      }
      return orig.call(this, k, v);
    };
    P[fnName].__aug_analyze__ = true;
  };
  
  wrapHeaderMethod('set');
  wrapHeaderMethod('append');
  console.log('[签名调试] 已在 Headers.set/append 上安装 nonce/sign 关联分析');
}

// ==================== 一键安装随机数分析 ====================

/**
 * 一键安装所有随机数分析相关的 Hook
 */
function installRandomAnalysis() {
  console.log('[签名调试] 开始安装随机数分析 Hook...');
  recordRandomValues();
  analyzeNonceSignRelation();
  console.log('[签名调试] 随机数分析 Hook 安装完成！');
  console.log('[签名调试] 现在可以触发请求，观察随机数与 nonce/sign 的关系');
}

// ==================== 手动分析工具 ====================

/**
 * 手动分析指定的 nonce 或 sign 与随机数的关系
 * @param {string} value - 要分析的值
 * @param {string} type - 值的类型，'nonce' 或 'sign'
 */
function manualAnalyze(value, type = 'auto') {
  const recentRandom = window.__randomValues__ || [];
  if (recentRandom.length === 0) {
    return console.warn('[签名调试] 没有记录的随机数，请先执行 recordRandomValues() 并触发请求');
  }
  
  const base64url = (bytes) => {
    let bin = String.fromCharCode.apply(null, bytes);
    let b64 = btoa(bin).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    return b64;
  };
  
  console.log(`[签名调试] 手动分析 ${type}: ${value}`);
  
  recentRandom.forEach((item, i) => {
    try {
      const bytes = new Uint8Array(item.bytes);
      const hex = item.hex.toUpperCase();
      const b64u = base64url(bytes);
      
      if (type === 'nonce' || type === 'auto') {
        if (value === b64u) {
          console.log(`[签名调试] ✓ 与随机数 #${i} 的 Base64URL 匹配 (${item.len} 字节)`);
        }
      }
      
      if (type === 'sign' || type === 'auto') {
        if (value.toUpperCase() === hex) {
          console.log(`[签名调试] ✓ 与随机数 #${i} 的 Hex 匹配 (${item.len} 字节)`);
        }
      }
    } catch(e) {
      console.warn(`[签名调试] 分析随机数 #${i} 失败:`, e);
    }
  });
}

// ==================== 使用说明 ====================

console.log(`
=== 随机数与签名关联分析工具 ===

使用方法：
1. 执行 installRandomAnalysis() 安装分析 Hook
2. 触发 /v2/chat 请求
3. 观察控制台输出的关联分析结果

可用函数：
- installRandomAnalysis()           // 一键安装所有分析 Hook
- recordRandomValues()              // 记录随机数生成
- analyzeNonceSignRelation()        // 分析 nonce/sign 关联
- manualAnalyze(value, type)        // 手动分析指定值

示例：
manualAnalyze('h2KBbwoYj64N1jtUeJKCg', 'nonce')
manualAnalyze('7FCC1FA9DF55FCAE780C86860464146D', 'sign')
`);
