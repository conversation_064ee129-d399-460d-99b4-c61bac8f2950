{"description": "当贝 AI /v2/chat 接口真实请求样例数据", "version": "1.0", "created": "2025-01-21", "samples": [{"id": 1, "description": "第一个真实请求样例", "request": {"url": "https://ai-api.dangbei.net/ai-search/chatApi/v2/chat", "method": "POST", "headers": {"Accept": "*/*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Connection": "keep-alive", "Origin": "https://ai.dangbei.com", "Referer": "https://ai.dangbei.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "cross-site", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "appType": "6", "appVersion": "1.1.19-1", "client-ver": "1.0.2", "content-type": "application/json", "deviceId": "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm", "lang": "zh", "nonce": "h2KBbwoYj64N1jtUeJKCg", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sign": "7FCC1FA9DF55FCAE780C86860464146D", "timestamp": "1755595831", "token": ""}, "body": {"stream": true, "botCode": "AI_SEARCH", "conversationId": "363769493893157061", "question": "[这里不能返回多个字段组成的字典吗 for n, a in records]", "model": "kimi-k2-0711-preview", "chatOption": {"searchKnowledge": false, "searchAllKnowledge": false, "searchSharedKnowledge": false}, "knowledgeList": [], "anonymousKey": "", "uuid": "363770791349322117", "chatId": "363770791349322117", "files": [], "reference": [], "role": "user", "status": "local", "content": "[这里不能返回多个字段组成的字典吗 for n, a in records]", "userAction": "", "agentId": ""}}}, {"id": 2, "description": "第二个真实请求样例", "request": {"url": "https://ai-api.dangbei.net/ai-search/chatApi/v2/chat", "method": "POST", "headers": {"Accept": "*/*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Connection": "keep-alive", "Origin": "https://ai.dangbei.com", "Referer": "https://ai.dangbei.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "cross-site", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "appType": "6", "appVersion": "1.1.19-1", "client-ver": "1.0.2", "content-type": "application/json", "deviceId": "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm", "lang": "zh", "nonce": "dCxp-tRqd39NUepVg3Nts", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sign": "6C82DFE53F2D42166211409B71DCDC2F", "timestamp": "1755596028", "token": ""}, "body": {"stream": true, "botCode": "AI_SEARCH", "conversationId": "363769493893157061", "question": "records 是一个字典列表, 有多个 key, 怎么输出其中的两个 key 组成的列表", "model": "kimi-k2-0711-preview", "chatOption": {"searchKnowledge": false, "searchAllKnowledge": false, "searchSharedKnowledge": false}, "knowledgeList": [], "anonymousKey": "", "uuid": "363771205009805509", "chatId": "363771205009805509", "files": [], "reference": [], "role": "user", "status": "local", "content": "records 是一个字典列表, 有多个 key, 怎么输出其中的两个 key 组成的列表", "userAction": "", "agentId": ""}}}, {"id": 3, "description": "第三个真实请求样例", "request": {"url": "https://ai-api.dangbei.net/ai-search/chatApi/v2/chat", "method": "POST", "headers": {"Accept": "*/*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Connection": "keep-alive", "Origin": "https://ai.dangbei.com", "Referer": "https://ai.dangbei.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "cross-site", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "appType": "6", "appVersion": "1.1.19-1", "client-ver": "1.0.2", "content-type": "application/json", "deviceId": "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm", "lang": "zh", "nonce": "_PZP1zdLBcECVpeymJHtW", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sign": "7284235F3A62F15C1E52E09C3BAA4DF5", "timestamp": "1755596071", "token": ""}, "body": {"stream": true, "botCode": "AI_SEARCH", "conversationId": "363769493893157061", "question": "如果records是一个嵌套字典列表呢，怎么输出其中两个key组成的列表？", "model": "kimi-k2-0711-preview", "chatOption": {"searchKnowledge": false, "searchAllKnowledge": false, "searchSharedKnowledge": false}, "knowledgeList": [], "anonymousKey": "", "uuid": "363771295053123781", "chatId": "363771295053123781", "files": [], "reference": [], "role": "user", "status": "local", "content": "如果records是一个嵌套字典列表呢，怎么输出其中两个key组成的列表？", "userAction": "", "agentId": ""}}}], "analysis": {"signature_characteristics": {"length": "32位十六进制", "case": "大写", "algorithm_suspected": "MD5", "variable_fields": ["nonce", "timestamp", "body"], "fixed_fields": ["deviceId", "appType", "appVersion", "client-ver", "lang"]}, "enumeration_results": {"basic_combinations_tested": "数千种", "extended_combinations_tested": "134,400+", "successful_matches": 0, "conclusion": "签名算法包含未掌握的关键要素，可能存在固定密钥或特殊拼接规则"}, "debugging_findings": {"code_location": ["_app-04ec0e01f4390115.js", "3536-845aec1084a70bcb.js"], "crypto_library_calls": "未捕获到标准加密库调用", "random_generation": "观察到6字节和32字节随机数生成", "direct_mapping": "随机数与nonce/sign无直接映射关系"}}}