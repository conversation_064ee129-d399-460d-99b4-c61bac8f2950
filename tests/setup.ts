/**
 * 测试环境设置文件
 * 配置Jest测试环境和全局设置
 */

// 设置测试超时时间
jest.setTimeout(30000);

// 模拟console方法以避免测试输出过多日志
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// 全局测试配置
(global as any).testConfig = {
  timeout: 10000,
  retries: 1
};
