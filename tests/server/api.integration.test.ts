/**
 * API 集成测试
 * 测试完整的 HTTP API 功能
 */

import request from 'supertest';
import { createApp } from '../../src/server/app';
import * as fs from 'fs';

// Mock fs 模块
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('API 集成测试', () => {
  let app: any;

  const mockModelsData = {
    success: true,
    data: {
      model: 'deepseek',
      modelList: [
        {
          value: 'deepseek',
          title: 'DeepSeek-R1最新版',
          hoverText: '专注逻辑推理与深度分析',
          recommend: true,
          pinned: true,
          option: [
            {
              title: '深度思考',
              value: 'deep',
              disable: false,
              selected: true
            }
          ]
        }
      ]
    }
  };

  beforeAll(() => {
    // Mock fs 方法
    mockFs.existsSync.mockReturnValue(true);
    mockFs.readFileSync.mockReturnValue(JSON.stringify(mockModelsData));
    
    // 创建测试应用
    app = createApp({ debug: false });
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('健康检查端点', () => {
    it('GET /health 应该返回健康状态', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('healthy');
      expect(response.body.data.timestamp).toBeDefined();
    });
  });

  describe('API 信息端点', () => {
    it('GET /api/info 应该返回API信息', async () => {
      const response = await request(app)
        .get('/api/info')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('当贝AI Provider API');
      expect(response.body.data.endpoints).toBeDefined();
    });
  });

  describe('模型列表端点', () => {
    it('GET /api/models 应该返回模型列表', async () => {
      const response = await request(app)
        .get('/api/models')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.models).toBeInstanceOf(Array);
      expect(response.body.data.total).toBe(1);
      expect(response.body.data.defaultModel).toBe('deepseek');
      expect(response.headers['x-request-id']).toBeDefined();
    });

    it('GET /api/models/recommended 应该返回推荐模型', async () => {
      const response = await request(app)
        .get('/api/models/recommended')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.models).toBeInstanceOf(Array);
      expect(response.body.data.total).toBe(1);
    });

    it('GET /api/models/:modelId 应该返回特定模型信息', async () => {
      const response = await request(app)
        .get('/api/models/deepseek')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe('deepseek');
      expect(response.body.data.name).toBe('DeepSeek-R1最新版');
    });

    it('GET /api/models/:modelId 对不存在的模型应该返回404', async () => {
      const response = await request(app)
        .get('/api/models/nonexistent')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('MODEL_NOT_FOUND');
    });

    it('POST /api/models/reload 应该重新加载模型数据', async () => {
      const response = await request(app)
        .post('/api/models/reload')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toContain('重新加载成功');
    });
  });

  describe('聊天端点', () => {
    it('POST /api/chat 应该验证请求参数', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_REQUEST');
    });

    it('POST /api/chat 应该验证模型是否支持', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          messages: [{ role: 'user', content: '你好' }],
          model: 'unsupported-model'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('UNSUPPORTED_MODEL');
    });

    it('POST /api/chat 应该验证消息格式', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          messages: [{ role: 'invalid', content: '你好' }],
          model: 'deepseek'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_REQUEST');
    });

    it('POST /api/chat 应该验证消息内容', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          messages: [{ role: 'user', content: '' }],
          model: 'deepseek'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_REQUEST');
    });
  });

  describe('错误处理', () => {
    it('应该处理404错误', async () => {
      const response = await request(app)
        .get('/nonexistent')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('ROUTE_NOT_FOUND');
    });

    it('应该处理无效的JSON', async () => {
      const response = await request(app)
        .post('/api/chat')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_JSON');
    });
  });

  describe('CORS 支持', () => {
    it('应该设置正确的CORS头', async () => {
      const response = await request(app)
        .options('/api/models')
        .expect(204);

      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toContain('GET');
      expect(response.headers['access-control-allow-methods']).toContain('POST');
    });
  });

  describe('请求ID', () => {
    it('所有响应都应该包含请求ID', async () => {
      const response = await request(app)
        .get('/api/models')
        .expect(200);

      expect(response.headers['x-request-id']).toBeDefined();
      expect(response.body.requestId).toBeDefined();
      expect(response.headers['x-request-id']).toBe(response.body.requestId);
    });
  });
});
