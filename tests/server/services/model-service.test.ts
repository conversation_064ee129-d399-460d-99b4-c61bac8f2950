/**
 * 模型服务单元测试
 */

import { ModelService } from '../../../src/server/services/model-service';
import * as fs from 'fs';

// Mock fs 模块
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('ModelService', () => {
  const mockModelsData = {
    success: true,
    data: {
      model: 'deepseek',
      modelList: [
        {
          value: 'deepseek',
          title: 'DeepSeek-R1最新版',
          hoverText: '专注逻辑推理与深度分析，擅长解决复杂问题，提供精准决策支持',
          icon: 'https://example.com/icon.png',
          banner: 'https://example.com/banner.png',
          innerBadgeText: 'HOT',
          recommend: true,
          pinned: true,
          option: [
            {
              title: '深度思考',
              value: 'deep',
              disable: false,
              selected: true
            },
            {
              title: '联网搜索',
              value: 'online',
              disable: false,
              selected: false
            }
          ]
        },
        {
          value: 'doubao-1_6-thinking',
          title: '豆包-1.6',
          hoverText: '豆包最新推理模型，创作、推理、数学大幅增强',
          recommend: true,
          pinned: false,
          option: [
            {
              title: '深度思考',
              value: 'deep',
              disable: false,
              selected: true
            }
          ]
        }
      ]
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock fs.existsSync 返回 true
    mockFs.existsSync.mockReturnValue(true);
    
    // Mock fs.readFileSync 返回模拟数据
    mockFs.readFileSync.mockReturnValue(JSON.stringify(mockModelsData));
  });

  describe('构造函数和初始化', () => {
    it('应该成功加载模型数据', () => {
      new ModelService();
      expect(mockFs.existsSync).toHaveBeenCalled();
      expect(mockFs.readFileSync).toHaveBeenCalled();
    });

    it('当文件不存在时应该抛出错误', () => {
      mockFs.existsSync.mockReturnValue(false);
      
      expect(() => {
        new ModelService();
      }).toThrow('模型数据文件不存在');
    });

    it('当文件格式无效时应该抛出错误', () => {
      mockFs.readFileSync.mockReturnValue('invalid json');
      
      expect(() => {
        new ModelService();
      }).toThrow('无法加载模型数据');
    });
  });

  describe('getModels', () => {
    it('应该返回所有模型列表', () => {
      const service = new ModelService();
      const result = service.getModels();
      
      expect(result.defaultModel).toBe('deepseek');
      expect(result.models).toHaveLength(2);
      expect(result.total).toBe(2);
      
      // 检查第一个模型
      const firstModel = result.models[0];
      expect(firstModel).toBeDefined();
      expect(firstModel!.id).toBe('deepseek');
      expect(firstModel!.name).toBe('DeepSeek-R1最新版');
      expect(firstModel!.recommended).toBe(true);
      expect(firstModel!.pinned).toBe(true);
      expect(firstModel!.options).toHaveLength(2);
    });

    it('应该按照置顶和推荐状态排序', () => {
      const service = new ModelService();
      const result = service.getModels();
      
      // 置顶的应该排在前面
      expect(result.models[0]?.pinned).toBe(true);
      expect(result.models[1]?.pinned).toBe(false);
    });
  });

  describe('getModelById', () => {
    it('应该返回指定ID的模型', () => {
      const service = new ModelService();
      const model = service.getModelById('deepseek');
      
      expect(model).not.toBeNull();
      expect(model!.id).toBe('deepseek');
      expect(model!.name).toBe('DeepSeek-R1最新版');
    });

    it('当模型不存在时应该返回null', () => {
      const service = new ModelService();
      const model = service.getModelById('nonexistent');
      
      expect(model).toBeNull();
    });
  });

  describe('isModelSupported', () => {
    it('对于存在的模型应该返回true', () => {
      const service = new ModelService();
      expect(service.isModelSupported('deepseek')).toBe(true);
    });

    it('对于不存在的模型应该返回false', () => {
      const service = new ModelService();
      expect(service.isModelSupported('nonexistent')).toBe(false);
    });
  });

  describe('getRecommendedModels', () => {
    it('应该只返回推荐的模型', () => {
      const service = new ModelService();
      const recommended = service.getRecommendedModels();
      
      expect(recommended).toHaveLength(2);
      recommended.forEach(model => {
        expect(model.recommended).toBe(true);
      });
    });
  });

  describe('getPinnedModels', () => {
    it('应该只返回置顶的模型', () => {
      const service = new ModelService();
      const pinned = service.getPinnedModels();
      
      expect(pinned).toHaveLength(1);
      expect(pinned[0]?.id).toBe('deepseek');
      expect(pinned[0]?.pinned).toBe(true);
    });
  });

  describe('getDefaultModel', () => {
    it('应该返回默认模型ID', () => {
      const service = new ModelService();
      expect(service.getDefaultModel()).toBe('deepseek');
    });
  });

  describe('getStatus', () => {
    it('应该返回服务状态信息', () => {
      const service = new ModelService();
      const status = service.getStatus();
      
      expect(status.loaded).toBe(true);
      expect(status.modelsCount).toBe(2);
      expect(status.defaultModel).toBe('deepseek');
      expect(status.lastUpdated).toBeInstanceOf(Date);
    });
  });

  describe('reloadModelsData', () => {
    it('应该重新加载模型数据', () => {
      const service = new ModelService();
      
      // 清除之前的调用记录
      jest.clearAllMocks();
      
      service.reloadModelsData();
      
      expect(mockFs.existsSync).toHaveBeenCalled();
      expect(mockFs.readFileSync).toHaveBeenCalled();
    });
  });
});
