/**
 * 消息处理逻辑测试
 * 验证继续对话时的消息处理优化
 */

const { JSDOM } = require('jsdom');
const fs = require('fs');
const path = require('path');

// 模拟浏览器环境
const dom = new JSDOM(`
<!DOCTYPE html>
<html>
<head>
    <title>消息处理测试</title>
</head>
<body>
    <div id="app">
        <div id="messages-list"></div>
        <textarea id="message-input"></textarea>
        <button id="send-button">发送</button>
    </div>
</body>
</html>
`, {
    url: 'http://localhost:8080',
    pretendToBeVisual: true,
    resources: 'usable'
});

global.window = dom.window;
global.document = dom.window.document;
global.localStorage = {
    data: {},
    getItem: function(key) { return this.data[key] || null; },
    setItem: function(key, value) { this.data[key] = value; },
    removeItem: function(key) { delete this.data[key]; },
    clear: function() { this.data = {}; }
};

// 模拟 fetch API
global.fetch = jest.fn();

// 加载应用代码
const appPath = path.join(__dirname, '../public/chat/js/app.js');
const appCode = fs.readFileSync(appPath, 'utf8');

// 模拟依赖
global.storage = {
    addMessage: jest.fn(),
    getSession: jest.fn(() => ({
        id: 'test-session',
        messages: [
            { role: 'user', content: '你好' },
            { role: 'assistant', content: '你好！有什么可以帮助你的吗？' }
        ]
    }))
};

global.apiClient = {
    sendStreamMessage: jest.fn(),
    clearConversationId: jest.fn()
};

describe('消息处理优化测试', () => {
    let chatApp;

    beforeEach(() => {
        // 重置模拟函数
        jest.clearAllMocks();
        
        // 重置 localStorage
        localStorage.clear();
        
        // 创建应用实例
        eval(appCode);
        chatApp = new ChatApp();
        
        // 模拟当前会话
        chatApp.currentSession = {
            id: 'test-session',
            messages: [
                { role: 'user', content: '你好' },
                { role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
                { role: 'user', content: '请介绍一下JavaScript' }
            ]
        };
        
        // 模拟选中的模型
        chatApp.selectedModel = { id: 'deepseek-r1' };
    });

    test('继续对话时应该只发送当前用户输入', async () => {
        // 模拟用户输入
        const messageInput = document.getElementById('message-input');
        messageInput.value = '什么是TypeScript？';
        
        // 模拟 sendStreamMessage 的实现
        apiClient.sendStreamMessage.mockImplementation((params, onMessage, onComplete, onError) => {
            // 验证发送的消息格式
            expect(params.messages).toEqual([{
                role: 'user',
                content: '什么是TypeScript？'
            }]);
            
            // 验证消息数组长度为1（只包含当前用户输入）
            expect(params.messages).toHaveLength(1);
            
            // 模拟成功回调
            setTimeout(() => onComplete(), 100);
            
            return Promise.resolve();
        });
        
        // 发送消息
        await chatApp.sendMessage();
        
        // 验证 API 调用
        expect(apiClient.sendStreamMessage).toHaveBeenCalledTimes(1);
        
        // 验证调用参数
        const callArgs = apiClient.sendStreamMessage.mock.calls[0][0];
        expect(callArgs.messages).toEqual([{
            role: 'user',
            content: '什么是TypeScript？'
        }]);
        expect(callArgs.model).toBe('deepseek-r1');
    });

    test('新建对话时应该正确处理第一条消息', async () => {
        // 模拟新会话（无历史消息）
        chatApp.currentSession = {
            id: 'new-session',
            messages: []
        };
        
        // 模拟用户输入
        const messageInput = document.getElementById('message-input');
        messageInput.value = '你好，这是第一条消息';
        
        // 模拟 sendStreamMessage 的实现
        apiClient.sendStreamMessage.mockImplementation((params, onMessage, onComplete, onError) => {
            // 验证发送的消息格式
            expect(params.messages).toEqual([{
                role: 'user',
                content: '你好，这是第一条消息'
            }]);
            
            // 验证消息数组长度为1
            expect(params.messages).toHaveLength(1);
            
            // 模拟成功回调
            setTimeout(() => onComplete(), 100);
            
            return Promise.resolve();
        });
        
        // 发送消息
        await chatApp.sendMessage();
        
        // 验证 API 调用
        expect(apiClient.sendStreamMessage).toHaveBeenCalledTimes(1);
    });

    test('应该正确传递选项参数', async () => {
        // 模拟用户输入
        const messageInput = document.getElementById('message-input');
        messageInput.value = '测试消息';
        
        // 模拟选项
        chatApp.getSelectedOptions = jest.fn(() => ({
            deep_thinking: true,
            online_search: false
        }));
        
        // 模拟 sendStreamMessage 的实现
        apiClient.sendStreamMessage.mockImplementation((params, onMessage, onComplete, onError) => {
            // 验证选项参数
            expect(params.options).toEqual({
                deep_thinking: true,
                online_search: false
            });
            
            // 模拟成功回调
            setTimeout(() => onComplete(), 100);
            
            return Promise.resolve();
        });
        
        // 发送消息
        await chatApp.sendMessage();
        
        // 验证选项传递
        expect(chatApp.getSelectedOptions).toHaveBeenCalledTimes(1);
    });

    test('应该正确处理空输入', async () => {
        // 模拟空输入
        const messageInput = document.getElementById('message-input');
        messageInput.value = '   '; // 只有空格
        
        // 发送消息
        await chatApp.sendMessage();
        
        // 验证不应该调用 API
        expect(apiClient.sendStreamMessage).not.toHaveBeenCalled();
    });

    test('应该正确处理未选择模型的情况', async () => {
        // 清除选中的模型
        chatApp.selectedModel = null;
        
        // 模拟用户输入
        const messageInput = document.getElementById('message-input');
        messageInput.value = '测试消息';
        
        // 模拟错误显示方法
        chatApp.showError = jest.fn();
        
        // 发送消息
        await chatApp.sendMessage();
        
        // 验证显示错误信息
        expect(chatApp.showError).toHaveBeenCalledWith('请先选择一个模型');
        
        // 验证不应该调用 API
        expect(apiClient.sendStreamMessage).not.toHaveBeenCalled();
    });
});

console.log('消息处理优化测试已创建');
console.log('运行测试: npm test tests/message-processing.test.js');
