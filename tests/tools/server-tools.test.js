#!/usr/bin/env node

/**
 * 服务器工具测试
 * 测试服务器启动、版本检测等工具功能
 * 
 * 合并来源:
 * - test-server.js - 简单服务器测试
 * - test-v2-detection.js - V2接口检测
 * 
 * <AUTHOR> Provider SDK
 * @version 2.0.0
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试配置
const TEST_CONFIG = {
  serverPort: 3001, // 使用不同端口避免冲突
  serverHost: 'localhost',
  timeout: 30000,
  startupDelay: 3000
};

/**
 * 服务器启动测试
 */
async function testServerStartup() {
  colorLog('cyan', '\n=== 服务器启动测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  let serverProcess = null;
  
  try {
    // 测试服务器启动
    totalTests++;
    colorLog('blue', '\n测试: 服务器启动');
    
    const serverScript = path.join(__dirname, '../../src/server/index.ts');
    const serverUrl = `http://${TEST_CONFIG.serverHost}:${TEST_CONFIG.serverPort}`;
    
    try {
      // 启动服务器进程
      serverProcess = spawn('node', ['-r', 'ts-node/register', serverScript], {
        env: { ...process.env, PORT: TEST_CONFIG.serverPort },
        stdio: ['pipe', 'pipe', 'pipe']
      });
      
      // 等待服务器启动
      await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.startupDelay));
      
      // 检查服务器是否响应
      const response = await axios.get(`${serverUrl}/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        colorLog('green', '✅ 服务器启动成功');
        passedTests++;
      } else {
        colorLog('red', '❌ 服务器启动失败');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 服务器启动测试异常: ${error.message}`);
    }
    
    // 测试健康检查接口
    totalTests++;
    colorLog('blue', '\n测试: 健康检查接口');
    
    try {
      const response = await axios.get(`${serverUrl}/health`, {
        timeout: 5000
      });
      
      if (response.status === 200 && response.data.success) {
        colorLog('green', '✅ 健康检查接口正常');
        console.log(`服务器状态: ${response.data.data.status}`);
        passedTests++;
      } else {
        colorLog('red', '❌ 健康检查接口异常');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 健康检查测试异常: ${error.message}`);
    }
    
    // 测试API接口可用性
    totalTests++;
    colorLog('blue', '\n测试: API接口可用性');
    
    try {
      const response = await axios.get(`${serverUrl}/api/models`, {
        timeout: 10000
      });
      
      if (response.status === 200) {
        colorLog('green', '✅ API接口可用');
        passedTests++;
      } else {
        colorLog('red', '❌ API接口不可用');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ API接口测试异常: ${error.message}`);
    }
    
  } catch (error) {
    colorLog('red', `❌ 服务器测试失败: ${error.message}`);
  } finally {
    // 清理服务器进程
    if (serverProcess) {
      serverProcess.kill('SIGTERM');
      colorLog('blue', '服务器进程已停止');
    }
  }
  
  colorLog('yellow', `\n服务器启动测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 版本检测测试
 */
async function testVersionDetection() {
  colorLog('cyan', '\n=== 版本检测测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const { DangbeiProvider } = require('../../dist');
    
    // 测试Provider版本信息
    totalTests++;
    colorLog('blue', '\n测试: Provider版本信息');
    
    try {
      const provider = new DangbeiProvider();
      
      // 检查是否有版本相关方法或属性
      if (provider) {
        colorLog('green', '✅ Provider实例创建成功');
        passedTests++;
      } else {
        colorLog('red', '❌ Provider实例创建失败');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ Provider版本测试异常: ${error.message}`);
    }
    
    // 测试V1/V2接口检测
    totalTests++;
    colorLog('blue', '\n测试: V1/V2接口检测');
    
    try {
      // 模拟V1接口检测
      const v1Endpoints = [
        '/ai-search/conversationApi/v1/batch/create',
        '/ai-search/commonApi/v1/generateId'
      ];
      
      // 模拟V2接口检测
      const v2Endpoints = [
        '/ai-search/chatApi/v2/chat'
      ];
      
      const allEndpoints = [...v1Endpoints, ...v2Endpoints];
      
      if (allEndpoints.length > 0) {
        colorLog('green', `✅ 接口版本检测完成，发现 ${allEndpoints.length} 个端点`);
        console.log(`V1接口: ${v1Endpoints.length} 个`);
        console.log(`V2接口: ${v2Endpoints.length} 个`);
        passedTests++;
      } else {
        colorLog('red', '❌ 接口版本检测失败');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 接口检测测试异常: ${error.message}`);
    }
    
    // 测试功能特性检测
    totalTests++;
    colorLog('blue', '\n测试: 功能特性检测');
    
    try {
      const features = {
        v1Signature: true,    // V1签名算法
        v2Signature: true,    // V2签名算法 (WASM)
        streamChat: true,     // 流式聊天
        threeColorData: true, // 三色数据
        searchCards: true     // 搜索卡片
      };
      
      const enabledFeatures = Object.entries(features)
        .filter(([_, enabled]) => enabled)
        .map(([feature, _]) => feature);
      
      if (enabledFeatures.length > 0) {
        colorLog('green', `✅ 功能特性检测完成，启用 ${enabledFeatures.length} 个特性`);
        enabledFeatures.forEach(feature => {
          console.log(`  - ${feature}`);
        });
        passedTests++;
      } else {
        colorLog('red', '❌ 功能特性检测失败');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 功能特性测试异常: ${error.message}`);
    }
    
  } catch (error) {
    colorLog('red', `❌ 版本检测测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n版本检测测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 工具函数测试
 */
async function testUtilityFunctions() {
  colorLog('cyan', '\n=== 工具函数测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    // 测试随机字符串生成
    totalTests++;
    colorLog('blue', '\n测试: 随机字符串生成');
    
    try {
      // 模拟nonce生成函数
      function generateNonce(length = 21) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
        let result = '';
        for (let i = 0; i < length; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      }
      
      const nonce1 = generateNonce();
      const nonce2 = generateNonce();
      
      if (nonce1 && nonce2 && nonce1 !== nonce2 && nonce1.length === 21) {
        colorLog('green', '✅ 随机字符串生成正常');
        console.log(`示例nonce: ${nonce1}`);
        passedTests++;
      } else {
        colorLog('red', '❌ 随机字符串生成异常');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 随机字符串测试异常: ${error.message}`);
    }
    
    // 测试时间戳生成
    totalTests++;
    colorLog('blue', '\n测试: 时间戳生成');
    
    try {
      const timestamp1 = Math.floor(Date.now() / 1000);
      const timestamp2 = Math.floor(Date.now() / 1000);
      
      if (timestamp1 > 0 && timestamp2 >= timestamp1) {
        colorLog('green', '✅ 时间戳生成正常');
        console.log(`当前时间戳: ${timestamp1}`);
        passedTests++;
      } else {
        colorLog('red', '❌ 时间戳生成异常');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 时间戳测试异常: ${error.message}`);
    }
    
    // 测试设备ID生成
    totalTests++;
    colorLog('blue', '\n测试: 设备ID生成');
    
    try {
      // 模拟设备ID生成
      function generateDeviceId() {
        const crypto = require('crypto');
        const randomPart = crypto.randomBytes(16).toString('hex');
        const suffix = crypto.randomBytes(8).toString('hex');
        return `${randomPart}_${suffix}`;
      }
      
      const deviceId1 = generateDeviceId();
      const deviceId2 = generateDeviceId();
      
      if (deviceId1 && deviceId2 && deviceId1 !== deviceId2 && deviceId1.includes('_')) {
        colorLog('green', '✅ 设备ID生成正常');
        console.log(`示例设备ID: ${deviceId1.substring(0, 20)}...`);
        passedTests++;
      } else {
        colorLog('red', '❌ 设备ID生成异常');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 设备ID测试异常: ${error.message}`);
    }
    
  } catch (error) {
    colorLog('red', `❌ 工具函数测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n工具函数测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 性能监控测试
 */
async function testPerformanceMonitoring() {
  colorLog('cyan', '\n=== 性能监控测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    // 测试内存使用监控
    totalTests++;
    colorLog('blue', '\n测试: 内存使用监控');
    
    try {
      const memUsage = process.memoryUsage();
      const memMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      };
      
      if (memMB.rss > 0 && memMB.heapUsed > 0) {
        colorLog('green', '✅ 内存使用监控正常');
        console.log(`RSS: ${memMB.rss}MB, Heap Used: ${memMB.heapUsed}MB`);
        passedTests++;
      } else {
        colorLog('red', '❌ 内存使用监控异常');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 内存监控测试异常: ${error.message}`);
    }
    
    // 测试CPU使用监控
    totalTests++;
    colorLog('blue', '\n测试: CPU使用监控');
    
    try {
      const cpuUsage = process.cpuUsage();
      
      if (cpuUsage.user >= 0 && cpuUsage.system >= 0) {
        colorLog('green', '✅ CPU使用监控正常');
        console.log(`User: ${cpuUsage.user}μs, System: ${cpuUsage.system}μs`);
        passedTests++;
      } else {
        colorLog('red', '❌ CPU使用监控异常');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ CPU监控测试异常: ${error.message}`);
    }
    
  } catch (error) {
    colorLog('red', `❌ 性能监控测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n性能监控测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 主测试函数
 */
async function runAllTests() {
  colorLog('magenta', '🚀 开始服务器工具测试');
  
  const results = [];
  
  // 运行所有测试
  results.push(await testServerStartup());
  results.push(await testVersionDetection());
  results.push(await testUtilityFunctions());
  results.push(await testPerformanceMonitoring());
  
  // 统计总结果
  const totalPassed = results.reduce((sum, result) => sum + result.passed, 0);
  const totalTests = results.reduce((sum, result) => sum + result.total, 0);
  const passRate = ((totalPassed / totalTests) * 100).toFixed(1);
  
  colorLog('magenta', '\n=== 测试总结 ===');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过测试: ${totalPassed}`);
  console.log(`失败测试: ${totalTests - totalPassed}`);
  console.log(`通过率: ${passRate}%`);
  
  if (totalPassed >= totalTests * 0.8) { // 80%通过率即可
    colorLog('green', '🎉 服务器工具测试通过！');
    process.exit(0);
  } else {
    colorLog('red', '❌ 服务器工具测试失败');
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests().catch(error => {
    colorLog('red', `测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testServerStartup,
  testVersionDetection,
  testUtilityFunctions,
  testPerformanceMonitoring,
  runAllTests
};
