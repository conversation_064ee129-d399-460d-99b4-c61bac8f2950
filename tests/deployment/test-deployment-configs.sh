#!/bin/bash
# 当贝AI聊天界面部署配置测试脚本
# 用于验证Caddy和Nginx配置文件的正确性

set -e

# 配置变量
CADDY_CONFIG="deployment/caddy/Caddyfile"
NGINX_CONFIG="deployment/nginx/dangbei-chat.conf"
TEMP_DIR="/tmp/dangbei-deployment-test"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_test "执行测试: $test_name"
    
    if eval "$test_command"; then
        log_info "✅ 测试通过: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "❌ 测试失败: $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 检查配置文件存在性
test_config_files_exist() {
    log_info "检查配置文件存在性..."
    
    run_test "Caddy配置文件存在" "[ -f '$CADDY_CONFIG' ]"
    run_test "Nginx配置文件存在" "[ -f '$NGINX_CONFIG' ]"
    run_test "部署文档存在" "[ -f 'deployment/README.md' ]"
    run_test "本地部署指南存在" "[ -f 'deployment/local-deployment-guide.md' ]"
    run_test "远程部署指南存在" "[ -f 'deployment/remote-deployment-guide.md' ]"
}

# 测试Caddy配置
test_caddy_config() {
    log_info "测试Caddy配置..."
    
    if [ ! -f "$CADDY_CONFIG" ]; then
        log_error "Caddy配置文件不存在: $CADDY_CONFIG"
        return 1
    fi
    
    # 检查配置文件内容
    run_test "Caddy配置包含本地配置" "grep -q 'localhost:8080' '$CADDY_CONFIG'"
    run_test "Caddy配置包含静态文件服务" "grep -q 'root \*' '$CADDY_CONFIG'"
    run_test "Caddy配置包含API代理" "grep -q 'reverse_proxy /api/\*' '$CADDY_CONFIG'"
    run_test "Caddy配置包含压缩设置" "grep -q 'encode gzip' '$CADDY_CONFIG'"
    run_test "Caddy配置包含缓存策略" "grep -q 'Cache-Control' '$CADDY_CONFIG'"
    run_test "Caddy配置包含安全头" "grep -q 'X-Content-Type-Options' '$CADDY_CONFIG'"
    
    # 如果安装了Caddy，验证配置语法
    if command -v caddy &> /dev/null; then
        run_test "Caddy配置语法正确" "caddy validate --config '$CADDY_CONFIG'"
    else
        log_warn "Caddy未安装，跳过语法验证"
    fi
}

# 测试Nginx配置
test_nginx_config() {
    log_info "测试Nginx配置..."
    
    if [ ! -f "$NGINX_CONFIG" ]; then
        log_error "Nginx配置文件不存在: $NGINX_CONFIG"
        return 1
    fi
    
    # 检查配置文件内容
    run_test "Nginx配置包含服务器块" "grep -q 'server {' '$NGINX_CONFIG'"
    run_test "Nginx配置包含监听端口" "grep -q 'listen.*443.*ssl' '$NGINX_CONFIG'"
    run_test "Nginx配置包含静态文件根目录" "grep -q 'root /var/www/dangbei-chat' '$NGINX_CONFIG'"
    run_test "Nginx配置包含API代理" "grep -q 'location /api/' '$NGINX_CONFIG'"
    run_test "Nginx配置包含上游配置" "grep -q 'upstream dangbei_api' '$NGINX_CONFIG'"
    run_test "Nginx配置包含Gzip压缩" "grep -q 'gzip on' '$NGINX_CONFIG'"
    run_test "Nginx配置包含SSL配置" "grep -q 'ssl_certificate' '$NGINX_CONFIG'"
    run_test "Nginx配置包含安全头" "grep -q 'add_header.*X-Frame-Options' '$NGINX_CONFIG'"
    run_test "Nginx配置包含缓存策略" "grep -q 'expires.*1y' '$NGINX_CONFIG'"
    
    # 创建临时测试环境
    mkdir -p "$TEMP_DIR"
    
    # 创建简化的测试配置
    cat > "$TEMP_DIR/test-nginx.conf" << EOF
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 包含我们的配置文件
    include $PWD/$NGINX_CONFIG;
}
EOF
    
    # 如果安装了Nginx，验证配置语法
    if command -v nginx &> /dev/null; then
        run_test "Nginx配置语法正确" "nginx -t -c '$TEMP_DIR/test-nginx.conf'"
    else
        log_warn "Nginx未安装，跳过语法验证"
    fi
    
    # 清理临时文件
    rm -rf "$TEMP_DIR"
}

# 测试配置文件安全性
test_config_security() {
    log_info "测试配置安全性..."
    
    # 检查Caddy安全配置
    if [ -f "$CADDY_CONFIG" ]; then
        run_test "Caddy配置包含HSTS" "grep -q 'Strict-Transport-Security' '$CADDY_CONFIG'"
        run_test "Caddy配置包含CSP" "grep -q 'Content-Security-Policy' '$CADDY_CONFIG'"
        run_test "Caddy配置包含X-Frame-Options" "grep -q 'X-Frame-Options' '$CADDY_CONFIG'"
    fi
    
    # 检查Nginx安全配置
    if [ -f "$NGINX_CONFIG" ]; then
        run_test "Nginx配置包含HSTS" "grep -q 'Strict-Transport-Security' '$NGINX_CONFIG'"
        run_test "Nginx配置包含安全SSL配置" "grep -q 'ssl_protocols.*TLSv1.2' '$NGINX_CONFIG'"
        run_test "Nginx配置禁止访问隐藏文件" "grep -q 'location ~ /\\\\.' '$NGINX_CONFIG'"
        run_test "Nginx配置包含错误页面" "grep -q 'error_page' '$NGINX_CONFIG'"
    fi
}

# 测试性能配置
test_performance_config() {
    log_info "测试性能配置..."
    
    # 检查Caddy性能配置
    if [ -f "$CADDY_CONFIG" ]; then
        run_test "Caddy配置启用压缩" "grep -q 'encode.*gzip' '$CADDY_CONFIG'"
        run_test "Caddy配置包含缓存策略" "grep -q 'max-age=31536000' '$CADDY_CONFIG'"
    fi
    
    # 检查Nginx性能配置
    if [ -f "$NGINX_CONFIG" ]; then
        run_test "Nginx配置启用Gzip" "grep -q 'gzip on' '$NGINX_CONFIG'"
        run_test "Nginx配置启用HTTP/2" "grep -q 'http2' '$NGINX_CONFIG'"
        run_test "Nginx配置包含keepalive" "grep -q 'keepalive' '$NGINX_CONFIG'"
        run_test "Nginx配置包含缓存策略" "grep -q 'expires.*1y' '$NGINX_CONFIG'"
    fi
}

# 测试路径配置
test_path_config() {
    log_info "测试路径配置..."
    
    # 检查静态文件路径
    if [ -f "$CADDY_CONFIG" ]; then
        run_test "Caddy静态文件路径正确" "grep -q '/var/www/dangbei-chat\\|/path/to.*public/chat' '$CADDY_CONFIG'"
    fi
    
    if [ -f "$NGINX_CONFIG" ]; then
        run_test "Nginx静态文件路径正确" "grep -q 'root /var/www/dangbei-chat' '$NGINX_CONFIG'"
        run_test "Nginx API代理路径正确" "grep -q 'proxy_pass.*localhost:3000' '$NGINX_CONFIG'"
    fi
}

# 测试日志配置
test_logging_config() {
    log_info "测试日志配置..."
    
    # 检查Caddy日志配置
    if [ -f "$CADDY_CONFIG" ]; then
        run_test "Caddy配置包含访问日志" "grep -q 'log {' '$CADDY_CONFIG'"
        run_test "Caddy日志格式为JSON" "grep -q 'format json' '$CADDY_CONFIG'"
    fi
    
    # 检查Nginx日志配置
    if [ -f "$NGINX_CONFIG" ]; then
        run_test "Nginx配置包含访问日志" "grep -q 'access_log' '$NGINX_CONFIG'"
        run_test "Nginx配置包含错误日志" "grep -q 'error_log' '$NGINX_CONFIG'"
    fi
}

# 测试文档完整性
test_documentation() {
    log_info "测试文档完整性..."
    
    local docs=(
        "deployment/README.md"
        "deployment/local-deployment-guide.md"
        "deployment/remote-deployment-guide.md"
        "deployment/optimization-guide.md"
        "docs/静态部署方案/README.md"
    )
    
    for doc in "${docs[@]}"; do
        if [ -f "$doc" ]; then
            run_test "文档存在: $(basename "$doc")" "[ -f '$doc' ]"
            run_test "文档非空: $(basename "$doc")" "[ -s '$doc' ]"
            run_test "文档包含标题: $(basename "$doc")" "grep -q '^#' '$doc'"
        else
            log_warn "文档不存在: $doc"
        fi
    done
}

# 测试部署脚本
test_deployment_scripts() {
    log_info "测试部署脚本..."
    
    # 检查是否有可执行的部署脚本
    if [ -f "deployment/scripts/build-and-deploy.sh" ]; then
        run_test "部署脚本可执行" "[ -x 'deployment/scripts/build-and-deploy.sh' ]"
        run_test "部署脚本包含shebang" "head -1 'deployment/scripts/build-and-deploy.sh' | grep -q '^#!/bin/bash'"
    fi
    
    # 检查测试脚本本身
    run_test "静态部署测试脚本存在" "[ -f 'tests/deployment/test-static-deployment.sh' ]"
    if [ -f "tests/deployment/test-static-deployment.sh" ]; then
        run_test "测试脚本可执行" "[ -x 'tests/deployment/test-static-deployment.sh' ]"
    fi
}

# 主测试流程
main() {
    log_info "🔧 开始部署配置测试"
    log_info "测试Caddy配置: $CADDY_CONFIG"
    log_info "测试Nginx配置: $NGINX_CONFIG"
    echo ""
    
    # 配置文件存在性测试
    test_config_files_exist
    echo ""
    
    # Caddy配置测试
    test_caddy_config
    echo ""
    
    # Nginx配置测试
    test_nginx_config
    echo ""
    
    # 安全配置测试
    test_config_security
    echo ""
    
    # 性能配置测试
    test_performance_config
    echo ""
    
    # 路径配置测试
    test_path_config
    echo ""
    
    # 日志配置测试
    test_logging_config
    echo ""
    
    # 文档完整性测试
    test_documentation
    echo ""
    
    # 部署脚本测试
    test_deployment_scripts
    echo ""
    
    # 输出测试结果
    echo "=========================================="
    log_info "📊 配置测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有配置测试通过！部署配置验证成功！${NC}"
        exit 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查配置文件${NC}"
        exit 1
    fi
}

# 清理函数
cleanup() {
    rm -rf "$TEMP_DIR"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 执行主函数
main "$@"
