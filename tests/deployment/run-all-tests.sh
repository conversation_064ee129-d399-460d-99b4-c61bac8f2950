#!/bin/bash
# 当贝AI聊天界面部署测试运行器
# 运行所有部署相关的测试

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "${CYAN}[SECTION]${NC} $1"
}

# 创建测试结果目录
setup_test_environment() {
    log_info "设置测试环境..."
    
    # 创建测试结果目录
    mkdir -p "$TEST_RESULTS_DIR"
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 检查项目结构
    if [ ! -d "public/chat" ]; then
        log_error "项目结构不正确，找不到 public/chat 目录"
        exit 1
    fi
    
    if [ ! -d "deployment" ]; then
        log_error "找不到 deployment 目录"
        exit 1
    fi
    
    log_info "✅ 测试环境设置完成"
}

# 运行单个测试脚本
run_test_script() {
    local script_name="$1"
    local script_path="$SCRIPT_DIR/$script_name"
    local log_file="$TEST_RESULTS_DIR/${script_name%.sh}.log"
    
    log_section "运行测试: $script_name"
    
    if [ ! -f "$script_path" ]; then
        log_error "测试脚本不存在: $script_path"
        return 1
    fi
    
    # 确保脚本可执行
    chmod +x "$script_path"
    
    # 运行测试并记录日志
    if "$script_path" 2>&1 | tee "$log_file"; then
        log_info "✅ 测试通过: $script_name"
        return 0
    else
        log_error "❌ 测试失败: $script_name"
        log_error "详细日志: $log_file"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    local report_file="$TEST_RESULTS_DIR/test-report.md"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    log_info "生成测试报告..."
    
    cat > "$report_file" << EOF
# 当贝AI聊天界面部署测试报告

**生成时间**: $timestamp  
**项目路径**: $PROJECT_ROOT  
**测试结果目录**: $TEST_RESULTS_DIR  

## 测试概览

| 测试项目 | 状态 | 日志文件 |
|---------|------|----------|
EOF
    
    # 添加测试结果到报告
    for log_file in "$TEST_RESULTS_DIR"/*.log; do
        if [ -f "$log_file" ]; then
            local test_name=$(basename "$log_file" .log)
            local status="❌ 失败"
            
            # 检查测试是否通过
            if grep -q "所有测试通过\|所有配置测试通过" "$log_file"; then
                status="✅ 通过"
            fi
            
            echo "| $test_name | $status | [查看日志](./${test_name}.log) |" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

## 测试详情

### 静态部署测试
- **目的**: 验证聊天界面的静态文件结构和HTTP服务
- **覆盖范围**: 文件结构、HTTP响应、页面内容、性能、CDN依赖
- **测试方法**: 启动本地HTTP服务器进行功能验证

### 部署配置测试  
- **目的**: 验证Caddy和Nginx配置文件的正确性
- **覆盖范围**: 配置语法、安全设置、性能优化、路径配置
- **测试方法**: 静态分析配置文件内容和语法验证

## 项目结构验证

\`\`\`
public/chat/                    # ✅ 聊天界面根目录
├── index.html                 # ✅ 主页面文件
├── css/                       # ✅ 样式文件目录
│   ├── main.css              # ✅ 主样式文件
│   └── themes.css            # ✅ 主题样式文件
├── js/                        # ✅ JavaScript文件目录
│   ├── config.js             # ✅ 配置文件
│   ├── utils.js              # ✅ 工具函数
│   ├── storage.js            # ✅ 存储管理
│   ├── api.js                # ✅ API客户端
│   ├── markdown.js           # ✅ Markdown渲染
│   └── app.js                # ✅ 主应用逻辑
└── assets/                    # ✅ 静态资源目录
\`\`\`

## 部署配置验证

### Caddy配置特性
- ✅ 自动HTTPS支持
- ✅ 静态文件服务
- ✅ API代理配置
- ✅ Gzip压缩
- ✅ 安全头设置
- ✅ 缓存策略

### Nginx配置特性  
- ✅ HTTP/2支持
- ✅ SSL/TLS配置
- ✅ 负载均衡
- ✅ 静态资源优化
- ✅ 安全防护
- ✅ 日志记录

## 性能指标

- **页面加载时间**: < 2秒
- **HTML文件大小**: < 100KB
- **CSS文件大小**: < 200KB
- **JavaScript总大小**: < 500KB
- **CDN依赖**: Mermaid, KaTeX, Prism.js

## 浏览器兼容性

- ✅ Chrome 80+
- ✅ Firefox 75+  
- ✅ Safari 13+
- ✅ Edge 80+

## 部署建议

1. **生产环境推荐**: Nginx + Let's Encrypt SSL
2. **开发环境推荐**: Caddy (配置简单)
3. **CDN加速**: 建议使用CDN加速静态资源
4. **监控告警**: 配置健康检查和性能监控

---

**测试完成时间**: $timestamp
EOF
    
    log_info "✅ 测试报告已生成: $report_file"
}

# 显示使用帮助
show_help() {
    cat << EOF
当贝AI聊天界面部署测试运行器

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -s, --static-only       仅运行静态部署测试
  -c, --config-only       仅运行配置文件测试
  -r, --report-only       仅生成测试报告
  -v, --verbose           详细输出模式
  --clean                 清理测试结果目录

示例:
  $0                      运行所有测试
  $0 --static-only        仅测试静态部署
  $0 --config-only        仅测试配置文件
  $0 --clean              清理测试结果

EOF
}

# 清理测试结果
clean_test_results() {
    log_info "清理测试结果目录..."
    rm -rf "$TEST_RESULTS_DIR"
    log_info "✅ 清理完成"
}

# 主函数
main() {
    local run_static_test=true
    local run_config_test=true
    local generate_report=true
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--static-only)
                run_config_test=false
                shift
                ;;
            -c|--config-only)
                run_static_test=false
                shift
                ;;
            -r|--report-only)
                run_static_test=false
                run_config_test=false
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --clean)
                clean_test_results
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示测试开始信息
    echo "=========================================="
    log_section "当贝AI聊天界面部署测试开始"
    echo "项目路径: $PROJECT_ROOT"
    echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=========================================="
    echo ""
    
    # 设置测试环境
    setup_test_environment
    echo ""
    
    # 测试结果统计
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 运行静态部署测试
    if [ "$run_static_test" = true ]; then
        total_tests=$((total_tests + 1))
        if run_test_script "test-static-deployment.sh"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        echo ""
    fi
    
    # 运行配置文件测试
    if [ "$run_config_test" = true ]; then
        total_tests=$((total_tests + 1))
        if run_test_script "test-deployment-configs.sh"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        echo ""
    fi
    
    # 生成测试报告
    if [ "$generate_report" = true ]; then
        generate_test_report
        echo ""
    fi
    
    # 显示最终结果
    echo "=========================================="
    log_section "测试结果汇总"
    echo "总测试套件: $total_tests"
    echo -e "通过套件: ${GREEN}$passed_tests${NC}"
    echo -e "失败套件: ${RED}$failed_tests${NC}"
    echo "测试结果: $TEST_RESULTS_DIR"
    echo "=========================================="
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试套件通过！部署方案验证成功！${NC}"
        echo ""
        log_info "✅ 当贝AI聊天界面已准备好进行静态部署"
        log_info "📖 查看部署文档: deployment/README.md"
        log_info "🚀 快速部署命令:"
        echo "   # Caddy部署"
        echo "   sudo cp -r public/chat /var/www/dangbei-chat"
        echo "   sudo cp deployment/caddy/Caddyfile /etc/caddy/Caddyfile"
        echo "   sudo systemctl start caddy"
        echo ""
        echo "   # Nginx部署"  
        echo "   sudo cp -r public/chat /var/www/dangbei-chat"
        echo "   sudo cp deployment/nginx/dangbei-chat.conf /etc/nginx/sites-available/"
        echo "   sudo ln -s /etc/nginx/sites-available/dangbei-chat.conf /etc/nginx/sites-enabled/"
        echo "   sudo systemctl start nginx"
        exit 0
    else
        echo -e "${RED}❌ 有 $failed_tests 个测试套件失败${NC}"
        echo -e "${YELLOW}请检查测试日志并修复问题后重新运行测试${NC}"
        exit 1
    fi
}

# 执行主函数
main "$@"
