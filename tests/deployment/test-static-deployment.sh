#!/bin/bash
# 当贝AI聊天界面静态部署测试脚本
# 用于验证静态部署的完整性和功能性

set -e

# 配置变量
CHAT_DIR="public/chat"
TEST_SERVER_PORT="8888"
API_SERVER_PORT="3000"
TEST_DOMAIN="localhost:${TEST_SERVER_PORT}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_test "执行测试: $test_name"
    
    if eval "$test_command"; then
        if [ -n "$expected_result" ]; then
            local result=$(eval "$test_command")
            if [[ "$result" == *"$expected_result"* ]]; then
                log_info "✅ 测试通过: $test_name"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                return 0
            else
                log_error "❌ 测试失败: $test_name (结果不匹配)"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
        else
            log_info "✅ 测试通过: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        fi
    else
        log_error "❌ 测试失败: $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查测试依赖..."
    
    local deps=("curl" "python3" "node")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_warn "$dep 未安装，某些测试可能无法执行"
        else
            log_info "✅ $dep 已安装"
        fi
    done
}

# 验证文件结构
test_file_structure() {
    log_info "测试文件结构..."
    
    run_test "检查聊天界面目录" "[ -d '$CHAT_DIR' ]"
    run_test "检查主页文件" "[ -f '$CHAT_DIR/index.html' ]"
    run_test "检查CSS目录" "[ -d '$CHAT_DIR/css' ]"
    run_test "检查JS目录" "[ -d '$CHAT_DIR/js' ]"
    run_test "检查主样式文件" "[ -f '$CHAT_DIR/css/main.css' ]"
    run_test "检查主题样式文件" "[ -f '$CHAT_DIR/css/themes.css' ]"
    run_test "检查配置文件" "[ -f '$CHAT_DIR/js/config.js' ]"
    run_test "检查API客户端" "[ -f '$CHAT_DIR/js/api.js' ]"
    run_test "检查主应用文件" "[ -f '$CHAT_DIR/js/app.js' ]"
}

# 验证文件内容
test_file_content() {
    log_info "测试文件内容..."
    
    run_test "HTML文件包含DOCTYPE" "grep -q '<!DOCTYPE html>' '$CHAT_DIR/index.html'"
    run_test "HTML文件包含聊天界面标题" "grep -q '当贝AI聊天' '$CHAT_DIR/index.html'"
    run_test "CSS文件包含样式定义" "grep -q 'app-header' '$CHAT_DIR/css/main.css'"
    run_test "JS配置文件包含配置对象" "grep -q 'ChatConfig' '$CHAT_DIR/js/config.js'"
    run_test "API文件包含ApiClient类" "grep -q 'class ApiClient' '$CHAT_DIR/js/api.js'"
}

# 启动测试服务器
start_test_server() {
    log_info "启动测试服务器..."
    
    # 检查端口是否被占用
    if lsof -Pi :$TEST_SERVER_PORT -sTCP:LISTEN -t >/dev/null; then
        log_warn "端口 $TEST_SERVER_PORT 已被占用，尝试停止现有进程..."
        pkill -f "python.*$TEST_SERVER_PORT" || true
        sleep 2
    fi
    
    # 启动Python简单HTTP服务器
    cd "$CHAT_DIR"
    python3 -m http.server $TEST_SERVER_PORT > /dev/null 2>&1 &
    TEST_SERVER_PID=$!
    cd - > /dev/null
    
    # 等待服务器启动
    sleep 3
    
    # 验证服务器是否启动成功
    if curl -s "http://$TEST_DOMAIN" > /dev/null; then
        log_info "✅ 测试服务器启动成功 (PID: $TEST_SERVER_PID)"
        return 0
    else
        log_error "❌ 测试服务器启动失败"
        return 1
    fi
}

# 停止测试服务器
stop_test_server() {
    if [ -n "$TEST_SERVER_PID" ]; then
        log_info "停止测试服务器 (PID: $TEST_SERVER_PID)..."
        kill $TEST_SERVER_PID 2>/dev/null || true
        wait $TEST_SERVER_PID 2>/dev/null || true
    fi
}

# HTTP响应测试
test_http_responses() {
    log_info "测试HTTP响应..."
    
    run_test "主页HTTP状态码" "curl -s -o /dev/null -w '%{http_code}' 'http://$TEST_DOMAIN/' | grep -q '200'"
    run_test "主页内容类型" "curl -s -I 'http://$TEST_DOMAIN/' | grep -q 'text/html'"
    run_test "CSS文件HTTP状态码" "curl -s -o /dev/null -w '%{http_code}' 'http://$TEST_DOMAIN/css/main.css' | grep -q '200'"
    run_test "CSS文件内容类型" "curl -s -I 'http://$TEST_DOMAIN/css/main.css' | grep -q 'text/css'"
    run_test "JS文件HTTP状态码" "curl -s -o /dev/null -w '%{http_code}' 'http://$TEST_DOMAIN/js/app.js' | grep -q '200'"
    run_test "JS文件内容类型" "curl -s -I 'http://$TEST_DOMAIN/js/app.js' | grep -q 'application/javascript\\|text/javascript'"
}

# 页面内容测试
test_page_content() {
    log_info "测试页面内容..."
    
    local page_content=$(curl -s "http://$TEST_DOMAIN/")
    
    run_test "页面包含标题" "echo '$page_content' | grep -q '当贝AI聊天'"
    run_test "页面包含CSS引用" "echo '$page_content' | grep -q 'main.css'"
    run_test "页面包含JS引用" "echo '$page_content' | grep -q 'app.js'"
    run_test "页面包含聊天容器" "echo '$page_content' | grep -q 'messages-container'"
    run_test "页面包含输入框" "echo '$page_content' | grep -q 'message-input'"
}

# 静态资源测试
test_static_resources() {
    log_info "测试静态资源..."
    
    # 测试CSS文件
    local css_content=$(curl -s "http://$TEST_DOMAIN/css/main.css")
    run_test "CSS包含应用样式" "echo '$css_content' | grep -q 'app-header'"
    
    # 测试JS文件
    local js_content=$(curl -s "http://$TEST_DOMAIN/js/config.js")
    run_test "JS配置文件有效" "echo '$js_content' | grep -q 'ChatConfig'"
    
    # 测试API客户端
    local api_content=$(curl -s "http://$TEST_DOMAIN/js/api.js")
    run_test "API客户端文件有效" "echo '$api_content' | grep -q 'ApiClient'"
}

# 性能测试
test_performance() {
    log_info "测试页面性能..."
    
    # 测试页面加载时间
    local load_time=$(curl -s -o /dev/null -w '%{time_total}' "http://$TEST_DOMAIN/")
    run_test "页面加载时间合理" "echo '$load_time < 2.0' | bc -l | grep -q '1'"
    
    # 测试文件大小
    local html_size=$(curl -s "http://$TEST_DOMAIN/" | wc -c)
    run_test "HTML文件大小合理" "[ $html_size -lt 100000 ]"  # 小于100KB
    
    local css_size=$(curl -s "http://$TEST_DOMAIN/css/main.css" | wc -c)
    run_test "CSS文件大小合理" "[ $css_size -lt 200000 ]"  # 小于200KB
}

# CDN依赖测试
test_cdn_dependencies() {
    log_info "测试CDN依赖..."
    
    # 检查页面中的CDN链接
    local page_content=$(curl -s "http://$TEST_DOMAIN/")
    
    run_test "包含Mermaid CDN" "echo '$page_content' | grep -q 'cdn.jsdelivr.net.*mermaid'"
    run_test "包含KaTeX CDN" "echo '$page_content' | grep -q 'cdn.jsdelivr.net.*katex'"
    run_test "包含Prism CDN" "echo '$page_content' | grep -q 'cdn.jsdelivr.net.*prismjs'"
    
    # 测试CDN可访问性（可选，需要网络连接）
    if curl -s --connect-timeout 5 "https://cdn.jsdelivr.net" > /dev/null; then
        run_test "Mermaid CDN可访问" "curl -s -o /dev/null -w '%{http_code}' 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js' | grep -q '200'"
        run_test "KaTeX CDN可访问" "curl -s -o /dev/null -w '%{http_code}' 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css' | grep -q '200'"
    else
        log_warn "跳过CDN可访问性测试（无网络连接）"
    fi
}

# 浏览器兼容性测试（使用Node.js模拟）
test_browser_compatibility() {
    log_info "测试浏览器兼容性..."
    
    if command -v node &> /dev/null; then
        # 创建临时测试脚本
        cat > /tmp/browser_test.js << 'EOF'
const http = require('http');
const url = require('url');

// 模拟浏览器请求
function testBrowserFeatures(baseUrl) {
    const tests = [
        { name: 'Fetch API支持', test: () => typeof fetch !== 'undefined' },
        { name: 'LocalStorage支持', test: () => typeof localStorage !== 'undefined' },
        { name: 'EventSource支持', test: () => typeof EventSource !== 'undefined' }
    ];
    
    console.log('浏览器兼容性测试结果:');
    tests.forEach(test => {
        try {
            const result = test.test();
            console.log(`${test.name}: ${result ? '✅' : '❌'}`);
        } catch (e) {
            console.log(`${test.name}: ❌ (${e.message})`);
        }
    });
}

// 测试页面JavaScript语法
function testJavaScriptSyntax(baseUrl) {
    const jsFiles = ['/js/config.js', '/js/utils.js', '/js/api.js', '/js/app.js'];
    
    jsFiles.forEach(file => {
        http.get(`${baseUrl}${file}`, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    // 简单的语法检查
                    if (data.includes('class ') || data.includes('function ') || data.includes('const ')) {
                        console.log(`${file}: ✅ 语法正常`);
                    } else {
                        console.log(`${file}: ❌ 可能存在语法问题`);
                    }
                } catch (e) {
                    console.log(`${file}: ❌ 语法错误 - ${e.message}`);
                }
            });
        });
    });
}

const baseUrl = process.argv[2] || 'http://localhost:8888';
testJavaScriptSyntax(baseUrl);
EOF
        
        run_test "JavaScript语法检查" "node /tmp/browser_test.js 'http://$TEST_DOMAIN' 2>/dev/null"
        rm -f /tmp/browser_test.js
    else
        log_warn "跳过浏览器兼容性测试（Node.js未安装）"
    fi
}

# 主测试流程
main() {
    log_info "🚀 开始当贝AI聊天界面静态部署测试"
    log_info "测试目标: $CHAT_DIR"
    log_info "测试服务器: http://$TEST_DOMAIN"
    echo ""
    
    # 检查依赖
    check_dependencies
    echo ""
    
    # 文件结构测试
    test_file_structure
    echo ""
    
    # 文件内容测试
    test_file_content
    echo ""
    
    # 启动测试服务器
    if start_test_server; then
        # HTTP响应测试
        test_http_responses
        echo ""
        
        # 页面内容测试
        test_page_content
        echo ""
        
        # 静态资源测试
        test_static_resources
        echo ""
        
        # 性能测试
        test_performance
        echo ""
        
        # CDN依赖测试
        test_cdn_dependencies
        echo ""
        
        # 浏览器兼容性测试
        test_browser_compatibility
        echo ""
        
        # 停止测试服务器
        stop_test_server
    else
        log_error "无法启动测试服务器，跳过HTTP相关测试"
    fi
    
    # 输出测试结果
    echo "=========================================="
    log_info "📊 测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！静态部署验证成功！${NC}"
        exit 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查配置${NC}"
        exit 1
    fi
}

# 清理函数
cleanup() {
    stop_test_server
    rm -f /tmp/browser_test.js
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 执行主函数
main "$@"
