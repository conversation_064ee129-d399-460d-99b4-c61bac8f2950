/**
 * 测试 "write after end" 错误修复
 * 验证在响应流结束后不会再次写入数据
 */

import { ImprovedSSEProcessor } from '../../src/services/improved-sse-processor';
import { ChatCallbacks, SSEMessageDelta, SSEChatCompleted } from '../../src/types';
import { Readable } from 'stream';

describe('Write After End 错误修复测试', () => {
  let processor: ImprovedSSEProcessor;
  let mockCallbacks: ChatCallbacks;
  let onMessageCalls: number;
  let onCompleteCalls: number;
  let onErrorCalls: number;

  beforeEach(() => {
    processor = new ImprovedSSEProcessor();
    onMessageCalls = 0;
    onCompleteCalls = 0;
    onErrorCalls = 0;

    mockCallbacks = {
      onMessage: jest.fn((content: string, data: SSEMessageDelta) => {
        onMessageCalls++;
        console.log(`消息回调 #${onMessageCalls}:`, content);
      }),
      onComplete: jest.fn((data: SSEChatCompleted) => {
        onCompleteCalls++;
        console.log(`完成回调 #${onCompleteCalls}:`, data);
      }),
      onError: jest.fn((error: Error) => {
        onErrorCalls++;
        console.log(`错误回调 #${onErrorCalls}:`, error.message);
      })
    };
  });

  afterEach(() => {
    processor.reset();
  });

  test('应该防止重复的完成事件触发', async () => {
    // 创建包含重复完成事件的 SSE 数据
    const sseData = [
      'event: conversation.message.delta',
      'data: {"content": "Hello", "id": "msg1", "role": "assistant"}',
      '',
      'event: conversation.chat.completed',
      'data: {"id": "chat1", "conversation_id": "conv1"}',
      '',
      'event: conversation.chat.completed', // 重复的完成事件
      'data: {"id": "chat1", "conversation_id": "conv1"}',
      '',
      'event: conversation.message.completed', // 另一种完成事件
      'data: {"id": "msg1", "conversation_id": "conv1"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证只调用了一次完成回调
    expect(onCompleteCalls).toBe(1);
    expect(onMessageCalls).toBe(1);
    expect(mockCallbacks.onComplete).toHaveBeenCalledTimes(1);
  });

  test('应该在流结束时发送默认完成事件（如果没有收到完成事件）', async () => {
    // 创建只有消息没有完成事件的 SSE 数据
    const sseData = [
      'event: conversation.message.delta',
      'data: {"content": "Hello", "id": "msg1", "role": "assistant"}',
      '',
      'event: conversation.message.delta',
      'data: {"content": " World", "id": "msg1", "role": "assistant"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证发送了默认完成事件
    expect(onCompleteCalls).toBe(1);
    expect(onMessageCalls).toBe(2);
    expect(mockCallbacks.onComplete).toHaveBeenCalledWith({
      id: '',
      parentMsgId: '',
      conversation_id: '',
      supportDownload: false
    });
  });

  test('应该在收到完成事件后不再发送默认完成事件', async () => {
    // 创建包含完成事件的 SSE 数据
    const sseData = [
      'event: conversation.message.delta',
      'data: {"content": "Hello", "id": "msg1", "role": "assistant"}',
      '',
      'event: conversation.chat.completed',
      'data: {"id": "chat1", "conversation_id": "conv1"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证只调用了一次完成回调（不会发送默认完成事件）
    expect(onCompleteCalls).toBe(1);
    expect(onMessageCalls).toBe(1);
    expect(mockCallbacks.onComplete).toHaveBeenCalledWith({
      id: 'chat1',
      parentMsgId: '',
      conversation_id: 'conv1',
      supportDownload: false
    });
  });

  test('重置后应该能够重新处理完成事件', async () => {
    // 第一次处理
    const sseData1 = [
      'event: conversation.chat.completed',
      'data: {"id": "chat1", "conversation_id": "conv1"}',
      ''
    ].join('\n');

    const stream1 = Readable.from([sseData1]);
    await processor.processStream(stream1, mockCallbacks);

    expect(onCompleteCalls).toBe(1);

    // 重置处理器
    processor.reset();

    // 第二次处理
    const sseData2 = [
      'event: conversation.chat.completed',
      'data: {"id": "chat2", "conversation_id": "conv2"}',
      ''
    ].join('\n');

    const stream2 = Readable.from([sseData2]);
    await processor.processStream(stream2, mockCallbacks);

    // 验证第二次也能正常处理完成事件
    expect(onCompleteCalls).toBe(2);
  });

  test('应该正确处理混合的事件类型', async () => {
    const sseData = [
      'event: conversation.message.delta',
      'data: {"content": "First", "id": "msg1", "role": "assistant"}',
      '',
      'event: conversation.message.delta',
      'data: {"content": " Second", "id": "msg1", "role": "assistant"}',
      '',
      'event: conversation.message.completed',
      'data: {"id": "msg1", "conversation_id": "conv1"}',
      '',
      'event: conversation.chat.completed', // 这个应该被跳过
      'data: {"id": "chat1", "conversation_id": "conv1"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证消息处理正常，完成事件只触发一次
    expect(onMessageCalls).toBe(2);
    expect(onCompleteCalls).toBe(1);
  });
});
