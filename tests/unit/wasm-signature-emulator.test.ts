/**
 * WASM签名模拟器单元测试
 * 测试WebAssembly签名算法模拟器的各种功能
 */

import { WasmSignatureEmulator } from '../../src/utils/wasm-signature-emulator';
import { SignatureV2Utils } from '../../src/utils/signature-v2';
import { SignatureParams } from '../../src/types';

describe('WasmSignatureEmulator', () => {
  let emulator: WasmSignatureEmulator;

  beforeEach(() => {
    emulator = new WasmSignatureEmulator({
      debug: false,
      strategy: 'hybrid',
      secretKey: 'test_secret_key'
    });
  });

  describe('构造函数和配置', () => {
    it('应该使用默认配置创建实例', () => {
      const defaultEmulator = new WasmSignatureEmulator();
      expect(defaultEmulator).toBeInstanceOf(WasmSignatureEmulator);
    });

    it('应该使用自定义配置创建实例', () => {
      const customEmulator = new WasmSignatureEmulator({
        debug: true,
        strategy: 'enhanced',
        timeOffset: 1000,
        secretKey: 'custom_key'
      });
      expect(customEmulator).toBeInstanceOf(WasmSignatureEmulator);
    });

    it('应该能够更新配置', () => {
      emulator.updateConfig({
        debug: true,
        strategy: 'standard'
      });
      // 配置更新应该不抛出错误
      expect(() => emulator.getSign('test', '123:nonce')).not.toThrow();
    });
  });

  describe('getSign方法', () => {
    it('应该生成有效的签名', () => {
      const result = emulator.getSign('test_data', '1755239241:test_nonce');
      
      expect(result).toHaveProperty('signature');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('strategy');
      
      expect(result.signature).toMatch(/^[A-F0-9]{32}$/);
      expect(typeof result.timestamp).toBe('number');
      expect(result.strategy).toBe('hybrid');
    });

    it('相同输入应该生成相同签名', () => {
      const requestData = 'consistent_test_data';
      const timestampNonce = '1755239241:consistent_nonce';
      
      const result1 = emulator.getSign(requestData, timestampNonce);
      const result2 = emulator.getSign(requestData, timestampNonce);
      
      expect(result1.signature).toBe(result2.signature);
    });

    it('不同输入应该生成不同签名', () => {
      const result1 = emulator.getSign('data1', '1755239241:nonce1');
      const result2 = emulator.getSign('data2', '1755239241:nonce2');
      
      expect(result1.signature).not.toBe(result2.signature);
    });

    it('应该处理空字符串输入', () => {
      const result = emulator.getSign('', '1755239241:empty_test');
      
      expect(result.signature).toMatch(/^[A-F0-9]{32}$/);
    });

    it('应该处理数字时间戳字符串', () => {
      const result = emulator.getSign('test', '1755239241');
      
      expect(result.signature).toMatch(/^[A-F0-9]{32}$/);
      expect(result.timestamp).toBe(1755239241);
    });

    it('应该处理毫秒级时间戳', () => {
      const result = emulator.getSign('test', '1755239241000');
      
      expect(result.signature).toMatch(/^[A-F0-9]{32}$/);
      expect(result.timestamp).toBe(1755239241);
    });
  });

  describe('不同策略测试', () => {
    it('标准策略应该生成有效签名', () => {
      const standardEmulator = new WasmSignatureEmulator({
        strategy: 'standard'
      });
      
      const result = standardEmulator.getSign('test', '1755239241:nonce');
      
      expect(result.signature).toMatch(/^[A-F0-9]{32}$/);
      expect(result.strategy).toBe('standard');
    });

    it('增强策略应该生成有效签名', () => {
      const enhancedEmulator = new WasmSignatureEmulator({
        strategy: 'enhanced'
      });
      
      const result = enhancedEmulator.getSign('test', '1755239241:nonce');
      
      expect(result.signature).toMatch(/^[A-F0-9]{32}$/);
      expect(result.strategy).toBe('enhanced');
    });

    it('不同策略应该生成不同签名', () => {
      const standardEmulator = new WasmSignatureEmulator({ strategy: 'standard' });
      const enhancedEmulator = new WasmSignatureEmulator({ strategy: 'enhanced' });
      const hybridEmulator = new WasmSignatureEmulator({ strategy: 'hybrid' });
      
      const testData = 'strategy_test_data';
      const testNonce = '1755239241:strategy_nonce';
      
      const standardResult = standardEmulator.getSign(testData, testNonce);
      const enhancedResult = enhancedEmulator.getSign(testData, testNonce);
      const hybridResult = hybridEmulator.getSign(testData, testNonce);
      
      expect(standardResult.signature).not.toBe(enhancedResult.signature);
      expect(enhancedResult.signature).not.toBe(hybridResult.signature);
      expect(standardResult.signature).not.toBe(hybridResult.signature);
    });
  });

  describe('verifySignature方法', () => {
    it('应该验证有效签名', () => {
      const requestData = 'verify_test_data';
      const timestamp = 1755239241;
      
      const result = emulator.getSign(requestData, timestamp.toString());
      const isValid = emulator.verifySignature(result.signature, requestData, timestamp);
      
      expect(isValid).toBe(true);
    });

    it('应该拒绝无效签名', () => {
      const isValid = emulator.verifySignature(
        'INVALID_SIGNATURE_123456789',
        'test_data',
        1755239241
      );
      
      expect(isValid).toBe(false);
    });
  });

  describe('调试模式', () => {
    it('调试模式应该返回调试信息', () => {
      const debugEmulator = new WasmSignatureEmulator({
        debug: true,
        strategy: 'hybrid'
      });
      
      const result = debugEmulator.getSign('debug_test', '1755239241:debug_nonce');
      
      expect(result.debug).toBeDefined();
      expect(result.debug).toHaveProperty('normalizedData');
      expect(result.debug).toHaveProperty('signString');
      expect(result.debug).toHaveProperty('algorithm');
    });

    it('非调试模式不应该返回调试信息', () => {
      const result = emulator.getSign('no_debug_test', '1755239241:no_debug_nonce');
      
      expect(result.debug).toBeUndefined();
    });
  });

  describe('错误处理', () => {
    it('应该处理无效的时间戳格式', () => {
      expect(() => {
        emulator.getSign('test', 'invalid_timestamp:nonce');
      }).not.toThrow();
    });

    it('应该处理空的nonce', () => {
      expect(() => {
        emulator.getSign('test', '1755239241:');
      }).not.toThrow();
    });
  });
});

describe('SignatureV2Utils集成测试', () => {
  describe('generateV2Signature', () => {
    it('应该为v2接口生成签名', () => {
      const params: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'v2_test_nonce',
        deviceId: 'v2_test_device',
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        data: { test: 'v2 data' }
      };
      
      const signature = SignatureV2Utils.generateV2Signature(params);
      
      expect(signature).toMatch(/^[A-F0-9]{32}$/);
    });

    it('应该为不同的v2请求生成不同签名', () => {
      const params1: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'nonce1',
        deviceId: 'device1',
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        data: { message: 'hello1' }
      };
      
      const params2: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'nonce2',
        deviceId: 'device1',
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        data: { message: 'hello2' }
      };
      
      const signature1 = SignatureV2Utils.generateV2Signature(params1);
      const signature2 = SignatureV2Utils.generateV2Signature(params2);
      
      expect(signature1).not.toBe(signature2);
    });

    it('应该处理GET请求', () => {
      const params: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'get_test_nonce',
        deviceId: 'get_test_device',
        method: 'GET',
        url: '/api/test?param1=value1&param2=value2'
      };
      
      const signature = SignatureV2Utils.generateV2Signature(params);
      
      expect(signature).toMatch(/^[A-F0-9]{32}$/);
    });

    it('应该处理带有额外配置的请求', () => {
      const params: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'extra_config_nonce',
        deviceId: 'extra_config_device',
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        data: { test: 'extra config' }
      };
      
      const extra = {
        appVersion: '1.1.18',
        clientVersion: '1.0.2',
        lang: 'zh'
      };
      
      const signature = SignatureV2Utils.generateV2Signature(params, extra);
      
      expect(signature).toMatch(/^[A-F0-9]{32}$/);
    });
  });

  describe('isV2Interface', () => {
    it('应该正确识别v2接口', () => {
      expect(SignatureV2Utils.isV2Interface('/ai-search/chatApi/v2/chat')).toBe(true);
      expect(SignatureV2Utils.isV2Interface('/api/v2/test')).toBe(true);
      expect(SignatureV2Utils.isV2Interface('/chatApi')).toBe(true);
      expect(SignatureV2Utils.isV2Interface('/chat')).toBe(true);
    });

    it('应该正确识别非v2接口', () => {
      expect(SignatureV2Utils.isV2Interface('/api/v1/test')).toBe(false);
      expect(SignatureV2Utils.isV2Interface('/api/test')).toBe(false);
      expect(SignatureV2Utils.isV2Interface('/v1/chat')).toBe(false);
    });
  });

  describe('generateDebugInfo', () => {
    it('应该生成完整的调试信息', () => {
      const params: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'debug_info_nonce',
        deviceId: 'debug_info_device',
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        data: { test: 'debug info' }
      };
      
      const debugInfo = SignatureV2Utils.generateDebugInfo(params);
      
      expect(debugInfo).toHaveProperty('url');
      expect(debugInfo).toHaveProperty('isV2');
      expect(debugInfo).toHaveProperty('strategies');
      
      expect(debugInfo.url).toBe('/ai-search/chatApi/v2/chat');
      expect(debugInfo.isV2).toBe(true);
      expect(Array.isArray(debugInfo.strategies)).toBe(true);
      expect(debugInfo.strategies.length).toBeGreaterThan(0);
      
      // 检查策略信息
      debugInfo.strategies.forEach(strategy => {
        expect(strategy).toHaveProperty('name');
        expect(strategy).toHaveProperty('signature');
        expect(strategy).toHaveProperty('description');
        expect(strategy.signature).toMatch(/^[A-F0-9]{32}$/);
      });
    });
  });
});

describe('性能测试', () => {
  it('签名生成应该在合理时间内完成', () => {
    const emulator = new WasmSignatureEmulator({
      debug: false,
      strategy: 'hybrid'
    });
    
    const startTime = Date.now();
    
    for (let i = 0; i < 100; i++) {
      emulator.getSign(`test_data_${i}`, `1755239241:nonce_${i}`);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    const avgTime = duration / 100;
    
    // 平均每次签名生成应该在10ms以内
    expect(avgTime).toBeLessThan(10);
  });

  it('大量数据签名应该保持性能', () => {
    const emulator = new WasmSignatureEmulator({
      debug: false,
      strategy: 'standard' // 使用最快的策略
    });
    
    const largeData = 'x'.repeat(10000); // 10KB数据
    
    const startTime = Date.now();
    emulator.getSign(largeData, '1755239241:large_data_nonce');
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    
    // 大数据签名应该在50ms以内完成
    expect(duration).toBeLessThan(50);
  });
});
