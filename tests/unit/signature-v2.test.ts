/**
 * v2接口签名算法单元测试
 * 测试基于 WASM 的签名生成逻辑
 */

import { SignatureV2Utils } from '../../src/utils/signature-v2';
import { SignatureParams } from '../../src/types';

describe('SignatureV2Utils', () => {
  describe('isV2Interface', () => {
    it('应该正确识别v2接口URL', () => {
      const v2Urls = [
        '/ai-search/chatApi/v2/chat',
        'https://ai.dangbei.com/ai-search/chatApi/v2/chat',
        '/api/v2/search',
        '/chatApi/stream',
        '/chat/message'
      ];

      v2Urls.forEach(url => {
        expect(SignatureV2Utils.isV2Interface(url)).toBe(true);
      });
    });

    it('应该正确排除非v2接口URL', () => {
      const nonV2Urls = [
        '/api/v1/search',
        '/v1/chat/message',
        '/api/user/profile',
        '/static/assets/image.png'
      ];

      nonV2Urls.forEach(url => {
        expect(SignatureV2Utils.isV2Interface(url)).toBe(false);
      });
    });
  });

  describe('generateV2Signature', () => {
    it('应该生成正确格式的v2签名', () => {
      const params: SignatureParams = {
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        bodyRaw: '{"message":"hello world"}',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature = SignatureV2Utils.generateV2Signature(params);

      // 验证签名格式：32位大写十六进制字符串
      expect(signature).toMatch(/^[A-F0-9]{32}$/);
      expect(signature.length).toBe(32);
    });

    it('相同参数应该生成相同的v2签名', () => {
      const params: SignatureParams = {
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        bodyRaw: '{"message":"test"}',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature1 = SignatureV2Utils.generateV2Signature(params);
      const signature2 = SignatureV2Utils.generateV2Signature(params);

      expect(signature1).toBe(signature2);
    });

    it('不同的请求数据应该生成不同的签名', () => {
      const baseParams = {
        method: 'POST' as const,
        url: '/ai-search/chatApi/v2/chat',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const params1: SignatureParams = {
        ...baseParams,
        bodyRaw: '{"message":"hello"}'
      };

      const params2: SignatureParams = {
        ...baseParams,
        bodyRaw: '{"message":"world"}'
      };

      const signature1 = SignatureV2Utils.generateV2Signature(params1);
      const signature2 = SignatureV2Utils.generateV2Signature(params2);

      expect(signature1).not.toBe(signature2);
    });

    it('不同的URL路径应该生成不同的签名', () => {
      const baseParams = {
        method: 'POST' as const,
        bodyRaw: '{"message":"test"}',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      // 使用更明显不同的路径来确保测试的可靠性
      const params1: SignatureParams = {
        ...baseParams,
        url: '/completely/different/path'
      };

      const params2: SignatureParams = {
        ...baseParams,
        url: '/ai-search/chatApi/v2/search'
      };

      const signature1 = SignatureV2Utils.generateV2Signature(params1);
      const signature2 = SignatureV2Utils.generateV2Signature(params2);

      // 验证签名确实不同
      expect(signature1).not.toBe(signature2);

      // 验证两个签名都是有效格式
      expect(signature1).toMatch(/^[A-F0-9]{32}$/);
      expect(signature2).toMatch(/^[A-F0-9]{32}$/);
    });

    it('应该正确处理GET请求的查询参数', () => {
      const params: SignatureParams = {
        method: 'GET',
        url: '/api/v2/search?q=test&limit=10',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature = SignatureV2Utils.generateV2Signature(params);

      expect(signature).toMatch(/^[A-F0-9]{32}$/);
    });

    it('应该正确处理完整URL', () => {
      const params: SignatureParams = {
        method: 'POST',
        url: 'https://ai.dangbei.com/ai-search/chatApi/v2/chat',
        bodyRaw: '{"message":"test"}',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature = SignatureV2Utils.generateV2Signature(params);

      expect(signature).toMatch(/^[A-F0-9]{32}$/);
    });
  });

  describe('generateDebugInfo', () => {
    it('应该生成完整的调试信息', () => {
      const params: SignatureParams = {
        method: 'POST',
        url: '/ai-search/chatApi/v2/chat',
        bodyRaw: '{"message":"test"}',
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const debugInfo = SignatureV2Utils.generateDebugInfo(params);

      expect(debugInfo.url).toBe(params.url);
      expect(debugInfo.isV2).toBe(true);
      expect(debugInfo.strategies).toHaveLength(4);
      
      // 验证所有策略都生成了签名
      debugInfo.strategies.forEach(strategy => {
        expect(strategy.signature).toMatch(/^[A-F0-9]{32}$/);
        expect(strategy.name).toBeTruthy();
        expect(strategy.description).toBeTruthy();
      });
    });
  });

  describe('getV2ErrorSuggestions', () => {
    it('应该为聊天接口提供特定建议', () => {
      const suggestions = SignatureV2Utils.getV2ErrorSuggestions('/chatApi/v2/chat');
      
      expect(suggestions.length).toBeGreaterThan(4);
      expect(suggestions.some(s => s.includes('聊天接口'))).toBe(true);
    });

    it('应该提供通用的错误处理建议', () => {
      const suggestions = SignatureV2Utils.getV2ErrorSuggestions('/api/v2/search');
      
      expect(suggestions.length).toBeGreaterThan(3);
      expect(suggestions.some(s => s.includes('签名算法'))).toBe(true);
    });
  });
});

describe('SignatureV2Utils - 边界情况测试', () => {
  it('应该处理空的请求体', () => {
    const params: SignatureParams = {
      method: 'POST',
      url: '/api/v2/test',
      bodyRaw: '',
      timestamp: 1755239240,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    const signature = SignatureV2Utils.generateV2Signature(params);
    expect(signature).toMatch(/^[A-F0-9]{32}$/);
  });

  it('应该处理包含特殊字符的URL', () => {
    const params: SignatureParams = {
      method: 'GET',
      url: '/api/v2/search?q=测试&category=AI&limit=10',
      timestamp: 1755239240,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    const signature = SignatureV2Utils.generateV2Signature(params);
    expect(signature).toMatch(/^[A-F0-9]{32}$/);
  });

  it('应该处理包含中文的请求体', () => {
    const params: SignatureParams = {
      method: 'POST',
      url: '/api/v2/chat',
      bodyRaw: '{"message":"你好，世界！","language":"zh-CN"}',
      timestamp: 1755239240,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    const signature = SignatureV2Utils.generateV2Signature(params);
    expect(signature).toMatch(/^[A-F0-9]{32}$/);
  });

  it('应该正确处理ASCII和UTF-8混合字符串', () => {
    // 测试纯 ASCII 字符串
    const asciiParams: SignatureParams = {
      method: 'POST',
      url: '/api/test',
      bodyRaw: 'hello world',
      timestamp: 1755239240,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    // 测试包含中文的字符串
    const mixedParams: SignatureParams = {
      method: 'POST',
      url: '/api/测试',
      bodyRaw: '{"message":"你好世界"}',
      timestamp: 1755239240,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    const asciiSignature = SignatureV2Utils.generateV2Signature(asciiParams);
    const mixedSignature = SignatureV2Utils.generateV2Signature(mixedParams);

    // 验证两个签名都是有效格式
    expect(asciiSignature).toMatch(/^[A-F0-9]{32}$/);
    expect(mixedSignature).toMatch(/^[A-F0-9]{32}$/);

    // 验证不同的输入产生不同的签名
    expect(asciiSignature).not.toBe(mixedSignature);
  });

  it('应该正确处理长JSON字符串', () => {
    // 模拟真实的聊天接口请求
    const longJsonParams: SignatureParams = {
      method: 'POST',
      url: '/chatApi/v2/chat',
      bodyRaw: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"364359551948554629","question":"怎么实现一个不依赖大模型特有能力的 agent, 也就是可以支持各种大模型","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"364359552203755717","chatId":"364359552203755717","files":[],"reference":[],"role":"user","status":"local","content":"怎么实现一个不依赖大模型特有能力的 agent, 也就是可以支持各种大模型","userAction":"deep","agentId":""}',
      timestamp: 1755239240,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    const signature = SignatureV2Utils.generateV2Signature(longJsonParams);

    // 验证签名格式
    expect(signature).toMatch(/^[A-F0-9]{32}$/);

    // 验证请求体长度（应该包含中文字符，UTF-8编码后会更长）
    const bodyRaw = longJsonParams.bodyRaw || '';
    const bodyLength = Buffer.byteLength(bodyRaw, 'utf8');
    expect(bodyLength).toBeGreaterThan(bodyRaw.length); // UTF-8编码后应该更长

    // 验证URL路径长度
    const url = longJsonParams.url || '';
    expect(url.length).toBe(16); // "/chatApi/v2/chat"
  });

  it('应该返回完整的签名结果对象', () => {
    const params: SignatureParams = {
      method: 'POST',
      url: '/ai-search/chatApi/v2/chat',
      bodyRaw: '{"test":"data","message":"hello"}',
      timestamp: 1755878885,
      nonce: 'testNonce123',
      deviceId: 'testDevice123'
    };

    const result = SignatureV2Utils.generateV2SignatureComplete(params);

    // 验证返回对象的结构
    expect(result).toHaveProperty('nonce');
    expect(result).toHaveProperty('sign');
    expect(result).toHaveProperty('timestamp');

    // 验证字段类型
    expect(typeof result.nonce).toBe('string');
    expect(typeof result.sign).toBe('string');
    expect(typeof result.timestamp).toBe('number');

    // 验证签名格式
    expect(result.sign).toMatch(/^[A-F0-9]{32}$/);

    // 验证nonce格式（通常是21位字符）
    expect(result.nonce.length).toBeGreaterThan(0);

    // 验证timestamp是合理的时间戳
    expect(result.timestamp).toBeGreaterThan(1000000000); // 大于2001年
  });
});
