/**
 * 工具类单元测试
 * 测试签名生成、设备ID生成等工具函数
 */

import { SignatureUtils, DeviceUtils } from '../../src/utils';
import { SignatureParams } from '../../src/types';

describe('SignatureUtils', () => {
  describe('generateSignature', () => {
    it('应该生成正确格式的MD5签名', () => {
      const params: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature = SignatureUtils.generateSignature(params);

      // 验证签名格式：32位大写十六进制字符串
      expect(signature).toMatch(/^[A-F0-9]{32}$/);
      expect(signature.length).toBe(32);
    });

    it('相同参数应该生成相同的签名', () => {
      const params: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature1 = SignatureUtils.generateSignature(params);
      const signature2 = SignatureUtils.generateSignature(params);

      expect(signature1).toBe(signature2);
    });

    it('不同参数应该生成不同的签名', () => {
      const params1: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const params2: SignatureParams = {
        timestamp: 1755239241,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature1 = SignatureUtils.generateSignature(params1);
      const signature2 = SignatureUtils.generateSignature(params2);

      expect(signature1).not.toBe(signature2);
    });

    it('应该正确处理包含数据的签名', () => {
      const params: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123',
        data: { test: 'value' }
      };

      const signature = SignatureUtils.generateSignature(params);

      expect(signature).toMatch(/^[A-F0-9]{32}$/);
    });
  });

    it('加入设备/应用字段应影响签名（排序后参与）', () => {
      // 基础参数
      const params: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'abcNonce-123',
        deviceId: 'device-id-xyz'
      };

      // 不带额外字段的签名
      const sig1 = SignatureUtils.generateSignature(params);

      // 加入 appType/appVersion/client-ver/lang 后的签名（应不同）
      const sig2 = SignatureUtils.generateSignature(params, {
        appType: 6,
        appVersion: '1.1.17-22',
        clientVersion: '1.0.2',
        lang: 'zh'
      });

      expect(sig1).not.toBe(sig2);
    });


  describe('generateNonce', () => {
    it('应该生成指定长度的nonce', () => {
      const nonce = SignatureUtils.generateNonce(21);
      expect(nonce.length).toBe(21);
    });

    it('应该生成包含有效字符的nonce', () => {
      const nonce = SignatureUtils.generateNonce(50);
      expect(nonce).toMatch(/^[A-Za-z0-9\-_]+$/);
    });

    it('每次生成的nonce应该不同', () => {
      const nonce1 = SignatureUtils.generateNonce();
      const nonce2 = SignatureUtils.generateNonce();
      expect(nonce1).not.toBe(nonce2);
    });
  });

  describe('getTimestamp', () => {
    it('应该返回合理的时间戳', () => {
      const timestamp = SignatureUtils.getTimestamp();
      const now = Math.floor(Date.now() / 1000);

      // 时间戳应该在当前时间附近（允许1秒误差）
      expect(Math.abs(timestamp - now)).toBeLessThanOrEqual(1);
    });
  });

  describe('verifySignature', () => {
    it('应该正确验证有效签名', () => {
      const params: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const signature = SignatureUtils.generateSignature(params);
      const isValid = SignatureUtils.verifySignature(signature, params);

      expect(isValid).toBe(true);
    });

    it('应该拒绝无效签名', () => {
      const params: SignatureParams = {
        timestamp: 1755239240,
        nonce: 'testNonce123',
        deviceId: 'testDevice123'
      };

      const isValid = SignatureUtils.verifySignature('INVALID_SIGNATURE', params);

      expect(isValid).toBe(false);
    });
  });
});

describe('DeviceUtils', () => {
  describe('generateDeviceId', () => {
    it('应该生成正确格式的设备ID', () => {
      const deviceId = DeviceUtils.generateDeviceId();

      // 验证格式：hash_suffix
      expect(deviceId).toMatch(/^[a-f0-9]{32}_[A-Za-z0-9]+$/);
    });

    it('相同种子应该生成相同的设备ID', () => {
      const seed = 'testSeed123';
      const deviceId1 = DeviceUtils.generateDeviceId(seed);
      const deviceId2 = DeviceUtils.generateDeviceId(seed);

      // 哈希部分应该相同，但随机后缀可能不同
      const [hash1] = deviceId1.split('_');
      const [hash2] = deviceId2.split('_');

      expect(hash1).toBe(hash2);
    });
  });

  describe('isValidDeviceId', () => {
    it('应该验证有效的设备ID', () => {
      const validDeviceId = 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm';
      expect(DeviceUtils.isValidDeviceId(validDeviceId)).toBe(true);
    });

    it('应该拒绝无效的设备ID', () => {
      const invalidIds = [
        'invalid',
        'eb845b952111b886e87bb092b2f718b8',
        '_3moaojk7xKMmLdud9MBm',
        'eb845b952111b886e87bb092b2f718b8_',
        'INVALID_HASH_3moaojk7xKMmLdud9MBm'
      ];

      invalidIds.forEach(id => {
        expect(DeviceUtils.isValidDeviceId(id)).toBe(false);
      });
    });
  });

  describe('createDefaultDeviceConfig', () => {
    it('应该创建包含所有必需字段的默认配置', () => {
      const config = DeviceUtils.createDefaultDeviceConfig();

      expect(config).toHaveProperty('deviceId');
      expect(config).toHaveProperty('appType', 6);
      expect(config).toHaveProperty('appVersion', '1.1.17-22');
      expect(config).toHaveProperty('clientVersion', '1.0.2');
      expect(config).toHaveProperty('lang', 'zh');
      expect(config).toHaveProperty('userAgent');

      expect(DeviceUtils.isValidDeviceId(config.deviceId)).toBe(true);
    });

    it('应该使用提供的设备ID', () => {
      const customDeviceId = 'eb845b952111b886e87bb092b2f718b8_customSuffix';
      const config = DeviceUtils.createDefaultDeviceConfig(customDeviceId);

      expect(config.deviceId).toBe(customDeviceId);
    });
  });

  describe('getStandardHeaders', () => {
    it('应该生成包含所有标准请求头的对象', () => {
      const deviceConfig = DeviceUtils.createDefaultDeviceConfig();
      const headers = DeviceUtils.getStandardHeaders(deviceConfig);

      // 验证关键请求头
      expect(headers).toHaveProperty('Accept', '*/*');
      expect(headers).toHaveProperty('content-type', 'application/json');
      expect(headers).toHaveProperty('deviceId', deviceConfig.deviceId);
      expect(headers).toHaveProperty('appType', '6');
      expect(headers).toHaveProperty('lang', 'zh');
    });

    it('应该合并额外的请求头', () => {
      const deviceConfig = DeviceUtils.createDefaultDeviceConfig();
      const additionalHeaders = {
        'Custom-Header': 'custom-value',
        'timestamp': '1755239240'
      };

      const headers = DeviceUtils.getStandardHeaders(deviceConfig, additionalHeaders);

      expect(headers).toHaveProperty('Custom-Header', 'custom-value');
      expect(headers).toHaveProperty('timestamp', '1755239240');
      expect(headers).toHaveProperty('deviceId', deviceConfig.deviceId);
    });
  });
});
