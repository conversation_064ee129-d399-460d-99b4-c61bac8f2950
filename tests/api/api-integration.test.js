#!/usr/bin/env node

/**
 * API集成测试
 * 合并所有API相关测试，提供完整的接口验证
 * 
 * 合并来源:
 * - test-api-direct.js - 直接API调用测试
 * - test-http-api.js - HTTP接口测试
 * - test-models.js - 模型功能测试
 * - test-text-generation.js - 文本生成测试
 * 
 * <AUTHOR> Provider SDK
 * @version 2.0.0
 */

const axios = require('axios');
const { DangbeiProvider } = require('../../dist');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置
const CONFIG = {
  LOCAL_API_URL: 'http://localhost:3000',
  REMOTE_API_URL: 'https://ai-api.dangbei.net',
  TIMEOUT: 10000,
  TEST_MODELS: [
    'deepseek-r1',
    'doubao-1_6-thinking', 
    'glm-4-5',
    'kimi-k2-0711-preview'
  ]
};

/**
 * 健康检查测试
 */
async function testHealthCheck() {
  colorLog('cyan', '\n=== 健康检查测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试本地API健康检查
  totalTests++;
  colorLog('blue', '\n测试: 本地API健康检查');
  
  try {
    const response = await axios.get(`${CONFIG.LOCAL_API_URL}/health`, {
      timeout: CONFIG.TIMEOUT
    });
    
    if (response.status === 200 && response.data.success) {
      colorLog('green', '✅ 本地API健康检查成功');
      console.log(`状态: ${response.data.data.status}`);
      console.log(`运行时间: ${response.data.data.uptime}s`);
      passedTests++;
    } else {
      colorLog('red', '❌ 本地API健康检查失败');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 本地API不可用: ${error.message}`);
  }
  
  // 测试API健康检查接口格式
  totalTests++;
  colorLog('blue', '\n测试: API响应格式验证');
  
  try {
    const response = await axios.get(`${CONFIG.LOCAL_API_URL}/health`);
    const data = response.data;
    
    const hasRequiredFields = data.success !== undefined && 
                             data.data !== undefined &&
                             data.requestId !== undefined &&
                             data.timestamp !== undefined;
    
    if (hasRequiredFields) {
      colorLog('green', '✅ API响应格式正确');
      passedTests++;
    } else {
      colorLog('red', '❌ API响应格式不完整');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 无法验证响应格式: ${error.message}`);
  }
  
  colorLog('yellow', `\n健康检查测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 模型列表测试
 */
async function testModelsList() {
  colorLog('cyan', '\n=== 模型列表测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试模型列表接口
  totalTests++;
  colorLog('blue', '\n测试: 模型列表接口');
  
  try {
    const response = await axios.get(`${CONFIG.LOCAL_API_URL}/api/models`, {
      timeout: CONFIG.TIMEOUT
    });
    
    if (response.status === 200 && response.data.success) {
      const models = response.data.data.models;
      colorLog('green', `✅ 模型列表获取成功，共 ${models.length} 个模型`);
      
      // 显示前几个模型
      models.slice(0, 3).forEach(model => {
        console.log(`  - ${model.name} (${model.id})`);
      });
      
      passedTests++;
    } else {
      colorLog('red', '❌ 模型列表获取失败');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 模型列表接口不可用: ${error.message}`);
  }
  
  // 测试模型数据结构
  totalTests++;
  colorLog('blue', '\n测试: 模型数据结构验证');
  
  try {
    const response = await axios.get(`${CONFIG.LOCAL_API_URL}/api/models`);
    const models = response.data.data.models;
    
    if (models && models.length > 0) {
      const firstModel = models[0];
      const hasRequiredFields = firstModel.id && firstModel.name;
      
      if (hasRequiredFields) {
        colorLog('green', '✅ 模型数据结构正确');
        passedTests++;
      } else {
        colorLog('red', '❌ 模型数据结构不完整');
      }
    } else {
      colorLog('red', '❌ 模型列表为空');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 无法验证模型数据结构: ${error.message}`);
  }
  
  colorLog('yellow', `\n模型列表测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 聊天对话测试
 */
async function testChatAPI() {
  colorLog('cyan', '\n=== 聊天对话测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试基础聊天接口
  totalTests++;
  colorLog('blue', '\n测试: 基础聊天接口');
  
  try {
    const chatRequest = {
      message: '你好，请简单介绍一下自己',
      model: 'deepseek-r1',
      stream: false
    };
    
    const response = await axios.post(`${CONFIG.LOCAL_API_URL}/api/chat`, chatRequest, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (response.status === 200 && response.data.success) {
      colorLog('green', '✅ 基础聊天接口测试成功');
      console.log(`回复长度: ${response.data.data.content.length} 字符`);
      passedTests++;
    } else {
      colorLog('red', '❌ 基础聊天接口测试失败');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 聊天接口不可用: ${error.message}`);
  }
  
  // 测试流式聊天接口
  totalTests++;
  colorLog('blue', '\n测试: 流式聊天接口');
  
  try {
    // 这里只测试接口可访问性，不测试实际流式响应
    const streamRequest = {
      message: '测试流式响应',
      model: 'deepseek-r1',
      stream: true
    };
    
    const response = await axios.post(`${CONFIG.LOCAL_API_URL}/api/chat`, streamRequest, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'stream'
    });
    
    if (response.status === 200) {
      colorLog('green', '✅ 流式聊天接口可访问');
      passedTests++;
    } else {
      colorLog('red', '❌ 流式聊天接口不可访问');
    }
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      colorLog('green', '✅ 流式聊天接口响应正常 (超时为预期行为)');
      passedTests++;
    } else {
      colorLog('yellow', `⚠️ 流式聊天接口测试异常: ${error.message}`);
    }
  }
  
  colorLog('yellow', `\n聊天对话测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 模型功能测试
 */
async function testModelFunctionality() {
  colorLog('cyan', '\n=== 模型功能测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const provider = new DangbeiProvider();
    
    // 测试Provider初始化
    totalTests++;
    colorLog('blue', '\n测试: Provider初始化');
    
    if (provider) {
      colorLog('green', '✅ Provider初始化成功');
      passedTests++;
    } else {
      colorLog('red', '❌ Provider初始化失败');
    }
    
    // 测试对话创建
    totalTests++;
    colorLog('blue', '\n测试: 对话创建');
    
    try {
      const conversationId = await provider.createConversation();
      if (conversationId) {
        colorLog('green', `✅ 对话创建成功: ${conversationId}`);
        passedTests++;
        
        // 测试简单聊天
        totalTests++;
        colorLog('blue', '\n测试: 简单聊天功能');
        
        try {
          const response = await provider.quickChat('你好', {
            conversationId,
            model: 'deepseek-r1'
          });
          
          if (response && response.length > 0) {
            colorLog('green', `✅ 简单聊天成功，回复长度: ${response.length} 字符`);
            passedTests++;
          } else {
            colorLog('red', '❌ 简单聊天失败，无回复内容');
          }
        } catch (error) {
          colorLog('yellow', `⚠️ 简单聊天测试异常: ${error.message}`);
        }
      } else {
        colorLog('red', '❌ 对话创建失败');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 对话创建测试异常: ${error.message}`);
    }
    
  } catch (error) {
    colorLog('red', `❌ Provider测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n模型功能测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 错误处理测试
 */
async function testErrorHandling() {
  colorLog('cyan', '\n=== 错误处理测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试无效模型
  totalTests++;
  colorLog('blue', '\n测试: 无效模型处理');
  
  try {
    const response = await axios.post(`${CONFIG.LOCAL_API_URL}/api/chat`, {
      message: '测试',
      model: 'invalid-model-name',
      stream: false
    }, {
      timeout: 10000,
      validateStatus: () => true // 允许所有状态码
    });
    
    if (response.status >= 400) {
      colorLog('green', '✅ 无效模型正确返回错误状态');
      passedTests++;
    } else {
      colorLog('yellow', '⚠️ 无效模型未返回错误状态');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 无效模型测试异常: ${error.message}`);
  }
  
  // 测试空消息
  totalTests++;
  colorLog('blue', '\n测试: 空消息处理');
  
  try {
    const response = await axios.post(`${CONFIG.LOCAL_API_URL}/api/chat`, {
      message: '',
      model: 'deepseek-r1',
      stream: false
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status >= 400 || (response.data && !response.data.success)) {
      colorLog('green', '✅ 空消息正确处理');
      passedTests++;
    } else {
      colorLog('yellow', '⚠️ 空消息处理可能需要改进');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 空消息测试异常: ${error.message}`);
  }
  
  // 测试超长消息
  totalTests++;
  colorLog('blue', '\n测试: 超长消息处理');
  
  try {
    const longMessage = 'x'.repeat(10000);
    const response = await axios.post(`${CONFIG.LOCAL_API_URL}/api/chat`, {
      message: longMessage,
      model: 'deepseek-r1',
      stream: false
    }, {
      timeout: 30000,
      validateStatus: () => true
    });
    
    if (response.status === 200 || response.status >= 400) {
      colorLog('green', '✅ 超长消息处理正常');
      passedTests++;
    } else {
      colorLog('yellow', '⚠️ 超长消息处理异常');
    }
  } catch (error) {
    colorLog('yellow', `⚠️ 超长消息测试异常: ${error.message}`);
  }
  
  colorLog('yellow', `\n错误处理测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 主测试函数
 */
async function runAllTests() {
  colorLog('magenta', '🚀 开始API集成测试');
  
  const results = [];
  
  // 运行所有测试
  results.push(await testHealthCheck());
  results.push(await testModelsList());
  results.push(await testChatAPI());
  results.push(await testModelFunctionality());
  results.push(await testErrorHandling());
  
  // 统计总结果
  const totalPassed = results.reduce((sum, result) => sum + result.passed, 0);
  const totalTests = results.reduce((sum, result) => sum + result.total, 0);
  const passRate = ((totalPassed / totalTests) * 100).toFixed(1);
  
  colorLog('magenta', '\n=== 测试总结 ===');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过测试: ${totalPassed}`);
  console.log(`失败测试: ${totalTests - totalPassed}`);
  console.log(`通过率: ${passRate}%`);
  
  if (totalPassed >= totalTests * 0.8) { // 80%通过率即可
    colorLog('green', '🎉 API集成测试通过！');
    process.exit(0);
  } else {
    colorLog('red', '❌ API集成测试失败');
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests().catch(error => {
    colorLog('red', `测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testHealthCheck,
  testModelsList,
  testChatAPI,
  testModelFunctionality,
  testErrorHandling,
  runAllTests
};
