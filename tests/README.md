# 当贝AI Provider 测试套件

## 📋 测试概述

本项目包含完整的测试套件，覆盖功能测试、集成测试、部署测试等多个方面，确保代码质量和部署可靠性。

## 🧪 测试分类

### 1. 单元测试 (`unit/`)
**位置**: `tests/unit/`
**功能**: 测试单个模块和函数的功能正确性

```bash
# 运行单元测试
npm test

# 监听模式运行
npm run test:watch
```

### 2. 集成测试 (`integration/`)
**位置**: `tests/integration/`
**功能**: 测试模块间的集成和API接口

```bash
# 运行集成测试
npm run test:integration

# 详细输出模式
npm run test:api:verbose
```

### 3. API测试 (`api/`)
**位置**: `tests/api/`
**功能**: 测试签名算法和API接口集成

```bash
# 运行签名算法测试
./tests/api/signature-comprehensive.test.js

# 运行API集成测试
./tests/api/api-integration.test.js
```

### 4. 功能测试 (`features/`)
**位置**: `tests/features/`
**功能**: 测试特定功能特性

```bash
# 运行三色数据功能测试
./tests/features/three-color-data.test.js
```

### 5. 工具测试 (`tools/`)
**位置**: `tests/tools/`
**功能**: 测试服务器工具和辅助功能

```bash
# 运行服务器工具测试
./tests/tools/server-tools.test.js
```

### 6. 部署测试 (`deployment/`)
**位置**: `tests/deployment/`
**功能**: 验证静态部署方案和配置文件

```bash
# 运行所有部署测试
./tests/deployment/run-all-tests.sh

# 仅测试静态部署
./tests/deployment/test-static-deployment.sh

# 仅测试配置文件
./tests/deployment/test-deployment-configs.sh
```

## 🚀 快速测试

### 使用综合测试运行器 (推荐)
```bash
# 运行所有测试套件
./tests/run-all-tests.js

# 仅运行必需的测试
./tests/run-all-tests.js --required-only

# 运行指定测试套件
./tests/run-all-tests.js --suite signature
./tests/run-all-tests.js --suite api
./tests/run-all-tests.js --suite deployment

# 跳过部署测试
./tests/run-all-tests.js --skip-deployment

# 详细输出模式
./tests/run-all-tests.js --verbose

# 显示帮助信息
./tests/run-all-tests.js --help
```

### 使用部署测试脚本
```bash
# 运行部署相关测试
./scripts/test-deployment.sh

# 仅运行静态部署测试
./scripts/test-deployment.sh --static

# 仅运行配置文件测试
./scripts/test-deployment.sh --config

# 快速测试 (跳过耗时测试)
./scripts/test-deployment.sh --quick

# 检查测试依赖
./scripts/test-deployment.sh --check

# 清理测试结果
./scripts/test-deployment.sh --clean
```

### 使用npm脚本
```bash
# 运行单元测试
npm test

# 运行集成测试
npm run test:integration

# 运行特定类型测试
npm run test:models          # 模型测试
npm run test:api            # API测试
npm run test:wasm           # WASM测试

# 运行示例
npm run example-basic       # 基础示例
npm run example-advanced    # 高级示例
```

## 📊 测试覆盖范围

### 功能测试覆盖
- ✅ **API接口测试** - 所有HTTP API端点
- ✅ **模型调用测试** - 多种AI模型调用
- ✅ **流式响应测试** - SSE流式输出
- ✅ **签名算法测试** - WASM和备用签名
- ✅ **错误处理测试** - 异常情况处理
- ✅ **性能测试** - 响应时间和吞吐量

### 部署测试覆盖
- ✅ **静态文件测试** - 文件结构和内容验证
- ✅ **HTTP服务测试** - Web服务器响应验证
- ✅ **配置文件测试** - Caddy和Nginx配置验证
- ✅ **安全配置测试** - SSL和安全头验证
- ✅ **性能配置测试** - 缓存和压缩配置
- ✅ **兼容性测试** - 浏览器和平台兼容性

### 集成测试覆盖
- ✅ **端到端测试** - 完整的用户流程
- ✅ **跨模块测试** - 模块间交互验证
- ✅ **数据流测试** - 数据传输和处理
- ✅ **并发测试** - 多用户并发访问
- ✅ **容错测试** - 故障恢复能力

## 🔧 测试环境

### 基础环境要求
- **Node.js**: 16.0+
- **npm**: 8.0+
- **操作系统**: Linux/macOS/Windows
- **内存**: 最少2GB可用内存

### 测试工具依赖
```bash
# 必需工具
curl                    # HTTP请求测试
python3                 # 本地HTTP服务器
bash                    # 脚本执行环境

# 可选工具 (增强测试功能)
node                    # JavaScript语法检查
bc                      # 数值计算
caddy                   # Caddy配置验证
nginx                   # Nginx配置验证
```

### 安装测试依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install curl python3 bc nodejs npm

# CentOS/RHEL
sudo yum install curl python3 bc nodejs npm

# macOS
brew install curl python3 bc node npm

# 安装项目依赖
npm install
```

## 📈 测试指标

### 性能基准
- **API响应时间**: < 2秒
- **页面加载时间**: < 2秒
- **文件大小限制**: HTML < 100KB, CSS < 200KB
- **并发支持**: 100+ 并发用户

### 质量指标
- **代码覆盖率**: > 80%
- **测试通过率**: 100%
- **配置验证**: 57项测试
- **功能验证**: 35项测试

### 兼容性支持
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Node.js**: 16.0+
- **操作系统**: Linux, macOS, Windows
- **Web服务器**: Caddy 2.0+, Nginx 1.18+

## 🔍 测试结果

### 查看测试结果
```bash
# 查看最新测试报告
cat test-results/test-report.md

# 查看详细日志
tail -f test-results/test-static-deployment.log
tail -f test-results/test-deployment-configs.log

# 查看测试统计
ls -la test-results/
```

### 测试结果解读
- **✅ 通过**: 测试成功，功能正常
- **❌ 失败**: 测试失败，需要修复
- **⚠️ 警告**: 非关键问题，建议关注
- **ℹ️ 信息**: 提示信息，仅供参考

## 🐛 故障排除

### 常见测试问题

1. **端口占用问题**
   ```bash
   # 检查端口占用
   lsof -i :8888
   
   # 停止占用进程
   pkill -f "python.*8888"
   ```

2. **权限问题**
   ```bash
   # 设置脚本权限
   chmod +x tests/deployment/*.sh
   chmod +x scripts/*.sh
   ```

3. **依赖缺失**
   ```bash
   # 检查依赖
   ./scripts/test-deployment.sh --check
   
   # 安装缺失依赖
   sudo apt install curl python3 bc
   ```

4. **网络问题**
   ```bash
   # 跳过CDN测试
   # CDN测试失败是正常的，不影响核心功能
   ```

### 测试失败处理

1. **查看详细日志**
   ```bash
   cat test-results/*.log | grep -A 5 -B 5 "ERROR\|FAIL"
   ```

2. **重新运行失败的测试**
   ```bash
   ./scripts/test-deployment.sh --static  # 重新运行静态测试
   ./scripts/test-deployment.sh --config  # 重新运行配置测试
   ```

3. **清理并重新测试**
   ```bash
   ./scripts/test-deployment.sh --clean
   ./scripts/test-deployment.sh --all
   ```

## 📞 获取帮助

- **测试文档**: `tests/deployment/README.md`
- **部署文档**: `deployment/README.md`
- **API文档**: `docs/api.md`
- **问题反馈**: 项目Issues页面

## 🎯 测试最佳实践

### 开发流程
1. **开发前**: 运行现有测试确保基线正常
2. **开发中**: 编写对应的单元测试
3. **开发后**: 运行完整测试套件验证
4. **提交前**: 确保所有测试通过

### 测试策略
- **频繁运行**: 开发过程中频繁运行相关测试
- **完整验证**: 重要变更前运行完整测试套件
- **持续集成**: 配置CI/CD自动运行测试
- **性能监控**: 定期运行性能测试检查回归

### 质量保证
- **测试覆盖**: 新功能必须包含对应测试
- **文档同步**: 测试文档与代码保持同步
- **问题跟踪**: 及时修复测试发现的问题
- **持续改进**: 根据测试结果持续优化

---

**当贝AI Provider 测试套件** - 确保代码质量，保障部署可靠！
