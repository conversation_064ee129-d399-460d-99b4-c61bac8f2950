#!/usr/bin/env node

/**
 * 三色数据功能测试
 * 测试联网搜索、思考过程、回答内容的处理
 * 
 * 合并来源:
 * - test-three-color-data.js - 完整三色数据测试
 * - test-three-color-simple.js - 简化三色数据测试
 * 
 * 三色数据类型：
 * - 蓝色 (progress): 联网搜索进度信息
 * - 黄色 (thinking): AI思考过程
 * - 绿色 (text): 正式回答内容
 * - 主色调 (card): 搜索结果卡片
 * 
 * <AUTHOR> Provider SDK
 * @version 2.0.0
 */

const { DangbeiProvider } = require('../../dist');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试配置
const TEST_CONFIG = {
  models: {
    withThinking: 'doubao-1_6-thinking',  // 支持思考过程的模型
    withSearch: 'qwen-plus',              // 支持联网搜索的模型
    standard: 'deepseek-r1'               // 标准模型
  },
  questions: {
    thinking: '请详细分析一下人工智能的发展趋势',
    search: '今天的天气如何？',
    standard: '你好，请介绍一下自己'
  }
};

/**
 * 蓝色数据 (progress) 测试
 */
async function testProgressData() {
  colorLog('cyan', '\n=== 蓝色数据 (联网搜索进度) 测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const provider = new DangbeiProvider();
    const conversationId = await provider.createConversation();
    
    // 测试联网搜索功能
    totalTests++;
    colorLog('blue', '\n测试: 联网搜索进度数据');
    
    let progressDataReceived = false;
    let searchSteps = [];
    
    try {
      await provider.streamChat(TEST_CONFIG.questions.search, {
        conversationId,
        model: TEST_CONFIG.models.withSearch,
        enableSearch: true,
        onMessage: (data) => {
          if (data.type === 'progress') {
            progressDataReceived = true;
            searchSteps.push(data.content);
            colorLog('blue', `  📊 搜索进度: ${data.content}`);
          }
        }
      });
      
      if (progressDataReceived) {
        colorLog('green', `✅ 联网搜索进度数据接收成功，共 ${searchSteps.length} 个步骤`);
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 未接收到联网搜索进度数据');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 联网搜索测试异常: ${error.message}`);
    }
    
    // 测试进度数据格式
    totalTests++;
    colorLog('blue', '\n测试: 进度数据格式验证');
    
    if (searchSteps.length > 0) {
      const validSteps = searchSteps.filter(step => 
        typeof step === 'string' && step.length > 0
      );
      
      if (validSteps.length === searchSteps.length) {
        colorLog('green', '✅ 进度数据格式正确');
        passedTests++;
      } else {
        colorLog('red', '❌ 进度数据格式不正确');
      }
    } else {
      colorLog('yellow', '⚠️ 无进度数据可验证格式');
    }
    
  } catch (error) {
    colorLog('red', `❌ 蓝色数据测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n蓝色数据测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 黄色数据 (thinking) 测试
 */
async function testThinkingData() {
  colorLog('cyan', '\n=== 黄色数据 (AI思考过程) 测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const provider = new DangbeiProvider();
    const conversationId = await provider.createConversation();
    
    // 测试思考过程功能
    totalTests++;
    colorLog('blue', '\n测试: AI思考过程数据');
    
    let thinkingDataReceived = false;
    let thinkingSteps = [];
    
    try {
      await provider.streamChat(TEST_CONFIG.questions.thinking, {
        conversationId,
        model: TEST_CONFIG.models.withThinking,
        enableThinking: true,
        onMessage: (data) => {
          if (data.type === 'thinking') {
            thinkingDataReceived = true;
            thinkingSteps.push(data.content);
            colorLog('yellow', `  🤔 思考过程: ${data.content.substring(0, 50)}...`);
          }
        }
      });
      
      if (thinkingDataReceived) {
        colorLog('green', `✅ AI思考过程数据接收成功，共 ${thinkingSteps.length} 个步骤`);
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 未接收到AI思考过程数据');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 思考过程测试异常: ${error.message}`);
    }
    
    // 测试思考数据质量
    totalTests++;
    colorLog('blue', '\n测试: 思考数据质量验证');
    
    if (thinkingSteps.length > 0) {
      const meaningfulSteps = thinkingSteps.filter(step => 
        typeof step === 'string' && step.length > 10
      );
      
      if (meaningfulSteps.length >= thinkingSteps.length * 0.8) {
        colorLog('green', '✅ 思考数据质量良好');
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 思考数据质量可能需要改进');
      }
    } else {
      colorLog('yellow', '⚠️ 无思考数据可验证质量');
    }
    
  } catch (error) {
    colorLog('red', `❌ 黄色数据测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n黄色数据测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 绿色数据 (text) 测试
 */
async function testTextData() {
  colorLog('cyan', '\n=== 绿色数据 (正式回答内容) 测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const provider = new DangbeiProvider();
    const conversationId = await provider.createConversation();
    
    // 测试正式回答内容
    totalTests++;
    colorLog('blue', '\n测试: 正式回答内容数据');
    
    let textDataReceived = false;
    let textContent = '';
    
    try {
      await provider.streamChat(TEST_CONFIG.questions.standard, {
        conversationId,
        model: TEST_CONFIG.models.standard,
        onMessage: (data) => {
          if (data.type === 'text') {
            textDataReceived = true;
            textContent += data.content;
            colorLog('green', `  📝 回答内容: ${data.content.substring(0, 30)}...`);
          }
        }
      });
      
      if (textDataReceived && textContent.length > 0) {
        colorLog('green', `✅ 正式回答内容接收成功，总长度: ${textContent.length} 字符`);
        passedTests++;
      } else {
        colorLog('red', '❌ 未接收到正式回答内容');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 回答内容测试异常: ${error.message}`);
    }
    
    // 测试回答内容质量
    totalTests++;
    colorLog('blue', '\n测试: 回答内容质量验证');
    
    if (textContent.length > 0) {
      const hasValidContent = textContent.length > 20 && 
                             !textContent.includes('error') &&
                             !textContent.includes('Error');
      
      if (hasValidContent) {
        colorLog('green', '✅ 回答内容质量良好');
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 回答内容质量可能需要改进');
      }
    } else {
      colorLog('red', '❌ 无回答内容可验证质量');
    }
    
  } catch (error) {
    colorLog('red', `❌ 绿色数据测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n绿色数据测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 搜索结果卡片测试
 */
async function testSearchCardData() {
  colorLog('cyan', '\n=== 搜索结果卡片测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const provider = new DangbeiProvider();
    const conversationId = await provider.createConversation();
    
    // 测试搜索结果卡片
    totalTests++;
    colorLog('blue', '\n测试: 搜索结果卡片数据');
    
    let cardDataReceived = false;
    let searchCards = [];
    
    try {
      await provider.streamChat('最新的人工智能新闻', {
        conversationId,
        model: TEST_CONFIG.models.withSearch,
        enableSearch: true,
        onMessage: (data) => {
          if (data.type === 'card') {
            cardDataReceived = true;
            searchCards.push(data.content);
            colorLog('magenta', `  🃏 搜索卡片: ${JSON.stringify(data.content).substring(0, 50)}...`);
          }
        }
      });
      
      if (cardDataReceived) {
        colorLog('green', `✅ 搜索结果卡片接收成功，共 ${searchCards.length} 个卡片`);
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 未接收到搜索结果卡片');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 搜索卡片测试异常: ${error.message}`);
    }
    
    // 测试卡片数据结构
    totalTests++;
    colorLog('blue', '\n测试: 卡片数据结构验证');
    
    if (searchCards.length > 0) {
      const validCards = searchCards.filter(card => 
        typeof card === 'object' && card !== null
      );
      
      if (validCards.length === searchCards.length) {
        colorLog('green', '✅ 卡片数据结构正确');
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 部分卡片数据结构不正确');
      }
    } else {
      colorLog('yellow', '⚠️ 无卡片数据可验证结构');
    }
    
  } catch (error) {
    colorLog('red', `❌ 搜索卡片测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n搜索卡片测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 数据流处理测试
 */
async function testDataStreamProcessing() {
  colorLog('cyan', '\n=== 数据流处理测试 ===');
  
  let passedTests = 0;
  let totalTests = 0;
  
  try {
    const provider = new DangbeiProvider();
    const conversationId = await provider.createConversation();
    
    // 测试数据流顺序
    totalTests++;
    colorLog('blue', '\n测试: 数据流顺序验证');
    
    let dataSequence = [];
    let streamCompleted = false;
    
    try {
      await provider.streamChat('请详细分析并搜索相关信息', {
        conversationId,
        model: TEST_CONFIG.models.withThinking,
        enableSearch: true,
        enableThinking: true,
        onMessage: (data) => {
          dataSequence.push({
            type: data.type,
            timestamp: Date.now(),
            hasContent: !!data.content
          });
        },
        onComplete: () => {
          streamCompleted = true;
        }
      });
      
      if (streamCompleted && dataSequence.length > 0) {
        colorLog('green', `✅ 数据流处理完成，共 ${dataSequence.length} 个数据包`);
        
        // 显示数据类型分布
        const typeCount = {};
        dataSequence.forEach(item => {
          typeCount[item.type] = (typeCount[item.type] || 0) + 1;
        });
        
        Object.entries(typeCount).forEach(([type, count]) => {
          console.log(`  ${type}: ${count} 个`);
        });
        
        passedTests++;
      } else {
        colorLog('yellow', '⚠️ 数据流处理未完成或无数据');
      }
    } catch (error) {
      colorLog('yellow', `⚠️ 数据流测试异常: ${error.message}`);
    }
    
    // 测试数据完整性
    totalTests++;
    colorLog('blue', '\n测试: 数据完整性验证');
    
    if (dataSequence.length > 0) {
      const validData = dataSequence.filter(item => 
        item.type && item.hasContent
      );
      
      const completeness = (validData.length / dataSequence.length) * 100;
      
      if (completeness >= 80) {
        colorLog('green', `✅ 数据完整性良好 (${completeness.toFixed(1)}%)`);
        passedTests++;
      } else {
        colorLog('yellow', `⚠️ 数据完整性需要改进 (${completeness.toFixed(1)}%)`);
      }
    } else {
      colorLog('yellow', '⚠️ 无数据可验证完整性');
    }
    
  } catch (error) {
    colorLog('red', `❌ 数据流处理测试失败: ${error.message}`);
  }
  
  colorLog('yellow', `\n数据流处理测试结果: ${passedTests}/${totalTests} 通过`);
  return { passed: passedTests, total: totalTests };
}

/**
 * 主测试函数
 */
async function runAllTests() {
  colorLog('magenta', '🚀 开始三色数据功能测试');
  
  const results = [];
  
  // 运行所有测试
  results.push(await testProgressData());
  results.push(await testThinkingData());
  results.push(await testTextData());
  results.push(await testSearchCardData());
  results.push(await testDataStreamProcessing());
  
  // 统计总结果
  const totalPassed = results.reduce((sum, result) => sum + result.passed, 0);
  const totalTests = results.reduce((sum, result) => sum + result.total, 0);
  const passRate = ((totalPassed / totalTests) * 100).toFixed(1);
  
  colorLog('magenta', '\n=== 测试总结 ===');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过测试: ${totalPassed}`);
  console.log(`失败测试: ${totalTests - totalPassed}`);
  console.log(`通过率: ${passRate}%`);
  
  if (totalPassed >= totalTests * 0.7) { // 70%通过率即可
    colorLog('green', '🎉 三色数据功能测试通过！');
    process.exit(0);
  } else {
    colorLog('red', '❌ 三色数据功能测试失败');
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests().catch(error => {
    colorLog('red', `测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testProgressData,
  testThinkingData,
  testTextData,
  testSearchCardData,
  testDataStreamProcessing,
  runAllTests
};
