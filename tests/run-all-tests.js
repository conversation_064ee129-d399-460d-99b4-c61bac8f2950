#!/usr/bin/env node

/**
 * 测试套件总运行器
 * 运行所有类型的测试并生成综合报告
 * 
 * <AUTHOR> Provider SDK
 * @version 2.0.0
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试配置
const TEST_SUITES = {
  unit: {
    name: '单元测试',
    command: 'npm',
    args: ['test'],
    timeout: 30000,
    required: true
  },
  signature: {
    name: '签名算法测试',
    command: 'node',
    args: ['tests/api/signature-comprehensive.test.js'],
    timeout: 60000,
    required: true
  },
  api: {
    name: 'API集成测试',
    command: 'node',
    args: ['tests/api/api-integration.test.js'],
    timeout: 120000,
    required: false // API服务器可能未启动
  },
  features: {
    name: '功能特性测试',
    command: 'node',
    args: ['tests/features/three-color-data.test.js'],
    timeout: 180000,
    required: false // 需要实际API调用
  },
  tools: {
    name: '工具函数测试',
    command: 'node',
    args: ['tests/tools/server-tools.test.js'],
    timeout: 90000,
    required: false // 服务器启动测试可能失败
  },
  deployment: {
    name: '部署配置测试',
    command: 'bash',
    args: ['tests/deployment/run-all-tests.sh'],
    timeout: 120000,
    required: true
  }
};

/**
 * 运行单个测试套件
 */
async function runTestSuite(suiteName, config) {
  colorLog('cyan', `\n=== 运行 ${config.name} ===`);
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const process = spawn(config.command, config.args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: path.join(__dirname, '..')
    });
    
    let stdout = '';
    let stderr = '';
    
    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    const timeout = setTimeout(() => {
      process.kill('SIGTERM');
      resolve({
        suite: suiteName,
        name: config.name,
        success: false,
        duration: Date.now() - startTime,
        error: '测试超时',
        stdout: stdout,
        stderr: stderr
      });
    }, config.timeout);
    
    process.on('close', (code) => {
      clearTimeout(timeout);
      const duration = Date.now() - startTime;
      
      const result = {
        suite: suiteName,
        name: config.name,
        success: code === 0,
        duration: duration,
        exitCode: code,
        stdout: stdout,
        stderr: stderr
      };
      
      if (code === 0) {
        colorLog('green', `✅ ${config.name} 通过 (${duration}ms)`);
      } else {
        colorLog('red', `❌ ${config.name} 失败 (退出码: ${code})`);
        if (stderr) {
          console.log('错误输出:', stderr.substring(0, 200));
        }
      }
      
      resolve(result);
    });
    
    process.on('error', (error) => {
      clearTimeout(timeout);
      resolve({
        suite: suiteName,
        name: config.name,
        success: false,
        duration: Date.now() - startTime,
        error: error.message,
        stdout: stdout,
        stderr: stderr
      });
    });
  });
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
  const timestamp = new Date().toISOString();
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  const passRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  const report = `# 当贝AI Provider SDK 测试报告

**生成时间**: ${timestamp}  
**测试环境**: Node.js ${process.version}  
**操作系统**: ${process.platform} ${process.arch}  

## 📊 测试概览

| 指标 | 数值 |
|------|------|
| 总测试套件 | ${totalTests} |
| 通过测试 | ${passedTests} |
| 失败测试 | ${failedTests} |
| 通过率 | ${passRate}% |

## 📋 测试结果详情

| 测试套件 | 状态 | 耗时 | 说明 |
|----------|------|------|------|
${results.map(result => {
  const status = result.success ? '✅ 通过' : '❌ 失败';
  const duration = `${result.duration}ms`;
  const note = result.error || '正常';
  return `| ${result.name} | ${status} | ${duration} | ${note} |`;
}).join('\n')}

## 🔍 详细分析

### 通过的测试
${results.filter(r => r.success).map(r => `- **${r.name}**: ${r.duration}ms`).join('\n')}

### 失败的测试
${results.filter(r => !r.success).map(r => `- **${r.name}**: ${r.error || '未知错误'}`).join('\n')}

## 📈 性能分析

- **最快测试**: ${Math.min(...results.map(r => r.duration))}ms
- **最慢测试**: ${Math.max(...results.map(r => r.duration))}ms
- **平均耗时**: ${Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length)}ms
- **总耗时**: ${results.reduce((sum, r) => sum + r.duration, 0)}ms

## 🎯 测试建议

${passRate >= 90 ? '🎉 测试通过率优秀，代码质量良好！' : 
  passRate >= 80 ? '✅ 测试通过率良好，建议关注失败的测试。' :
  passRate >= 60 ? '⚠️ 测试通过率一般，需要修复失败的测试。' :
  '❌ 测试通过率较低，需要重点关注代码质量。'}

${failedTests > 0 ? `
### 修复建议
${results.filter(r => !r.success).map(r => `
#### ${r.name}
- **问题**: ${r.error || '测试失败'}
- **建议**: 检查相关代码和依赖
`).join('')}
` : ''}

---

**当贝AI Provider SDK** - 测试报告生成于 ${timestamp}
`;

  return report;
}

/**
 * 保存测试报告
 */
function saveTestReport(report) {
  const reportDir = path.join(__dirname, '../test-results');
  const reportFile = path.join(reportDir, 'comprehensive-test-report.md');
  
  // 确保目录存在
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  fs.writeFileSync(reportFile, report, 'utf8');
  colorLog('blue', `📄 测试报告已保存: ${reportFile}`);
}

/**
 * 显示使用帮助
 */
function showHelp() {
  console.log(`
当贝AI Provider SDK 测试套件运行器

用法: node tests/run-all-tests.js [选项]

选项:
  --help, -h          显示此帮助信息
  --suite <name>      仅运行指定的测试套件
  --required-only     仅运行必需的测试套件
  --skip-deployment   跳过部署测试
  --timeout <ms>      设置测试超时时间
  --verbose           详细输出模式

可用的测试套件:
${Object.entries(TEST_SUITES).map(([key, config]) => 
  `  ${key.padEnd(12)} ${config.name} ${config.required ? '(必需)' : '(可选)'}`
).join('\n')}

示例:
  node tests/run-all-tests.js                    # 运行所有测试
  node tests/run-all-tests.js --required-only    # 仅运行必需测试
  node tests/run-all-tests.js --suite signature  # 仅运行签名测试
  node tests/run-all-tests.js --skip-deployment  # 跳过部署测试
`);
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  // 解析命令行参数
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  const suiteName = args.includes('--suite') ? 
    args[args.indexOf('--suite') + 1] : null;
  const requiredOnly = args.includes('--required-only');
  const skipDeployment = args.includes('--skip-deployment');
  const verbose = args.includes('--verbose');
  
  colorLog('magenta', '🚀 开始运行当贝AI Provider SDK 测试套件');
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`工作目录: ${process.cwd()}`);
  
  // 确定要运行的测试套件
  let suitesToRun = Object.entries(TEST_SUITES);
  
  if (suiteName) {
    if (!TEST_SUITES[suiteName]) {
      colorLog('red', `❌ 未知的测试套件: ${suiteName}`);
      process.exit(1);
    }
    suitesToRun = [[suiteName, TEST_SUITES[suiteName]]];
  } else if (requiredOnly) {
    suitesToRun = suitesToRun.filter(([_, config]) => config.required);
  }
  
  if (skipDeployment) {
    suitesToRun = suitesToRun.filter(([name, _]) => name !== 'deployment');
  }
  
  colorLog('blue', `\n将运行 ${suitesToRun.length} 个测试套件:`);
  suitesToRun.forEach(([name, config]) => {
    console.log(`  - ${config.name} ${config.required ? '(必需)' : '(可选)'}`);
  });
  
  // 运行测试套件
  const results = [];
  const startTime = Date.now();
  
  for (const [suiteName, config] of suitesToRun) {
    const result = await runTestSuite(suiteName, config);
    results.push(result);
    
    if (verbose && result.stdout) {
      console.log('\n--- 详细输出 ---');
      console.log(result.stdout.substring(0, 1000));
      console.log('--- 输出结束 ---\n');
    }
  }
  
  const totalDuration = Date.now() - startTime;
  
  // 生成和保存报告
  const report = generateTestReport(results);
  saveTestReport(report);
  
  // 显示总结
  const passedTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  const passRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  colorLog('magenta', '\n=== 测试总结 ===');
  console.log(`总耗时: ${totalDuration}ms`);
  console.log(`测试套件: ${totalTests}`);
  console.log(`通过: ${passedTests}`);
  console.log(`失败: ${totalTests - passedTests}`);
  console.log(`通过率: ${passRate}%`);
  
  // 检查必需测试是否通过
  const requiredResults = results.filter(r => 
    TEST_SUITES[r.suite] && TEST_SUITES[r.suite].required
  );
  const requiredPassed = requiredResults.filter(r => r.success).length;
  const requiredTotal = requiredResults.length;
  
  if (requiredPassed === requiredTotal) {
    colorLog('green', '\n🎉 所有必需测试通过！项目状态良好！');
    process.exit(0);
  } else {
    colorLog('red', `\n❌ 必需测试失败 (${requiredPassed}/${requiredTotal})，请修复后重试`);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    colorLog('red', `测试运行器执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTestSuite, generateTestReport, main };
