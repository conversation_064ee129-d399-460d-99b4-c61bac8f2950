/**
 * Provider集成测试
 * 测试DangbeiProvider的完整功能
 */

import { DangbeiProvider } from '../../src/providers';
import { ChatCallbacks } from '../../src/types';

describe('DangbeiProvider Integration Tests', () => {
  let provider: DangbeiProvider;

  beforeEach(() => {
    provider = new DangbeiProvider({
      debug: false, // 在测试中关闭调试日志
      timeout: 10000,
      retries: 1
    });
  });

  afterEach(() => {
    provider.destroy();
  });

  describe('初始化', () => {
    it('应该成功创建Provider实例', () => {
      expect(provider).toBeInstanceOf(DangbeiProvider);
    });

    it('应该生成有效的设备配置', () => {
      const deviceConfig = provider.getDeviceConfig();
      
      expect(deviceConfig).toHaveProperty('deviceId');
      expect(deviceConfig).toHaveProperty('appType', 6);
      expect(deviceConfig).toHaveProperty('appVersion', '1.1.17-22');
      expect(deviceConfig.deviceId).toMatch(/^[a-f0-9]{32}_[A-Za-z0-9]+$/);
    });
  });

  describe('ID生成', () => {
    it('应该能够生成有效的ID', async () => {
      const id = await provider.generateId();
      
      // ID应该是数字字符串
      expect(id).toMatch(/^\d+$/);
      expect(id.length).toBeGreaterThan(10);
    });

    it('应该能够生成多个不同的ID', async () => {
      const id1 = await provider.generateId();
      const id2 = await provider.generateId();
      
      expect(id1).not.toBe(id2);
    });
  });

  describe('对话创建', () => {
    it('应该能够创建新对话', async () => {
      // 注意：这个测试需要真实的API连接
      // 在实际环境中可能需要模拟API响应
      try {
        const conversation = await provider.createConversation();
        
        expect(conversation).toHaveProperty('conversationId');
        expect(conversation).toHaveProperty('title');
        expect(conversation.conversationId).toMatch(/^\d+$/);
      } catch (error) {
        // 如果API不可用，跳过测试
        console.warn('API不可用，跳过对话创建测试:', error);
      }
    }, 15000);

    it('应该能够使用自定义选项创建对话', async () => {
      try {
        const conversation = await provider.createConversation({
          superAgentPath: '/custom-chat',
          isAnonymous: true,
          source: 'test'
        });
        
        expect(conversation).toHaveProperty('conversationId');
        expect(conversation.metaData.superAgentPath).toBe('/custom-chat');
        expect(conversation.isAnonymous).toBe(true);
      } catch (error) {
        console.warn('API不可用，跳过自定义对话创建测试:', error);
      }
    }, 15000);
  });

  describe('快速聊天', () => {
    it('应该能够进行快速聊天', async () => {
      try {
        let messageReceived = false;
        let chatCompleted = false;

        const callbacks: ChatCallbacks = {
          onMessage: (content, data) => {
            messageReceived = true;
            expect(typeof content).toBe('string');
            expect(data).toHaveProperty('role', 'assistant');
          },
          onComplete: (data) => {
            chatCompleted = true;
            expect(data).toHaveProperty('id');
            expect(data).toHaveProperty('conversation_id');
          },
          onError: (error) => {
            console.error('聊天错误:', error);
          }
        };

        const response = await provider.quickChat('你好，请简单介绍一下自己', callbacks);
        
        expect(response).toHaveProperty('content');
        expect(response).toHaveProperty('messageId');
        expect(response).toHaveProperty('conversationId');
        expect(response.content.length).toBeGreaterThan(0);
        
        // 验证回调是否被调用
        expect(messageReceived).toBe(true);
        expect(chatCompleted).toBe(true);
      } catch (error) {
        console.warn('API不可用，跳过快速聊天测试:', error);
      }
    }, 30000);
  });

  describe('聊天功能', () => {
    it('应该验证聊天参数', async () => {
      try {
        // 测试空对话ID
        await expect(provider.chat({
          conversationId: '',
          question: '测试问题'
        })).rejects.toThrow('对话ID不能为空');

        // 测试空问题
        await expect(provider.chat({
          conversationId: '123456789',
          question: ''
        })).rejects.toThrow('问题内容不能为空');

        // 测试过长问题
        const longQuestion = 'a'.repeat(10001);
        await expect(provider.chat({
          conversationId: '123456789',
          question: longQuestion
        })).rejects.toThrow('问题内容过长');
      } catch (error) {
        // 参数验证应该在本地进行，不依赖API
        throw error;
      }
    });
  });

  describe('服务状态', () => {
    it('应该能够获取服务状态', () => {
      const status = provider.getStatus();
      
      expect(status).toHaveProperty('deviceId');
      expect(status).toHaveProperty('chatStatus');
      expect(status).toHaveProperty('serviceAvailable');
      expect(status.chatStatus).toHaveProperty('isConnected');
      expect(status.chatStatus).toHaveProperty('readyState');
    });

    it('应该能够检查服务可用性', async () => {
      try {
        const isAvailable = await provider.checkServiceAvailability();
        expect(typeof isAvailable).toBe('boolean');
      } catch (error) {
        console.warn('服务可用性检查失败:', error);
      }
    });
  });

  describe('配置管理', () => {
    it('应该能够更新设备配置', () => {
      const originalConfig = provider.getDeviceConfig();
      
      provider.updateDeviceConfig({
        lang: 'en',
        appVersion: '2.0.0'
      });
      
      const updatedConfig = provider.getDeviceConfig();
      
      expect(updatedConfig.lang).toBe('en');
      expect(updatedConfig.appVersion).toBe('2.0.0');
      expect(updatedConfig.deviceId).toBe(originalConfig.deviceId); // 设备ID不应改变
    });
  });

  describe('资源清理', () => {
    it('应该能够正确停止聊天', () => {
      expect(() => provider.stopChat()).not.toThrow();
    });

    it('应该能够正确销毁实例', () => {
      expect(() => provider.destroy()).not.toThrow();
    });
  });
});
