/**
 * 聊天界面配置文件
 * 用于控制调试模式和其他全局设置
 */

// 全局配置对象
window.ChatConfig = {
  // 调试模式开关 - 设置为true可显示所有调试信息
  DEBUG_MODE: false,
  
  // 消息显示配置
  MESSAGE_DISPLAY: {
    // 消息类型显示顺序 (1=最先显示, 3=最后显示)
    ORDER: {
      SEARCH_CARDS: 1,    // 联网搜索结果卡片
      THINKING: 2,        // AI思考过程
      TEXT_CONTENT: 3     // 正式回答内容
    },
    
    // 搜索卡片样式配置
    SEARCH_CARD: {
      COMPACT_MODE: true,     // 紧凑模式
      FONT_SIZE_SCALE: 0.85,  // 字体缩放比例
      REDUCED_PADDING: true   // 减少内边距
    }
  },
  
  // 开发者工具配置
  DEV_TOOLS: {
    SHOW_PERFORMANCE_METRICS: false,  // 显示性能指标
    LOG_API_REQUESTS: false,          // 记录API请求
    SHOW_MESSAGE_STRUCTURE: false     // 显示消息结构
  }
};

/**
 * 启用调试模式
 */
function enableDebugMode() {
  window.ChatConfig.DEBUG_MODE = true;
  console.log('🔧 调试模式已启用');
  
  // 如果聊天应用已初始化，更新其调试状态
  if (window.chatApp) {
    window.chatApp.DEBUG_MODE = true;
    console.log('✅ 聊天应用调试模式已同步');
  }
}

/**
 * 禁用调试模式
 */
function disableDebugMode() {
  window.ChatConfig.DEBUG_MODE = false;
  console.log('🔇 调试模式已禁用');
  
  // 如果聊天应用已初始化，更新其调试状态
  if (window.chatApp) {
    window.chatApp.DEBUG_MODE = false;
    console.log('✅ 聊天应用调试模式已同步');
  }
}

/**
 * 切换调试模式
 */
function toggleDebugMode() {
  if (window.ChatConfig.DEBUG_MODE) {
    disableDebugMode();
  } else {
    enableDebugMode();
  }
}

// 将函数暴露到全局作用域，方便在控制台中调用
window.enableDebugMode = enableDebugMode;
window.disableDebugMode = disableDebugMode;
window.toggleDebugMode = toggleDebugMode;

// 在控制台中提示可用的调试命令
console.log(`
🎛️  当贝AI聊天界面 - 调试控制台
可用命令：
- enableDebugMode()  : 启用调试模式
- disableDebugMode() : 禁用调试模式  
- toggleDebugMode()  : 切换调试模式
- ChatConfig         : 查看完整配置

当前调试模式: ${window.ChatConfig.DEBUG_MODE ? '启用' : '禁用'}
`);
