/**
 * 当贝AI聊天界面 - 本地存储管理器
 * 管理会话数据、用户设置等的本地存储
 */

class StorageManager {
  constructor() {
    this.storageKey = 'dangbei-chat';
    this.settingsKey = 'dangbei-chat-settings';
    this.maxSessions = 100; // 最大会话数量
    this.maxMessagesPerSession = 1000; // 每个会话最大消息数量
    
    // 初始化存储
    this.initStorage();
  }

  /**
   * 初始化存储结构
   */
  initStorage() {
    try {
      const data = this.getData();
      if (!data.sessions) {
        data.sessions = [];
      }
      if (!data.currentSession) {
        data.currentSession = null;
      }
      this.setData(data);
      
      // 初始化设置
      const settings = this.getSettings();
      if (!settings.theme) {
        settings.theme = 'light';
      }
      if (!settings.autoScroll) {
        settings.autoScroll = true;
      }
      if (!settings.soundEnabled) {
        settings.soundEnabled = false;
      }
      this.setSettings(settings);
    } catch (error) {
      console.error('初始化存储失败:', error);
      this.clearData();
    }
  }

  /**
   * 获取存储数据
   * @returns {Object} 存储的数据
   */
  getData() {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : { sessions: [], currentSession: null };
    } catch (error) {
      console.error('读取存储数据失败:', error);
      return { sessions: [], currentSession: null };
    }
  }

  /**
   * 设置存储数据
   * @param {Object} data - 要存储的数据
   */
  setData(data) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('保存存储数据失败:', error);
      // 如果存储空间不足，清理旧数据
      this.cleanupOldData();
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(data));
      } catch (retryError) {
        console.error('重试保存失败:', retryError);
      }
    }
  }

  /**
   * 获取用户设置
   * @returns {Object} 用户设置
   */
  getSettings() {
    try {
      const settings = localStorage.getItem(this.settingsKey);
      return settings ? JSON.parse(settings) : {};
    } catch (error) {
      console.error('读取设置失败:', error);
      return {};
    }
  }

  /**
   * 设置用户设置
   * @param {Object} settings - 用户设置
   */
  setSettings(settings) {
    try {
      localStorage.setItem(this.settingsKey, JSON.stringify(settings));
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  }

  /**
   * 创建新会话
   * @param {string} title - 会话标题
   * @param {string} model - 使用的模型
   * @returns {Object} 新创建的会话
   */
  createSession(title = '新对话', model = '') {
    const session = {
      id: generateId('session'),
      title: title,
      model: model,
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      conversationId: null // 服务器端对话ID
    };

    const data = this.getData();
    data.sessions.unshift(session);
    
    // 限制会话数量
    if (data.sessions.length > this.maxSessions) {
      data.sessions = data.sessions.slice(0, this.maxSessions);
    }
    
    data.currentSession = session.id;
    this.setData(data);
    
    return session;
  }

  /**
   * 获取所有会话
   * @returns {Array} 会话列表
   */
  getSessions() {
    const data = this.getData();
    return data.sessions || [];
  }

  /**
   * 获取当前会话
   * @returns {Object|null} 当前会话
   */
  getCurrentSession() {
    const data = this.getData();
    if (!data.currentSession) {
      return null;
    }
    return data.sessions.find(session => session.id === data.currentSession) || null;
  }

  /**
   * 根据ID获取会话
   * @param {string} sessionId - 会话ID
   * @returns {Object|null} 会话对象
   */
  getSession(sessionId) {
    const data = this.getData();
    return data.sessions.find(session => session.id === sessionId) || null;
  }

  /**
   * 设置当前会话
   * @param {string} sessionId - 会话ID
   */
  setCurrentSession(sessionId) {
    const data = this.getData();
    data.currentSession = sessionId;
    this.setData(data);
  }

  /**
   * 更新会话
   * @param {string} sessionId - 会话ID
   * @param {Object} updates - 更新的数据
   */
  updateSession(sessionId, updates) {
    const data = this.getData();
    const sessionIndex = data.sessions.findIndex(session => session.id === sessionId);
    
    if (sessionIndex !== -1) {
      data.sessions[sessionIndex] = {
        ...data.sessions[sessionIndex],
        ...updates,
        updatedAt: Date.now()
      };
      this.setData(data);
    }
  }

  /**
   * 删除会话
   * @param {string} sessionId - 会话ID
   */
  deleteSession(sessionId) {
    const data = this.getData();
    data.sessions = data.sessions.filter(session => session.id !== sessionId);
    
    // 如果删除的是当前会话，切换到第一个会话
    if (data.currentSession === sessionId) {
      data.currentSession = data.sessions.length > 0 ? data.sessions[0].id : null;
    }
    
    this.setData(data);
  }

  /**
   * 添加消息到会话
   * @param {string} sessionId - 会话ID
   * @param {Object} message - 消息对象
   */
  addMessage(sessionId, message) {
    const data = this.getData();
    const sessionIndex = data.sessions.findIndex(session => session.id === sessionId);
    
    if (sessionIndex !== -1) {
      const session = data.sessions[sessionIndex];
      
      // 添加消息
      session.messages.push({
        id: generateId('msg'),
        timestamp: Date.now(),
        ...message
      });
      
      // 限制消息数量
      if (session.messages.length > this.maxMessagesPerSession) {
        session.messages = session.messages.slice(-this.maxMessagesPerSession);
      }
      
      // 更新会话标题（如果是第一条用户消息）
      if (session.messages.length === 1 && message.role === 'user') {
        session.title = this.generateSessionTitle(message.content);
      }
      
      session.updatedAt = Date.now();
      
      // 将会话移到最前面
      data.sessions.splice(sessionIndex, 1);
      data.sessions.unshift(session);
      
      this.setData(data);
    }
  }

  /**
   * 获取会话消息
   * @param {string} sessionId - 会话ID
   * @returns {Array} 消息列表
   */
  getSessionMessages(sessionId) {
    const data = this.getData();
    const session = data.sessions.find(session => session.id === sessionId);
    return session ? session.messages : [];
  }

  /**
   * 更新消息
   * @param {string} sessionId - 会话ID
   * @param {string} messageId - 消息ID
   * @param {Object} updates - 更新的数据
   */
  updateMessage(sessionId, messageId, updates) {
    const data = this.getData();
    const sessionIndex = data.sessions.findIndex(session => session.id === sessionId);
    
    if (sessionIndex !== -1) {
      const session = data.sessions[sessionIndex];
      const messageIndex = session.messages.findIndex(msg => msg.id === messageId);
      
      if (messageIndex !== -1) {
        session.messages[messageIndex] = {
          ...session.messages[messageIndex],
          ...updates
        };
        session.updatedAt = Date.now();
        this.setData(data);
      }
    }
  }

  /**
   * 生成会话标题
   * @param {string} content - 消息内容
   * @returns {string} 会话标题
   */
  generateSessionTitle(content) {
    // 移除多余的空白字符
    const cleaned = content.trim().replace(/\s+/g, ' ');
    
    // 限制长度
    if (cleaned.length <= 20) {
      return cleaned;
    }
    
    // 截取前20个字符并添加省略号
    return cleaned.substring(0, 20) + '...';
  }

  /**
   * 清理旧数据
   */
  cleanupOldData() {
    try {
      const data = this.getData();
      
      // 只保留最近的会话
      if (data.sessions.length > this.maxSessions / 2) {
        data.sessions = data.sessions
          .sort((a, b) => b.updatedAt - a.updatedAt)
          .slice(0, Math.floor(this.maxSessions / 2));
      }
      
      // 清理每个会话的消息
      data.sessions.forEach(session => {
        if (session.messages.length > this.maxMessagesPerSession / 2) {
          session.messages = session.messages.slice(-Math.floor(this.maxMessagesPerSession / 2));
        }
      });
      
      this.setData(data);
    } catch (error) {
      console.error('清理数据失败:', error);
    }
  }

  /**
   * 清空所有数据
   */
  clearData() {
    try {
      localStorage.removeItem(this.storageKey);
      this.initStorage();
    } catch (error) {
      console.error('清空数据失败:', error);
    }
  }

  /**
   * 导出数据
   * @returns {Object} 导出的数据
   */
  exportData() {
    return {
      data: this.getData(),
      settings: this.getSettings(),
      exportTime: Date.now(),
      version: '1.0.0'
    };
  }

  /**
   * 导入数据
   * @param {Object} importData - 要导入的数据
   */
  importData(importData) {
    try {
      if (importData.data) {
        this.setData(importData.data);
      }
      if (importData.settings) {
        this.setSettings(importData.settings);
      }
    } catch (error) {
      console.error('导入数据失败:', error);
      throw new Error('导入数据格式错误');
    }
  }

  /**
   * 获取存储使用情况
   * @returns {Object} 存储使用情况
   */
  getStorageUsage() {
    try {
      const data = JSON.stringify(this.getData());
      const settings = JSON.stringify(this.getSettings());
      
      return {
        dataSize: new Blob([data]).size,
        settingsSize: new Blob([settings]).size,
        totalSize: new Blob([data + settings]).size,
        sessionCount: this.getSessions().length,
        messageCount: this.getSessions().reduce((total, session) => total + session.messages.length, 0)
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return {
        dataSize: 0,
        settingsSize: 0,
        totalSize: 0,
        sessionCount: 0,
        messageCount: 0
      };
    }
  }
}

// 创建全局存储管理器实例
const storage = new StorageManager();
