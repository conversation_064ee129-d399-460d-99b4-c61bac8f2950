<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具 - 调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .tabs { border: 1px solid #ddd; margin: 20px 0; }
        .tab-headers { display: flex; background: #f5f5f5; }
        .tab-header { padding: 10px 20px; border: none; background: none; cursor: pointer; }
        .tab-header.active { background: white; border-bottom: 2px solid blue; }
        .tab-content { display: none; padding: 20px; }
        .tab-content.active { display: block; }
        .test-result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>API测试工具调试页面</h1>
    
    <div class="debug-section">
        <h2>JavaScript加载测试</h2>
        <div id="js-test-result" class="test-result">测试中...</div>
    </div>

    <div class="debug-section">
        <h2>标签页功能测试</h2>
        <div class="tabs" id="test-tabs">
            <div class="tab-headers">
                <button class="tab-header active" data-tab="tab1">标签页1</button>
                <button class="tab-header" data-tab="tab2">标签页2</button>
                <button class="tab-header" data-tab="tab3">标签页3</button>
            </div>
            <div class="tab-content active" data-tab="tab1">
                <h3>标签页1内容</h3>
                <p>这是第一个标签页的内容。</p>
            </div>
            <div class="tab-content" data-tab="tab2">
                <h3>标签页2内容</h3>
                <p>这是第二个标签页的内容。</p>
            </div>
            <div class="tab-content" data-tab="tab3">
                <h3>标签页3内容</h3>
                <p>这是第三个标签页的内容。</p>
            </div>
        </div>
        <div id="tab-test-result" class="test-result">点击上面的标签页测试切换功能</div>
    </div>

    <div class="debug-section">
        <h2>DOM元素检查</h2>
        <div id="dom-test-result" class="test-result">检查中...</div>
    </div>

    <div class="debug-section">
        <h2>API测试</h2>
        <button onclick="testAPI()">测试API调用</button>
        <div id="api-test-result" class="test-result">点击按钮测试API</div>
    </div>

    <script>
        // 测试JavaScript加载
        function testJavaScriptLoading() {
            const resultDiv = document.getElementById('js-test-result');
            try {
                // 检查全局对象是否存在
                if (typeof Utils !== 'undefined') {
                    resultDiv.innerHTML = '<span class="success">✅ Utils对象已加载</span>';
                } else {
                    resultDiv.innerHTML = '<span class="error">❌ Utils对象未加载</span>';
                }
                
                if (typeof apiClient !== 'undefined') {
                    resultDiv.innerHTML += '<br><span class="success">✅ apiClient对象已加载</span>';
                } else {
                    resultDiv.innerHTML += '<br><span class="error">❌ apiClient对象未加载</span>';
                }
                
                if (typeof uiComponents !== 'undefined') {
                    resultDiv.innerHTML += '<br><span class="success">✅ uiComponents对象已加载</span>';
                } else {
                    resultDiv.innerHTML += '<br><span class="error">❌ uiComponents对象未加载</span>';
                }
                
                if (typeof app !== 'undefined') {
                    resultDiv.innerHTML += '<br><span class="success">✅ app对象已加载</span>';
                } else {
                    resultDiv.innerHTML += '<br><span class="error">❌ app对象未加载</span>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ JavaScript加载错误: ' + error.message + '</span>';
            }
        }

        // 测试标签页功能
        function testTabFunctionality() {
            const tabHeaders = document.querySelectorAll('#test-tabs .tab-header');
            const resultDiv = document.getElementById('tab-test-result');
            
            tabHeaders.forEach(header => {
                header.addEventListener('click', () => {
                    const tabName = header.getAttribute('data-tab');
                    
                    // 手动实现标签页切换逻辑
                    const tabContainer = header.closest('.tabs');
                    
                    // 更新标签头状态
                    tabContainer.querySelectorAll('.tab-header').forEach(h => {
                        h.classList.remove('active');
                    });
                    header.classList.add('active');
                    
                    // 更新标签内容状态
                    tabContainer.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    const targetContent = tabContainer.querySelector(`.tab-content[data-tab="${tabName}"]`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                        resultDiv.innerHTML = `<span class="success">✅ 成功切换到: ${tabName}</span>`;
                    } else {
                        resultDiv.innerHTML = `<span class="error">❌ 未找到标签页内容: ${tabName}</span>`;
                    }
                });
            });
        }

        // 测试DOM元素
        function testDOMElements() {
            const resultDiv = document.getElementById('dom-test-result');
            let results = [];
            
            // 检查主要元素
            const elements = [
                'api-groups',
                'history-list',
                'request-method',
                'request-url',
                'headers-list',
                'params-list',
                'request-body',
                'response-body',
                'response-headers',
                'stream-output'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results.push(`<span class="success">✅ #${id} 存在</span>`);
                } else {
                    results.push(`<span class="error">❌ #${id} 不存在</span>`);
                }
            });
            
            resultDiv.innerHTML = results.join('<br>');
        }

        // 测试API调用
        async function testAPI() {
            const resultDiv = document.getElementById('api-test-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('/api/info');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<span class="success">✅ API调用成功</span><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ API调用失败: ${response.status}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ API调用错误: ${error.message}</span>`;
            }
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('调试页面加载完成');
            testJavaScriptLoading();
            testTabFunctionality();
            testDOMElements();
        });
    </script>

    <!-- 加载原始的JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/ui-components.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 延迟重新测试JavaScript加载
        setTimeout(() => {
            testJavaScriptLoading();
        }, 1000);
    </script>
</body>
</html>
