<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-panel { 
            position: fixed; 
            top: 10px; 
            right: 10px; 
            width: 400px; 
            background: white; 
            border: 2px solid #333; 
            padding: 15px; 
            z-index: 10000;
            max-height: 80vh;
            overflow-y: auto;
        }
        .log-entry { 
            margin: 5px 0; 
            padding: 5px; 
            border-left: 3px solid #ccc; 
            font-size: 12px;
        }
        .log-error { border-left-color: #f00; background: #ffe6e6; }
        .log-warn { border-left-color: #fa0; background: #fff3cd; }
        .log-info { border-left-color: #00f; background: #e6f3ff; }
        .log-success { border-left-color: #0a0; background: #e6ffe6; }
        .test-button { 
            margin: 5px; 
            padding: 8px 12px; 
            background: #007bff; 
            color: white; 
            border: none; 
            cursor: pointer; 
        }
        .test-button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>API测试工具控制台调试</h1>
    
    <div class="debug-panel">
        <h3>调试面板</h3>
        <button class="test-button" onclick="testTabSwitching()">测试标签页切换</button>
        <button class="test-button" onclick="testAPICall()">测试API调用</button>
        <button class="test-button" onclick="testDOMElements()">检查DOM元素</button>
        <button class="test-button" onclick="clearLogs()">清空日志</button>
        <div id="debug-logs"></div>
    </div>

    <!-- 包含原始的HTML结构 -->
    <div style="margin-right: 420px;">
        <!-- 简化的标签页测试 -->
        <div class="tabs">
            <div class="tab-headers">
                <button class="tab-header active" data-tab="headers">请求头</button>
                <button class="tab-header" data-tab="params">URL 参数</button>
                <button class="tab-header" data-tab="body">请求体</button>
            </div>
            <div class="tab-content active" data-tab="headers">
                <h3>请求头标签页</h3>
                <div class="headers-editor">
                    <div class="headers-list" id="headers-list">
                        <!-- 请求头列表 -->
                    </div>
                    <button id="add-header" class="btn btn-small">添加请求头</button>
                </div>
            </div>
            <div class="tab-content" data-tab="params">
                <h3>URL参数标签页</h3>
                <div class="params-editor">
                    <div class="params-list" id="params-list">
                        <!-- 参数列表 -->
                    </div>
                    <button id="add-param" class="btn btn-small">添加参数</button>
                </div>
            </div>
            <div class="tab-content" data-tab="body">
                <h3>请求体标签页</h3>
                <textarea id="request-body" placeholder="请输入请求体内容..." rows="10" style="width: 100%;"></textarea>
            </div>
        </div>

        <!-- 响应区域 -->
        <div class="tabs">
            <div class="tab-headers">
                <button class="tab-header active" data-tab="response-body">响应体</button>
                <button class="tab-header" data-tab="response-headers">响应头</button>
            </div>
            <div class="tab-content active" data-tab="response-body">
                <h3>响应体</h3>
                <pre id="response-body">等待响应...</pre>
            </div>
            <div class="tab-content" data-tab="response-headers">
                <h3>响应头</h3>
                <pre id="response-headers">等待响应...</pre>
            </div>
        </div>
    </div>

    <!-- 加载原始CSS -->
    <link rel="stylesheet" href="css/style.css">

    <script>
        // 调试日志函数
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('debug-logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('debug-logs').innerHTML = '';
        }

        // 重写console方法来捕获日志
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addLog(args.join(' '), 'warn');
        };

        // 捕获未处理的错误
        window.addEventListener('error', (e) => {
            addLog(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });

        // 测试函数
        function testTabSwitching() {
            addLog('开始测试标签页切换...', 'info');
            
            const tabHeaders = document.querySelectorAll('.tab-header');
            addLog(`找到 ${tabHeaders.length} 个标签页头部`, 'info');
            
            tabHeaders.forEach((header, index) => {
                const tabName = header.getAttribute('data-tab');
                const isActive = header.classList.contains('active');
                addLog(`标签页 ${index + 1}: ${tabName} (${isActive ? '激活' : '未激活'})`, 'info');
            });

            const tabContents = document.querySelectorAll('.tab-content');
            addLog(`找到 ${tabContents.length} 个标签页内容`, 'info');
            
            tabContents.forEach((content, index) => {
                const tabName = content.getAttribute('data-tab');
                const isActive = content.classList.contains('active');
                const isVisible = window.getComputedStyle(content).display !== 'none';
                addLog(`内容 ${index + 1}: ${tabName} (${isActive ? '激活' : '未激活'}, ${isVisible ? '可见' : '隐藏'})`, 'info');
            });
        }

        function testAPICall() {
            addLog('开始测试API调用...', 'info');
            
            fetch('/api/info')
                .then(response => {
                    addLog(`API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                    return response.json();
                })
                .then(data => {
                    addLog('API调用成功', 'success');
                    addLog(`响应数据: ${JSON.stringify(data).substring(0, 100)}...`, 'info');
                })
                .catch(error => {
                    addLog(`API调用失败: ${error.message}`, 'error');
                });
        }

        function testDOMElements() {
            addLog('开始检查DOM元素...', 'info');
            
            const elements = [
                'headers-list',
                'params-list', 
                'request-body',
                'response-body',
                'response-headers'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    addLog(`✓ #${id} 存在 (${rect.width}x${rect.height})`, 'success');
                } else {
                    addLog(`✗ #${id} 不存在`, 'error');
                }
            });
        }

        // 手动实现标签页切换
        function manualSwitchTab(tabHeader) {
            addLog(`手动切换标签页: ${tabHeader.getAttribute('data-tab')}`, 'info');
            
            const tabContainer = tabHeader.closest('.tabs');
            if (!tabContainer) {
                addLog('未找到标签页容器', 'error');
                return;
            }

            const tabName = tabHeader.getAttribute('data-tab');
            
            // 更新标签头状态
            tabContainer.querySelectorAll('.tab-header').forEach(header => {
                header.classList.remove('active');
            });
            tabHeader.classList.add('active');

            // 更新标签内容状态
            tabContainer.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            const targetContent = tabContainer.querySelector(`.tab-content[data-tab="${tabName}"]`);
            if (targetContent) {
                targetContent.classList.add('active');
                addLog(`标签页切换成功: ${tabName}`, 'success');
            } else {
                addLog(`未找到标签页内容: ${tabName}`, 'error');
            }
        }

        // 绑定点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-header')) {
                manualSwitchTab(e.target);
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            addLog('调试页面加载完成', 'success');
        });
    </script>

    <!-- 加载原始JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/ui-components.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 延迟检查
        setTimeout(() => {
            addLog('检查全局对象...', 'info');
            
            if (typeof Utils !== 'undefined') {
                addLog('✓ Utils 已加载', 'success');
            } else {
                addLog('✗ Utils 未加载', 'error');
            }
            
            if (typeof window.apiClient !== 'undefined') {
                addLog('✓ apiClient 已加载', 'success');
            } else {
                addLog('✗ apiClient 未加载', 'error');
            }
            
            if (typeof window.uiComponents !== 'undefined') {
                addLog('✓ uiComponents 已加载', 'success');
            } else {
                addLog('✗ uiComponents 未加载', 'error');
            }
            
            if (typeof window.app !== 'undefined') {
                addLog('✓ app 已加载', 'success');
            } else {
                addLog('✗ app 未加载', 'error');
            }
        }, 2000);
    </script>
</body>
</html>
