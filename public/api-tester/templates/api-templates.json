{"get_api_models": {"description": "获取所有支持的模型列表", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_api_models_recommended": {"description": "获取推荐模型列表", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_api_models__modelId_": {"description": "获取特定模型信息", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none", "pathParams": {"modelId": "deepseek"}}, "post_api_models_reload": {"description": "重新加载模型数据", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "params": {}, "body": {}, "bodyType": "json"}, "post_api_chat": {"description": "聊天对话接口（非流式）", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "params": {}, "body": {"messages": [{"role": "user", "content": "你好，请介绍一下你自己"}], "model": "deepseek", "stream": false, "conversation_id": null, "options": {"deep_thinking": true, "online_search": false}}, "bodyType": "json"}, "post_api_chat_stream": {"description": "聊天对话接口（流式）", "headers": {"Content-Type": "application/json", "Accept": "text/event-stream"}, "params": {"stream": "true"}, "body": {"messages": [{"role": "user", "content": "请写一首关于春天的诗"}], "model": "deepseek", "stream": true, "conversation_id": null, "options": {"deep_thinking": true, "online_search": false}}, "bodyType": "json"}, "post_api_text_generate": {"description": "文本生成接口（非流式）", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "params": {}, "body": {"prompt": "请写一篇关于人工智能发展的文章", "model": "deepseek", "stream": false, "task_type": "creative", "max_tokens": 1000, "temperature": 0.7, "options": {"style": "专业", "format": "markdown", "language": "zh", "deep_thinking": true, "online_search": false}}, "bodyType": "json"}, "post_api_text_generate_stream": {"description": "文本生成接口（流式）", "headers": {"Content-Type": "application/json", "Accept": "text/event-stream"}, "params": {"stream": "true"}, "body": {"prompt": "请写一个Python函数来计算斐波那契数列", "model": "deepseek", "stream": true, "task_type": "code", "max_tokens": 500, "temperature": 0.3, "options": {"style": "简洁", "format": "code", "language": "python", "deep_thinking": true, "online_search": false}}, "bodyType": "json"}, "get_api_text_models": {"description": "获取支持文本生成的模型列表", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_health": {"description": "健康检查", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_stats": {"description": "获取使用统计", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_api_info": {"description": "获取API信息", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_v1_models": {"description": "获取模型列表（OpenAI兼容格式）", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none"}, "get_v1_models__model_id_": {"description": "获取特定模型信息（OpenAI兼容格式）", "headers": {"Accept": "application/json"}, "params": {}, "body": null, "bodyType": "none", "pathParams": {"model_id": "deepseek"}}, "post_v1_chat_completions": {"description": "聊天补全接口（OpenAI兼容格式）", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "params": {}, "body": {"model": "deepseek", "messages": [{"role": "user", "content": "Hello! How are you today?"}], "stream": false, "max_tokens": 1000, "temperature": 0.7, "top_p": 1, "frequency_penalty": 0, "presence_penalty": 0}, "bodyType": "json"}, "post_v1_chat_completions_stream": {"description": "聊天补全接口（OpenAI兼容格式，流式）", "headers": {"Content-Type": "application/json", "Accept": "text/event-stream"}, "params": {}, "body": {"model": "deepseek", "messages": [{"role": "user", "content": "Please write a short story about a robot learning to paint."}], "stream": true, "max_tokens": 1000, "temperature": 0.8, "top_p": 1, "frequency_penalty": 0, "presence_penalty": 0}, "bodyType": "json"}, "common_examples": {"chat_with_context": {"description": "带上下文的聊天对话", "body": {"messages": [{"role": "system", "content": "你是一个专业的编程助手，擅长解答技术问题。"}, {"role": "user", "content": "请解释一下什么是RESTful API？"}, {"role": "assistant", "content": "RESTful API是一种基于REST架构风格的Web API设计方法..."}, {"role": "user", "content": "能给我一个具体的例子吗？"}], "model": "deepseek", "stream": false}}, "code_generation": {"description": "代码生成示例", "body": {"prompt": "请用Python写一个快速排序算法，要求包含详细注释", "model": "deepseek", "task_type": "code", "options": {"style": "详细注释", "format": "code", "language": "python"}}}, "creative_writing": {"description": "创意写作示例", "body": {"prompt": "写一个关于时间旅行的科幻短故事，大约500字", "model": "doubao-1_6-thinking", "task_type": "creative", "options": {"style": "科幻", "format": "story", "language": "zh"}}}, "document_summary": {"description": "文档摘要示例", "body": {"prompt": "请总结以下文档的主要内容：\n\n[在这里粘贴要总结的文档内容]", "model": "doubao-1_6-thinking", "task_type": "summary", "options": {"style": "简洁", "format": "bullet_points", "language": "zh"}}}, "translation": {"description": "翻译示例", "body": {"prompt": "请将以下英文翻译成中文：\n\nArtificial Intelligence is transforming the way we work and live.", "model": "glm-4-5", "task_type": "translation", "options": {"style": "准确", "format": "text", "source_language": "en", "target_language": "zh"}}}, "qa_example": {"description": "问答示例", "body": {"messages": [{"role": "user", "content": "什么是机器学习？它与人工智能有什么关系？"}], "model": "qwen-plus", "options": {"deep_thinking": true, "online_search": true}}}}}