<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天API流式响应测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .test-request {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .test-response {
            background: #fff;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-disconnected { background: #ccc; }
        .status-connecting { background: #ffa500; }
        .status-connected { background: #4caf50; }
        .status-error { background: #f44336; }
        .message-stats {
            display: flex;
            gap: 15px;
            margin-top: 10px;
            font-size: 12px;
        }
        .stat-item {
            padding: 4px 8px;
            border-radius: 4px;
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 聊天API流式响应测试</h1>
        
        <!-- 测试配置 -->
        <div class="test-section">
            <h2>测试配置</h2>
            <div class="form-row">
                <div class="form-group">
                    <label for="api-endpoint">API端点</label>
                    <input type="text" id="api-endpoint" class="form-control" 
                           value="http://localhost:3033/api/chat" placeholder="API端点URL">
                </div>
                <div class="form-group">
                    <label for="test-model">模型</label>
                    <select id="test-model" class="form-control">
                        <option value="deepseek">deepseek</option>
                        <option value="gpt-4">gpt-4</option>
                        <option value="claude">claude</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enable-deep-thinking" checked>
                        启用深度思考 (deep_thinking)
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enable-online-search" checked>
                        启用联网搜索 (online_search)
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enable-stream" checked>
                        启用流式响应 (stream)
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="test-question">测试问题</label>
                <textarea id="test-question" class="form-control" rows="3" 
                          placeholder="输入测试问题...">a股低洼板块?</textarea>
            </div>
            
            <div class="form-actions">
                <button id="start-test" class="btn btn-primary">开始测试</button>
                <button id="stop-test" class="btn btn-secondary">停止测试</button>
                <button id="clear-response" class="btn btn-secondary">清空响应</button>
            </div>
        </div>

        <!-- 请求信息 -->
        <div class="test-section">
            <h2>请求信息</h2>
            <div class="test-request">
                <pre id="request-info">等待发送请求...</pre>
            </div>
        </div>

        <!-- 响应状态 -->
        <div class="test-section">
            <h2>响应状态</h2>
            <div class="status-info">
                <span class="status-indicator status-disconnected" id="connection-status"></span>
                <span id="status-text">未连接</span>
                
                <div class="message-stats">
                    <div class="stat-item">总消息: <span id="total-messages">0</span></div>
                    <div class="stat-item">进度消息: <span id="progress-messages">0</span></div>
                    <div class="stat-item">思考消息: <span id="thinking-messages">0</span></div>
                    <div class="stat-item">文本消息: <span id="text-messages">0</span></div>
                    <div class="stat-item">卡片消息: <span id="card-messages">0</span></div>
                    <div class="stat-item">未知消息: <span id="unknown-messages">0</span></div>
                </div>
            </div>
        </div>

        <!-- 流式响应 -->
        <div class="test-section">
            <h2>流式响应</h2>
            <div class="stream-toolbar">
                <label>
                    <input type="checkbox" id="enable-sse-styling" checked>
                    启用样式区分
                </label>
                <label>
                    <input type="checkbox" id="show-raw-data">
                    显示原始数据
                </label>
            </div>
            <div class="test-response" id="stream-messages">
                <!-- 流式消息将在这里显示 -->
            </div>
        </div>

        <!-- 错误日志 -->
        <div class="test-section">
            <h2>错误日志</h2>
            <div class="test-response" id="error-log">
                <!-- 错误信息将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/three-color-data-handler.js"></script>
    
    <script>
        // 测试应用类
        class ChatApiTester {
            constructor() {
                this.apiClient = new ApiClient();
                this.isConnected = false;
                this.messageStats = {
                    total: 0,
                    progress: 0,
                    thinking: 0,
                    text: 0,
                    card: 0,
                    unknown: 0
                };
                this.stopFunction = null;
                
                this.initializeEventListeners();
                this.updateUI();
            }
            
            initializeEventListeners() {
                document.getElementById('start-test').addEventListener('click', () => this.startTest());
                document.getElementById('stop-test').addEventListener('click', () => this.stopTest());
                document.getElementById('clear-response').addEventListener('click', () => this.clearResponse());
            }
            
            async startTest() {
                try {
                    this.updateStatus('connecting', '正在连接...');
                    this.clearResponse();
                    this.resetStats();
                    
                    const requestData = this.buildRequestData();
                    this.displayRequestInfo(requestData);
                    
                    // 发送流式请求
                    this.stopFunction = this.apiClient.sendStreamRequest(
                        {
                            method: 'POST',
                            url: document.getElementById('api-endpoint').value,
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: requestData
                        },
                        (data, event) => this.handleMessage(data, event),
                        (error) => this.handleError(error),
                        () => this.handleComplete()
                    );
                    
                    this.updateStatus('connected', '已连接');
                    
                } catch (error) {
                    this.handleError(error);
                }
            }
            
            stopTest() {
                if (this.stopFunction) {
                    this.stopFunction();
                    this.stopFunction = null;
                }
                this.updateStatus('disconnected', '已断开');
            }
            
            buildRequestData() {
                return {
                    messages: [
                        {
                            role: "system",
                            content: "你是一个有用的AI助手，请用中文回答问题。",
                            id: "msg-system-001",
                            timestamp: Date.now()
                        },
                        {
                            role: "user",
                            content: document.getElementById('test-question').value,
                            id: "msg-user-001",
                            timestamp: Date.now()
                        }
                    ],
                    model: document.getElementById('test-model').value,
                    stream: document.getElementById('enable-stream').checked,
                    options: {
                        deep_thinking: document.getElementById('enable-deep-thinking').checked,
                        online_search: document.getElementById('enable-online-search').checked
                    }
                };
            }
            
            displayRequestInfo(requestData) {
                document.getElementById('request-info').textContent = 
                    JSON.stringify(requestData, null, 2);
            }
            
            handleMessage(data, event) {
                this.messageStats.total++;
                
                // 检测消息类型
                const contentType = this.detectContentType(data);
                if (contentType && this.messageStats[contentType] !== undefined) {
                    this.messageStats[contentType]++;
                } else {
                    this.messageStats.unknown++;
                }
                
                this.updateStats();
                this.displayMessage(data, contentType);
            }
            
            detectContentType(data) {
                if (typeof data === 'object') {
                    return data.content_type || 
                           (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content_type);
                }
                return null;
            }
            
            displayMessage(data, contentType) {
                const container = document.getElementById('stream-messages');
                const enableStyling = document.getElementById('enable-sse-styling').checked;
                const showRawData = document.getElementById('show-raw-data').checked;
                
                const messageDiv = document.createElement('div');
                messageDiv.className = 'stream-message';
                
                if (enableStyling && contentType) {
                    messageDiv.classList.add(`message-${contentType}`);
                }
                
                const timestamp = new Date().toLocaleTimeString();
                const content = this.extractContent(data);
                
                messageDiv.innerHTML = `
                    <div class="message-header">
                        <span class="message-type">[${contentType || 'unknown'}]</span>
                        <span class="message-time">${timestamp}</span>
                    </div>
                    <div class="message-content">${content}</div>
                    ${showRawData ? `<details><summary>原始数据</summary><pre>${JSON.stringify(data, null, 2)}</pre></details>` : ''}
                `;
                
                container.appendChild(messageDiv);
                container.scrollTop = container.scrollHeight;
            }
            
            extractContent(data) {
                if (typeof data === 'string') return data;
                if (data.choices && data.choices[0] && data.choices[0].delta) {
                    return data.choices[0].delta.content || '';
                }
                return data.content || JSON.stringify(data);
            }
            
            handleError(error) {
                this.updateStatus('error', '连接错误');
                this.logError(error);
            }
            
            handleComplete() {
                this.updateStatus('disconnected', '连接完成');
            }
            
            logError(error) {
                const errorLog = document.getElementById('error-log');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-entry';
                errorDiv.innerHTML = `
                    <div class="error-time">${new Date().toLocaleTimeString()}</div>
                    <div class="error-message">${error.message || error}</div>
                `;
                errorLog.appendChild(errorDiv);
            }
            
            updateStatus(status, text) {
                const indicator = document.getElementById('connection-status');
                const statusText = document.getElementById('status-text');
                
                indicator.className = `status-indicator status-${status}`;
                statusText.textContent = text;
                this.isConnected = status === 'connected';
            }
            
            updateStats() {
                Object.keys(this.messageStats).forEach(key => {
                    const element = document.getElementById(`${key === 'total' ? 'total' : key}-messages`);
                    if (element) {
                        element.textContent = this.messageStats[key];
                    }
                });
            }
            
            resetStats() {
                Object.keys(this.messageStats).forEach(key => {
                    this.messageStats[key] = 0;
                });
                this.updateStats();
            }
            
            clearResponse() {
                document.getElementById('stream-messages').innerHTML = '';
                document.getElementById('error-log').innerHTML = '';
            }
            
            updateUI() {
                // 初始化UI状态
                this.updateStats();
            }
        }
        
        // 初始化测试应用
        document.addEventListener('DOMContentLoaded', () => {
            window.chatApiTester = new ChatApiTester();
        });
    </script>
</body>
</html>
