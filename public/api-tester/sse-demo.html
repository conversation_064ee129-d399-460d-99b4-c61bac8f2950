<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE 样式区分功能演示</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-controls {
            background: var(--bg-secondary);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }
        
        .demo-controls h3 {
            margin-top: 0;
            color: var(--text-primary);
        }
        
        .control-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .demo-output {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 20px;
            min-height: 400px;
        }
        
        .demo-output h3 {
            margin-top: 0;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-hover);
        }
        
        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }
        
        .btn-info {
            background: var(--info-color);
            color: white;
        }
        
        .btn-warning {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-success {
            background: var(--success-color);
            color: white;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙</button>
    
    <div class="demo-container">
        <div class="demo-header">
            <h1>🧪 SSE 样式区分功能演示</h1>
            <p>演示基于 <code>content_type</code> 字段的 Server-Sent Events 响应样式区分功能</p>
        </div>
        
        <div class="demo-controls">
            <h3>📋 控制面板</h3>
            
            <div class="control-group">
                <button class="btn btn-info" onclick="addProgressMessage()">
                    🔍 添加联网搜索消息
                </button>
                <button class="btn btn-primary" onclick="addCardMessage()">
                    📋 添加搜索结果卡片
                </button>
                <button class="btn btn-warning" onclick="addThinkingMessage()">
                    🤔 添加思考过程消息
                </button>
                <button class="btn btn-success" onclick="addTextMessage()">
                    💬 添加正式回答消息
                </button>
            </div>
            
            <div class="control-group">
                <button class="btn btn-secondary" onclick="addAllTypes()">
                    🎯 添加所有类型示例
                </button>
                <button class="btn btn-secondary" onclick="clearMessages()">
                    🗑️ 清空消息
                </button>
                <button class="btn btn-secondary" onclick="simulateStream()">
                    ⚡ 模拟流式响应
                </button>
            </div>
            
            <div class="control-group">
                <label style="display: flex; align-items: center; gap: 8px; color: var(--text-primary);">
                    <input type="checkbox" id="enable-sse-styling" checked onchange="toggleStyling()">
                    启用样式区分
                </label>
            </div>
        </div>
        
        <div class="demo-output">
            <h3>📺 消息显示区域</h3>
            <div class="stream-content">
                <div id="stream-messages" class="stream-messages">
                    <!-- SSE 消息将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟App类的核心方法
        class DemoApp {
            constructor() {
                this.messageCount = 0;
            }

            getContentTypeClass(contentType) {
                const typeMap = {
                    'progress': 'progress',
                    'card': 'card',
                    'thinking': 'thinking',
                    'text': 'text'
                };
                return typeMap[contentType] || 'unknown';
            }

            getContentTypeIcon(contentType) {
                const iconMap = {
                    'progress': '🔍',
                    'card': '📋',
                    'thinking': '🤔',
                    'text': '💬'
                };
                return iconMap[contentType] || '📄';
            }

            getContentTypeLabel(contentType) {
                const labelMap = {
                    'progress': '联网搜索',
                    'card': '搜索结果',
                    'thinking': '思考过程',
                    'text': '正式回答'
                };
                return labelMap[contentType] || '未知类型';
            }

            formatTimestamp(timestamp) {
                if (timestamp < 10000000000) {
                    timestamp *= 1000;
                }
                
                const date = new Date(timestamp);
                return date.toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }

            renderStyledSSEMessage(data, container) {
                const messageElement = document.createElement('div');
                messageElement.className = `sse-message ${this.getContentTypeClass(data.content_type)}`;
                
                const headerElement = document.createElement('div');
                headerElement.className = 'sse-message-header';
                
                const iconElement = document.createElement('span');
                iconElement.className = 'sse-message-icon';
                iconElement.textContent = this.getContentTypeIcon(data.content_type);
                
                const typeElement = document.createElement('span');
                typeElement.className = 'sse-message-type';
                typeElement.textContent = this.getContentTypeLabel(data.content_type);
                
                const timestampElement = document.createElement('span');
                timestampElement.className = 'sse-message-timestamp';
                timestampElement.textContent = this.formatTimestamp(data.created_at || Date.now());
                
                headerElement.appendChild(iconElement);
                headerElement.appendChild(typeElement);
                headerElement.appendChild(timestampElement);
                
                const contentElement = document.createElement('div');
                contentElement.className = 'sse-message-content';
                
                if (data.content_type === 'card' && typeof data.content === 'string') {
                    try {
                        const parsedContent = JSON.parse(data.content);
                        contentElement.textContent = JSON.stringify(parsedContent, null, 2);
                    } catch {
                        contentElement.textContent = data.content;
                    }
                } else {
                    contentElement.textContent = data.content || JSON.stringify(data, null, 2);
                }
                
                messageElement.appendChild(headerElement);
                messageElement.appendChild(contentElement);
                container.appendChild(messageElement);
                
                // 自动滚动
                container.scrollTop = container.scrollHeight;
                
                return messageElement;
            }

            handleStreamMessage(data) {
                const streamMessages = document.getElementById('stream-messages');
                const enableStyling = document.getElementById('enable-sse-styling')?.checked ?? true;
                
                if (!streamMessages) return;

                if (enableStyling && typeof data === 'object' && data.content_type) {
                    this.renderStyledSSEMessage(data, streamMessages);
                } else {
                    // 简单的回退显示
                    const div = document.createElement('div');
                    div.textContent = JSON.stringify(data, null, 2);
                    div.style.cssText = 'background: var(--bg-tertiary); padding: 10px; margin: 5px 0; border-radius: 4px; font-family: monospace;';
                    streamMessages.appendChild(div);
                }
            }
        }

        // 创建应用实例
        const app = new DemoApp();

        // 示例数据
        const sampleMessages = {
            progress: {
                content: "正在联网搜索相关信息...",
                content_type: "progress",
                created_at: Date.now() / 1000
            },
            card: {
                content: '{"cardType":"DB-CARD-2","cardInfo":{"initTitle":"搜索结果","items":[{"title":"相关文档1","url":"https://example.com/1"},{"title":"相关文档2","url":"https://example.com/2"}]}}',
                content_type: "card",
                created_at: Date.now() / 1000
            },
            thinking: {
                content: "让我分析一下这个问题的关键点...\n1. 首先需要理解用户的需求\n2. 然后搜索相关信息\n3. 最后整合答案",
                content_type: "thinking",
                created_at: Date.now() / 1000
            },
            text: {
                content: "根据搜索结果和分析，我可以为您提供以下答案：\n\nSSE（Server-Sent Events）是一种服务器推送技术，允许服务器向客户端推送实时数据。它具有以下特点：\n\n1. 单向通信：只能从服务器向客户端推送\n2. 自动重连：连接断开时会自动重连\n3. 简单易用：基于HTTP协议，实现简单",
                content_type: "text",
                created_at: Date.now() / 1000
            }
        };

        // 控制函数
        function addProgressMessage() {
            app.handleStreamMessage(sampleMessages.progress);
        }

        function addCardMessage() {
            app.handleStreamMessage(sampleMessages.card);
        }

        function addThinkingMessage() {
            app.handleStreamMessage(sampleMessages.thinking);
        }

        function addTextMessage() {
            app.handleStreamMessage(sampleMessages.text);
        }

        function addAllTypes() {
            Object.values(sampleMessages).forEach((message, index) => {
                setTimeout(() => {
                    app.handleStreamMessage(message);
                }, index * 500);
            });
        }

        function clearMessages() {
            const container = document.getElementById('stream-messages');
            if (container) {
                container.innerHTML = '';
            }
        }

        function simulateStream() {
            const messages = [
                { ...sampleMessages.progress, content: "开始处理您的请求..." },
                { ...sampleMessages.thinking, content: "分析问题中..." },
                { ...sampleMessages.progress, content: "联网搜索相关资料..." },
                { ...sampleMessages.card, content: '{"cardType":"search-result","results":["结果1","结果2","结果3"]}' },
                { ...sampleMessages.thinking, content: "整理和分析搜索结果..." },
                { ...sampleMessages.text, content: "基于搜索结果，我为您整理了以下信息..." }
            ];

            messages.forEach((message, index) => {
                setTimeout(() => {
                    app.handleStreamMessage(message);
                }, index * 800);
            });
        }

        function toggleStyling() {
            const checkbox = document.getElementById('enable-sse-styling');
            console.log('样式区分已', checkbox.checked ? '启用' : '禁用');
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️' : '🌙';
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('SSE 样式区分功能演示页面已加载');
        });
    </script>
</body>
</html>
