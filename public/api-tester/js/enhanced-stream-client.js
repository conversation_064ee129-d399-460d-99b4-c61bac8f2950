/**
 * 增强的流式客户端
 * 提供完整的 SSE 流式响应支持，包括重连、超时、错误处理等功能
 */

// 连接状态枚举
const ConnectionState = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting', 
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
};

// 错误类型枚举
const StreamErrorType = {
  NETWORK_ERROR: 'network_error',
  TIMEOUT_ERROR: 'timeout_error',
  PARSE_ERROR: 'parse_error',
  CONNECTION_ERROR: 'connection_error',
  SERVER_ERROR: 'server_error',
  UNKNOWN_ERROR: 'unknown_error'
};

/**
 * 增强的流式客户端类
 * 支持自动重连、错误恢复、连接监控等高级功能
 */
class EnhancedStreamClient {
  constructor(options = {}) {
    // 默认配置
    this.config = {
      // 连接配置
      connectionTimeout: 10000,        // 连接超时时间（毫秒）
      responseTimeout: 30000,          // 响应超时时间（毫秒）
      heartbeatInterval: 30000,        // 心跳间隔（毫秒）
      
      // 重试配置
      enableRetry: true,               // 是否启用自动重试
      maxRetries: 3,                   // 最大重试次数
      retryDelay: 1000,                // 重试延迟（毫秒）
      retryBackoff: 2,                 // 重试延迟倍数
      
      // 数据处理配置
      enableDataValidation: true,      // 是否启用数据验证
      maxMessageSize: 1024 * 1024,     // 最大消息大小（字节）
      enableCompression: false,        // 是否启用压缩
      
      // 监控配置
      enableMetrics: true,             // 是否启用性能监控
      metricsInterval: 1000,           // 监控数据更新间隔（毫秒）
      
      ...options
    };

    // 连接状态
    this.state = ConnectionState.DISCONNECTED;
    this.eventSource = null;
    this.url = null;
    this.headers = {};
    
    // 重试相关
    this.retryCount = 0;
    this.retryTimer = null;
    
    // 超时相关
    this.connectionTimer = null;
    this.responseTimer = null;
    this.heartbeatTimer = null;
    
    // 回调函数
    this.callbacks = {
      onOpen: null,
      onMessage: null,
      onError: null,
      onClose: null,
      onStateChange: null,
      onMetrics: null
    };
    
    // 性能监控数据
    this.metrics = {
      connectionTime: 0,
      totalMessages: 0,
      totalBytes: 0,
      messagesPerSecond: 0,
      bytesPerSecond: 0,
      lastMessageTime: 0,
      errors: 0,
      reconnections: 0
    };
    
    // 消息缓冲区
    this.messageBuffer = [];
    this.lastMetricsUpdate = Date.now();
    
    console.log('🚀 增强流式客户端已初始化', this.config);
  }

  /**
   * 连接到流式端点
   * @param {string} url - 流式端点URL
   * @param {object} headers - 请求头
   * @param {object} callbacks - 回调函数
   * @returns {Promise} 连接Promise
   */
  async connect(url, headers = {}, callbacks = {}) {
    this.url = url;
    this.headers = { ...headers };
    this.callbacks = { ...this.callbacks, ...callbacks };
    
    console.log(`[流式客户端] 开始连接到: ${url}`);
    
    return this.establishConnection();
  }

  /**
   * 建立连接
   * @private
   */
  async establishConnection() {
    return new Promise((resolve, reject) => {
      try {
        // 清理之前的连接
        this.cleanup();
        
        // 更新状态
        this.setState(this.retryCount > 0 ? ConnectionState.RECONNECTING : ConnectionState.CONNECTING);
        
        // 创建 EventSource
        this.eventSource = new EventSource(this.url, {
          withCredentials: false
        });
        
        // 设置连接超时
        this.connectionTimer = setTimeout(() => {
          if (this.state === ConnectionState.CONNECTING || this.state === ConnectionState.RECONNECTING) {
            console.error('[流式客户端] 连接超时');
            this.handleConnectionError(new Error('连接超时'), reject);
          }
        }, this.config.connectionTimeout);
        
        // 连接成功
        this.eventSource.onopen = () => {
          console.log('[流式客户端] 连接已建立');
          this.setState(ConnectionState.CONNECTED);
          this.retryCount = 0;
          this.metrics.connectionTime = Date.now();
          
          if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
          }
          
          // 启动心跳和监控
          this.startHeartbeat();
          this.startMetricsCollection();
          
          if (this.callbacks.onOpen) {
            this.callbacks.onOpen();
          }
          
          resolve();
        };
        
        // 接收消息
        this.eventSource.onmessage = (event) => {
          this.handleMessage(event);
        };
        
        // 连接错误
        this.eventSource.onerror = (error) => {
          console.error('[流式客户端] 连接错误:', error);
          this.handleConnectionError(error, reject);
        };
        
      } catch (error) {
        console.error('[流式客户端] 建立连接失败:', error);
        this.handleConnectionError(error, reject);
      }
    });
  }

  /**
   * 处理消息
   * @param {MessageEvent} event - 消息事件
   * @private
   */
  handleMessage(event) {
    try {
      // 重置响应超时
      this.resetResponseTimeout();
      
      // 更新监控数据
      this.metrics.totalMessages++;
      this.metrics.totalBytes += event.data.length;
      this.metrics.lastMessageTime = Date.now();
      
      // 数据验证
      if (this.config.enableDataValidation) {
        if (event.data.length > this.config.maxMessageSize) {
          throw new Error(`消息大小超过限制: ${event.data.length} > ${this.config.maxMessageSize}`);
        }
      }
      
      // 解析数据
      let parsedData;
      try {
        // 检查是否为结束标记
        if (event.data === '[DONE]') {
          console.log('[流式客户端] 收到结束标记');
          this.disconnect();
          return;
        }
        
        // 尝试解析 JSON
        parsedData = JSON.parse(event.data);
      } catch (parseError) {
        // 如果不是 JSON，使用原始数据
        parsedData = event.data;
      }
      
      // 添加到消息缓冲区
      this.messageBuffer.push({
        data: parsedData,
        timestamp: Date.now(),
        size: event.data.length
      });
      
      // 调用消息回调
      if (this.callbacks.onMessage) {
        this.callbacks.onMessage(parsedData, event);
      }
      
    } catch (error) {
      console.error('[流式客户端] 处理消息失败:', error);
      this.handleError(StreamErrorType.PARSE_ERROR, error);
    }
  }

  /**
   * 处理连接错误
   * @param {Error} error - 错误对象
   * @param {Function} reject - Promise reject 函数
   * @private
   */
  handleConnectionError(error, reject) {
    this.setState(ConnectionState.ERROR);
    this.metrics.errors++;
    
    // 清理定时器
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
    
    // 判断是否需要重试
    if (this.config.enableRetry && this.retryCount < this.config.maxRetries) {
      this.scheduleRetry();
    } else {
      // 不再重试，调用错误回调
      const streamError = this.createStreamError(StreamErrorType.CONNECTION_ERROR, error);
      
      if (this.callbacks.onError) {
        this.callbacks.onError(streamError);
      } else if (reject) {
        reject(streamError);
      }
    }
  }

  /**
   * 安排重试
   * @private
   */
  scheduleRetry() {
    this.retryCount++;
    this.metrics.reconnections++;
    
    const delay = this.config.retryDelay * Math.pow(this.config.retryBackoff, this.retryCount - 1);
    
    console.log(`[流式客户端] 将在 ${delay}ms 后进行第 ${this.retryCount} 次重试`);
    
    this.retryTimer = setTimeout(() => {
      this.establishConnection().catch(error => {
        console.error('[流式客户端] 重试失败:', error);
      });
    }, delay);
  }

  /**
   * 创建流式错误对象
   * @param {string} type - 错误类型
   * @param {Error} originalError - 原始错误
   * @returns {object} 流式错误对象
   * @private
   */
  createStreamError(type, originalError) {
    return {
      type,
      message: originalError.message || '未知错误',
      timestamp: Date.now(),
      retryCount: this.retryCount,
      originalError
    };
  }

  /**
   * 处理一般错误
   * @param {string} type - 错误类型
   * @param {Error} error - 错误对象
   * @private
   */
  handleError(type, error) {
    this.metrics.errors++;
    
    const streamError = this.createStreamError(type, error);
    
    if (this.callbacks.onError) {
      this.callbacks.onError(streamError);
    }
  }

  /**
   * 设置连接状态
   * @param {string} newState - 新状态
   * @private
   */
  setState(newState) {
    const oldState = this.state;
    this.state = newState;
    
    console.log(`[流式客户端] 状态变更: ${oldState} -> ${newState}`);
    
    if (this.callbacks.onStateChange) {
      this.callbacks.onStateChange(newState, oldState);
    }
  }

  /**
   * 启动心跳检测
   * @private
   */
  startHeartbeat() {
    if (this.config.heartbeatInterval > 0) {
      this.heartbeatTimer = setInterval(() => {
        // 检查连接状态
        if (this.eventSource && this.eventSource.readyState !== EventSource.OPEN) {
          console.warn('[流式客户端] 心跳检测发现连接异常');
          this.handleConnectionError(new Error('心跳检测失败'));
        }
      }, this.config.heartbeatInterval);
    }
  }

  /**
   * 启动性能监控数据收集
   * @private
   */
  startMetricsCollection() {
    if (this.config.enableMetrics && this.config.metricsInterval > 0) {
      setInterval(() => {
        this.updateMetrics();
      }, this.config.metricsInterval);
    }
  }

  /**
   * 更新性能监控数据
   * @private
   */
  updateMetrics() {
    const now = Date.now();
    const timeDiff = (now - this.lastMetricsUpdate) / 1000; // 转换为秒
    
    if (timeDiff > 0) {
      // 计算速率
      const messagesSinceLastUpdate = this.messageBuffer.length;
      const bytesSinceLastUpdate = this.messageBuffer.reduce((sum, msg) => sum + msg.size, 0);
      
      this.metrics.messagesPerSecond = messagesSinceLastUpdate / timeDiff;
      this.metrics.bytesPerSecond = bytesSinceLastUpdate / timeDiff;
      
      // 清空缓冲区
      this.messageBuffer = [];
      this.lastMetricsUpdate = now;
      
      // 调用监控回调
      if (this.callbacks.onMetrics) {
        this.callbacks.onMetrics({ ...this.metrics });
      }
    }
  }

  /**
   * 重置响应超时
   * @private
   */
  resetResponseTimeout() {
    if (this.responseTimer) {
      clearTimeout(this.responseTimer);
    }
    
    if (this.config.responseTimeout > 0) {
      this.responseTimer = setTimeout(() => {
        console.warn('[流式客户端] 响应超时');
        this.handleError(StreamErrorType.TIMEOUT_ERROR, new Error('响应超时'));
      }, this.config.responseTimeout);
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    console.log('[流式客户端] 断开连接');
    
    this.cleanup();
    this.setState(ConnectionState.DISCONNECTED);
    
    if (this.callbacks.onClose) {
      this.callbacks.onClose();
    }
  }

  /**
   * 清理资源
   * @private
   */
  cleanup() {
    // 关闭 EventSource
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    
    // 清理定时器
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
    
    if (this.responseTimer) {
      clearTimeout(this.responseTimer);
      this.responseTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }
  }

  /**
   * 获取当前状态
   * @returns {string} 当前连接状态
   */
  getState() {
    return this.state;
  }

  /**
   * 获取性能监控数据
   * @returns {object} 监控数据
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * 是否已连接
   * @returns {boolean} 连接状态
   */
  isConnected() {
    return this.state === ConnectionState.CONNECTED;
  }

  /**
   * 更新配置
   * @param {object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('[流式客户端] 配置已更新:', this.config);
  }
}

// 导出类和常量
if (typeof window !== 'undefined') {
  window.EnhancedStreamClient = EnhancedStreamClient;
  window.ConnectionState = ConnectionState;
  window.StreamErrorType = StreamErrorType;
}

// 如果支持模块化，也导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    EnhancedStreamClient,
    ConnectionState,
    StreamErrorType
  };
}
