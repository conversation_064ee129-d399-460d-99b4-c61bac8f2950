/**
 * 流式响应过滤和搜索功能
 * 提供消息内容搜索、类型过滤、时间范围筛选等功能
 */

/**
 * 流式消息过滤器类
 * 提供多种过滤和搜索功能
 */
class StreamFilter {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.messagesContainer = document.getElementById('stream-messages');
    this.allMessages = [];
    this.filteredMessages = [];
    this.currentFilters = {
      search: '',
      type: 'all',
      timeRange: 'all',
      startTime: null,
      endTime: null
    };
    
    this.init();
  }

  /**
   * 初始化过滤器
   */
  init() {
    this.createFilterUI();
    this.bindEvents();
    this.startMessageObserver();
    console.log('🔍 流式消息过滤器已初始化');
  }

  /**
   * 创建过滤器界面
   */
  createFilterUI() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="stream-filter">
        <div class="filter-header">
          <h4>消息过滤</h4>
          <button id="toggle-filter" class="btn btn-small">
            <span class="toggle-text">显示过滤器</span>
          </button>
        </div>
        
        <div class="filter-panel" id="filter-panel" style="display: none;">
          <!-- 搜索框 -->
          <div class="filter-group">
            <label for="message-search">内容搜索:</label>
            <div class="search-input-group">
              <input type="text" id="message-search" class="form-control" 
                     placeholder="搜索消息内容..." autocomplete="off">
              <button id="clear-search" class="btn btn-small btn-secondary">清空</button>
            </div>
            <div class="search-options">
              <label>
                <input type="checkbox" id="case-sensitive"> 区分大小写
              </label>
              <label>
                <input type="checkbox" id="regex-search"> 正则表达式
              </label>
            </div>
          </div>

          <!-- 消息类型过滤 -->
          <div class="filter-group">
            <label for="type-filter">消息类型:</label>
            <select id="type-filter" class="form-control">
              <option value="all">所有类型</option>
              <option value="text">文本消息</option>
              <option value="progress">进度消息</option>
              <option value="card">卡片消息</option>
              <option value="thinking">思考消息</option>
              <option value="error">错误消息</option>
              <option value="unknown">未知类型</option>
            </select>
          </div>

          <!-- 时间范围过滤 -->
          <div class="filter-group">
            <label for="time-range">时间范围:</label>
            <select id="time-range" class="form-control">
              <option value="all">所有时间</option>
              <option value="last-minute">最近1分钟</option>
              <option value="last-5-minutes">最近5分钟</option>
              <option value="last-hour">最近1小时</option>
              <option value="custom">自定义范围</option>
            </select>
          </div>

          <!-- 自定义时间范围 -->
          <div class="filter-group custom-time-range" id="custom-time-range" style="display: none;">
            <div class="time-inputs">
              <div>
                <label for="start-time">开始时间:</label>
                <input type="datetime-local" id="start-time" class="form-control">
              </div>
              <div>
                <label for="end-time">结束时间:</label>
                <input type="datetime-local" id="end-time" class="form-control">
              </div>
            </div>
          </div>

          <!-- 过滤操作 -->
          <div class="filter-actions">
            <button id="apply-filters" class="btn btn-small btn-primary">应用过滤</button>
            <button id="reset-filters" class="btn btn-small btn-secondary">重置</button>
            <button id="export-filtered" class="btn btn-small btn-secondary">导出结果</button>
          </div>

          <!-- 过滤结果统计 -->
          <div class="filter-stats" id="filter-stats">
            <span class="stats-text">显示 0 / 0 条消息</span>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 切换过滤器面板
    const toggleBtn = document.getElementById('toggle-filter');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleFilterPanel());
    }

    // 搜索输入
    const searchInput = document.getElementById('message-search');
    if (searchInput) {
      searchInput.addEventListener('input', Utils.debounce(() => {
        this.updateSearchFilter();
        this.applyFilters();
      }, 300));
    }

    // 清空搜索
    const clearSearchBtn = document.getElementById('clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        searchInput.value = '';
        this.updateSearchFilter();
        this.applyFilters();
      });
    }

    // 搜索选项
    const caseSensitive = document.getElementById('case-sensitive');
    const regexSearch = document.getElementById('regex-search');
    if (caseSensitive) {
      caseSensitive.addEventListener('change', () => {
        this.updateSearchFilter();
        this.applyFilters();
      });
    }
    if (regexSearch) {
      regexSearch.addEventListener('change', () => {
        this.updateSearchFilter();
        this.applyFilters();
      });
    }

    // 类型过滤
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
      typeFilter.addEventListener('change', () => {
        this.updateTypeFilter();
        this.applyFilters();
      });
    }

    // 时间范围过滤
    const timeRange = document.getElementById('time-range');
    if (timeRange) {
      timeRange.addEventListener('change', () => {
        this.updateTimeRangeFilter();
        this.applyFilters();
      });
    }

    // 自定义时间范围
    const startTime = document.getElementById('start-time');
    const endTime = document.getElementById('end-time');
    if (startTime) {
      startTime.addEventListener('change', () => {
        this.updateCustomTimeFilter();
        this.applyFilters();
      });
    }
    if (endTime) {
      endTime.addEventListener('change', () => {
        this.updateCustomTimeFilter();
        this.applyFilters();
      });
    }

    // 过滤操作按钮
    const applyBtn = document.getElementById('apply-filters');
    const resetBtn = document.getElementById('reset-filters');
    const exportBtn = document.getElementById('export-filtered');

    if (applyBtn) {
      applyBtn.addEventListener('click', () => this.applyFilters());
    }
    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetFilters());
    }
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportFilteredMessages());
    }
  }

  /**
   * 切换过滤器面板
   */
  toggleFilterPanel() {
    const panel = document.getElementById('filter-panel');
    const toggleBtn = document.getElementById('toggle-filter');
    const toggleText = toggleBtn?.querySelector('.toggle-text');

    if (panel && toggleText) {
      const isVisible = panel.style.display !== 'none';
      panel.style.display = isVisible ? 'none' : 'block';
      toggleText.textContent = isVisible ? '显示过滤器' : '隐藏过滤器';
    }
  }

  /**
   * 开始监听消息变化
   */
  startMessageObserver() {
    if (!this.messagesContainer) return;

    // 使用 MutationObserver 监听消息容器的变化
    const observer = new MutationObserver((mutations) => {
      let hasNewMessages = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          hasNewMessages = true;
        }
      });

      if (hasNewMessages) {
        this.updateMessageList();
        this.applyFilters();
      }
    });

    observer.observe(this.messagesContainer, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 更新消息列表
   */
  updateMessageList() {
    if (!this.messagesContainer) return;

    const messageElements = this.messagesContainer.querySelectorAll('.sse-message, .stream-message');
    this.allMessages = Array.from(messageElements).map((element, index) => ({
      id: index,
      element: element,
      content: this.extractMessageContent(element),
      type: this.extractMessageType(element),
      timestamp: this.extractMessageTimestamp(element),
      visible: true
    }));
  }

  /**
   * 提取消息内容
   * @param {Element} element - 消息元素
   * @returns {string} 消息内容
   */
  extractMessageContent(element) {
    const contentElement = element.querySelector('.sse-message-content, .message-content');
    return contentElement ? contentElement.textContent.trim() : '';
  }

  /**
   * 提取消息类型
   * @param {Element} element - 消息元素
   * @returns {string} 消息类型
   */
  extractMessageType(element) {
    // 从CSS类名中提取类型
    const classList = Array.from(element.classList);
    const typeClasses = ['text', 'progress', 'card', 'thinking', 'error'];
    
    for (const type of typeClasses) {
      if (classList.includes(type)) {
        return type;
      }
    }
    
    return 'unknown';
  }

  /**
   * 提取消息时间戳
   * @param {Element} element - 消息元素
   * @returns {number} 时间戳
   */
  extractMessageTimestamp(element) {
    const timestampAttr = element.getAttribute('data-timestamp');
    if (timestampAttr) {
      return parseInt(timestampAttr);
    }
    
    // 尝试从时间戳元素中提取
    const timestampElement = element.querySelector('.sse-message-timestamp, .message-timestamp');
    if (timestampElement) {
      const timeText = timestampElement.textContent.trim();
      // 这里可以添加时间解析逻辑
      return Date.now(); // 暂时返回当前时间
    }
    
    return Date.now();
  }

  /**
   * 更新搜索过滤条件
   */
  updateSearchFilter() {
    const searchInput = document.getElementById('message-search');
    const caseSensitive = document.getElementById('case-sensitive');
    const regexSearch = document.getElementById('regex-search');

    this.currentFilters.search = searchInput?.value || '';
    this.currentFilters.caseSensitive = caseSensitive?.checked || false;
    this.currentFilters.regexSearch = regexSearch?.checked || false;
  }

  /**
   * 更新类型过滤条件
   */
  updateTypeFilter() {
    const typeFilter = document.getElementById('type-filter');
    this.currentFilters.type = typeFilter?.value || 'all';
  }

  /**
   * 更新时间范围过滤条件
   */
  updateTimeRangeFilter() {
    const timeRange = document.getElementById('time-range');
    const customTimeRange = document.getElementById('custom-time-range');
    
    this.currentFilters.timeRange = timeRange?.value || 'all';
    
    // 显示/隐藏自定义时间范围
    if (customTimeRange) {
      customTimeRange.style.display = this.currentFilters.timeRange === 'custom' ? 'block' : 'none';
    }
    
    // 计算时间范围
    const now = Date.now();
    switch (this.currentFilters.timeRange) {
      case 'last-minute':
        this.currentFilters.startTime = now - 60 * 1000;
        this.currentFilters.endTime = now;
        break;
      case 'last-5-minutes':
        this.currentFilters.startTime = now - 5 * 60 * 1000;
        this.currentFilters.endTime = now;
        break;
      case 'last-hour':
        this.currentFilters.startTime = now - 60 * 60 * 1000;
        this.currentFilters.endTime = now;
        break;
      case 'custom':
        this.updateCustomTimeFilter();
        break;
      default:
        this.currentFilters.startTime = null;
        this.currentFilters.endTime = null;
    }
  }

  /**
   * 更新自定义时间过滤条件
   */
  updateCustomTimeFilter() {
    const startTime = document.getElementById('start-time');
    const endTime = document.getElementById('end-time');

    this.currentFilters.startTime = startTime?.value ? new Date(startTime.value).getTime() : null;
    this.currentFilters.endTime = endTime?.value ? new Date(endTime.value).getTime() : null;
  }

  /**
   * 应用过滤条件
   */
  applyFilters() {
    this.updateMessageList();
    
    this.filteredMessages = this.allMessages.filter(message => {
      return this.matchesSearchFilter(message) &&
             this.matchesTypeFilter(message) &&
             this.matchesTimeFilter(message);
    });

    this.updateMessageVisibility();
    this.updateFilterStats();
  }

  /**
   * 检查消息是否匹配搜索条件
   * @param {object} message - 消息对象
   * @returns {boolean} 是否匹配
   */
  matchesSearchFilter(message) {
    if (!this.currentFilters.search) return true;

    let searchText = this.currentFilters.search;
    let content = message.content;

    if (!this.currentFilters.caseSensitive) {
      searchText = searchText.toLowerCase();
      content = content.toLowerCase();
    }

    if (this.currentFilters.regexSearch) {
      try {
        const regex = new RegExp(searchText, this.currentFilters.caseSensitive ? 'g' : 'gi');
        return regex.test(content);
      } catch (error) {
        console.warn('正则表达式错误:', error);
        return false;
      }
    } else {
      return content.includes(searchText);
    }
  }

  /**
   * 检查消息是否匹配类型条件
   * @param {object} message - 消息对象
   * @returns {boolean} 是否匹配
   */
  matchesTypeFilter(message) {
    if (this.currentFilters.type === 'all') return true;
    return message.type === this.currentFilters.type;
  }

  /**
   * 检查消息是否匹配时间条件
   * @param {object} message - 消息对象
   * @returns {boolean} 是否匹配
   */
  matchesTimeFilter(message) {
    if (!this.currentFilters.startTime && !this.currentFilters.endTime) return true;

    const messageTime = message.timestamp;
    
    if (this.currentFilters.startTime && messageTime < this.currentFilters.startTime) {
      return false;
    }
    
    if (this.currentFilters.endTime && messageTime > this.currentFilters.endTime) {
      return false;
    }
    
    return true;
  }

  /**
   * 更新消息可见性
   */
  updateMessageVisibility() {
    this.allMessages.forEach(message => {
      const isVisible = this.filteredMessages.includes(message);
      message.element.style.display = isVisible ? 'block' : 'none';
      message.visible = isVisible;
    });
  }

  /**
   * 更新过滤统计信息
   */
  updateFilterStats() {
    const statsElement = document.getElementById('filter-stats');
    if (statsElement) {
      const statsText = statsElement.querySelector('.stats-text');
      if (statsText) {
        statsText.textContent = `显示 ${this.filteredMessages.length} / ${this.allMessages.length} 条消息`;
      }
    }
  }

  /**
   * 重置过滤条件
   */
  resetFilters() {
    // 重置表单
    const searchInput = document.getElementById('message-search');
    const caseSensitive = document.getElementById('case-sensitive');
    const regexSearch = document.getElementById('regex-search');
    const typeFilter = document.getElementById('type-filter');
    const timeRange = document.getElementById('time-range');
    const startTime = document.getElementById('start-time');
    const endTime = document.getElementById('end-time');

    if (searchInput) searchInput.value = '';
    if (caseSensitive) caseSensitive.checked = false;
    if (regexSearch) regexSearch.checked = false;
    if (typeFilter) typeFilter.value = 'all';
    if (timeRange) timeRange.value = 'all';
    if (startTime) startTime.value = '';
    if (endTime) endTime.value = '';

    // 重置过滤条件
    this.currentFilters = {
      search: '',
      type: 'all',
      timeRange: 'all',
      startTime: null,
      endTime: null
    };

    // 应用重置后的过滤条件
    this.applyFilters();
    
    uiComponents.showNotification('过滤条件已重置', 'info');
  }

  /**
   * 导出过滤后的消息
   */
  exportFilteredMessages() {
    if (this.filteredMessages.length === 0) {
      uiComponents.showNotification('没有可导出的消息', 'warning');
      return;
    }

    // 这里可以集成导出功能
    // 暂时显示统计信息
    const stats = {
      total: this.allMessages.length,
      filtered: this.filteredMessages.length,
      filters: this.currentFilters
    };

    console.log('导出过滤后的消息:', stats);
    uiComponents.showNotification(`准备导出 ${this.filteredMessages.length} 条消息`, 'info');
  }

  /**
   * 获取过滤后的消息
   * @returns {Array} 过滤后的消息数组
   */
  getFilteredMessages() {
    return this.filteredMessages;
  }

  /**
   * 销毁过滤器
   */
  destroy() {
    if (this.container) {
      this.container.innerHTML = '';
    }
    
    console.log('🔍 流式消息过滤器已销毁');
  }
}

// 导出类
if (typeof window !== 'undefined') {
  window.StreamFilter = StreamFilter;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = StreamFilter;
}
