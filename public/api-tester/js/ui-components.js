/**
 * UI组件模块
 * 提供用户界面组件和交互功能
 */

// UI组件管理器
class UIComponents {
  constructor() {
    this.notifications = [];
    this.modals = [];
    this.currentTheme = 'light';
    this.init();
  }

  /**
   * 初始化UI组件
   */
  init() {
    this.initTheme();
    this.initEventListeners();
    this.initTabs();
    this.initKeyValueEditors();
  }

  /**
   * 初始化主题
   */
  initTheme() {
    // 从本地存储加载主题
    const savedTheme = Utils.storage.get('app_theme', 'light');
    this.setTheme(savedTheme);
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    this.currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    
    // 更新主题切换按钮图标
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      const icon = themeToggle.querySelector('.theme-icon');
      if (icon) {
        icon.textContent = theme === 'light' ? '🌙' : '☀️';
      }
    }
    
    // 保存到本地存储
    Utils.storage.set('app_theme', theme);
  }

  /**
   * 切换主题
   */
  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  /**
   * 初始化事件监听器
   */
  initEventListeners() {
    // 主题切换
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    // 模态框关闭
    const modalOverlay = document.getElementById('modal-overlay');
    const modalClose = document.getElementById('modal-close');
    const modalCancel = document.getElementById('modal-cancel');
    
    if (modalOverlay) {
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          this.closeModal();
        }
      });
    }
    
    if (modalClose) {
      modalClose.addEventListener('click', () => this.closeModal());
    }
    
    if (modalCancel) {
      modalCancel.addEventListener('click', () => this.closeModal());
    }

    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeModal();
      }
    });
  }

  /**
   * 初始化选项卡
   */
  initTabs() {
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('tab-header')) {
        console.log('点击标签页:', e.target.getAttribute('data-tab'));
        this.switchTab(e.target);
      }
    });

    // 确保初始状态正确
    setTimeout(() => {
      this.initializeTabStates();
    }, 100);
  }

  /**
   * 初始化标签页状态
   */
  initializeTabStates() {
    const tabContainers = document.querySelectorAll('.tabs');
    tabContainers.forEach(container => {
      const activeHeader = container.querySelector('.tab-header.active');
      if (activeHeader) {
        const tabName = activeHeader.getAttribute('data-tab');
        const targetContent = container.querySelector(`.tab-content[data-tab="${tabName}"]`);
        if (targetContent) {
          // 确保只有对应的内容显示
          container.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
          });
          targetContent.classList.add('active');
          console.log('初始化标签页状态:', tabName);
        }
      }
    });
  }

  /**
   * 切换选项卡
   * @param {HTMLElement} tabHeader - 选项卡头部元素
   */
  switchTab(tabHeader) {
    const tabContainer = tabHeader.closest('.tabs');
    if (!tabContainer) return;

    const tabName = tabHeader.getAttribute('data-tab');

    // 更新选项卡头部状态
    tabContainer.querySelectorAll('.tab-header').forEach(header => {
      header.classList.remove('active');
    });
    tabHeader.classList.add('active');

    // 更新选项卡内容状态
    tabContainer.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });

    // 只选择 tab-content 元素，避免与 tab-header 冲突
    const targetContent = tabContainer.querySelector(`.tab-content[data-tab="${tabName}"]`);
    if (targetContent) {
      targetContent.classList.add('active');
      console.log(`切换到标签页: ${tabName}`);
    } else {
      console.warn(`未找到标签页内容: ${tabName}`);
    }
  }

  /**
   * 初始化键值对编辑器
   */
  initKeyValueEditors() {
    // 请求头编辑器
    this.initHeadersEditor();
    // URL参数编辑器
    this.initParamsEditor();
  }

  /**
   * 初始化请求头编辑器
   */
  initHeadersEditor() {
    const addHeaderBtn = document.getElementById('add-header');
    const clearHeadersBtn = document.getElementById('clear-headers');
    const headersList = document.getElementById('headers-list');

    if (addHeaderBtn) {
      addHeaderBtn.addEventListener('click', () => {
        this.addHeaderRow();
      });
    }

    if (clearHeadersBtn) {
      clearHeadersBtn.addEventListener('click', () => {
        this.clearHeaders();
      });
    }

    // 延迟添加默认的Content-Type头，确保DOM已准备好
    setTimeout(() => {
      this.addHeaderRow('Content-Type', 'application/json');
    }, 100);
  }

  /**
   * 添加请求头行
   * @param {string} key - 键
   * @param {string} value - 值
   */
  addHeaderRow(key = '', value = '') {
    const headersList = document.getElementById('headers-list');
    if (!headersList) return;

    const row = document.createElement('div');
    row.className = 'key-value-row';
    row.innerHTML = `
      <input type="text" class="form-control key-input" placeholder="请求头名称" value="${key}">
      <input type="text" class="form-control value-input" placeholder="请求头值" value="${value}">
      <button type="button" class="remove-btn" title="删除">×</button>
    `;

    // 添加删除事件
    const removeBtn = row.querySelector('.remove-btn');
    removeBtn.addEventListener('click', () => {
      row.remove();
    });

    headersList.appendChild(row);
  }

  /**
   * 清空请求头
   */
  clearHeaders() {
    const headersList = document.getElementById('headers-list');
    if (headersList) {
      headersList.innerHTML = '';
      // 重新添加默认的Content-Type头
      this.addHeaderRow('Content-Type', 'application/json');
    }
  }

  /**
   * 获取请求头
   * @returns {object} 请求头对象
   */
  getHeaders() {
    const headers = {};
    const headersList = document.getElementById('headers-list');
    if (!headersList) return headers;

    const rows = headersList.querySelectorAll('.key-value-row');
    rows.forEach(row => {
      const keyInput = row.querySelector('.key-input');
      const valueInput = row.querySelector('.value-input');
      
      if (keyInput && valueInput && keyInput.value.trim() && valueInput.value.trim()) {
        headers[keyInput.value.trim()] = valueInput.value.trim();
      }
    });

    return headers;
  }

  /**
   * 设置请求头
   * @param {object} headers - 请求头对象
   */
  setHeaders(headers) {
    this.clearHeaders();
    Object.entries(headers).forEach(([key, value]) => {
      this.addHeaderRow(key, value);
    });
  }

  /**
   * 初始化URL参数编辑器
   */
  initParamsEditor() {
    const addParamBtn = document.getElementById('add-param');
    const clearParamsBtn = document.getElementById('clear-params');

    if (addParamBtn) {
      addParamBtn.addEventListener('click', () => {
        this.addParamRow();
      });
    }

    if (clearParamsBtn) {
      clearParamsBtn.addEventListener('click', () => {
        this.clearParams();
      });
    }
  }

  /**
   * 添加URL参数行
   * @param {string} key - 键
   * @param {string} value - 值
   */
  addParamRow(key = '', value = '') {
    const paramsList = document.getElementById('params-list');
    if (!paramsList) return;

    const row = document.createElement('div');
    row.className = 'key-value-row';
    row.innerHTML = `
      <input type="text" class="form-control key-input" placeholder="参数名称" value="${key}">
      <input type="text" class="form-control value-input" placeholder="参数值" value="${value}">
      <button type="button" class="remove-btn" title="删除">×</button>
    `;

    // 添加删除事件
    const removeBtn = row.querySelector('.remove-btn');
    removeBtn.addEventListener('click', () => {
      row.remove();
    });

    paramsList.appendChild(row);
  }

  /**
   * 清空URL参数
   */
  clearParams() {
    const paramsList = document.getElementById('params-list');
    if (paramsList) {
      paramsList.innerHTML = '';
    }
  }

  /**
   * 获取URL参数
   * @returns {object} 参数对象
   */
  getParams() {
    const params = {};
    const paramsList = document.getElementById('params-list');
    if (!paramsList) return params;

    const rows = paramsList.querySelectorAll('.key-value-row');
    rows.forEach(row => {
      const keyInput = row.querySelector('.key-input');
      const valueInput = row.querySelector('.value-input');
      
      if (keyInput && valueInput && keyInput.value.trim() && valueInput.value.trim()) {
        params[keyInput.value.trim()] = valueInput.value.trim();
      }
    });

    return params;
  }

  /**
   * 设置URL参数
   * @param {object} params - 参数对象
   */
  setParams(params) {
    this.clearParams();
    Object.entries(params).forEach(([key, value]) => {
      this.addParamRow(key, value);
    });
  }

  /**
   * 显示通知
   * @param {string} message - 消息内容
   * @param {string} type - 通知类型 ('success' | 'error' | 'warning' | 'info')
   * @param {number} duration - 显示时长（毫秒）
   */
  showNotification(message, type = 'info', duration = 5000) {
    const notificationsContainer = document.getElementById('notifications');
    if (!notificationsContainer) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    notification.innerHTML = `
      <div class="notification-icon">${icons[type] || icons.info}</div>
      <div class="notification-content">
        <div class="notification-message">${message}</div>
      </div>
      <button class="notification-close">×</button>
    `;

    // 添加关闭事件
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      this.removeNotification(notification);
    });

    notificationsContainer.appendChild(notification);
    this.notifications.push(notification);

    // 自动移除
    if (duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification);
      }, duration);
    }
  }

  /**
   * 移除通知
   * @param {HTMLElement} notification - 通知元素
   */
  removeNotification(notification) {
    if (notification && notification.parentNode) {
      notification.style.animation = 'slideOut 0.3s ease-out forwards';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
          this.notifications.splice(index, 1);
        }
      }, 300);
    }
  }

  /**
   * 显示加载指示器
   * @param {string} message - 加载消息
   */
  showLoading(message = '正在处理请求...') {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      const loadingText = loadingIndicator.querySelector('.loading-text');
      if (loadingText) {
        loadingText.textContent = message;
      }
      loadingIndicator.style.display = 'flex';
    }
  }

  /**
   * 隐藏加载指示器
   */
  hideLoading() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = 'none';
    }
  }

  /**
   * 显示模态框
   * @param {string} title - 标题
   * @param {string} content - 内容
   * @param {object} options - 选项
   */
  showModal(title, content, options = {}) {
    const modalOverlay = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    const modalConfirm = document.getElementById('modal-confirm');
    
    if (!modalOverlay || !modalTitle || !modalBody) return;

    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    
    // 设置确认按钮
    if (options.confirmText) {
      modalConfirm.textContent = options.confirmText;
      modalConfirm.style.display = 'inline-flex';
    } else {
      modalConfirm.style.display = 'none';
    }

    // 设置确认回调
    if (options.onConfirm) {
      modalConfirm.onclick = () => {
        options.onConfirm();
        this.closeModal();
      };
    }

    modalOverlay.style.display = 'flex';
  }

  /**
   * 关闭模态框
   */
  closeModal() {
    const modalOverlay = document.getElementById('modal-overlay');
    if (modalOverlay) {
      modalOverlay.style.display = 'none';
    }
  }
}

// 等待DOM加载完成后创建全局UI组件实例
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.uiComponents = new UIComponents();
  });
} else {
  // DOM已经加载完成
  window.uiComponents = new UIComponents();
}

// 导出UI组件类（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UIComponents;
}
