/**
 * 流式响应监控组件
 * 提供实时的连接状态、性能统计和数据可视化
 */

/**
 * 流式监控器类
 * 负责监控和显示流式连接的各种状态和统计信息
 */
class StreamMonitor {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.isVisible = false;
    this.updateInterval = null;
    this.charts = {};
    
    // 监控数据
    this.data = {
      connectionState: 'disconnected',
      startTime: null,
      duration: 0,
      totalMessages: 0,
      totalBytes: 0,
      messagesPerSecond: 0,
      bytesPerSecond: 0,
      errors: 0,
      reconnections: 0,
      lastMessageTime: null,
      messageHistory: [], // 最近的消息统计历史
      errorHistory: []    // 错误历史
    };
    
    this.init();
  }

  /**
   * 初始化监控器
   */
  init() {
    this.createMonitorUI();
    this.bindEvents();
    console.log('📊 流式监控器已初始化');
  }

  /**
   * 创建监控界面
   */
  createMonitorUI() {
    if (!this.container) {
      console.error('监控容器未找到');
      return;
    }

    this.container.innerHTML = `
      <div class="stream-monitor">
        <!-- 监控头部 -->
        <div class="monitor-header">
          <h3 class="monitor-title">
            <span class="monitor-icon">📊</span>
            流式连接监控
          </h3>
          <div class="monitor-controls">
            <button id="toggle-monitor" class="btn btn-small">
              <span class="toggle-text">显示详情</span>
            </button>
            <button id="reset-monitor" class="btn btn-small btn-secondary">重置</button>
          </div>
        </div>

        <!-- 基本状态显示 -->
        <div class="monitor-basic">
          <div class="status-grid">
            <div class="status-item">
              <div class="status-label">连接状态</div>
              <div class="status-value" id="connection-status">
                <span class="status-indicator disconnected"></span>
                <span class="status-text">未连接</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">连接时长</div>
              <div class="status-value" id="connection-duration">00:00:00</div>
            </div>
            <div class="status-item">
              <div class="status-label">消息数量</div>
              <div class="status-value" id="message-count">0</div>
            </div>
            <div class="status-item">
              <div class="status-label">数据量</div>
              <div class="status-value" id="data-size">0 B</div>
            </div>
          </div>
        </div>

        <!-- 详细监控面板 -->
        <div class="monitor-details" id="monitor-details" style="display: none;">
          <!-- 实时统计 -->
          <div class="monitor-section">
            <h4>实时统计</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-label">消息速率</div>
                <div class="stat-value" id="message-rate">0 msg/s</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">数据速率</div>
                <div class="stat-value" id="data-rate">0 B/s</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">错误次数</div>
                <div class="stat-value" id="error-count">0</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">重连次数</div>
                <div class="stat-value" id="reconnect-count">0</div>
              </div>
            </div>
          </div>

          <!-- 性能图表 -->
          <div class="monitor-section">
            <h4>性能趋势</h4>
            <div class="chart-container">
              <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
          </div>

          <!-- 最近活动 -->
          <div class="monitor-section">
            <h4>最近活动</h4>
            <div class="activity-log" id="activity-log">
              <div class="activity-item">
                <span class="activity-time">等待连接...</span>
                <span class="activity-desc">监控器已就绪</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 切换详细面板显示
    const toggleBtn = document.getElementById('toggle-monitor');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => {
        this.toggleDetails();
      });
    }

    // 重置监控数据
    const resetBtn = document.getElementById('reset-monitor');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.reset();
      });
    }
  }

  /**
   * 切换详细面板显示
   */
  toggleDetails() {
    const details = document.getElementById('monitor-details');
    const toggleBtn = document.getElementById('toggle-monitor');
    const toggleText = toggleBtn.querySelector('.toggle-text');
    
    if (details && toggleText) {
      this.isVisible = !this.isVisible;
      details.style.display = this.isVisible ? 'block' : 'none';
      toggleText.textContent = this.isVisible ? '隐藏详情' : '显示详情';
      
      if (this.isVisible) {
        this.startRealTimeUpdate();
        this.initChart();
      } else {
        this.stopRealTimeUpdate();
      }
    }
  }

  /**
   * 更新连接状态
   * @param {string} state - 连接状态
   */
  updateConnectionState(state) {
    this.data.connectionState = state;
    
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
      const indicator = statusElement.querySelector('.status-indicator');
      const text = statusElement.querySelector('.status-text');
      
      if (indicator && text) {
        // 移除所有状态类
        indicator.className = 'status-indicator';
        
        // 添加当前状态类和设置文本
        switch (state) {
          case 'connected':
            indicator.classList.add('connected');
            text.textContent = '已连接';
            this.data.startTime = Date.now();
            break;
          case 'connecting':
            indicator.classList.add('connecting');
            text.textContent = '连接中...';
            break;
          case 'reconnecting':
            indicator.classList.add('reconnecting');
            text.textContent = '重连中...';
            break;
          case 'error':
            indicator.classList.add('error');
            text.textContent = '连接错误';
            break;
          default:
            indicator.classList.add('disconnected');
            text.textContent = '未连接';
            this.data.startTime = null;
        }
      }
    }
    
    this.addActivity(`连接状态变更为: ${this.getStateText(state)}`);
  }

  /**
   * 更新监控数据
   * @param {object} metrics - 性能指标数据
   */
  updateMetrics(metrics) {
    // 更新数据
    Object.assign(this.data, metrics);
    
    // 更新基本显示
    this.updateBasicDisplay();
    
    // 如果详细面板可见，更新详细数据
    if (this.isVisible) {
      this.updateDetailedDisplay();
      this.updateChart();
    }
  }

  /**
   * 更新基本显示
   */
  updateBasicDisplay() {
    // 更新连接时长
    const durationElement = document.getElementById('connection-duration');
    if (durationElement && this.data.startTime) {
      const duration = Date.now() - this.data.startTime;
      durationElement.textContent = this.formatDuration(duration);
    }
    
    // 更新消息数量
    const messageCountElement = document.getElementById('message-count');
    if (messageCountElement) {
      messageCountElement.textContent = this.data.totalMessages.toLocaleString();
    }
    
    // 更新数据量
    const dataSizeElement = document.getElementById('data-size');
    if (dataSizeElement) {
      dataSizeElement.textContent = this.formatBytes(this.data.totalBytes);
    }
  }

  /**
   * 更新详细显示
   */
  updateDetailedDisplay() {
    // 更新消息速率
    const messageRateElement = document.getElementById('message-rate');
    if (messageRateElement) {
      messageRateElement.textContent = `${this.data.messagesPerSecond.toFixed(1)} msg/s`;
    }
    
    // 更新数据速率
    const dataRateElement = document.getElementById('data-rate');
    if (dataRateElement) {
      dataRateElement.textContent = `${this.formatBytes(this.data.bytesPerSecond)}/s`;
    }
    
    // 更新错误次数
    const errorCountElement = document.getElementById('error-count');
    if (errorCountElement) {
      errorCountElement.textContent = this.data.errors.toString();
    }
    
    // 更新重连次数
    const reconnectCountElement = document.getElementById('reconnect-count');
    if (reconnectCountElement) {
      reconnectCountElement.textContent = this.data.reconnections.toString();
    }
  }

  /**
   * 记录新消息
   * @param {object} messageData - 消息数据
   */
  recordMessage(messageData) {
    this.data.totalMessages++;
    this.data.totalBytes += messageData.size || 0;
    this.data.lastMessageTime = Date.now();
    
    // 添加到消息历史（保留最近50条）
    this.data.messageHistory.push({
      timestamp: Date.now(),
      size: messageData.size || 0
    });
    
    if (this.data.messageHistory.length > 50) {
      this.data.messageHistory.shift();
    }
    
    this.addActivity(`收到消息 (${this.formatBytes(messageData.size || 0)})`);
  }

  /**
   * 记录错误
   * @param {object} error - 错误信息
   */
  recordError(error) {
    this.data.errors++;
    
    // 添加到错误历史
    this.data.errorHistory.push({
      timestamp: Date.now(),
      type: error.type || 'unknown',
      message: error.message || '未知错误'
    });
    
    this.addActivity(`发生错误: ${error.message}`, 'error');
  }

  /**
   * 记录重连
   */
  recordReconnection() {
    this.data.reconnections++;
    this.addActivity('尝试重新连接', 'warning');
  }

  /**
   * 添加活动记录
   * @param {string} description - 活动描述
   * @param {string} type - 活动类型
   */
  addActivity(description, type = 'info') {
    const activityLog = document.getElementById('activity-log');
    if (!activityLog) return;
    
    const activityItem = document.createElement('div');
    activityItem.className = `activity-item ${type}`;
    
    const time = new Date().toLocaleTimeString();
    activityItem.innerHTML = `
      <span class="activity-time">${time}</span>
      <span class="activity-desc">${description}</span>
    `;
    
    // 插入到顶部
    activityLog.insertBefore(activityItem, activityLog.firstChild);
    
    // 限制显示数量
    const items = activityLog.querySelectorAll('.activity-item');
    if (items.length > 20) {
      activityLog.removeChild(items[items.length - 1]);
    }
  }

  /**
   * 启动实时更新
   */
  startRealTimeUpdate() {
    if (this.updateInterval) return;
    
    this.updateInterval = setInterval(() => {
      this.updateBasicDisplay();
      if (this.isVisible) {
        this.updateDetailedDisplay();
      }
    }, 1000);
  }

  /**
   * 停止实时更新
   */
  stopRealTimeUpdate() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * 初始化图表
   */
  initChart() {
    const canvas = document.getElementById('performance-chart');
    if (!canvas) return;
    
    // 这里可以集成图表库，如 Chart.js
    // 暂时使用简单的文本显示
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#666';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('性能图表 (待实现)', canvas.width / 2, canvas.height / 2);
  }

  /**
   * 更新图表
   */
  updateChart() {
    // 图表更新逻辑
    // 可以在这里绘制实时的性能数据图表
  }

  /**
   * 重置监控数据
   */
  reset() {
    this.data = {
      connectionState: 'disconnected',
      startTime: null,
      duration: 0,
      totalMessages: 0,
      totalBytes: 0,
      messagesPerSecond: 0,
      bytesPerSecond: 0,
      errors: 0,
      reconnections: 0,
      lastMessageTime: null,
      messageHistory: [],
      errorHistory: []
    };
    
    this.updateConnectionState('disconnected');
    this.updateBasicDisplay();
    
    if (this.isVisible) {
      this.updateDetailedDisplay();
    }
    
    // 清空活动日志
    const activityLog = document.getElementById('activity-log');
    if (activityLog) {
      activityLog.innerHTML = `
        <div class="activity-item">
          <span class="activity-time">等待连接...</span>
          <span class="activity-desc">监控器已重置</span>
        </div>
      `;
    }
    
    console.log('📊 监控数据已重置');
  }

  /**
   * 格式化时长
   * @param {number} milliseconds - 毫秒数
   * @returns {string} 格式化的时长
   */
  formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * 格式化字节数
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取状态文本
   * @param {string} state - 状态
   * @returns {string} 状态文本
   */
  getStateText(state) {
    const stateTexts = {
      'connected': '已连接',
      'connecting': '连接中',
      'reconnecting': '重连中',
      'disconnected': '未连接',
      'error': '连接错误'
    };
    
    return stateTexts[state] || state;
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopRealTimeUpdate();
    
    if (this.container) {
      this.container.innerHTML = '';
    }
    
    console.log('📊 流式监控器已销毁');
  }
}

// 导出类
if (typeof window !== 'undefined') {
  window.StreamMonitor = StreamMonitor;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = StreamMonitor;
}
