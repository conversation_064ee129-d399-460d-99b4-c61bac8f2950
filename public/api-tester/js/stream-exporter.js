/**
 * 流式响应导出功能
 * 支持将流式数据导出为多种格式
 */

/**
 * 流式数据导出器类
 * 提供多种格式的流式数据导出功能
 */
class StreamExporter {
  constructor() {
    this.exportFormats = {
      JSON: 'json',
      CSV: 'csv',
      TXT: 'txt',
      HTML: 'html'
    };
    
    this.init();
  }

  /**
   * 初始化导出器
   */
  init() {
    this.createExportUI();
    this.bindEvents();
    console.log('📤 流式数据导出器已初始化');
  }

  /**
   * 创建导出界面
   */
  createExportUI() {
    // 在流式工具栏中添加导出按钮
    const streamToolbar = document.querySelector('.stream-toolbar');
    if (streamToolbar) {
      const exportButton = document.createElement('button');
      exportButton.id = 'export-stream-data';
      exportButton.className = 'btn btn-small btn-secondary';
      exportButton.innerHTML = '📤 导出数据';
      exportButton.title = '导出流式响应数据';
      
      streamToolbar.appendChild(exportButton);
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    const exportButton = document.getElementById('export-stream-data');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.showExportDialog();
      });
    }
  }

  /**
   * 显示导出对话框
   */
  showExportDialog() {
    const streamMessages = this.collectStreamMessages();
    
    if (streamMessages.length === 0) {
      uiComponents.showNotification('没有可导出的流式数据', 'warning');
      return;
    }

    const dialogContent = `
      <div class="export-dialog">
        <div class="export-options">
          <h4>导出选项</h4>
          
          <div class="form-group">
            <label for="export-format">导出格式:</label>
            <select id="export-format" class="form-control">
              <option value="json">JSON 格式</option>
              <option value="csv">CSV 格式</option>
              <option value="txt">纯文本格式</option>
              <option value="html">HTML 格式</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="export-filter">内容过滤:</label>
            <select id="export-filter" class="form-control">
              <option value="all">所有消息</option>
              <option value="text">仅文本消息</option>
              <option value="progress">仅进度消息</option>
              <option value="card">仅卡片消息</option>
              <option value="thinking">仅思考消息</option>
              <option value="error">仅错误消息</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" id="include-metadata" checked>
              包含元数据（时间戳、类型等）
            </label>
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" id="include-styling" checked>
              保留样式信息
            </label>
          </div>
          
          <div class="export-preview">
            <h5>数据预览:</h5>
            <div class="preview-stats">
              <span>总消息数: ${streamMessages.length}</span>
              <span>数据大小: ${this.calculateDataSize(streamMessages)}</span>
            </div>
          </div>
        </div>
      </div>
    `;

    uiComponents.showModal('导出流式数据', dialogContent, {
      confirmText: '导出',
      onConfirm: () => {
        this.performExport(streamMessages);
      }
    });
  }

  /**
   * 收集流式消息数据
   * @returns {Array} 消息数据数组
   */
  collectStreamMessages() {
    const streamMessages = document.getElementById('stream-messages');
    if (!streamMessages) return [];

    const messages = [];
    const messageElements = streamMessages.querySelectorAll('.sse-message, .stream-message');

    messageElements.forEach((element, index) => {
      const messageData = {
        id: index + 1,
        timestamp: element.getAttribute('data-timestamp') || Date.now(),
        type: element.getAttribute('data-message-type') || 'unknown',
        content: this.extractMessageContent(element),
        metadata: this.extractMessageMetadata(element)
      };

      messages.push(messageData);
    });

    return messages;
  }

  /**
   * 提取消息内容
   * @param {Element} element - 消息元素
   * @returns {string} 消息内容
   */
  extractMessageContent(element) {
    const contentElement = element.querySelector('.sse-message-content, .message-content');
    return contentElement ? contentElement.textContent.trim() : '';
  }

  /**
   * 提取消息元数据
   * @param {Element} element - 消息元素
   * @returns {object} 元数据对象
   */
  extractMessageMetadata(element) {
    return {
      messageId: element.getAttribute('data-message-id'),
      contentType: element.getAttribute('data-content-type'),
      className: element.className,
      size: element.textContent.length
    };
  }

  /**
   * 执行导出
   * @param {Array} messages - 消息数据
   */
  performExport(messages) {
    const format = document.getElementById('export-format')?.value || 'json';
    const filter = document.getElementById('export-filter')?.value || 'all';
    const includeMetadata = document.getElementById('include-metadata')?.checked ?? true;
    const includeStyling = document.getElementById('include-styling')?.checked ?? true;

    // 过滤消息
    const filteredMessages = this.filterMessages(messages, filter);
    
    if (filteredMessages.length === 0) {
      uiComponents.showNotification('没有符合过滤条件的消息', 'warning');
      return;
    }

    // 处理消息数据
    const processedMessages = this.processMessages(filteredMessages, includeMetadata, includeStyling);

    // 根据格式导出
    try {
      switch (format) {
        case 'json':
          this.exportAsJSON(processedMessages);
          break;
        case 'csv':
          this.exportAsCSV(processedMessages);
          break;
        case 'txt':
          this.exportAsText(processedMessages);
          break;
        case 'html':
          this.exportAsHTML(processedMessages);
          break;
        default:
          throw new Error('不支持的导出格式');
      }

      uiComponents.showNotification(`成功导出 ${filteredMessages.length} 条消息`, 'success');
    } catch (error) {
      console.error('导出失败:', error);
      uiComponents.showNotification('导出失败: ' + error.message, 'error');
    }
  }

  /**
   * 过滤消息
   * @param {Array} messages - 原始消息
   * @param {string} filter - 过滤条件
   * @returns {Array} 过滤后的消息
   */
  filterMessages(messages, filter) {
    if (filter === 'all') return messages;

    return messages.filter(message => {
      switch (filter) {
        case 'text':
          return message.type === 'text';
        case 'progress':
          return message.type === 'progress';
        case 'card':
          return message.type === 'card';
        case 'thinking':
          return message.type === 'thinking';
        case 'error':
          return message.type === 'error';
        default:
          return true;
      }
    });
  }

  /**
   * 处理消息数据
   * @param {Array} messages - 消息数组
   * @param {boolean} includeMetadata - 是否包含元数据
   * @param {boolean} includeStyling - 是否包含样式信息
   * @returns {Array} 处理后的消息
   */
  processMessages(messages, includeMetadata, includeStyling) {
    return messages.map(message => {
      const processed = {
        id: message.id,
        content: message.content,
        timestamp: new Date(message.timestamp).toISOString(),
        type: message.type
      };

      if (includeMetadata) {
        processed.metadata = message.metadata;
      }

      if (!includeStyling) {
        delete processed.metadata?.className;
      }

      return processed;
    });
  }

  /**
   * 导出为 JSON 格式
   * @param {Array} messages - 消息数据
   */
  exportAsJSON(messages) {
    const jsonData = {
      exportTime: new Date().toISOString(),
      totalMessages: messages.length,
      messages: messages
    };

    const jsonString = JSON.stringify(jsonData, null, 2);
    this.downloadFile(jsonString, 'stream-data.json', 'application/json');
  }

  /**
   * 导出为 CSV 格式
   * @param {Array} messages - 消息数据
   */
  exportAsCSV(messages) {
    const headers = ['ID', '时间戳', '类型', '内容', '大小'];
    const csvRows = [headers.join(',')];

    messages.forEach(message => {
      const row = [
        message.id,
        message.timestamp,
        message.type,
        `"${message.content.replace(/"/g, '""')}"`, // 转义双引号
        message.metadata?.size || 0
      ];
      csvRows.push(row.join(','));
    });

    const csvString = csvRows.join('\n');
    this.downloadFile(csvString, 'stream-data.csv', 'text/csv');
  }

  /**
   * 导出为纯文本格式
   * @param {Array} messages - 消息数据
   */
  exportAsText(messages) {
    const lines = [
      '流式响应数据导出',
      `导出时间: ${new Date().toLocaleString()}`,
      `消息总数: ${messages.length}`,
      '=' .repeat(50),
      ''
    ];

    messages.forEach(message => {
      lines.push(`[${message.id}] ${new Date(message.timestamp).toLocaleString()}`);
      lines.push(`类型: ${message.type}`);
      lines.push(`内容: ${message.content}`);
      lines.push('-'.repeat(30));
      lines.push('');
    });

    const textString = lines.join('\n');
    this.downloadFile(textString, 'stream-data.txt', 'text/plain');
  }

  /**
   * 导出为 HTML 格式
   * @param {Array} messages - 消息数据
   */
  exportAsHTML(messages) {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应数据导出</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
        .message { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .message-header { font-weight: bold; color: #666; margin-bottom: 10px; }
        .message-content { white-space: pre-wrap; }
        .type-text { border-left: 4px solid #28a745; }
        .type-progress { border-left: 4px solid #17a2b8; }
        .type-card { border-left: 4px solid #007bff; }
        .type-thinking { border-left: 4px solid #ffc107; }
        .type-error { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>流式响应数据导出</h1>
        <p>导出时间: ${new Date().toLocaleString()}</p>
        <p>消息总数: ${messages.length}</p>
    </div>
    
    <div class="messages">
        ${messages.map(message => `
            <div class="message type-${message.type}">
                <div class="message-header">
                    [${message.id}] ${new Date(message.timestamp).toLocaleString()} - ${message.type}
                </div>
                <div class="message-content">${this.escapeHtml(message.content)}</div>
            </div>
        `).join('')}
    </div>
</body>
</html>`;

    this.downloadFile(html, 'stream-data.html', 'text/html');
  }

  /**
   * 转义 HTML 字符
   * @param {string} text - 原始文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 下载文件
   * @param {string} content - 文件内容
   * @param {string} filename - 文件名
   * @param {string} mimeType - MIME 类型
   */
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 清理 URL 对象
    setTimeout(() => URL.revokeObjectURL(url), 100);
  }

  /**
   * 计算数据大小
   * @param {Array} messages - 消息数组
   * @returns {string} 格式化的数据大小
   */
  calculateDataSize(messages) {
    const totalBytes = messages.reduce((sum, message) => {
      return sum + (message.content?.length || 0);
    }, 0);

    return this.formatBytes(totalBytes);
  }

  /**
   * 格式化字节数
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 导出类
if (typeof window !== 'undefined') {
  window.StreamExporter = StreamExporter;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = StreamExporter;
}
