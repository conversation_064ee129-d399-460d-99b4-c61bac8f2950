/**
 * 三色数据处理增强脚本
 * 专门用于处理和显示当贝AI的三色数据（progress/thinking/text/card）
 * 提供更好的错误处理、数据验证和用户体验
 */

class ThreeColorDataHandler {
  constructor() {
    this.messageStats = {
      progress: 0,
      thinking: 0,
      text: 0,
      card: 0,
      unknown: 0,
      total: 0
    };
    this.isMonitoring = false;
    this.startTime = null;
    this.messageHistory = [];
    this.maxHistorySize = 1000;
  }

  /**
   * 开始监控三色数据
   */
  startMonitoring() {
    this.isMonitoring = true;
    this.startTime = Date.now();
    this.resetStats();
    console.log('[三色数据处理] 开始监控三色数据');
    this.updateMonitoringUI();
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('[三色数据处理] 停止监控');
    this.generateReport();
    this.updateMonitoringUI();
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    Object.keys(this.messageStats).forEach(key => {
      this.messageStats[key] = 0;
    });
    this.messageHistory = [];
  }

  /**
   * 处理SSE消息
   * @param {Object} data - SSE消息数据
   * @param {HTMLElement} container - 显示容器
   */
  handleSSEMessage(data, container) {
    if (!data || typeof data !== 'object') {
      console.warn('[三色数据处理] 无效的消息数据:', data);
      return;
    }

    // 验证消息完整性
    const validation = this.validateMessage(data);
    if (!validation.isValid) {
      console.error('[三色数据处理] 消息验证失败:', validation.errors);
      this.renderErrorMessage(validation.errors, container);
      return;
    }

    // 记录统计信息
    this.recordMessage(data);

    // 渲染消息
    this.renderEnhancedMessage(data, container);

    // 更新实时统计显示
    this.updateStatsDisplay();
  }

  /**
   * 验证消息数据
   * @param {Object} data - 消息数据
   * @returns {Object} 验证结果
   */
  validateMessage(data) {
    const errors = [];
    const warnings = [];

    // 检查必要字段
    const requiredFields = ['content', 'content_type'];
    requiredFields.forEach(field => {
      if (!data[field]) {
        errors.push(`缺少必要字段: ${field}`);
      }
    });

    // 检查content_type是否为已知类型
    const knownTypes = ['progress', 'thinking', 'text', 'card'];
    if (data.content_type && !knownTypes.includes(data.content_type)) {
      warnings.push(`未知的content_type: ${data.content_type}`);
    }

    // 检查内容长度
    if (data.content && data.content.length === 0) {
      warnings.push('消息内容为空');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 记录消息统计
   * @param {Object} data - 消息数据
   */
  recordMessage(data) {
    if (!this.isMonitoring) return;

    const contentType = data.content_type || 'unknown';
    
    // 更新统计
    if (this.messageStats.hasOwnProperty(contentType)) {
      this.messageStats[contentType]++;
    } else {
      this.messageStats.unknown++;
    }
    this.messageStats.total++;

    // 记录到历史
    this.messageHistory.push({
      timestamp: Date.now(),
      contentType,
      contentLength: data.content ? data.content.length : 0,
      messageId: data.id,
      preview: data.content ? data.content.substring(0, 100) : ''
    });

    // 限制历史记录大小
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
    }

    // 输出调试信息
    this.logMessage(data);
  }

  /**
   * 输出消息日志
   * @param {Object} data - 消息数据
   */
  logMessage(data) {
    const icons = {
      progress: '🔍',
      thinking: '🤔',
      text: '💬',
      card: '📋',
      unknown: '❓'
    };

    const icon = icons[data.content_type] || '❓';
    const preview = data.content ? data.content.substring(0, 50) + '...' : '';
    
    console.log(`[三色数据处理] ${icon} ${data.content_type?.toUpperCase()} - ${preview}`, {
      messageId: data.id,
      contentLength: data.content?.length || 0,
      timestamp: new Date().toLocaleTimeString()
    });
  }

  /**
   * 渲染增强的消息
   * @param {Object} data - 消息数据
   * @param {HTMLElement} container - 容器元素
   */
  renderEnhancedMessage(data, container) {
    const messageElement = document.createElement('div');
    messageElement.className = `sse-message enhanced-message ${this.getContentTypeClass(data.content_type)}`;
    messageElement.setAttribute('data-content-type', data.content_type);
    messageElement.setAttribute('data-message-id', data.id || '');

    // 创建消息头部
    const headerElement = this.createMessageHeader(data);
    messageElement.appendChild(headerElement);

    // 创建消息内容
    const contentElement = this.createMessageContent(data);
    messageElement.appendChild(contentElement);

    // 添加调试信息（仅在调试模式下）
    if (this.isDebugMode()) {
      const debugElement = this.createDebugInfo(data);
      messageElement.appendChild(debugElement);
    }

    // 添加到容器
    container.appendChild(messageElement);

    // 自动滚动到底部
    this.scrollToBottom(container);
  }

  /**
   * 创建消息头部
   * @param {Object} data - 消息数据
   * @returns {HTMLElement} 头部元素
   */
  createMessageHeader(data) {
    const headerElement = document.createElement('div');
    headerElement.className = 'sse-message-header enhanced-header';

    // 图标
    const iconElement = document.createElement('span');
    iconElement.className = 'sse-message-icon';
    iconElement.textContent = this.getContentTypeIcon(data.content_type);

    // 类型标签
    const typeElement = document.createElement('span');
    typeElement.className = 'sse-message-type';
    typeElement.textContent = this.getContentTypeLabel(data.content_type);

    // 时间戳
    const timestampElement = document.createElement('span');
    timestampElement.className = 'sse-message-timestamp';
    timestampElement.textContent = this.formatTimestamp(data.created_at || Date.now());

    // 消息长度指示器
    const lengthElement = document.createElement('span');
    lengthElement.className = 'sse-message-length';
    lengthElement.textContent = `${data.content?.length || 0} 字符`;

    headerElement.appendChild(iconElement);
    headerElement.appendChild(typeElement);
    headerElement.appendChild(timestampElement);
    headerElement.appendChild(lengthElement);

    return headerElement;
  }

  /**
   * 创建消息内容
   * @param {Object} data - 消息数据
   * @returns {HTMLElement} 内容元素
   */
  createMessageContent(data) {
    const contentElement = document.createElement('div');
    contentElement.className = 'sse-message-content enhanced-content';

    if (data.content_type === 'card') {
      // 特殊处理card类型
      this.renderCardContent(data.content, contentElement);
    } else {
      // 普通文本内容
      contentElement.textContent = data.content || '';
    }

    return contentElement;
  }

  /**
   * 渲染卡片内容
   * @param {string} content - 内容字符串
   * @param {HTMLElement} container - 容器元素
   */
  renderCardContent(content, container) {
    try {
      const cardData = JSON.parse(content);
      const formattedContent = JSON.stringify(cardData, null, 2);
      
      const preElement = document.createElement('pre');
      preElement.className = 'card-content-json';
      preElement.textContent = formattedContent;
      
      container.appendChild(preElement);
    } catch (error) {
      // JSON解析失败，显示原始内容
      container.textContent = content;
      console.warn('[三色数据处理] Card内容JSON解析失败:', error);
    }
  }

  /**
   * 创建调试信息
   * @param {Object} data - 消息数据
   * @returns {HTMLElement} 调试信息元素
   */
  createDebugInfo(data) {
    const debugElement = document.createElement('div');
    debugElement.className = 'sse-message-debug';
    debugElement.innerHTML = `
      <details>
        <summary>调试信息</summary>
        <pre>${JSON.stringify(data, null, 2)}</pre>
      </details>
    `;
    return debugElement;
  }

  /**
   * 渲染错误消息
   * @param {Array} errors - 错误列表
   * @param {HTMLElement} container - 容器元素
   */
  renderErrorMessage(errors, container) {
    const errorElement = document.createElement('div');
    errorElement.className = 'sse-message error-message';
    
    const headerElement = document.createElement('div');
    headerElement.className = 'sse-message-header';
    headerElement.innerHTML = `
      <span class="sse-message-icon">❌</span>
      <span class="sse-message-type">数据错误</span>
      <span class="sse-message-timestamp">${new Date().toLocaleTimeString()}</span>
    `;
    
    const contentElement = document.createElement('div');
    contentElement.className = 'sse-message-content';
    contentElement.innerHTML = `
      <strong>消息处理错误:</strong><br>
      ${errors.map(error => `• ${error}`).join('<br>')}
    `;
    
    errorElement.appendChild(headerElement);
    errorElement.appendChild(contentElement);
    container.appendChild(errorElement);
  }

  /**
   * 获取content_type对应的CSS类名
   */
  getContentTypeClass(contentType) {
    const typeMap = {
      progress: 'progress-message',
      thinking: 'thinking-message', 
      text: 'text-message',
      card: 'card-message'
    };
    return typeMap[contentType] || 'unknown-message';
  }

  /**
   * 获取content_type对应的图标
   */
  getContentTypeIcon(contentType) {
    const iconMap = {
      progress: '🔍',
      thinking: '🤔',
      text: '💬',
      card: '📋'
    };
    return iconMap[contentType] || '❓';
  }

  /**
   * 获取content_type对应的中文标签
   */
  getContentTypeLabel(contentType) {
    const labelMap = {
      progress: '联网搜索',
      thinking: '思考过程',
      text: '正式回答',
      card: '搜索结果'
    };
    return labelMap[contentType] || '未知类型';
  }

  /**
   * 格式化时间戳
   */
  formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  }

  /**
   * 滚动到底部
   */
  scrollToBottom(container) {
    setTimeout(() => {
      container.scrollTop = container.scrollHeight;
    }, 100);
  }

  /**
   * 检查是否为调试模式
   */
  isDebugMode() {
    return localStorage.getItem('three-color-debug') === 'true' || 
           new URLSearchParams(window.location.search).has('debug');
  }

  /**
   * 更新统计显示
   */
  updateStatsDisplay() {
    const statsElement = document.getElementById('three-color-stats');
    if (!statsElement) return;

    const duration = this.startTime ? (Date.now() - this.startTime) / 1000 : 0;
    
    statsElement.innerHTML = `
      <div class="stats-summary">
        <h4>三色数据统计 (${duration.toFixed(1)}s)</h4>
        <div class="stats-grid">
          <div class="stat-item progress">🔍 联网搜索: ${this.messageStats.progress}</div>
          <div class="stat-item thinking">🤔 思考过程: ${this.messageStats.thinking}</div>
          <div class="stat-item text">💬 正式回答: ${this.messageStats.text}</div>
          <div class="stat-item card">📋 搜索结果: ${this.messageStats.card}</div>
          <div class="stat-item unknown">❓ 未知类型: ${this.messageStats.unknown}</div>
          <div class="stat-item total">📊 总计: ${this.messageStats.total}</div>
        </div>
      </div>
    `;
  }

  /**
   * 更新监控UI状态
   */
  updateMonitoringUI() {
    const monitorButton = document.getElementById('toggle-monitoring');
    if (monitorButton) {
      monitorButton.textContent = this.isMonitoring ? '停止监控' : '开始监控';
      monitorButton.className = this.isMonitoring ? 'btn btn-danger' : 'btn btn-success';
    }
  }

  /**
   * 生成统计报告
   */
  generateReport() {
    if (!this.isMonitoring && this.messageStats.total === 0) return;

    console.log('\n=== 三色数据统计报告 ===');
    console.log(`监控时长: ${this.startTime ? ((Date.now() - this.startTime) / 1000).toFixed(2) : 0} 秒`);
    console.log(`消息总数: ${this.messageStats.total}`);
    
    Object.keys(this.messageStats).forEach(type => {
      if (type !== 'total' && this.messageStats[type] > 0) {
        const percentage = ((this.messageStats[type] / this.messageStats.total) * 100).toFixed(1);
        console.log(`${type}: ${this.messageStats[type]} (${percentage}%)`);
      }
    });

    // 检查是否有缺失的数据类型
    const expectedTypes = ['progress', 'thinking', 'text', 'card'];
    const missingTypes = expectedTypes.filter(type => this.messageStats[type] === 0);
    
    if (missingTypes.length > 0) {
      console.warn('⚠️ 缺失的数据类型:', missingTypes);
    }

    if (this.messageStats.unknown > 0) {
      console.warn('⚠️ 发现未知类型的消息:', this.messageStats.unknown);
    }
  }
}

// 创建全局实例
window.threeColorDataHandler = new ThreeColorDataHandler();

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThreeColorDataHandler;
}
