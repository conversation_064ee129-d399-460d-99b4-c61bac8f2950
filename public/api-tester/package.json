{"name": "api-tester", "version": "1.0.0", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "jest --config tests/jest.config.js", "test:watch": "jest --config tests/jest.config.js --watch", "test:coverage": "jest --config tests/jest.config.js --coverage", "test:verbose": "jest --config tests/jest.config.js --verbose"}, "keywords": ["sse", "server-sent-events", "api-testing", "dangbei", "ai", "streaming"], "author": "Dangbei AI Team", "license": "ISC", "description": "当贝AI Provider HTTP API 测试工具，支持SSE样式区分功能", "devDependencies": {"jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0"}}