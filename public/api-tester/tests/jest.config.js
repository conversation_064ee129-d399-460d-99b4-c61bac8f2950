/**
 * Jest 测试配置文件
 * 用于 SSE 样式区分功能的单元测试和集成测试
 */

module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',
  
  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // 忽略的文件和目录
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ],
  
  // 覆盖率收集
  collectCoverage: true,
  collectCoverageFrom: [
    '../js/**/*.js',
    '!../js/utils.js', // 排除工具函数
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  
  // 覆盖率输出目录
  coverageDirectory: './coverage',
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/setup.js'],
  
  // 模块名称映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../$1'
  },
  
  // 测试超时时间（毫秒）
  testTimeout: 10000,
  
  // 详细输出
  verbose: true,
  
  // 在测试失败时显示错误详情
  errorOnDeprecated: true,
  
  // 清除模拟调用和实例
  clearMocks: true,
  
  // 每次测试前重置模拟状态
  resetMocks: true,
  
  // 每次测试前恢复模拟状态
  restoreMocks: true
};
