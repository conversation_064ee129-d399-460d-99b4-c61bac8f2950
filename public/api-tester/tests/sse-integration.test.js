/**
 * SSE 样式区分功能集成测试
 * 测试完整的消息渲染流程和DOM操作
 */

const { TestApp, testMessages } = require('./sse-styling.test.js');

describe('SSE 样式区分集成测试', () => {
  let app;
  let container;

  beforeEach(() => {
    // 设置DOM结构
    document.body.innerHTML = `
      <div id="stream-messages" class="stream-messages"></div>
      <input type="checkbox" id="enable-sse-styling" checked>
    `;

    app = new TestApp();
    container = document.getElementById('stream-messages');
  });

  describe('消息渲染测试', () => {
    test('应该正确渲染progress类型消息', () => {
      const messageElement = app.renderStyledSSEMessage(testMessages.progress, container);
      
      // 检查基本结构
      expect(messageElement).toBeTruthy();
      expect(messageElement.classList.contains('sse-message')).toBe(true);
      expect(messageElement.classList.contains('progress')).toBe(true);
      
      // 检查头部元素
      const header = messageElement.querySelector('.sse-message-header');
      expect(header).toBeTruthy();
      
      // 检查图标
      const icon = header.querySelector('.sse-message-icon');
      expect(icon.textContent).toBe('🔍');
      
      // 检查类型标签
      const type = header.querySelector('.sse-message-type');
      expect(type.textContent).toBe('联网搜索');
      
      // 检查内容
      const content = messageElement.querySelector('.sse-message-content');
      expect(content.textContent).toBe(testMessages.progress.content);
    });

    test('应该正确渲染card类型消息', () => {
      const messageElement = app.renderStyledSSEMessage(testMessages.card, container);
      
      expect(messageElement.classList.contains('card')).toBe(true);
      
      const icon = messageElement.querySelector('.sse-message-icon');
      expect(icon.textContent).toBe('📋');
      
      const type = messageElement.querySelector('.sse-message-type');
      expect(type.textContent).toBe('搜索结果');
      
      // card类型应该格式化JSON内容
      const content = messageElement.querySelector('.sse-message-content');
      expect(content.textContent).toContain('cardType');
      expect(content.textContent).toContain('DB-CARD-2');
    });

    test('应该正确渲染thinking类型消息', () => {
      const messageElement = app.renderStyledSSEMessage(testMessages.thinking, container);
      
      expect(messageElement.classList.contains('thinking')).toBe(true);
      
      const icon = messageElement.querySelector('.sse-message-icon');
      expect(icon.textContent).toBe('🤔');
      
      const type = messageElement.querySelector('.sse-message-type');
      expect(type.textContent).toBe('思考过程');
    });

    test('应该正确渲染text类型消息', () => {
      const messageElement = app.renderStyledSSEMessage(testMessages.text, container);
      
      expect(messageElement.classList.contains('text')).toBe(true);
      
      const icon = messageElement.querySelector('.sse-message-icon');
      expect(icon.textContent).toBe('💬');
      
      const type = messageElement.querySelector('.sse-message-type');
      expect(type.textContent).toBe('正式回答');
    });
  });

  describe('消息处理流程测试', () => {
    test('启用样式时应该渲染样式化消息', () => {
      // 确保样式开关启用
      const checkbox = document.getElementById('enable-sse-styling');
      checkbox.checked = true;
      
      const messageElement = app.handleStreamMessage(testMessages.progress);
      
      expect(messageElement).toBeTruthy();
      expect(messageElement.classList.contains('sse-message')).toBe(true);
      expect(container.children.length).toBe(1);
    });

    test('禁用样式时应该返回null', () => {
      // 禁用样式开关
      const checkbox = document.getElementById('enable-sse-styling');
      checkbox.checked = false;
      
      const messageElement = app.handleStreamMessage(testMessages.progress);
      
      expect(messageElement).toBeNull();
    });

    test('无content_type字段时应该返回null', () => {
      const invalidMessage = {
        role: "assistant",
        content: "测试消息",
        // 缺少 content_type 字段
      };
      
      const messageElement = app.handleStreamMessage(invalidMessage);
      expect(messageElement).toBeNull();
    });

    test('非对象类型数据应该返回null', () => {
      const stringMessage = "这是一个字符串消息";
      const messageElement = app.handleStreamMessage(stringMessage);
      expect(messageElement).toBeNull();
    });
  });

  describe('多消息渲染测试', () => {
    test('应该能够渲染多个不同类型的消息', () => {
      // 渲染所有测试消息
      Object.values(testMessages).forEach(message => {
        app.renderStyledSSEMessage(message, container);
      });
      
      // 检查容器中的消息数量
      expect(container.children.length).toBe(4);
      
      // 检查每个消息的类型
      const messages = Array.from(container.children);
      expect(messages[0].classList.contains('progress')).toBe(true);
      expect(messages[1].classList.contains('card')).toBe(true);
      expect(messages[2].classList.contains('thinking')).toBe(true);
      expect(messages[3].classList.contains('text')).toBe(true);
    });

    test('消息应该按顺序添加到容器中', () => {
      const messageIds = [];
      
      Object.values(testMessages).forEach(message => {
        const element = app.renderStyledSSEMessage(message, container);
        messageIds.push(element.getAttribute('data-message-id'));
      });
      
      // 检查消息ID是否递增
      expect(messageIds).toEqual(['1', '2', '3', '4']);
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空内容', () => {
      const emptyMessage = {
        ...testMessages.text,
        content: ''
      };
      
      const messageElement = app.renderStyledSSEMessage(emptyMessage, container);
      const content = messageElement.querySelector('.sse-message-content');
      
      // 空内容时应该显示JSON字符串
      expect(content.textContent).toContain('"content": ""');
    });

    test('应该处理无效的JSON内容（card类型）', () => {
      const invalidJsonMessage = {
        ...testMessages.card,
        content: '这不是有效的JSON'
      };
      
      const messageElement = app.renderStyledSSEMessage(invalidJsonMessage, container);
      const content = messageElement.querySelector('.sse-message-content');
      
      // 无效JSON时应该显示原始内容
      expect(content.textContent).toBe('这不是有效的JSON');
    });

    test('应该处理缺少时间戳的消息', () => {
      const noTimestampMessage = {
        ...testMessages.text
      };
      delete noTimestampMessage.created_at;
      
      const messageElement = app.renderStyledSSEMessage(noTimestampMessage, container);
      const timestamp = messageElement.querySelector('.sse-message-timestamp');
      
      // 应该使用当前时间
      expect(timestamp.textContent).toMatch(/\d{2}:\d{2}:\d{2}/);
    });

    test('应该处理null或undefined的content_type', () => {
      const nullTypeMessage = {
        ...testMessages.text,
        content_type: null
      };
      
      const messageElement = app.renderStyledSSEMessage(nullTypeMessage, container);
      expect(messageElement.classList.contains('unknown')).toBe(true);
    });
  });

  describe('DOM操作测试', () => {
    test('消息应该正确添加到容器中', () => {
      expect(container.children.length).toBe(0);
      
      app.renderStyledSSEMessage(testMessages.progress, container);
      expect(container.children.length).toBe(1);
      
      app.renderStyledSSEMessage(testMessages.text, container);
      expect(container.children.length).toBe(2);
    });

    test('每个消息都应该有唯一的data-message-id', () => {
      app.renderStyledSSEMessage(testMessages.progress, container);
      app.renderStyledSSEMessage(testMessages.text, container);
      
      const messages = Array.from(container.children);
      const ids = messages.map(msg => msg.getAttribute('data-message-id'));
      
      expect(ids).toEqual(['1', '2']);
      expect(new Set(ids).size).toBe(2); // 确保ID唯一
    });

    test('消息结构应该完整', () => {
      const messageElement = app.renderStyledSSEMessage(testMessages.progress, container);
      
      // 检查必要的子元素
      expect(messageElement.querySelector('.sse-message-header')).toBeTruthy();
      expect(messageElement.querySelector('.sse-message-icon')).toBeTruthy();
      expect(messageElement.querySelector('.sse-message-type')).toBeTruthy();
      expect(messageElement.querySelector('.sse-message-timestamp')).toBeTruthy();
      expect(messageElement.querySelector('.sse-message-content')).toBeTruthy();
    });
  });
});

// 性能测试
describe('SSE 样式区分性能测试', () => {
  let app;
  let container;

  beforeEach(() => {
    // 设置DOM结构
    document.body.innerHTML = `
      <div id="stream-messages" class="stream-messages"></div>
      <input type="checkbox" id="enable-sse-styling" checked>
    `;

    app = new TestApp();
    container = document.getElementById('stream-messages');
  });

  test('应该能够快速渲染大量消息', () => {
    const startTime = performance.now();
    
    // 渲染100个消息
    for (let i = 0; i < 100; i++) {
      const message = {
        ...testMessages.text,
        id: `test-${i}`,
        content: `测试消息 ${i}`
      };
      app.renderStyledSSEMessage(message, container);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // 渲染100个消息应该在100ms内完成
    expect(duration).toBeLessThan(100);
    expect(container.children.length).toBe(100);
  });
});

module.exports = { TestApp, testMessages };
