/**
 * 增强流式功能测试
 * 测试新增的流式客户端、监控器、导出器和过滤器功能
 */

describe('增强流式功能测试', () => {
  let mockContainer;
  let mockStreamMessages;

  beforeEach(() => {
    // 设置测试DOM环境
    document.body.innerHTML = `
      <div class="stream-viewer">
        <div class="stream-toolbar">
          <button id="start-stream" class="btn btn-small">开始流式请求</button>
          <button id="stop-stream" class="btn btn-small btn-secondary">停止</button>
          <button id="clear-stream" class="btn btn-small btn-secondary">清空</button>
          <label class="stream-option">
            <input type="checkbox" id="enable-sse-styling" checked>
            启用样式区分
          </label>
          <span class="stream-status" id="stream-status">未连接</span>
        </div>
        <div class="stream-content">
          <div id="stream-messages" class="stream-messages"></div>
        </div>
      </div>
      <div id="test-monitor-container"></div>
      <div id="test-filter-container"></div>
    `;

    mockContainer = document.getElementById('test-monitor-container');
    mockStreamMessages = document.getElementById('stream-messages');
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  describe('EnhancedStreamClient 测试', () => {
    let client;

    beforeEach(() => {
      client = new EnhancedStreamClient({
        connectionTimeout: 5000,
        maxRetries: 2,
        enableMetrics: true
      });
    });

    afterEach(() => {
      if (client) {
        client.disconnect();
      }
    });

    test('应该正确初始化客户端', () => {
      expect(client).toBeDefined();
      expect(client.getState()).toBe('disconnected');
      expect(client.config.connectionTimeout).toBe(5000);
      expect(client.config.maxRetries).toBe(2);
    });

    test('应该正确更新配置', () => {
      const newConfig = {
        connectionTimeout: 10000,
        maxRetries: 5
      };

      client.updateConfig(newConfig);
      
      expect(client.config.connectionTimeout).toBe(10000);
      expect(client.config.maxRetries).toBe(5);
    });

    test('应该正确获取性能指标', () => {
      const metrics = client.getMetrics();
      
      expect(metrics).toHaveProperty('connectionTime');
      expect(metrics).toHaveProperty('totalMessages');
      expect(metrics).toHaveProperty('totalBytes');
      expect(metrics).toHaveProperty('messagesPerSecond');
      expect(metrics).toHaveProperty('errors');
    });

    test('应该正确创建流式错误对象', () => {
      const originalError = new Error('测试错误');
      const streamError = client.createStreamError('network_error', originalError);
      
      expect(streamError.type).toBe('network_error');
      expect(streamError.message).toBe('测试错误');
      expect(streamError).toHaveProperty('timestamp');
      expect(streamError.originalError).toBe(originalError);
    });
  });

  describe('StreamMonitor 测试', () => {
    let monitor;

    beforeEach(() => {
      monitor = new StreamMonitor('test-monitor-container');
    });

    afterEach(() => {
      if (monitor) {
        monitor.destroy();
      }
    });

    test('应该正确初始化监控器', () => {
      expect(monitor).toBeDefined();
      expect(mockContainer.querySelector('.stream-monitor')).toBeTruthy();
    });

    test('应该正确更新连接状态', () => {
      monitor.updateConnectionState('connected');
      
      const statusIndicator = mockContainer.querySelector('.status-indicator');
      const statusText = mockContainer.querySelector('.status-text');
      
      expect(statusIndicator.classList.contains('connected')).toBe(true);
      expect(statusText.textContent).toBe('已连接');
    });

    test('应该正确记录消息', () => {
      const messageData = {
        data: { content: '测试消息' },
        size: 100,
        timestamp: Date.now()
      };

      monitor.recordMessage(messageData);
      
      expect(monitor.data.totalMessages).toBe(1);
      expect(monitor.data.totalBytes).toBe(100);
    });

    test('应该正确记录错误', () => {
      const error = {
        type: 'network_error',
        message: '网络连接失败'
      };

      monitor.recordError(error);
      
      expect(monitor.data.errors).toBe(1);
      expect(monitor.data.errorHistory.length).toBe(1);
      expect(monitor.data.errorHistory[0].type).toBe('network_error');
    });

    test('应该正确格式化时长', () => {
      const duration1 = monitor.formatDuration(65000); // 1分5秒
      const duration2 = monitor.formatDuration(3665000); // 1小时1分5秒
      
      expect(duration1).toBe('00:01:05');
      expect(duration2).toBe('01:01:05');
    });

    test('应该正确格式化字节数', () => {
      expect(monitor.formatBytes(0)).toBe('0 B');
      expect(monitor.formatBytes(1024)).toBe('1 KB');
      expect(monitor.formatBytes(1048576)).toBe('1 MB');
      expect(monitor.formatBytes(1536)).toBe('1.5 KB');
    });
  });

  describe('StreamExporter 测试', () => {
    let exporter;

    beforeEach(() => {
      // 添加一些测试消息到DOM
      mockStreamMessages.innerHTML = `
        <div class="sse-message text" data-message-type="text" data-timestamp="1640995200000">
          <div class="sse-message-content">这是一条文本消息</div>
        </div>
        <div class="sse-message progress" data-message-type="progress" data-timestamp="1640995260000">
          <div class="sse-message-content">搜索进度: 50%</div>
        </div>
        <div class="sse-message error" data-message-type="error" data-timestamp="1640995320000">
          <div class="sse-message-content">发生了一个错误</div>
        </div>
      `;

      exporter = new StreamExporter();
    });

    test('应该正确收集流式消息', () => {
      const messages = exporter.collectStreamMessages();
      
      expect(messages.length).toBe(3);
      expect(messages[0].type).toBe('text');
      expect(messages[0].content).toBe('这是一条文本消息');
      expect(messages[1].type).toBe('progress');
      expect(messages[2].type).toBe('error');
    });

    test('应该正确过滤消息', () => {
      const allMessages = exporter.collectStreamMessages();
      
      const textMessages = exporter.filterMessages(allMessages, 'text');
      const errorMessages = exporter.filterMessages(allMessages, 'error');
      
      expect(textMessages.length).toBe(1);
      expect(textMessages[0].type).toBe('text');
      
      expect(errorMessages.length).toBe(1);
      expect(errorMessages[0].type).toBe('error');
    });

    test('应该正确处理消息数据', () => {
      const messages = exporter.collectStreamMessages();
      const processed = exporter.processMessages(messages, true, true);
      
      expect(processed.length).toBe(3);
      expect(processed[0]).toHaveProperty('id');
      expect(processed[0]).toHaveProperty('content');
      expect(processed[0]).toHaveProperty('timestamp');
      expect(processed[0]).toHaveProperty('type');
      expect(processed[0]).toHaveProperty('metadata');
    });

    test('应该正确计算数据大小', () => {
      const messages = exporter.collectStreamMessages();
      const size = exporter.calculateDataSize(messages);
      
      expect(size).toMatch(/\d+(\.\d+)?\s(B|KB|MB|GB)/);
    });

    test('应该正确转义HTML字符', () => {
      const html = '<script>alert("test")</script>';
      const escaped = exporter.escapeHtml(html);
      
      expect(escaped).toBe('&lt;script&gt;alert("test")&lt;/script&gt;');
    });
  });

  describe('StreamFilter 测试', () => {
    let filter;

    beforeEach(() => {
      // 添加测试消息
      mockStreamMessages.innerHTML = `
        <div class="sse-message text" data-timestamp="1640995200000">
          <div class="sse-message-content">这是第一条文本消息</div>
        </div>
        <div class="sse-message progress" data-timestamp="1640995260000">
          <div class="sse-message-content">搜索进度: 50%</div>
        </div>
        <div class="sse-message text" data-timestamp="1640995320000">
          <div class="sse-message-content">这是第二条文本消息</div>
        </div>
        <div class="sse-message error" data-timestamp="1640995380000">
          <div class="sse-message-content">发生了网络错误</div>
        </div>
      `;

      filter = new StreamFilter('test-filter-container');
    });

    afterEach(() => {
      if (filter) {
        filter.destroy();
      }
    });

    test('应该正确初始化过滤器', () => {
      expect(filter).toBeDefined();
      expect(document.getElementById('test-filter-container').querySelector('.stream-filter')).toBeTruthy();
    });

    test('应该正确提取消息内容', () => {
      const messageElement = mockStreamMessages.querySelector('.sse-message');
      const content = filter.extractMessageContent(messageElement);
      
      expect(content).toBe('这是第一条文本消息');
    });

    test('应该正确提取消息类型', () => {
      const textElement = mockStreamMessages.querySelector('.sse-message.text');
      const progressElement = mockStreamMessages.querySelector('.sse-message.progress');
      
      expect(filter.extractMessageType(textElement)).toBe('text');
      expect(filter.extractMessageType(progressElement)).toBe('progress');
    });

    test('应该正确更新消息列表', () => {
      filter.updateMessageList();
      
      expect(filter.allMessages.length).toBe(4);
      expect(filter.allMessages[0].type).toBe('text');
      expect(filter.allMessages[1].type).toBe('progress');
    });

    test('应该正确匹配搜索条件', () => {
      filter.updateMessageList();
      filter.currentFilters.search = '文本';
      filter.currentFilters.caseSensitive = false;
      filter.currentFilters.regexSearch = false;

      const message1 = filter.allMessages[0]; // 包含"文本"
      const message2 = filter.allMessages[1]; // 不包含"文本"

      expect(filter.matchesSearchFilter(message1)).toBe(true);
      expect(filter.matchesSearchFilter(message2)).toBe(false);
    });

    test('应该正确匹配类型条件', () => {
      filter.updateMessageList();
      filter.currentFilters.type = 'text';

      const textMessage = filter.allMessages[0];
      const progressMessage = filter.allMessages[1];

      expect(filter.matchesTypeFilter(textMessage)).toBe(true);
      expect(filter.matchesTypeFilter(progressMessage)).toBe(false);
    });

    test('应该正确应用过滤条件', () => {
      filter.updateMessageList();
      filter.currentFilters.type = 'text';
      filter.applyFilters();

      expect(filter.filteredMessages.length).toBe(2); // 两条文本消息
      
      // 检查消息可见性
      const allMessageElements = mockStreamMessages.querySelectorAll('.sse-message');
      const textElements = mockStreamMessages.querySelectorAll('.sse-message.text');
      const nonTextElements = mockStreamMessages.querySelectorAll('.sse-message:not(.text)');

      textElements.forEach(element => {
        expect(element.style.display).not.toBe('none');
      });

      nonTextElements.forEach(element => {
        expect(element.style.display).toBe('none');
      });
    });

    test('应该正确重置过滤条件', () => {
      filter.currentFilters.search = '测试';
      filter.currentFilters.type = 'error';
      
      filter.resetFilters();
      
      expect(filter.currentFilters.search).toBe('');
      expect(filter.currentFilters.type).toBe('all');
      expect(filter.currentFilters.timeRange).toBe('all');
    });
  });

  describe('集成测试', () => {
    let app;

    beforeEach(() => {
      // 模拟全局对象
      window.uiComponents = {
        showNotification: jest.fn()
      };
      
      window.apiClient = {
        loadHistory: jest.fn(),
        stopStreamRequest: jest.fn()
      };

      app = new ApiTesterApp();
    });

    test('应该正确初始化所有组件', () => {
      expect(app.enhancedStreamClient).toBeDefined();
      expect(app.streamMonitor).toBeDefined();
      expect(app.streamExporter).toBeDefined();
      expect(app.streamFilter).toBeDefined();
    });

    test('应该正确处理流式消息', () => {
      const testData = {
        content_type: 'text',
        content: '测试消息内容',
        created_at: Date.now()
      };

      const mockEvent = {
        data: JSON.stringify(testData)
      };

      const messageElement = app.handleStreamMessage(testData, mockEvent);
      
      expect(messageElement).toBeTruthy();
      expect(mockStreamMessages.children.length).toBeGreaterThan(0);
    });

    test('应该正确处理流式错误', () => {
      const testError = {
        type: 'network_error',
        message: '网络连接失败',
        timestamp: Date.now()
      };

      app.handleStreamError(testError);
      
      expect(window.uiComponents.showNotification).toHaveBeenCalledWith(
        expect.stringContaining('网络连接错误'),
        'error',
        5000
      );
    });

    test('应该正确格式化错误消息', () => {
      const error = {
        type: 'timeout_error',
        message: '连接超时',
        retryCount: 2,
        timestamp: Date.now()
      };

      const formatted = app.formatErrorMessage(error);
      
      expect(formatted).toContain('[timeout_error]');
      expect(formatted).toContain('连接超时');
      expect(formatted).toContain('重试次数: 2');
    });
  });

  describe('性能测试', () => {
    test('应该能够处理大量消息', () => {
      const monitor = new StreamMonitor('test-monitor-container');
      const startTime = performance.now();

      // 模拟处理1000条消息
      for (let i = 0; i < 1000; i++) {
        monitor.recordMessage({
          data: { content: `消息 ${i}` },
          size: 50,
          timestamp: Date.now()
        });
      }

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(monitor.data.totalMessages).toBe(1000);
      expect(processingTime).toBeLessThan(1000); // 应该在1秒内完成

      monitor.destroy();
    });

    test('过滤器应该能够快速处理大量消息', () => {
      // 创建大量测试消息
      const messagesHTML = [];
      for (let i = 0; i < 500; i++) {
        messagesHTML.push(`
          <div class="sse-message text" data-timestamp="${Date.now() + i}">
            <div class="sse-message-content">测试消息 ${i}</div>
          </div>
        `);
      }
      mockStreamMessages.innerHTML = messagesHTML.join('');

      const filter = new StreamFilter('test-filter-container');
      const startTime = performance.now();

      filter.updateMessageList();
      filter.currentFilters.search = '测试';
      filter.applyFilters();

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(filter.allMessages.length).toBe(500);
      expect(filter.filteredMessages.length).toBe(500); // 所有消息都包含"测试"
      expect(processingTime).toBeLessThan(500); // 应该在0.5秒内完成

      filter.destroy();
    });
  });
});
