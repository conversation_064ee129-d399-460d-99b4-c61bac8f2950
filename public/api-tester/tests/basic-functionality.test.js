/**
 * 基本功能测试
 * 测试流式输出功能的基本组件
 */

// 手动加载必要的类定义
const fs = require('fs');
const path = require('path');

// 加载脚本内容
function loadScriptContent(scriptPath) {
  const fullPath = path.join(__dirname, '..', scriptPath);
  if (fs.existsSync(fullPath)) {
    return fs.readFileSync(fullPath, 'utf8');
  }
  return '';
}

// 模拟浏览器环境
global.window = global;
global.document = {
  getElementById: jest.fn(),
  createElement: jest.fn(() => ({
    className: '',
    innerHTML: '',
    style: {},
    classList: {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn(),
      toggle: jest.fn()
    },
    addEventListener: jest.fn(),
    appendChild: jest.fn(),
    insertAdjacentElement: jest.fn(),
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(() => []),
    setAttribute: jest.fn(),
    getAttribute: jest.fn(),
    textContent: ''
  })),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => []),
  body: {
    innerHTML: '',
    appendChild: jest.fn()
  }
};

// 模拟 Utils
global.Utils = {
  generateId: () => Math.random().toString(36).substr(2, 9),
  debounce: (func, wait) => func, // 简化版本
  buildUrlParams: (params) => {
    return Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  },
  storage: {
    get: jest.fn(),
    set: jest.fn(),
    remove: jest.fn()
  }
};

// 模拟 uiComponents
global.uiComponents = {
  showNotification: jest.fn(),
  showModal: jest.fn(),
  closeModal: jest.fn()
};

// 加载并执行脚本
const enhancedStreamClientCode = loadScriptContent('js/enhanced-stream-client.js');
const streamMonitorCode = loadScriptContent('js/stream-monitor.js');
const streamExporterCode = loadScriptContent('js/stream-exporter.js');
const streamFilterCode = loadScriptContent('js/stream-filter.js');

// 执行脚本代码
eval(enhancedStreamClientCode);
eval(streamMonitorCode);
eval(streamExporterCode);
eval(streamFilterCode);

describe('基本功能测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('EnhancedStreamClient 基本功能', () => {
    test('应该能够创建客户端实例', () => {
      expect(() => {
        const client = new EnhancedStreamClient();
        expect(client).toBeDefined();
      }).not.toThrow();
    });

    test('应该有正确的默认配置', () => {
      const client = new EnhancedStreamClient();
      expect(client.config).toBeDefined();
      expect(client.config.connectionTimeout).toBe(10000);
      expect(client.config.enableRetry).toBe(true);
      expect(client.config.maxRetries).toBe(3);
    });

    test('应该能够更新配置', () => {
      const client = new EnhancedStreamClient();
      const newConfig = { connectionTimeout: 20000 };
      
      client.updateConfig(newConfig);
      expect(client.config.connectionTimeout).toBe(20000);
    });

    test('应该能够获取状态', () => {
      const client = new EnhancedStreamClient();
      expect(client.getState()).toBe('disconnected');
    });

    test('应该能够获取性能指标', () => {
      const client = new EnhancedStreamClient();
      const metrics = client.getMetrics();
      
      expect(metrics).toHaveProperty('totalMessages');
      expect(metrics).toHaveProperty('totalBytes');
      expect(metrics).toHaveProperty('errors');
    });
  });

  describe('StreamMonitor 基本功能', () => {
    test('应该能够创建监控器实例', () => {
      // 模拟容器元素
      const mockContainer = {
        innerHTML: '',
        querySelector: jest.fn(),
        insertAdjacentElement: jest.fn()
      };
      
      global.document.getElementById.mockReturnValue(mockContainer);
      
      expect(() => {
        const monitor = new StreamMonitor('test-container');
        expect(monitor).toBeDefined();
      }).not.toThrow();
    });

    test('应该有正确的初始数据', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const monitor = new StreamMonitor('test-container');
      expect(monitor.data).toBeDefined();
      expect(monitor.data.totalMessages).toBe(0);
      expect(monitor.data.totalBytes).toBe(0);
      expect(monitor.data.errors).toBe(0);
    });

    test('应该能够记录消息', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const monitor = new StreamMonitor('test-container');
      const messageData = {
        data: { content: '测试消息' },
        size: 100,
        timestamp: Date.now()
      };

      monitor.recordMessage(messageData);
      expect(monitor.data.totalMessages).toBe(1);
      expect(monitor.data.totalBytes).toBe(100);
    });

    test('应该能够格式化字节数', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const monitor = new StreamMonitor('test-container');
      
      expect(monitor.formatBytes(0)).toBe('0 B');
      expect(monitor.formatBytes(1024)).toBe('1 KB');
      expect(monitor.formatBytes(1048576)).toBe('1 MB');
    });

    test('应该能够格式化时长', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const monitor = new StreamMonitor('test-container');
      
      expect(monitor.formatDuration(65000)).toBe('00:01:05');
      expect(monitor.formatDuration(3665000)).toBe('01:01:05');
    });
  });

  describe('StreamExporter 基本功能', () => {
    test('应该能够创建导出器实例', () => {
      expect(() => {
        const exporter = new StreamExporter();
        expect(exporter).toBeDefined();
      }).not.toThrow();
    });

    test('应该有正确的导出格式', () => {
      const exporter = new StreamExporter();
      expect(exporter.exportFormats).toBeDefined();
      expect(exporter.exportFormats.JSON).toBe('json');
      expect(exporter.exportFormats.CSV).toBe('csv');
      expect(exporter.exportFormats.TXT).toBe('txt');
      expect(exporter.exportFormats.HTML).toBe('html');
    });

    test('应该能够转义HTML字符', () => {
      const exporter = new StreamExporter();
      const html = '<script>alert("test")</script>';
      const escaped = exporter.escapeHtml(html);
      
      expect(escaped).toBe('&lt;script&gt;alert("test")&lt;/script&gt;');
    });

    test('应该能够计算数据大小', () => {
      const exporter = new StreamExporter();
      const messages = [
        { content: 'Hello' },
        { content: 'World' }
      ];
      
      const size = exporter.calculateDataSize(messages);
      expect(size).toMatch(/\d+(\.\d+)?\s(B|KB|MB|GB)/);
    });

    test('应该能够过滤消息', () => {
      const exporter = new StreamExporter();
      const messages = [
        { type: 'text', content: '文本消息' },
        { type: 'progress', content: '进度消息' },
        { type: 'error', content: '错误消息' }
      ];
      
      const textMessages = exporter.filterMessages(messages, 'text');
      expect(textMessages).toHaveLength(1);
      expect(textMessages[0].type).toBe('text');
      
      const allMessages = exporter.filterMessages(messages, 'all');
      expect(allMessages).toHaveLength(3);
    });
  });

  describe('StreamFilter 基本功能', () => {
    test('应该能够创建过滤器实例', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      expect(() => {
        const filter = new StreamFilter('test-container');
        expect(filter).toBeDefined();
      }).not.toThrow();
    });

    test('应该有正确的初始过滤条件', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const filter = new StreamFilter('test-container');
      expect(filter.currentFilters).toBeDefined();
      expect(filter.currentFilters.search).toBe('');
      expect(filter.currentFilters.type).toBe('all');
      expect(filter.currentFilters.timeRange).toBe('all');
    });

    test('应该能够提取消息类型', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const filter = new StreamFilter('test-container');
      
      // 模拟元素
      const mockElement = {
        classList: ['sse-message', 'text']
      };
      
      const type = filter.extractMessageType(mockElement);
      expect(type).toBe('text');
    });

    test('应该能够匹配搜索条件', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const filter = new StreamFilter('test-container');
      filter.currentFilters.search = '测试';
      filter.currentFilters.caseSensitive = false;
      filter.currentFilters.regexSearch = false;
      
      const message1 = { content: '这是一个测试消息' };
      const message2 = { content: '这是普通消息' };
      
      expect(filter.matchesSearchFilter(message1)).toBe(true);
      expect(filter.matchesSearchFilter(message2)).toBe(false);
    });

    test('应该能够匹配类型条件', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const filter = new StreamFilter('test-container');
      filter.currentFilters.type = 'text';
      
      const textMessage = { type: 'text' };
      const progressMessage = { type: 'progress' };
      
      expect(filter.matchesTypeFilter(textMessage)).toBe(true);
      expect(filter.matchesTypeFilter(progressMessage)).toBe(false);
    });
  });

  describe('错误处理测试', () => {
    test('EnhancedStreamClient 应该能够创建错误对象', () => {
      const client = new EnhancedStreamClient();
      const originalError = new Error('测试错误');
      const streamError = client.createStreamError('network_error', originalError);
      
      expect(streamError.type).toBe('network_error');
      expect(streamError.message).toBe('测试错误');
      expect(streamError).toHaveProperty('timestamp');
      expect(streamError.originalError).toBe(originalError);
    });

    test('StreamMonitor 应该能够记录错误', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const monitor = new StreamMonitor('test-container');
      const error = {
        type: 'network_error',
        message: '网络连接失败'
      };

      monitor.recordError(error);
      expect(monitor.data.errors).toBe(1);
      expect(monitor.data.errorHistory).toHaveLength(1);
      expect(monitor.data.errorHistory[0].type).toBe('network_error');
    });
  });

  describe('性能测试', () => {
    test('StreamMonitor 应该能够处理大量消息', () => {
      const mockContainer = { innerHTML: '' };
      global.document.getElementById.mockReturnValue(mockContainer);
      
      const monitor = new StreamMonitor('test-container');
      const startTime = performance.now();

      // 模拟处理100条消息
      for (let i = 0; i < 100; i++) {
        monitor.recordMessage({
          data: { content: `消息 ${i}` },
          size: 50,
          timestamp: Date.now()
        });
      }

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(monitor.data.totalMessages).toBe(100);
      expect(processingTime).toBeLessThan(100); // 应该很快完成
    });
  });
});
