/**
 * Jest 测试环境设置文件
 * 配置测试环境和全局模拟
 */

// 模拟 performance API
global.performance = global.performance || {
  now: () => Date.now()
};

// 模拟 console 方法以避免测试输出污染
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
};
global.sessionStorage = sessionStorageMock;

// 模拟 fetch API
global.fetch = jest.fn();

// 模拟 EventSource
global.EventSource = jest.fn(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  close: jest.fn(),
  readyState: 1,
  url: '',
  withCredentials: false
}));

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 设置默认的时区
process.env.TZ = 'Asia/Shanghai';

// 在每个测试前重置所有模拟
beforeEach(() => {
  jest.clearAllMocks();
  
  // 重置 localStorage 模拟
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  // 重置 sessionStorage 模拟
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
  
  // 重置 performance 模拟（如果是模拟函数）
  if (performance.now && typeof performance.now.mockClear === 'function') {
    performance.now.mockClear();
    performance.now.mockReturnValue(Date.now());
  }
});

// 在每个测试后清理
afterEach(() => {
  // 清理 DOM
  if (document.body) {
    document.body.innerHTML = '';
  }
  
  // 清理定时器
  jest.clearAllTimers();
});

// 全局测试工具函数
global.testUtils = {
  /**
   * 创建模拟的 SSE 消息
   * @param {string} contentType - 内容类型
   * @param {string} content - 消息内容
   * @param {object} options - 其他选项
   * @returns {object} 模拟的 SSE 消息
   */
  createMockSSEMessage: (contentType, content, options = {}) => ({
    role: 'assistant',
    type: 'answer',
    content,
    content_type: contentType,
    id: options.id || `test-${Date.now()}`,
    parentMsgId: options.parentMsgId || 'parent-test',
    conversation_id: options.conversationId || 'conversation-test',
    created_at: options.createdAt || Math.floor(Date.now() / 1000),
    requestId: options.requestId || `request-${Date.now()}`,
    supportDownload: options.supportDownload || false,
    ...options
  }),

  /**
   * 等待 DOM 更新
   * @param {number} ms - 等待时间（毫秒）
   * @returns {Promise} Promise 对象
   */
  waitForDOM: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * 模拟用户点击事件
   * @param {HTMLElement} element - 目标元素
   */
  simulateClick: (element) => {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(event);
  },

  /**
   * 模拟用户输入事件
   * @param {HTMLInputElement} element - 输入元素
   * @param {string} value - 输入值
   */
  simulateInput: (element, value) => {
    element.value = value;
    const event = new Event('input', {
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  },

  /**
   * 检查元素是否具有指定的 CSS 类
   * @param {HTMLElement} element - 目标元素
   * @param {string} className - CSS 类名
   * @returns {boolean} 是否具有指定类
   */
  hasClass: (element, className) => {
    return element && element.classList && element.classList.contains(className);
  },

  /**
   * 获取元素的计算样式
   * @param {HTMLElement} element - 目标元素
   * @param {string} property - CSS 属性名
   * @returns {string} 属性值
   */
  getComputedStyle: (element, property) => {
    if (!element || !window.getComputedStyle) {
      return '';
    }
    return window.getComputedStyle(element).getPropertyValue(property);
  }
};

// 自定义匹配器
expect.extend({
  /**
   * 检查元素是否具有指定的 CSS 类
   * @param {HTMLElement} received - 接收的元素
   * @param {string} className - 期望的 CSS 类名
   * @returns {object} 匹配结果
   */
  toHaveClass(received, className) {
    const pass = received && received.classList && received.classList.contains(className);
    
    if (pass) {
      return {
        message: () => `期望元素不具有类 "${className}"`,
        pass: true
      };
    } else {
      return {
        message: () => `期望元素具有类 "${className}"`,
        pass: false
      };
    }
  },

  /**
   * 检查元素是否包含指定文本
   * @param {HTMLElement} received - 接收的元素
   * @param {string} text - 期望的文本
   * @returns {object} 匹配结果
   */
  toContainText(received, text) {
    const pass = received && received.textContent && received.textContent.includes(text);
    
    if (pass) {
      return {
        message: () => `期望元素不包含文本 "${text}"`,
        pass: true
      };
    } else {
      return {
        message: () => `期望元素包含文本 "${text}"，但实际文本为 "${received?.textContent || ''}"`,
        pass: false
      };
    }
  }
});

// 加载项目的JavaScript文件
const fs = require('fs');
const path = require('path');

function loadScript(scriptPath) {
  const fullPath = path.join(__dirname, '..', scriptPath);
  if (fs.existsSync(fullPath)) {
    const scriptContent = fs.readFileSync(fullPath, 'utf8');
    // 移除模块导出部分，避免在测试环境中出错
    const cleanedContent = scriptContent
      .replace(/if \(typeof module !== 'undefined' && module\.exports\) \{[\s\S]*?\}/g, '')
      .replace(/module\.exports = [\s\S]*?;/g, '');
    eval(cleanedContent);
  }
}

// 模拟全局工具函数
global.Utils = {
  generateId: () => Math.random().toString(36).substr(2, 9),
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
  buildUrlParams: (params) => {
    return Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  },
  storage: {
    get: jest.fn(),
    set: jest.fn(),
    remove: jest.fn()
  }
};

// 按顺序加载所有必需的脚本
try {
  loadScript('js/enhanced-stream-client.js');
  loadScript('js/stream-monitor.js');
  loadScript('js/stream-exporter.js');
  loadScript('js/stream-filter.js');
  loadScript('js/ui-components.js');
  loadScript('js/api-client.js');
  loadScript('js/app.js');
} catch (error) {
  console.warn('加载脚本时出错:', error.message);
}

// 设置测试超时
jest.setTimeout(10000);
