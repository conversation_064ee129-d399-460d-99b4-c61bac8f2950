/**
 * SSE 样式区分功能单元测试
 * 测试基于 content_type 字段的响应结果样式区分功能
 */

// 模拟DOM环境 - 使用jest-environment-jsdom自动提供的DOM
// 在setup.js中已经配置了jsdom环境

// 模拟App类的相关方法
class TestApp {
  constructor() {
    this.messageCount = 0;
  }

  /**
   * 获取content_type对应的CSS类名
   * @param {string} contentType - 内容类型
   * @returns {string} CSS类名
   */
  getContentTypeClass(contentType) {
    const typeMap = {
      'progress': 'progress',    // 联网搜索进度
      'card': 'card',           // 搜索结果卡片
      'thinking': 'thinking',   // AI思考内容
      'text': 'text'           // 正式回答
    };
    return typeMap[contentType] || 'unknown';
  }

  /**
   * 获取content_type对应的图标
   * @param {string} contentType - 内容类型
   * @returns {string} 图标字符
   */
  getContentTypeIcon(contentType) {
    const iconMap = {
      'progress': '🔍',    // 联网搜索进度 - 搜索图标
      'card': '📋',        // 搜索结果卡片 - 卡片图标
      'thinking': '🤔',    // AI思考内容 - 思考图标
      'text': '💬'         // 正式回答 - 对话图标
    };
    return iconMap[contentType] || '📄';
  }

  /**
   * 获取content_type对应的中文标签
   * @param {string} contentType - 内容类型
   * @returns {string} 中文标签
   */
  getContentTypeLabel(contentType) {
    const labelMap = {
      'progress': '联网搜索',
      'card': '搜索结果',
      'thinking': '思考过程',
      'text': '正式回答'
    };
    return labelMap[contentType] || '未知类型';
  }

  /**
   * 格式化时间戳
   * @param {number} timestamp - 时间戳（毫秒或秒）
   * @returns {string} 格式化的时间字符串
   */
  formatTimestamp(timestamp) {
    // 处理秒级时间戳
    if (timestamp < 10000000000) {
      timestamp *= 1000;
    }
    
    const date = new Date(timestamp);
    const now = new Date();
    
    // 如果是今天，只显示时间
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
    
    // 否则显示完整日期时间
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  /**
   * 渲染样式化的SSE消息
   * @param {object} data - SSE消息数据
   * @param {HTMLElement} container - 容器元素
   */
  renderStyledSSEMessage(data, container) {
    const messageElement = document.createElement('div');
    messageElement.className = `sse-message ${this.getContentTypeClass(data.content_type)}`;
    messageElement.setAttribute('data-message-id', ++this.messageCount);
    
    // 创建消息头部
    const headerElement = document.createElement('div');
    headerElement.className = 'sse-message-header';
    
    // 添加图标
    const iconElement = document.createElement('span');
    iconElement.className = 'sse-message-icon';
    iconElement.textContent = this.getContentTypeIcon(data.content_type);
    
    // 添加类型标签
    const typeElement = document.createElement('span');
    typeElement.className = 'sse-message-type';
    typeElement.textContent = this.getContentTypeLabel(data.content_type);
    
    // 添加时间戳
    const timestampElement = document.createElement('span');
    timestampElement.className = 'sse-message-timestamp';
    timestampElement.textContent = this.formatTimestamp(data.created_at || Date.now());
    
    headerElement.appendChild(iconElement);
    headerElement.appendChild(typeElement);
    headerElement.appendChild(timestampElement);
    
    // 创建消息内容
    const contentElement = document.createElement('div');
    contentElement.className = 'sse-message-content';
    
    // 根据content_type处理内容显示
    if (data.content_type === 'card' && typeof data.content === 'string') {
      try {
        // 尝试格式化JSON内容
        const parsedContent = JSON.parse(data.content);
        contentElement.textContent = JSON.stringify(parsedContent, null, 2);
      } catch {
        contentElement.textContent = data.content;
      }
    } else {
      contentElement.textContent = data.content || JSON.stringify(data, null, 2);
    }
    
    messageElement.appendChild(headerElement);
    messageElement.appendChild(contentElement);
    container.appendChild(messageElement);
    
    return messageElement;
  }

  /**
   * 处理流式消息（测试版本）
   * @param {any} data - 流式数据
   */
  handleStreamMessage(data) {
    const streamMessages = document.getElementById('stream-messages');
    const enableStyling = document.getElementById('enable-sse-styling')?.checked ?? true;
    
    if (!streamMessages) return null;

    // 如果启用样式区分，使用增强的消息渲染
    if (enableStyling && typeof data === 'object' && data.content_type) {
      return this.renderStyledSSEMessage(data, streamMessages);
    }
    
    return null;
  }
}

// 测试数据
const testMessages = {
  progress: {
    "role": "assistant",
    "type": "answer",
    "content": "联网搜索中...",
    "content_type": "progress",
    "id": "364946235703955653",
    "parentMsgId": "364946235699761349",
    "conversation_id": "364945829712105669",
    "created_at": 1756156327,
    "requestId": "39e7d59a-8e84-4938-b336-732c6f979837",
    "supportDownload": false
  },
  card: {
    "role": "assistant",
    "type": "answer",
    "content": "{\"cardType\":\"DB-CARD-2\",\"cardInfo\":{\"initTitle\":\"理解问题\"}}",
    "content_type": "card",
    "id": "364946235703955654",
    "parentMsgId": "364946235699761349",
    "conversation_id": "364945829712105669",
    "created_at": 1756156330,
    "requestId": "39e7d59a-8e84-4938-b336-732c6f979837",
    "supportDownload": false
  },
  thinking: {
    "role": "assistant",
    "type": "answer",
    "content": "用户询问关于SSE的问题，我需要分析...",
    "content_type": "thinking",
    "id": "364945836066476229",
    "parentMsgId": "364945836067127685",
    "conversation_id": "364945829712105669",
    "created_at": 1756156137,
    "requestId": "f8beb618-c404-4761-8d2a-14bbde790f33",
    "supportDownload": false
  },
  text: {
    "role": "assistant",
    "type": "answer",
    "content": "根据您的问题，SSE是一种服务器推送技术...",
    "content_type": "text",
    "id": "363022966520938885",
    "parentMsgId": "363022966516744581",
    "conversation_id": "363022964585267589",
    "created_at": 1755239259,
    "requestId": "a3855317-f6b2-4613-bb0e-c53092f182f8",
    "supportDownload": false
  }
};

// 测试套件
describe('SSE 样式区分功能测试', () => {
  let app;
  let container;

  beforeEach(() => {
    // 设置DOM结构
    document.body.innerHTML = `
      <div id="stream-messages" class="stream-messages"></div>
      <input type="checkbox" id="enable-sse-styling" checked>
    `;

    app = new TestApp();
    container = document.getElementById('stream-messages');
  });

  describe('CSS类名映射测试', () => {
    test('应该正确映射progress类型', () => {
      expect(app.getContentTypeClass('progress')).toBe('progress');
    });

    test('应该正确映射card类型', () => {
      expect(app.getContentTypeClass('card')).toBe('card');
    });

    test('应该正确映射thinking类型', () => {
      expect(app.getContentTypeClass('thinking')).toBe('thinking');
    });

    test('应该正确映射text类型', () => {
      expect(app.getContentTypeClass('text')).toBe('text');
    });

    test('应该为未知类型返回unknown', () => {
      expect(app.getContentTypeClass('unknown_type')).toBe('unknown');
      expect(app.getContentTypeClass('')).toBe('unknown');
      expect(app.getContentTypeClass(null)).toBe('unknown');
    });
  });

  describe('图标映射测试', () => {
    test('应该为每种类型返回正确的图标', () => {
      expect(app.getContentTypeIcon('progress')).toBe('🔍');
      expect(app.getContentTypeIcon('card')).toBe('📋');
      expect(app.getContentTypeIcon('thinking')).toBe('🤔');
      expect(app.getContentTypeIcon('text')).toBe('💬');
      expect(app.getContentTypeIcon('unknown')).toBe('📄');
    });
  });

  describe('中文标签映射测试', () => {
    test('应该为每种类型返回正确的中文标签', () => {
      expect(app.getContentTypeLabel('progress')).toBe('联网搜索');
      expect(app.getContentTypeLabel('card')).toBe('搜索结果');
      expect(app.getContentTypeLabel('thinking')).toBe('思考过程');
      expect(app.getContentTypeLabel('text')).toBe('正式回答');
      expect(app.getContentTypeLabel('unknown')).toBe('未知类型');
    });
  });

  describe('时间戳格式化测试', () => {
    test('应该正确处理秒级时间戳', () => {
      const timestamp = 1756156327; // 秒级时间戳
      const result = app.formatTimestamp(timestamp);
      expect(result).toMatch(/\d{2}:\d{2}:\d{2}/); // 应该包含时间格式
    });

    test('应该正确处理毫秒级时间戳', () => {
      const timestamp = 1756156327000; // 毫秒级时间戳
      const result = app.formatTimestamp(timestamp);
      expect(result).toMatch(/\d{2}:\d{2}:\d{2}/); // 应该包含时间格式
    });
  });
});

// 导出测试类供其他测试使用
module.exports = { TestApp, testMessages };
