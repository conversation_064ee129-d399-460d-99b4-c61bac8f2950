<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单标签页测试</title>
    <style>
        .tabs { border: 1px solid #ddd; margin: 20px; }
        .tab-headers { display: flex; background: #f5f5f5; }
        .tab-header { 
            padding: 10px 20px; 
            border: none; 
            background: none; 
            cursor: pointer; 
            border-right: 1px solid #ddd;
        }
        .tab-header.active { 
            background: white; 
            color: blue;
            border-bottom: 2px solid blue; 
        }
        .tab-content { 
            display: none; 
            padding: 20px; 
            background: white;
        }
        .tab-content.active { 
            display: block; 
        }
        .test-info {
            margin: 20px;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>简单标签页测试</h1>
    
    <div class="test-info">
        <h3>测试说明</h3>
        <p>这是一个简化的标签页测试，用于验证基本的标签页切换功能。</p>
        <p>点击下面的标签页按钮，观察内容是否正确切换。</p>
    </div>

    <!-- 测试标签页1 -->
    <div class="tabs">
        <div class="tab-headers">
            <button class="tab-header active" data-tab="test1">测试1</button>
            <button class="tab-header" data-tab="test2">测试2</button>
            <button class="tab-header" data-tab="test3">测试3</button>
        </div>
        <div class="tab-content active" data-tab="test1">
            <h3>测试1内容</h3>
            <p>这是第一个测试标签页的内容。</p>
            <button onclick="alert('测试1按钮点击')">测试按钮</button>
        </div>
        <div class="tab-content" data-tab="test2">
            <h3>测试2内容</h3>
            <p>这是第二个测试标签页的内容。</p>
            <input type="text" placeholder="测试输入框" />
        </div>
        <div class="tab-content" data-tab="test3">
            <h3>测试3内容</h3>
            <p>这是第三个测试标签页的内容。</p>
            <textarea placeholder="测试文本区域"></textarea>
        </div>
    </div>

    <!-- 测试标签页2 -->
    <div class="tabs">
        <div class="tab-headers">
            <button class="tab-header active" data-tab="req-headers">请求头</button>
            <button class="tab-header" data-tab="req-params">URL参数</button>
            <button class="tab-header" data-tab="req-body">请求体</button>
        </div>
        <div class="tab-content active" data-tab="req-headers">
            <h3>请求头配置</h3>
            <div id="headers-list">
                <div style="display: flex; gap: 10px; margin: 5px 0;">
                    <input type="text" placeholder="请求头名称" value="Content-Type" />
                    <input type="text" placeholder="请求头值" value="application/json" />
                    <button onclick="this.parentElement.remove()">删除</button>
                </div>
            </div>
            <button onclick="addHeader()">添加请求头</button>
        </div>
        <div class="tab-content" data-tab="req-params">
            <h3>URL参数配置</h3>
            <div id="params-list">
                <!-- 参数列表 -->
            </div>
            <button onclick="addParam()">添加参数</button>
        </div>
        <div class="tab-content" data-tab="req-body">
            <h3>请求体配置</h3>
            <textarea id="request-body" style="width: 100%; height: 200px;" placeholder="请输入JSON格式的请求体...">
{
  "message": "Hello, World!",
  "test": true
}
            </textarea>
            <br><br>
            <button onclick="formatJSON()">格式化JSON</button>
        </div>
    </div>

    <div class="test-info">
        <h3>调试信息</h3>
        <div id="debug-info">等待用户操作...</div>
    </div>

    <script>
        // 简单的标签页切换函数
        function switchTab(tabHeader) {
            const tabContainer = tabHeader.closest('.tabs');
            if (!tabContainer) {
                console.error('未找到标签页容器');
                return;
            }

            const tabName = tabHeader.getAttribute('data-tab');
            console.log('切换到标签页:', tabName);
            
            // 更新调试信息
            document.getElementById('debug-info').innerHTML = `切换到标签页: ${tabName} (${new Date().toLocaleTimeString()})`;
            
            // 更新标签头状态
            tabContainer.querySelectorAll('.tab-header').forEach(header => {
                header.classList.remove('active');
            });
            tabHeader.classList.add('active');

            // 更新标签内容状态
            tabContainer.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            const targetContent = tabContainer.querySelector(`.tab-content[data-tab="${tabName}"]`);
            if (targetContent) {
                targetContent.classList.add('active');
                console.log('标签页切换成功');
            } else {
                console.error('未找到标签页内容:', tabName);
            }
        }

        // 绑定标签页点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-header')) {
                switchTab(e.target);
            }
        });

        // 添加请求头
        function addHeader() {
            const headersList = document.getElementById('headers-list');
            const headerRow = document.createElement('div');
            headerRow.style.cssText = 'display: flex; gap: 10px; margin: 5px 0;';
            headerRow.innerHTML = `
                <input type="text" placeholder="请求头名称" />
                <input type="text" placeholder="请求头值" />
                <button onclick="this.parentElement.remove()">删除</button>
            `;
            headersList.appendChild(headerRow);
        }

        // 添加URL参数
        function addParam() {
            const paramsList = document.getElementById('params-list');
            const paramRow = document.createElement('div');
            paramRow.style.cssText = 'display: flex; gap: 10px; margin: 5px 0;';
            paramRow.innerHTML = `
                <input type="text" placeholder="参数名称" />
                <input type="text" placeholder="参数值" />
                <button onclick="this.parentElement.remove()">删除</button>
            `;
            paramsList.appendChild(paramRow);
        }

        // 格式化JSON
        function formatJSON() {
            const textarea = document.getElementById('request-body');
            try {
                const parsed = JSON.parse(textarea.value);
                textarea.value = JSON.stringify(parsed, null, 2);
                document.getElementById('debug-info').innerHTML = 'JSON格式化成功';
            } catch (error) {
                document.getElementById('debug-info').innerHTML = 'JSON格式化失败: ' + error.message;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('简单测试页面加载完成');
            document.getElementById('debug-info').innerHTML = '页面加载完成，可以开始测试';
        });
    </script>
</body>
</html>
