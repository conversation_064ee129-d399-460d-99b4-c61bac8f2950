/**
 * 聊天API服务
 * 负责与后端API进行通信，处理聊天请求和SSE流式响应
 */

import { ChatRequest, SSEMessageData, ModelInfo } from '../types/chat';

/**
 * SSE事件回调接口
 */
export interface SSECallbacks {
  /** 接收到消息片段时的回调 */
  onMessage?: (content: string, data: SSEMessageData) => void;
  /** 聊天完成时的回调 */
  onComplete?: (data: any) => void;
  /** 发生错误时的回调 */
  onError?: (error: Error) => void;
  /** 连接建立时的回调 */
  onConnect?: () => void;
}

/**
 * 聊天API服务类
 */
export class ChatApiService {
  private baseUrl: string;
  private currentEventSource: EventSource | null = null;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<ModelInfo[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/models`);
      if (!response.ok) {
        throw new Error(`获取模型列表失败: ${response.statusText}`);
      }
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('获取模型列表失败:', error);
      // 返回默认模型列表
      return [
        {
          id: 'doubao-1_6-thinking',
          name: '豆包思考模型',
          description: '支持深度思考的智能对话模型',
          recommended: true,
          pinned: true,
          badge: '推荐'
        },
        {
          id: 'doubao-1_6',
          name: '豆包标准模型',
          description: '快速响应的标准对话模型'
        }
      ];
    }
  }

  /**
   * 发送聊天消息（流式响应）
   */
  async sendMessage(
    request: ChatRequest,
    callbacks: SSECallbacks
  ): Promise<void> {
    try {
      // 关闭之前的连接
      this.closeConnection();

      console.log('发送聊天请求:', request);

      // 发送POST请求
      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('text/event-stream')) {
        // 处理SSE流式响应
        await this.handleSSEResponse(response, callbacks);
      } else {
        // 处理普通JSON响应
        const data = await response.json();
        if (callbacks.onMessage && data.message) {
          callbacks.onMessage(data.message.content, {
            role: data.message.role,
            type: 'answer',
            content: data.message.content,
            content_type: 'text',
            id: data.message_id,
            parentMsgId: '',
            conversation_id: data.conversation_id,
            created_at: Date.now(),
            requestId: data.request_id
          });
        }
        if (callbacks.onComplete) {
          callbacks.onComplete(data);
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      if (callbacks.onError) {
        callbacks.onError(error as Error);
      }
    }
  }

  /**
   * 处理SSE流式响应
   */
  private async handleSSEResponse(
    response: Response,
    callbacks: SSECallbacks
  ): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      if (callbacks.onConnect) {
        callbacks.onConnect();
      }

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          await this.processSSELine(line, callbacks);
        }
      }

      // 处理剩余的缓冲区内容
      if (buffer.trim()) {
        await this.processSSELine(buffer, callbacks);
      }
    } catch (error) {
      console.error('处理SSE响应失败:', error);
      if (callbacks.onError) {
        callbacks.onError(error as Error);
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 处理单行SSE数据
   */
  private async processSSELine(
    line: string,
    callbacks: SSECallbacks
  ): Promise<void> {
    const trimmedLine = line.trim();
    if (!trimmedLine || trimmedLine.startsWith(':')) {
      return; // 跳过空行和注释行
    }

    if (trimmedLine.startsWith('data: ')) {
      const dataStr = trimmedLine.substring(6);
      
      if (dataStr === '[DONE]') {
        // 流式响应结束
        if (callbacks.onComplete) {
          callbacks.onComplete({ status: 'completed' });
        }
        return;
      }

      try {
        const data = JSON.parse(dataStr);
        
        if (data.event === 'conversation.message.delta' && data.data) {
          // 消息增量数据
          const messageData: SSEMessageData = {
            role: data.data.role || 'assistant',
            type: data.data.type || 'answer',
            content: data.data.content || '',
            content_type: data.data.content_type || 'text',
            id: data.data.id || '',
            parentMsgId: data.data.parentMsgId || '',
            conversation_id: data.data.conversation_id || '',
            created_at: data.data.created_at || Date.now(),
            requestId: data.data.requestId || ''
          };

          if (callbacks.onMessage && messageData.content) {
            callbacks.onMessage(messageData.content, messageData);
          }
        } else if (data.event === 'conversation.chat.completed') {
          // 聊天完成
          if (callbacks.onComplete) {
            callbacks.onComplete(data.data);
          }
        }
      } catch (error) {
        console.warn('解析SSE数据失败:', error, '原始数据:', dataStr);
      }
    }
  }

  /**
   * 停止当前聊天
   */
  stopChat(): void {
    this.closeConnection();
  }

  /**
   * 关闭当前连接
   */
  private closeConnection(): void {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = null;
    }
  }
}

// 创建默认的API服务实例
export const chatApi = new ChatApiService();
