/**
 * 聊天相关类型定义
 * 基于现有的后端API接口定义前端类型
 */

/**
 * 聊天消息角色
 */
export type MessageRole = 'user' | 'assistant' | 'system';

/**
 * 消息内容类型
 */
export type ContentType = 'text' | 'thinking' | 'progress' | 'card';

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  /** 消息ID */
  id: string;
  /** 消息角色 */
  role: MessageRole;
  /** 消息内容 */
  content: string;
  /** 内容类型 */
  contentType?: ContentType;
  /** 时间戳 */
  timestamp: number;
  /** 是否正在流式输出 */
  streaming?: boolean;
  /** 消息状态 */
  status?: 'sending' | 'sent' | 'error';
}

/**
 * 聊天会话接口
 */
export interface ChatSession {
  /** 会话ID */
  id: string;
  /** 会话标题 */
  title: string;
  /** 消息列表 */
  messages: ChatMessage[];
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 使用的模型 */
  model: string;
  /** 服务器端对话ID */
  conversationId?: string;
}

/**
 * 模型信息接口
 */
export interface ModelInfo {
  /** 模型ID */
  id: string;
  /** 模型名称 */
  name: string;
  /** 模型描述 */
  description: string;
  /** 是否推荐 */
  recommended?: boolean;
  /** 是否置顶 */
  pinned?: boolean;
  /** 图标 */
  icon?: string;
  /** 徽章 */
  badge?: string;
}

/**
 * 聊天选项接口
 */
export interface ChatOptions {
  /** 是否启用深度思考 */
  deep_thinking?: boolean;
  /** 是否启用联网搜索 */
  online_search?: boolean;
}

/**
 * API请求接口
 */
export interface ChatRequest {
  /** 消息列表 */
  messages: Array<{
    role: MessageRole;
    content: string;
  }>;
  /** 使用的模型 */
  model: string;
  /** 是否启用流式响应 */
  stream: boolean;
  /** 对话ID */
  conversation_id?: string;
  /** 聊天选项 */
  options?: ChatOptions;
}

/**
 * SSE消息数据接口
 */
export interface SSEMessageData {
  /** 角色 */
  role: string;
  /** 类型 */
  type: string;
  /** 内容片段 */
  content: string;
  /** 内容类型 */
  content_type: string;
  /** 消息ID */
  id: string;
  /** 父消息ID */
  parentMsgId: string;
  /** 对话ID */
  conversation_id: string;
  /** 创建时间 */
  created_at: number;
  /** 请求ID */
  requestId: string;
}

/**
 * 聊天状态枚举
 */
export enum ChatStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  STREAMING = 'streaming',
  ERROR = 'error',
  COMPLETED = 'completed'
}

/**
 * 错误信息接口
 */
export interface ChatError {
  /** 错误消息 */
  message: string;
  /** 错误代码 */
  code?: string;
  /** 错误类型 */
  type?: string;
}
