/**
 * 主应用组件
 * 整合聊天界面的所有功能模块
 */

import React from 'react';
import {
  Box,
  Container,
  Paper,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
} from '@mui/icons-material';
import ChatInterface from './components/ChatInterface';
import { useChat } from './hooks/useChat';

/**
 * 主应用组件
 */
const App: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [darkMode, setDarkMode] = React.useState(false);
  const [sidebarOpen, setSidebarOpen] = React.useState(!isMobile);

  // 使用聊天Hook
  const chatHook = useChat();

  /**
   * 切换主题模式
   */
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  /**
   * 切换侧边栏
   */
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <Box sx={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      bgcolor: 'background.default'
    }}>
      {/* 顶部应用栏 */}
      <AppBar 
        position="static" 
        elevation={1}
        sx={{ 
          bgcolor: 'background.paper',
          color: 'text.primary',
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            aria-label="菜单"
            onClick={toggleSidebar}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              flexGrow: 1,
              fontWeight: 500,
              color: 'primary.main'
            }}
          >
            当贝AI聊天 - Material-UI版本
          </Typography>
          
          <IconButton
            color="inherit"
            onClick={toggleDarkMode}
            aria-label="切换主题"
          >
            {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* 主要内容区域 */}
      <Box sx={{ 
        flex: 1, 
        display: 'flex', 
        overflow: 'hidden',
        position: 'relative'
      }}>
        <Container 
          maxWidth="xl" 
          sx={{ 
            height: '100%',
            display: 'flex',
            p: { xs: 1, sm: 2 },
            gap: 2
          }}
        >
          {/* 聊天界面 */}
          <Paper
            elevation={2}
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              borderRadius: 2,
              bgcolor: 'background.paper'
            }}
          >
            <ChatInterface 
              chatHook={chatHook}
              sidebarOpen={sidebarOpen}
              onToggleSidebar={toggleSidebar}
              isMobile={isMobile}
            />
          </Paper>
        </Container>
      </Box>
    </Box>
  );
};

export default App;
