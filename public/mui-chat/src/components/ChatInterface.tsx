/**
 * 聊天界面主组件
 * 整合消息列表、输入框、侧边栏等功能
 */

import React, { useState } from 'react';
import {
  Box,
  Drawer,
  useTheme,
  // useMediaQuery,
  Alert,
  Snackbar,
} from '@mui/material';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import Sidebar from './Sidebar';
import { UseChatReturn } from '../hooks/useChat';
import { ChatOptions } from '../types/chat';

/**
 * 聊天界面组件属性
 */
interface ChatInterfaceProps {
  /** 聊天Hook */
  chatHook: UseChatReturn;
  /** 侧边栏是否打开 */
  sidebarOpen: boolean;
  /** 切换侧边栏回调 */
  onToggleSidebar: () => void;
  /** 是否为移动端 */
  isMobile: boolean;
}

/**
 * 聊天界面主组件
 */
const ChatInterface: React.FC<ChatInterfaceProps> = ({
  chatHook,
  sidebarOpen,
  onToggleSidebar,
  isMobile,
}) => {
  const theme = useTheme();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [chatOptions, setChatOptions] = useState<ChatOptions>({});

  const {
    currentSession,
    sessions,
    status,
    error,
    models,
    selectedModel,
    sendMessage,
    createNewSession,
    selectSession,
    deleteSession,
    clearCurrentSession,
    stopChat,
    setSelectedModel,
    retryLastMessage,
  } = chatHook;

  // 侧边栏宽度
  const sidebarWidth = 280;

  /**
   * 处理发送消息
   */
  const handleSendMessage = async (content: string) => {
    try {
      await sendMessage(content, chatOptions);
    } catch (error) {
      console.error('发送消息失败:', error);
      setSnackbarOpen(true);
    }
  };

  /**
   * 处理聊天选项变化
   */
  const handleOptionsChange = (options: ChatOptions) => {
    setChatOptions(options);
  };

  /**
   * 关闭错误提示
   */
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  /**
   * 侧边栏内容
   */
  const sidebarContent = (
    <Sidebar
      sessions={sessions}
      currentSession={currentSession}
      models={models}
      selectedModel={selectedModel}
      onCreateNewSession={createNewSession}
      onSelectSession={selectSession}
      onDeleteSession={deleteSession}
      onClearCurrentSession={clearCurrentSession}
      onSelectModel={setSelectedModel}
      onOptionsChange={handleOptionsChange}
      chatOptions={chatOptions}
    />
  );

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex',
      position: 'relative'
    }}>
      {/* 侧边栏 */}
      {isMobile ? (
        <Drawer
          variant="temporary"
          anchor="left"
          open={sidebarOpen}
          onClose={onToggleSidebar}
          ModalProps={{
            keepMounted: true, // 提升移动端性能
          }}
          sx={{
            '& .MuiDrawer-paper': {
              width: sidebarWidth,
              boxSizing: 'border-box',
              bgcolor: 'background.paper',
              borderRight: 1,
              borderColor: 'divider',
            },
          }}
        >
          {sidebarContent}
        </Drawer>
      ) : (
        <Drawer
          variant="persistent"
          anchor="left"
          open={sidebarOpen}
          sx={{
            width: sidebarOpen ? sidebarWidth : 0,
            flexShrink: 0,
            transition: theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
            '& .MuiDrawer-paper': {
              width: sidebarWidth,
              boxSizing: 'border-box',
              bgcolor: 'background.paper',
              borderRight: 1,
              borderColor: 'divider',
              position: 'relative',
            },
          }}
        >
          {sidebarContent}
        </Drawer>
      )}

      {/* 主聊天区域 */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          marginLeft: isMobile ? 0 : (sidebarOpen ? 0 : `-${sidebarWidth}px`),
        }}
      >
        {/* 消息列表 */}
        <Box sx={{ 
          flex: 1, 
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <MessageList
            messages={currentSession?.messages || []}
            status={status}
            onRetry={retryLastMessage}
            onStop={stopChat}
          />
        </Box>

        {/* 消息输入区域 */}
        <Box sx={{ 
          flexShrink: 0,
          borderTop: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper'
        }}>
          <MessageInput
            onSendMessage={handleSendMessage}
            disabled={status === 'streaming'}
            placeholder={
              currentSession 
                ? "输入您的问题..." 
                : "创建新对话或选择现有对话开始聊天"
            }
            showOptions={true}
            chatOptions={chatOptions}
            onOptionsChange={handleOptionsChange}
          />
        </Box>
      </Box>

      {/* 错误提示 */}
      <Snackbar
        open={snackbarOpen || !!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity="error" 
          sx={{ width: '100%' }}
        >
          {error?.message || '发送消息失败，请重试'}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ChatInterface;
