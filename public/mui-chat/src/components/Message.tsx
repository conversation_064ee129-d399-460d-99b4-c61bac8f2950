/**
 * 消息组件
 * 渲染单个聊天消息，支持不同类型的消息样式
 */

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  IconButton,
  Tooltip,
  Chip,
  Fade,
  // useTheme,
} from '@mui/material';
import {
  Person as UserIcon,
  SmartToy as BotIcon,
  ContentCopy as CopyIcon,
  Search as SearchIcon,
  Psychology as ThinkingIcon,
  Chat as ChatIcon,
  Dashboard as CardIcon,
  Check as CheckIcon,
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ChatMessage, ContentType } from '../types/chat';

/**
 * 消息组件属性
 */
interface MessageProps {
  /** 消息数据 */
  message: ChatMessage;
  /** 是否为最后一条消息 */
  isLast?: boolean;
}

/**
 * 获取内容类型的图标和颜色
 */
const getContentTypeInfo = (contentType: ContentType) => {
  switch (contentType) {
    case 'progress':
      return {
        icon: <SearchIcon sx={{ fontSize: 16 }} />,
        color: '#1976d2',
        label: '搜索进度',
        bgColor: '#e3f2fd',
      };
    case 'thinking':
      return {
        icon: <ThinkingIcon sx={{ fontSize: 16 }} />,
        color: '#ed6c02',
        label: '思考过程',
        bgColor: '#fff3e0',
      };
    case 'card':
      return {
        icon: <CardIcon sx={{ fontSize: 16 }} />,
        color: '#2e7d32',
        label: '搜索结果',
        bgColor: '#e8f5e8',
      };
    case 'text':
    default:
      return {
        icon: <ChatIcon sx={{ fontSize: 16 }} />,
        color: '#1976d2',
        label: '回答',
        bgColor: '#f5f5f5',
      };
  }
};

/**
 * 复制文本到剪贴板
 */
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
};

/**
 * 消息组件
 */
const Message: React.FC<MessageProps> = ({ message }) => {
  // const theme = useTheme();
  const [copied, setCopied] = useState(false);
  const isUser = message.role === 'user';
  const contentTypeInfo = getContentTypeInfo(message.contentType || 'text');

  /**
   * 处理复制消息
   */
  const handleCopy = async () => {
    const success = await copyToClipboard(message.content);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  /**
   * 格式化时间
   */
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Fade in timeout={300}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: isUser ? 'row-reverse' : 'row',
          alignItems: 'flex-start',
          gap: 1,
          mb: 2,
          px: 1,
        }}
      >
        {/* 头像 */}
        <Avatar
          sx={{
            width: 36,
            height: 36,
            bgcolor: isUser ? 'primary.main' : 'secondary.main',
            flexShrink: 0,
          }}
        >
          {isUser ? (
            <UserIcon sx={{ fontSize: 20 }} />
          ) : (
            <BotIcon sx={{ fontSize: 20 }} />
          )}
        </Avatar>

        {/* 消息内容 */}
        <Box
          sx={{
            flex: 1,
            maxWidth: '70%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: isUser ? 'flex-end' : 'flex-start',
          }}
        >
          {/* 消息气泡 */}
          <Paper
            elevation={1}
            sx={{
              p: 2,
              borderRadius: 2,
              bgcolor: isUser ? 'primary.main' : 'background.paper',
              color: isUser ? 'primary.contrastText' : 'text.primary',
              border: isUser ? 'none' : 1,
              borderColor: 'divider',
              position: 'relative',
              maxWidth: '100%',
              wordBreak: 'break-word',
              ...(isUser
                ? {
                    borderBottomRightRadius: 4,
                  }
                : {
                    borderBottomLeftRadius: 4,
                  }),
            }}
          >
            {/* 内容类型标签（仅助手消息） */}
            {!isUser && message.contentType && message.contentType !== 'text' && (
              <Box sx={{ mb: 1 }}>
                <Chip
                  icon={contentTypeInfo.icon}
                  label={contentTypeInfo.label}
                  size="small"
                  sx={{
                    height: 24,
                    fontSize: '0.75rem',
                    bgcolor: contentTypeInfo.bgColor,
                    color: contentTypeInfo.color,
                    '& .MuiChip-icon': {
                      color: contentTypeInfo.color,
                    },
                  }}
                />
              </Box>
            )}

            {/* 消息内容 */}
            {isUser ? (
              <Typography
                variant="body1"
                sx={{
                  whiteSpace: 'pre-wrap',
                  lineHeight: 1.5,
                }}
              >
                {message.content}
              </Typography>
            ) : (
              <Box
                sx={{
                  '& p': { margin: 0, mb: 1 },
                  '& p:last-child': { mb: 0 },
                  '& pre': {
                    bgcolor: 'grey.100',
                    p: 1,
                    borderRadius: 1,
                    overflow: 'auto',
                    fontSize: '0.875rem',
                  },
                  '& code': {
                    bgcolor: 'grey.100',
                    px: 0.5,
                    py: 0.25,
                    borderRadius: 0.5,
                    fontSize: '0.875rem',
                  },
                  '& blockquote': {
                    borderLeft: 4,
                    borderColor: 'primary.main',
                    pl: 2,
                    ml: 0,
                    fontStyle: 'italic',
                    color: 'text.secondary',
                  },
                  '& ul, & ol': {
                    pl: 2,
                  },
                  '& table': {
                    borderCollapse: 'collapse',
                    width: '100%',
                    mt: 1,
                    mb: 1,
                  },
                  '& th, & td': {
                    border: 1,
                    borderColor: 'divider',
                    p: 1,
                    textAlign: 'left',
                  },
                  '& th': {
                    bgcolor: 'grey.50',
                    fontWeight: 600,
                  },
                }}
              >
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {message.content || ''}
                </ReactMarkdown>
              </Box>
            )}

            {/* 流式输出指示器 */}
            {message.streaming && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mt: 1,
                  gap: 0.5,
                }}
              >
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    bgcolor: 'text.secondary',
                    animation: 'pulse 1.5s ease-in-out infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 0.3 },
                      '50%': { opacity: 1 },
                      '100%': { opacity: 0.3 },
                    },
                  }}
                />
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    bgcolor: 'text.secondary',
                    animation: 'pulse 1.5s ease-in-out infinite 0.2s',
                  }}
                />
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    bgcolor: 'text.secondary',
                    animation: 'pulse 1.5s ease-in-out infinite 0.4s',
                  }}
                />
              </Box>
            )}
          </Paper>

          {/* 消息操作和时间 */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mt: 0.5,
              opacity: 0.7,
            }}
          >
            {/* 时间戳 */}
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: '0.75rem' }}
            >
              {formatTime(message.timestamp)}
            </Typography>

            {/* 复制按钮 */}
            {message.content && (
              <Tooltip title={copied ? '已复制' : '复制消息'}>
                <IconButton
                  size="small"
                  onClick={handleCopy}
                  sx={{
                    width: 20,
                    height: 20,
                    color: 'text.secondary',
                    '&:hover': {
                      color: 'primary.main',
                    },
                  }}
                >
                  {copied ? (
                    <CheckIcon sx={{ fontSize: 14 }} />
                  ) : (
                    <CopyIcon sx={{ fontSize: 14 }} />
                  )}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
      </Box>
    </Fade>
  );
};

export default Message;
