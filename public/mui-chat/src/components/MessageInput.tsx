/**
 * 消息输入组件
 * 提供消息输入框、发送按钮和聊天选项
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Paper,
  Typography,
  Collapse,
  FormControlLabel,
  Switch,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  Send as SendIcon,
  Settings as SettingsIcon,
  Psychology as ThinkingIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { ChatOptions } from '../types/chat';

/**
 * 消息输入组件属性
 */
interface MessageInputProps {
  /** 发送消息回调 */
  onSendMessage: (content: string) => void;
  /** 是否禁用输入 */
  disabled?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示选项 */
  showOptions?: boolean;
  /** 聊天选项 */
  chatOptions?: ChatOptions;
  /** 选项变化回调 */
  onOptionsChange?: (options: ChatOptions) => void;
}

/**
 * 消息输入组件
 */
const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "输入您的问题...",
  showOptions = false,
  chatOptions = {},
  onOptionsChange,
}) => {
  const [message, setMessage] = useState('');
  const [showOptionsPanel, setShowOptionsPanel] = useState(false);
  const textFieldRef = useRef<HTMLTextAreaElement>(null);
  const maxLength = 4000;

  /**
   * 处理发送消息
   */
  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      onSendMessage(trimmedMessage);
      setMessage('');
      // 重置文本框高度
      if (textFieldRef.current) {
        textFieldRef.current.style.height = 'auto';
      }
    }
  };

  /**
   * 处理键盘事件
   */
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  /**
   * 处理输入变化
   */
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (value.length <= maxLength) {
      setMessage(value);
    }
  };

  /**
   * 自动调整文本框高度
   */
  const adjustTextareaHeight = () => {
    const textarea = textFieldRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 120; // 最大高度
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  };

  /**
   * 处理选项变化
   */
  const handleOptionChange = (option: keyof ChatOptions, value: boolean) => {
    const newOptions = { ...chatOptions, [option]: value };
    onOptionsChange?.(newOptions);
  };

  /**
   * 切换选项面板
   */
  const toggleOptionsPanel = () => {
    setShowOptionsPanel(!showOptionsPanel);
  };

  // 监听消息变化，自动调整高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // 获取当前启用的选项
  const activeOptions = Object.entries(chatOptions).filter(([_, value]) => value);

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        borderTop: 1,
        borderColor: 'divider',
        bgcolor: 'background.paper',
      }}
    >
      {/* 选项面板 */}
      {showOptions && (
        <Collapse in={showOptionsPanel}>
          <Box
            sx={{
              mb: 2,
              p: 2,
              bgcolor: 'background.default',
              borderRadius: 1,
              border: 1,
              borderColor: 'divider',
            }}
          >
            <Typography variant="subtitle2" gutterBottom>
              聊天选项
            </Typography>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={chatOptions.deep_thinking || false}
                    onChange={(e) => handleOptionChange('deep_thinking', e.target.checked)}
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ThinkingIcon sx={{ fontSize: 16 }} />
                    <Typography variant="body2">深度思考模式</Typography>
                  </Box>
                }
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={chatOptions.online_search || false}
                    onChange={(e) => handleOptionChange('online_search', e.target.checked)}
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SearchIcon sx={{ fontSize: 16 }} />
                    <Typography variant="body2">联网搜索</Typography>
                  </Box>
                }
              />
            </Box>
          </Box>
        </Collapse>
      )}

      {/* 输入区域 */}
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
        {/* 文本输入框 */}
        <TextField
          inputRef={textFieldRef}
          fullWidth
          multiline
          maxRows={6}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          variant="outlined"
          size="small"
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              bgcolor: 'background.paper',
              '& fieldset': {
                borderColor: 'divider',
              },
              '&:hover fieldset': {
                borderColor: 'primary.main',
              },
              '&.Mui-focused fieldset': {
                borderColor: 'primary.main',
              },
            },
            '& .MuiInputBase-input': {
              resize: 'none',
              lineHeight: 1.5,
            },
          }}
        />

        {/* 选项按钮 */}
        {showOptions && (
          <Tooltip title="聊天选项">
            <IconButton
              onClick={toggleOptionsPanel}
              color={showOptionsPanel ? 'primary' : 'default'}
              sx={{
                width: 40,
                height: 40,
                borderRadius: 2,
                border: 1,
                borderColor: showOptionsPanel ? 'primary.main' : 'divider',
                bgcolor: showOptionsPanel ? 'primary.50' : 'transparent',
              }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        )}

        {/* 发送按钮 */}
        <Tooltip title="发送消息 (Enter)">
          <IconButton
            onClick={handleSend}
            disabled={disabled || !message.trim()}
            color="primary"
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              bgcolor: 'primary.main',
              color: 'white',
              '&:hover': {
                bgcolor: 'primary.dark',
              },
              '&.Mui-disabled': {
                bgcolor: 'action.disabledBackground',
                color: 'action.disabled',
              },
            }}
          >
            <SendIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* 底部信息栏 */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: 1,
          px: 1,
        }}
      >
        {/* 左侧：启用的选项 */}
        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
          {activeOptions.map(([key, _]) => (
            <Chip
              key={key}
              label={
                key === 'deep_thinking' ? '深度思考' :
                key === 'online_search' ? '联网搜索' : key
              }
              size="small"
              variant="outlined"
              icon={
                key === 'deep_thinking' ? <ThinkingIcon sx={{ fontSize: 14 }} /> :
                key === 'online_search' ? <SearchIcon sx={{ fontSize: 14 }} /> : undefined
              }
              sx={{
                height: 20,
                fontSize: '0.7rem',
                '& .MuiChip-icon': {
                  fontSize: 12,
                },
              }}
            />
          ))}
        </Box>

        {/* 右侧：字符计数和提示 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography
            variant="caption"
            color={message.length > maxLength * 0.9 ? 'error' : 'text.secondary'}
            sx={{ fontSize: '0.75rem' }}
          >
            {message.length}/{maxLength}
          </Typography>
          
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ fontSize: '0.7rem', display: { xs: 'none', sm: 'block' } }}
          >
            Enter 发送，Shift+Enter 换行
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};

export default MessageInput;
