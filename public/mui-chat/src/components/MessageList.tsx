/**
 * 消息列表组件
 * 显示聊天消息列表，支持不同类型的消息渲染
 */

import React, { useEffect, useRef } from 'react';
import {
  Box,
  List,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Fade,
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Refresh as RetryIcon,
  Stop as StopIcon,
} from '@mui/icons-material';
import Message from './Message';
import { ChatMessage, ChatStatus } from '../types/chat';

/**
 * 消息列表组件属性
 */
interface MessageListProps {
  /** 消息列表 */
  messages: ChatMessage[];
  /** 聊天状态 */
  status: ChatStatus;
  /** 重试回调 */
  onRetry: () => void;
  /** 停止聊天回调 */
  onStop: () => void;
}

/**
 * 欢迎消息组件
 */
const WelcomeMessage: React.FC = () => (
  <Fade in timeout={1000}>
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        textAlign: 'center',
        p: 4,
      }}
    >
      <Paper
        elevation={0}
        sx={{
          p: 4,
          borderRadius: 3,
          bgcolor: 'background.default',
          border: 1,
          borderColor: 'divider',
          maxWidth: 400,
        }}
      >
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: '50%',
            bgcolor: 'primary.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 3,
          }}
        >
          <BotIcon sx={{ fontSize: 40, color: 'white' }} />
        </Box>
        
        <Typography
          variant="h5"
          component="h2"
          gutterBottom
          sx={{
            fontWeight: 600,
            color: 'text.primary',
            mb: 2,
          }}
        >
          欢迎使用当贝AI聊天
        </Typography>
        
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{ lineHeight: 1.6 }}
        >
          这是基于 Material-UI 设计的全新聊天界面
          <br />
          选择一个模型，开始与AI助手对话吧！
        </Typography>
      </Paper>
    </Box>
  </Fade>
);

/**
 * 加载状态组件
 */
const LoadingIndicator: React.FC<{ status: ChatStatus }> = ({ status }) => {
  const getStatusText = () => {
    switch (status) {
      case ChatStatus.CONNECTING:
        return '正在连接...';
      case ChatStatus.STREAMING:
        return 'AI正在思考...';
      default:
        return '处理中...';
    }
  };

  return (
    <Fade in>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
          gap: 2,
        }}
      >
        <CircularProgress size={20} />
        <Typography variant="body2" color="text.secondary">
          {getStatusText()}
        </Typography>
      </Box>
    </Fade>
  );
};

/**
 * 消息列表组件
 */
const MessageList: React.FC<MessageListProps> = ({
  messages,
  status,
  onRetry,
  onStop,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  /**
   * 滚动到底部
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'end'
    });
  };

  // 当消息更新时自动滚动到底部
  useEffect(() => {
    const timer = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timer);
  }, [messages]);

  // 如果没有消息，显示欢迎界面
  if (messages.length === 0) {
    return <WelcomeMessage />;
  }

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      {/* 消息列表 */}
      <Box
        ref={listRef}
        sx={{
          flex: 1,
          overflow: 'auto',
          px: { xs: 1, sm: 2 },
          py: 1,
        }}
      >
        <List sx={{ p: 0 }}>
          {messages.map((message, index) => (
            <Message
              key={message.id}
              message={message}
              isLast={index === messages.length - 1}
            />
          ))}
        </List>

        {/* 加载状态指示器 */}
        {(status === ChatStatus.CONNECTING || status === ChatStatus.STREAMING) && (
          <LoadingIndicator status={status} />
        )}

        {/* 错误状态和重试按钮 */}
        {status === ChatStatus.ERROR && (
          <Fade in>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                p: 2,
                gap: 1,
              }}
            >
              <Button
                variant="outlined"
                size="small"
                startIcon={<RetryIcon />}
                onClick={onRetry}
                sx={{ borderRadius: 2 }}
              >
                重试
              </Button>
            </Box>
          </Fade>
        )}

        {/* 停止按钮 */}
        {status === ChatStatus.STREAMING && (
          <Fade in>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                p: 2,
              }}
            >
              <Button
                variant="contained"
                size="small"
                startIcon={<StopIcon />}
                onClick={onStop}
                color="error"
                sx={{ borderRadius: 2 }}
              >
                停止生成
              </Button>
            </Box>
          </Fade>
        )}

        {/* 滚动锚点 */}
        <div ref={messagesEndRef} />
      </Box>
    </Box>
  );
};

export default MessageList;
