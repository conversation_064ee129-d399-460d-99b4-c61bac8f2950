/**
 * 聊天状态管理Hook
 * 管理聊天消息、会话状态和API调用
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  ChatMessage, 
  ChatSession, 
  ChatStatus, 
  ChatError, 
  ModelInfo,
  ChatOptions,
  ContentType 
} from '../types/chat';
import { chatApi, SSECallbacks } from '../services/chatApi';

/**
 * 聊天Hook返回值接口
 */
export interface UseChatReturn {
  // 状态
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  status: ChatStatus;
  error: ChatError | null;
  models: ModelInfo[];
  selectedModel: string;
  
  // 操作方法
  sendMessage: (content: string, options?: ChatOptions) => Promise<void>;
  createNewSession: (model?: string) => void;
  selectSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  clearCurrentSession: () => void;
  stopChat: () => void;
  setSelectedModel: (modelId: string) => void;
  retryLastMessage: () => Promise<void>;
}

/**
 * 生成唯一ID
 */
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * 生成会话标题
 */
const generateSessionTitle = (firstMessage: string): string => {
  const maxLength = 20;
  const cleaned = firstMessage.replace(/\s+/g, ' ').trim();
  return cleaned.length > maxLength 
    ? cleaned.substring(0, maxLength) + '...' 
    : cleaned || '新对话';
};

/**
 * 聊天状态管理Hook
 */
export const useChat = (): UseChatReturn => {
  // 状态定义
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [status, setStatus] = useState<ChatStatus>(ChatStatus.IDLE);
  const [error, setError] = useState<ChatError | null>(null);
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('doubao-1_6-thinking');
  
  // 引用
  const currentMessageRef = useRef<ChatMessage | null>(null);
  const lastUserMessageRef = useRef<string>('');

  // 初始化：加载模型列表和会话数据
  useEffect(() => {
    loadModels();
    loadSessions();
  }, []);

  /**
   * 加载模型列表
   */
  const loadModels = async () => {
    try {
      const modelList = await chatApi.getModels();
      setModels(modelList);
      
      // 设置默认模型
      if (modelList.length > 0) {
        const defaultModel = modelList.find(m => m.recommended) || modelList[0];
        setSelectedModel(defaultModel.id);
      }
    } catch (error) {
      console.error('加载模型列表失败:', error);
    }
  };

  /**
   * 从本地存储加载会话
   */
  const loadSessions = () => {
    try {
      const savedSessions = localStorage.getItem('dangbei-mui-chat-sessions');
      if (savedSessions) {
        const parsedSessions = JSON.parse(savedSessions);
        setSessions(parsedSessions);
        
        // 恢复当前会话
        const currentSessionId = localStorage.getItem('dangbei-mui-chat-current-session');
        if (currentSessionId) {
          const session = parsedSessions.find((s: ChatSession) => s.id === currentSessionId);
          if (session) {
            setCurrentSession(session);
          }
        }
      }
    } catch (error) {
      console.error('加载会话数据失败:', error);
    }
  };

  /**
   * 保存会话到本地存储
   */
  const saveSessions = useCallback((newSessions: ChatSession[], currentSessionId?: string) => {
    try {
      localStorage.setItem('dangbei-mui-chat-sessions', JSON.stringify(newSessions));
      if (currentSessionId) {
        localStorage.setItem('dangbei-mui-chat-current-session', currentSessionId);
      }
    } catch (error) {
      console.error('保存会话数据失败:', error);
    }
  }, []);

  /**
   * 创建新会话
   */
  const createNewSession = useCallback((model?: string) => {
    const newSession: ChatSession = {
      id: generateId(),
      title: '新对话',
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      model: model || selectedModel
    };

    const newSessions = [newSession, ...sessions];
    setSessions(newSessions);
    setCurrentSession(newSession);
    saveSessions(newSessions, newSession.id);
    setError(null);
  }, [sessions, selectedModel, saveSessions]);

  /**
   * 选择会话
   */
  const selectSession = useCallback((sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSession(session);
      localStorage.setItem('dangbei-mui-chat-current-session', sessionId);
      setError(null);
    }
  }, [sessions]);

  /**
   * 删除会话
   */
  const deleteSession = useCallback((sessionId: string) => {
    const newSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(newSessions);
    
    if (currentSession?.id === sessionId) {
      const nextSession = newSessions.length > 0 ? newSessions[0] : null;
      setCurrentSession(nextSession);
      if (nextSession) {
        localStorage.setItem('dangbei-mui-chat-current-session', nextSession.id);
      } else {
        localStorage.removeItem('dangbei-mui-chat-current-session');
      }
    }
    
    saveSessions(newSessions);
  }, [sessions, currentSession, saveSessions]);

  /**
   * 清空当前会话
   */
  const clearCurrentSession = useCallback(() => {
    if (!currentSession) return;

    const clearedSession: ChatSession = {
      ...currentSession,
      messages: [],
      updatedAt: Date.now()
    };

    const newSessions = sessions.map(s => 
      s.id === currentSession.id ? clearedSession : s
    );

    setSessions(newSessions);
    setCurrentSession(clearedSession);
    saveSessions(newSessions, clearedSession.id);
    setError(null);
  }, [currentSession, sessions, saveSessions]);

  /**
   * 更新会话
   */
  const updateSession = useCallback((updatedSession: ChatSession) => {
    const newSessions = sessions.map(s => 
      s.id === updatedSession.id ? updatedSession : s
    );
    
    // 将更新的会话移到最前面
    const sessionIndex = newSessions.findIndex(s => s.id === updatedSession.id);
    if (sessionIndex > 0) {
      const [session] = newSessions.splice(sessionIndex, 1);
      newSessions.unshift(session);
    }

    setSessions(newSessions);
    setCurrentSession(updatedSession);
    saveSessions(newSessions, updatedSession.id);
  }, [sessions, saveSessions]);

  /**
   * 发送消息
   */
  const sendMessage = useCallback(async (content: string, options?: ChatOptions) => {
    if (!content.trim() || status === ChatStatus.STREAMING) {
      return;
    }

    // 如果没有当前会话，创建新会话
    let session = currentSession;
    if (!session) {
      session = {
        id: generateId(),
        title: generateSessionTitle(content),
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        model: selectedModel
      };
      setCurrentSession(session);
    }

    // 创建用户消息
    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content: content.trim(),
      timestamp: Date.now(),
      status: 'sent'
    };

    // 创建助手消息（用于流式更新）
    const assistantMessage: ChatMessage = {
      id: generateId(),
      role: 'assistant',
      content: '',
      timestamp: Date.now(),
      streaming: true,
      status: 'sending'
    };

    // 更新会话
    const updatedSession: ChatSession = {
      ...session,
      title: session.messages.length === 0 ? generateSessionTitle(content) : session.title,
      messages: [...session.messages, userMessage, assistantMessage],
      updatedAt: Date.now(),
      model: selectedModel
    };

    updateSession(updatedSession);
    setStatus(ChatStatus.CONNECTING);
    setError(null);
    lastUserMessageRef.current = content;
    currentMessageRef.current = assistantMessage;

    // 准备API请求
    const request = {
      messages: [...session.messages, userMessage].map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      model: selectedModel,
      stream: true,
      conversation_id: session.conversationId,
      options
    };

    // 设置SSE回调
    const callbacks: SSECallbacks = {
      onConnect: () => {
        setStatus(ChatStatus.STREAMING);
      },
      
      onMessage: (content: string, data) => {
        if (!currentMessageRef.current) return;

        // 根据content_type设置消息类型
        const contentType: ContentType = data.content_type as ContentType || 'text';
        
        // 更新助手消息内容
        currentMessageRef.current = {
          ...currentMessageRef.current,
          content: currentMessageRef.current.content + content,
          contentType,
          streaming: true
        };

        // 更新会话中的消息
        const newSession = { ...updatedSession };
        const messageIndex = newSession.messages.findIndex(m => m.id === assistantMessage.id);
        if (messageIndex !== -1) {
          newSession.messages[messageIndex] = { ...currentMessageRef.current };
          newSession.updatedAt = Date.now();
          
          // 保存对话ID
          if (data.conversation_id && !newSession.conversationId) {
            newSession.conversationId = data.conversation_id;
          }
          
          updateSession(newSession);
        }
      },
      
      onComplete: (data) => {
        if (!currentMessageRef.current) return;

        // 完成流式输出
        const finalMessage: ChatMessage = {
          ...currentMessageRef.current,
          streaming: false,
          status: 'sent'
        };

        const newSession = { ...updatedSession };
        const messageIndex = newSession.messages.findIndex(m => m.id === assistantMessage.id);
        if (messageIndex !== -1) {
          newSession.messages[messageIndex] = finalMessage;
          newSession.updatedAt = Date.now();
          
          // 保存对话ID
          if (data.conversation_id && !newSession.conversationId) {
            newSession.conversationId = data.conversation_id;
          }
          
          updateSession(newSession);
        }

        setStatus(ChatStatus.COMPLETED);
        currentMessageRef.current = null;
      },
      
      onError: (error) => {
        console.error('聊天错误:', error);
        setError({
          message: error.message || '发送消息失败',
          type: 'chat_error'
        });
        setStatus(ChatStatus.ERROR);
        
        // 移除失败的助手消息
        if (currentMessageRef.current) {
          const newSession = { ...updatedSession };
          newSession.messages = newSession.messages.filter(m => m.id !== assistantMessage.id);
          updateSession(newSession);
          currentMessageRef.current = null;
        }
      }
    };

    // 发送请求
    try {
      await chatApi.sendMessage(request, callbacks);
    } catch (error) {
      console.error('发送消息失败:', error);
      callbacks.onError?.(error as Error);
    }
  }, [currentSession, selectedModel, status, updateSession]);

  /**
   * 停止聊天
   */
  const stopChat = useCallback(() => {
    chatApi.stopChat();
    setStatus(ChatStatus.IDLE);
    
    // 停止当前消息的流式输出
    if (currentMessageRef.current && currentSession) {
      const finalMessage: ChatMessage = {
        ...currentMessageRef.current,
        streaming: false,
        status: 'sent'
      };

      const newSession = { ...currentSession };
      const messageIndex = newSession.messages.findIndex(m => m.id === currentMessageRef.current!.id);
      if (messageIndex !== -1) {
        newSession.messages[messageIndex] = finalMessage;
        updateSession(newSession);
      }
      
      currentMessageRef.current = null;
    }
  }, [currentSession, updateSession]);

  /**
   * 重试最后一条消息
   */
  const retryLastMessage = useCallback(async () => {
    if (lastUserMessageRef.current) {
      await sendMessage(lastUserMessageRef.current);
    }
  }, [sendMessage]);

  return {
    // 状态
    currentSession,
    sessions,
    status,
    error,
    models,
    selectedModel,
    
    // 操作方法
    sendMessage,
    createNewSession,
    selectSession,
    deleteSession,
    clearCurrentSession,
    stopChat,
    setSelectedModel,
    retryLastMessage
  };
};
