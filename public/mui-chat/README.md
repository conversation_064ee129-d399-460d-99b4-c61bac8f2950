# 当贝AI聊天界面 - Material-UI版本

这是一个基于 Material-UI 设计的全新聊天界面，为当贝AI提供现代化的用户体验。

## 🌟 功能特性

### 🎨 现代化设计
- **Material-UI组件库**：使用Google Material Design设计语言
- **响应式布局**：完美适配桌面端和移动端
- **优雅的动画效果**：流畅的过渡和交互动画
- **中文字体优化**：支持中文显示的字体配置

### 💬 聊天功能
- **流式响应**：实时显示AI回复内容
- **多模型支持**：支持切换不同的AI模型
- **消息类型区分**：支持文本、思考过程、搜索进度、搜索结果等不同类型
- **Markdown渲染**：支持代码高亮、表格、列表等格式
- **消息操作**：复制消息、重试发送等功能

### 🔧 高级选项
- **深度思考模式**：启用AI的深度推理能力
- **联网搜索**：获取最新信息进行回答
- **会话管理**：创建、删除、清空对话
- **本地存储**：自动保存聊天历史

### 📱 用户体验
- **侧边栏导航**：便捷的会话切换和模型选择
- **智能输入框**：自动调整高度、字符计数
- **错误处理**：友好的错误提示和重试机制
- **加载状态**：清晰的加载和处理状态指示

## 🚀 快速开始

### 前置要求
- Node.js 16.0.0 或更高版本
- npm 或 yarn 包管理器
- 当贝AI Provider后端服务运行在 http://localhost:3000

### 安装依赖
```bash
cd public/mui-chat
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 http://localhost:3001 启动，并自动代理API请求到后端服务器。

### 构建生产版本
```bash
npm run build
```

构建文件将输出到 `dist` 目录。

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
public/mui-chat/
├── src/
│   ├── components/          # React组件
│   │   ├── ChatInterface.tsx    # 主聊天界面
│   │   ├── MessageList.tsx      # 消息列表
│   │   ├── Message.tsx          # 单个消息
│   │   ├── MessageInput.tsx     # 消息输入
│   │   └── Sidebar.tsx          # 侧边栏
│   ├── hooks/              # 自定义Hook
│   │   └── useChat.ts           # 聊天状态管理
│   ├── services/           # API服务
│   │   └── chatApi.ts           # 聊天API调用
│   ├── types/              # TypeScript类型定义
│   │   └── chat.ts              # 聊天相关类型
│   ├── App.tsx             # 主应用组件
│   └── main.tsx            # 应用入口
├── index.html              # HTML模板
├── package.json            # 项目配置
├── vite.config.ts          # Vite配置
└── tsconfig.json           # TypeScript配置
```

## 🔧 技术栈

- **React 18**：现代化的React框架
- **TypeScript**：类型安全的JavaScript
- **Material-UI v5**：Google Material Design组件库
- **Vite**：快速的前端构建工具
- **React Markdown**：Markdown内容渲染
- **Emotion**：CSS-in-JS样式解决方案

## 🎯 核心组件说明

### ChatInterface
主聊天界面组件，整合所有功能模块：
- 管理侧边栏显示状态
- 处理错误提示
- 协调各子组件交互

### MessageList
消息列表组件，负责显示聊天消息：
- 自动滚动到最新消息
- 支持不同消息状态显示
- 提供重试和停止功能

### Message
单个消息组件，支持多种消息类型：
- 用户消息和AI回复的不同样式
- Markdown内容渲染
- 消息操作（复制、时间显示）

### MessageInput
消息输入组件，提供丰富的输入功能：
- 自适应高度的文本框
- 聊天选项配置
- 字符计数和快捷键提示

### Sidebar
侧边栏组件，包含导航和设置：
- 会话列表管理
- 模型选择
- 当前会话操作

### useChat Hook
聊天状态管理Hook，核心功能包括：
- 会话状态管理
- 消息发送和接收
- 本地存储同步
- 错误处理

## 🔌 API集成

新界面通过HTTP API与后端服务通信：

### 模型列表
```
GET /api/models
```

### 聊天对话
```
POST /api/chat
Content-Type: application/json

{
  "messages": [...],
  "model": "doubao-1_6-thinking",
  "stream": true,
  "options": {
    "deep_thinking": true,
    "online_search": false
  }
}
```

### 流式响应
支持Server-Sent Events (SSE)流式响应，实时接收AI回复。

## 🎨 主题和样式

### Material-UI主题配置
- 支持中文字体（Noto Sans SC）
- 自定义滚动条样式
- 优化的颜色方案

### 响应式设计
- 移动端：抽屉式侧边栏
- 桌面端：固定侧边栏
- 自适应布局和字体大小

## 📝 开发说明

### 添加新功能
1. 在相应目录创建组件或Hook
2. 更新类型定义（如需要）
3. 在主组件中集成新功能
4. 更新文档

### 样式定制
使用Material-UI的主题系统进行样式定制：
```typescript
const theme = createTheme({
  palette: {
    primary: { main: '#1976d2' },
    // 其他配置...
  }
});
```

### 状态管理
使用React Hooks进行状态管理，主要状态包括：
- 当前会话和会话列表
- 聊天状态（连接中、流式输出等）
- 错误状态和用户设置

## 🔄 与原版界面的关系

### 功能复用
- **完全复用**后端API和业务逻辑
- **保持兼容**现有的聊天协议
- **独立运行**不影响原有界面

### 技术优势
- **现代化技术栈**：React + TypeScript + Material-UI
- **更好的用户体验**：流畅动画、响应式设计
- **更强的可维护性**：组件化架构、类型安全
- **更丰富的功能**：更多交互选项和视觉反馈

## 🚀 部署说明

### 开发环境
1. 确保后端服务运行在 http://localhost:3000
2. 启动前端开发服务器：`npm run dev`
3. 访问 http://localhost:3001

### 生产环境
1. 构建生产版本：`npm run build`
2. 将 `dist` 目录部署到Web服务器
3. 配置反向代理将API请求转发到后端服务

## 📄 许可证

本项目遵循 MIT 许可证。
