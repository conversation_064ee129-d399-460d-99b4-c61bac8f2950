#!/bin/bash

# 当贝AI聊天界面 - Material-UI版本启动脚本

echo "🚀 启动当贝AI聊天界面 - Material-UI版本"
echo "=================================="

# 检查Node.js版本
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js 16.0.0或更高版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2)
echo "📦 Node.js版本: $NODE_VERSION"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📥 正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已安装"
fi

# 检查后端服务
echo "🔍 检查后端服务状态..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 后端服务运行正常"
else
    echo "⚠️  警告: 后端服务未运行，请先启动后端服务："
    echo "   cd ../../ && npm run server"
    echo ""
    echo "🔄 继续启动前端服务..."
fi

echo ""
echo "🌐 启动开发服务器..."
echo "📍 前端地址: http://localhost:3001"
echo "📍 后端地址: http://localhost:3000"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动开发服务器
npm run dev
