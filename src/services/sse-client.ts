/**
 * SSE客户端服务
 * 处理Server-Sent Events流式响应
 */

import EventSource from 'eventsource';
import { 
  SSEMessageType, 
  SSEEventData, 
  SSEMessageDelta, 
  SSEChatCompleted,
  ChatCallbacks,
  DangbeiApiError,
  ErrorType
} from '../types';



/**
 * SSE客户端类
 * 负责处理流式响应数据
 */
export class SSEClient {
  private eventSource: EventSource | null = null;
  private isConnected: boolean = false;

  /**
   * 连接SSE流
   * 
   * @param url SSE端点URL
   * @param headers 请求头
   * @param callbacks 回调函数
   * @returns Promise，连接建立后resolve
   */
  public async connect(
    url: string,
    headers: Record<string, string>,
    callbacks: ChatCallbacks
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 创建EventSource实例
        this.eventSource = new EventSource(url, {
          headers
        });

        // 连接打开事件
        this.eventSource.onopen = () => {
          this.isConnected = true;
          resolve();
        };

        // 接收消息事件
        this.eventSource.onmessage = (event) => {
          this.handleMessage(event, callbacks);
        };

        // 错误事件
        this.eventSource.onerror = (_error) => {
          this.isConnected = false;
          const apiError = new DangbeiApiError(
            'SSE连接错误',
            ErrorType.NETWORK_ERROR
          );

          if (callbacks.onError) {
            callbacks.onError(apiError);
          } else {
            reject(apiError);
          }
        };

        // 设置超时
        setTimeout(() => {
          if (!this.isConnected) {
            this.close();
            reject(new DangbeiApiError(
              'SSE连接超时',
              ErrorType.TIMEOUT_ERROR
            ));
          }
        }, 10000); // 10秒超时

      } catch (error) {
        reject(new DangbeiApiError(
          `SSE连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
          ErrorType.NETWORK_ERROR
        ));
      }
    });
  }

  /**
   * 处理SSE消息
   * 
   * @param event SSE事件
   * @param callbacks 回调函数
   */
  private handleMessage(event: MessageEvent, callbacks: ChatCallbacks): void {
    try {
      // 解析事件数据
      const eventData = this.parseEventData(event.data);
      if (!eventData) {
        return;
      }

      // 根据事件类型处理
      switch (eventData.type) {
        case SSEMessageType.MESSAGE_DELTA:
          this.handleMessageDelta(eventData.data as SSEMessageDelta, callbacks);
          break;

        case SSEMessageType.CHAT_COMPLETED:
          this.handleChatCompleted(eventData.data as SSEChatCompleted, callbacks);
          break;

        default:
          console.warn('未知的SSE事件类型:', eventData.type);
      }
    } catch (error) {
      const apiError = new DangbeiApiError(
        `处理SSE消息失败: ${error instanceof Error ? error.message : '未知错误'}`,
        ErrorType.UNKNOWN_ERROR
      );
      
      if (callbacks.onError) {
        callbacks.onError(apiError);
      }
    }
  }

  /**
   * 解析事件数据
   * 
   * @param data 原始事件数据
   * @returns 解析后的事件数据
   */
  private parseEventData(data: string): { type: string; data: SSEEventData } | null {
    try {
      // 尝试解析JSON数据
      const parsedData = JSON.parse(data) as SSEEventData;
      
      // 根据数据结构判断事件类型
      if ('content' in parsedData && 'role' in parsedData) {
        return {
          type: SSEMessageType.MESSAGE_DELTA,
          data: parsedData as SSEMessageDelta
        };
      }
      
      if ('id' in parsedData && 'conversation_id' in parsedData && !('content' in parsedData)) {
        return {
          type: SSEMessageType.CHAT_COMPLETED,
          data: parsedData as SSEChatCompleted
        };
      }
      
      return null;
    } catch (error) {
      console.warn('解析SSE数据失败:', error, '原始数据:', data);
      return null;
    }
  }

  /**
   * 处理消息增量事件
   * 
   * @param data 消息增量数据
   * @param callbacks 回调函数
   */
  private handleMessageDelta(data: SSEMessageDelta, callbacks: ChatCallbacks): void {
    if (callbacks.onMessage) {
      callbacks.onMessage(data.content, data);
    }
  }

  /**
   * 处理聊天完成事件
   * 
   * @param data 聊天完成数据
   * @param callbacks 回调函数
   */
  private handleChatCompleted(data: SSEChatCompleted, callbacks: ChatCallbacks): void {
    if (callbacks.onComplete) {
      callbacks.onComplete(data);
    }
    
    // 聊天完成后自动关闭连接
    this.close();
  }

  /**
   * 关闭SSE连接
   */
  public close(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
  }

  /**
   * 检查连接状态
   * 
   * @returns 是否已连接
   */
  public isConnectedToStream(): boolean {
    return this.isConnected;
  }

  /**
   * 获取连接状态
   * 
   * @returns 连接状态字符串
   */
  public getReadyState(): string {
    if (!this.eventSource) {
      return 'CLOSED';
    }
    
    switch (this.eventSource.readyState) {
      case EventSource.CONNECTING:
        return 'CONNECTING';
      case EventSource.OPEN:
        return 'OPEN';
      case EventSource.CLOSED:
        return 'CLOSED';
      default:
        return 'UNKNOWN';
    }
  }
}
