/**
 * 具有重连能力的 SSE 客户端
 * 支持自动重连、指数退避和错误恢复
 */

import EventSource from 'eventsource';
import { ChatCallbacks, DangbeiApiError, ErrorType } from '../types';

/**
 * 重连配置
 */
interface ReconnectConfig {
  /** 最大重连次数 */
  maxRetries: number;
  /** 初始重连延迟（毫秒） */
  initialDelay: number;
  /** 最大重连延迟（毫秒） */
  maxDelay: number;
  /** 延迟倍增因子 */
  backoffFactor: number;
  /** 连接超时时间（毫秒） */
  connectionTimeout: number;
}

/**
 * 连接状态
 */
enum ConnectionState {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  RECONNECTING = 'RECONNECTING',
  FAILED = 'FAILED'
}

/**
 * 具有弹性的 SSE 客户端
 */
export class ResilientSSEClient {
  private eventSource: EventSource | null = null;
  private state: ConnectionState = ConnectionState.DISCONNECTED;
  private retryCount: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private connectionTimer: NodeJS.Timeout | null = null;
  
  private readonly config: ReconnectConfig = {
    maxRetries: 5,
    initialDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    connectionTimeout: 10000
  };

  private url: string = '';
  private headers: Record<string, string> = {};
  private callbacks: ChatCallbacks = {};

  constructor(config?: Partial<ReconnectConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
  }

  /**
   * 连接到 SSE 流
   * 
   * @param url SSE 端点 URL
   * @param headers 请求头
   * @param callbacks 回调函数
   */
  public async connect(
    url: string,
    headers: Record<string, string>,
    callbacks: ChatCallbacks
  ): Promise<void> {
    this.url = url;
    this.headers = headers;
    this.callbacks = callbacks;
    this.retryCount = 0;

    return this.attemptConnection();
  }

  /**
   * 尝试建立连接
   */
  private async attemptConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.state === ConnectionState.CONNECTING || this.state === ConnectionState.CONNECTED) {
        resolve();
        return;
      }

      this.state = ConnectionState.CONNECTING;
      console.log(`[SSE] 尝试连接到: ${this.url} (第 ${this.retryCount + 1} 次)`);

      try {
        // 清理之前的连接
        this.cleanup();

        // 创建新的 EventSource
        this.eventSource = new EventSource(this.url, {
          headers: this.headers
        });

        // 设置连接超时
        this.connectionTimer = setTimeout(() => {
          if (this.state === ConnectionState.CONNECTING) {
            console.error('[SSE] 连接超时');
            this.handleConnectionError(new Error('连接超时'), reject);
          }
        }, this.config.connectionTimeout);

        // 连接成功
        this.eventSource.onopen = () => {
          console.log('[SSE] 连接已建立');
          this.state = ConnectionState.CONNECTED;
          this.retryCount = 0;
          
          if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
          }
          
          resolve();
        };

        // 接收消息
        this.eventSource.onmessage = (event) => {
          this.handleMessage(event);
        };

        // 连接错误
        this.eventSource.onerror = (error) => {
          console.error('[SSE] 连接错误:', error);
          this.handleConnectionError(error, reject);
        };

      } catch (error) {
        console.error('[SSE] 创建连接时出错:', error);
        this.handleConnectionError(error as Error, reject);
      }
    });
  }

  /**
   * 处理连接错误
   * 
   * @param error 错误对象
   * @param reject Promise reject 函数
   */
  private handleConnectionError(error: any, reject?: (reason?: any) => void): void {
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }

    // 如果是首次连接失败，直接拒绝
    if (this.retryCount === 0 && reject) {
      this.state = ConnectionState.FAILED;
      const apiError = new DangbeiApiError(
        `SSE 连接失败: ${error.message || '未知错误'}`,
        ErrorType.NETWORK_ERROR
      );
      reject(apiError);
      return;
    }

    // 检查是否应该重连
    if (this.shouldReconnect()) {
      this.scheduleReconnect();
    } else {
      this.state = ConnectionState.FAILED;
      console.error('[SSE] 达到最大重连次数，停止重连');
      
      if (this.callbacks.onError) {
        const apiError = new DangbeiApiError(
          'SSE 连接失败，已达到最大重连次数',
          ErrorType.NETWORK_ERROR
        );
        this.callbacks.onError(apiError);
      }
    }
  }

  /**
   * 判断是否应该重连
   */
  private shouldReconnect(): boolean {
    return this.retryCount < this.config.maxRetries && 
           this.state !== ConnectionState.FAILED;
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.state = ConnectionState.RECONNECTING;
    this.retryCount++;

    // 计算重连延迟（指数退避）
    const delay = Math.min(
      this.config.initialDelay * Math.pow(this.config.backoffFactor, this.retryCount - 1),
      this.config.maxDelay
    );

    console.log(`[SSE] 将在 ${delay}ms 后重连 (第 ${this.retryCount} 次重试)`);

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.attemptConnection().catch((error) => {
        console.error('[SSE] 重连失败:', error);
      });
    }, delay);
  }

  /**
   * 处理接收到的消息
   * 
   * @param event 消息事件
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // 这里可以集成改进的消息解析逻辑
      const data = event.data;
      
      if (!data || data === '') {
        return;
      }

      // 尝试解析 JSON
      let parsedData;
      try {
        parsedData = JSON.parse(data);
      } catch (parseError) {
        console.warn('[SSE] JSON 解析失败:', parseError, '原始数据:', data);
        return;
      }

      // 根据数据类型调用相应的回调
      if (parsedData.content && this.callbacks.onMessage) {
        this.callbacks.onMessage(parsedData.content, parsedData);
      } else if (parsedData.id && parsedData.conversation_id && !parsedData.content && this.callbacks.onComplete) {
        this.callbacks.onComplete(parsedData);
      }

    } catch (error) {
      console.error('[SSE] 处理消息时出错:', error);
      
      if (this.callbacks.onError) {
        const apiError = new DangbeiApiError(
          `处理 SSE 消息失败: ${error instanceof Error ? error.message : '未知错误'}`,
          ErrorType.UNKNOWN_ERROR
        );
        this.callbacks.onError(apiError);
      }
    }
  }

  /**
   * 关闭连接
   */
  public close(): void {
    console.log('[SSE] 关闭连接');
    this.state = ConnectionState.DISCONNECTED;
    this.cleanup();
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
  }

  /**
   * 获取当前连接状态
   */
  public getState(): ConnectionState {
    return this.state;
  }

  /**
   * 检查是否已连接
   */
  public isConnected(): boolean {
    return this.state === ConnectionState.CONNECTED;
  }

  /**
   * 手动触发重连
   */
  public reconnect(): void {
    if (this.state === ConnectionState.CONNECTED) {
      this.close();
    }
    
    this.retryCount = 0;
    this.attemptConnection().catch((error) => {
      console.error('[SSE] 手动重连失败:', error);
    });
  }
}
