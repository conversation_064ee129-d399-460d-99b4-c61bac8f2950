/**
 * 通用服务
 * 提供ID生成等通用功能
 */

import { BaseApiResponse } from '../types';
import { HttpClient } from './http-client';
import { SignatureUtils } from '../utils';

/**
 * 通用服务类
 * 提供ID生成等通用API接口
 */
export class CommonService {
  private readonly httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * 生成唯一ID
   * 用于消息ID、聊天ID等
   *
   * @param timestamp 时间戳，如果不提供则使用当前时间
   * @returns 生成的ID字符串
   */
  public async generateId(timestamp?: number): Promise<string> {
    // 使用提供的时间戳或当前时间戳（毫秒级，这是API要求的格式）
    const ts = timestamp || SignatureUtils.getTimestampMs();

    // 构建请求数据（注意：API要求请求体中使用毫秒级时间戳）
    const requestData = {
      timestamp: ts
    };

    // 发送生成ID请求
    const response: BaseApiResponse<string> =
      await this.httpClient.post('/ai-search/commonApi/v1/generateId', requestData);

    return response.data;
  }

  /**
   * 批量生成ID
   *
   * @param count 生成数量
   * @returns ID数组
   */
  public async generateIds(count: number): Promise<string[]> {
    const ids: string[] = [];
    const baseTimestamp = SignatureUtils.getTimestampMs();

    // 并发生成多个ID，使用不同的时间戳避免冲突（毫秒级时间戳）
    const promises = Array.from({ length: count }, (_, index) =>
      this.generateId(baseTimestamp + index)
    );

    const results = await Promise.all(promises);
    ids.push(...results);

    return ids;
  }

  /**
   * 验证ID格式
   * 
   * @param id 待验证的ID
   * @returns 是否为有效格式
   */
  public isValidId(id: string): boolean {
    // ID应该是数字字符串，长度通常在15-20位
    return /^\d{15,20}$/.test(id);
  }

  /**
   * 生成本地ID
   * 当无法连接服务器时的备用方案
   * 
   * @returns 本地生成的ID
   */
  public generateLocalId(): string {
    // 使用时间戳 + 随机数生成本地ID
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    return `${timestamp}${random.toString().padStart(6, '0')}`;
  }

  /**
   * 检查服务可用性
   * 
   * @returns 服务是否可用
   */
  public async checkServiceAvailability(): Promise<boolean> {
    try {
      await this.generateId();
      return true;
    } catch (error) {
      console.warn('通用服务不可用:', error);
      return false;
    }
  }
}
