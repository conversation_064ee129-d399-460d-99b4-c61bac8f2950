/**
 * 对话服务
 * 负责处理对话的创建和管理
 */

import { 
  CreateConversationRequest, 
  CreateConversationResponse,
  BaseApiResponse
} from '../types';
import { HttpClient } from './http-client';

/**
 * 对话服务类
 * 提供对话创建和管理的API接口
 */
export class ConversationService {
  private readonly httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * 创建新的对话会话
   * 
   * @param options 创建选项
   * @returns 对话信息
   */
  public async createConversation(options?: {
    /** 超级代理路径，默认为'/chat' */
    superAgentPath?: string;
    /** 是否匿名，默认为false */
    isAnonymous?: boolean;
    /** 来源标识 */
    source?: string;
  }): Promise<CreateConversationResponse> {
    // 构建请求参数
    const requestData: CreateConversationRequest = {
      conversationList: [{
        metaData: {
          chatModelConfig: {},
          superAgentPath: options?.superAgentPath || '/chat'
        },
        shareId: '',
        isAnonymous: options?.isAnonymous || false,
        source: options?.source || ''
      }]
    };

    // 发送创建对话请求
    const response: BaseApiResponse<CreateConversationResponse> = 
      await this.httpClient.post('/ai-search/conversationApi/v1/batch/create', requestData);

    return response.data;
  }

  /**
   * 批量创建对话
   * 
   * @param conversations 对话配置列表
   * @returns 对话信息
   */
  public async createConversations(conversations: Array<{
    superAgentPath?: string;
    isAnonymous?: boolean;
    source?: string;
  }>): Promise<CreateConversationResponse> {
    // 构建请求参数
    const requestData: CreateConversationRequest = {
      conversationList: conversations.map(conv => ({
        metaData: {
          chatModelConfig: {},
          superAgentPath: conv.superAgentPath || '/chat'
        },
        shareId: '',
        isAnonymous: conv.isAnonymous || false,
        source: conv.source || ''
      }))
    };

    // 发送批量创建对话请求
    const response: BaseApiResponse<CreateConversationResponse> = 
      await this.httpClient.post('/ai-search/conversationApi/v1/batch/create', requestData);

    return response.data;
  }

  /**
   * 验证对话ID格式
   * 
   * @param conversationId 对话ID
   * @returns 是否为有效格式
   */
  public isValidConversationId(conversationId: string): boolean {
    // 对话ID应该是数字字符串
    return /^\d+$/.test(conversationId) && conversationId.length > 0;
  }

  /**
   * 获取对话的默认配置
   * 
   * @returns 默认对话配置
   */
  public getDefaultConversationConfig(): {
    superAgentPath: string;
    isAnonymous: boolean;
    source: string;
  } {
    return {
      superAgentPath: '/chat',
      isAnonymous: false,
      source: ''
    };
  }
}
