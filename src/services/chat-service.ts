/**
 * 聊天服务
 * 负责处理聊天消息的发送和接收
 */

import {
  ChatRequest,
  ChatOptions,
  ChatResponse,
  ChatCallbacks,
  SSEMessageDelta,
  SSEChatCompleted,
  DangbeiApiError,
  ErrorType
} from '../types';
import { HttpClient } from './http-client';
import { SSEClient } from './sse-client';
import { CommonService } from './common-service';
import { V2ErrorHandler } from '../utils/v2-error-handler';
import { ImprovedSSEProcessor } from './improved-sse-processor';
import { convertOptionsToUserAction } from '../utils/options-converter';

/**
 * 聊天服务类
 * 提供聊天消息发送和流式响应处理
 */
export class ChatService {
  private readonly httpClient: HttpClient;
  private readonly sseClient: SSEClient;
  private readonly commonService: CommonService;
  private readonly sseProcessor: ImprovedSSEProcessor;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
    this.sseClient = new SSEClient();
    this.commonService = new CommonService(httpClient);
    this.sseProcessor = new ImprovedSSEProcessor();
  }

  /**
   * 发送聊天消息（流式响应）
   *
   * @param options 聊天选项
   * @param callbacks 回调函数（可选）
   * @returns 聊天响应或void
   */
  public async chat(options: ChatOptions): Promise<ChatResponse>;
  public async chat(options: ChatOptions, callbacks: ChatCallbacks): Promise<void>;
  public async chat(options: ChatOptions, callbacks?: ChatCallbacks): Promise<ChatResponse | void> {
    // 生成消息ID
    const messageId = await this.commonService.generateId();

    // 构建聊天请求
    const chatRequest = await this.buildChatRequest(options, messageId);

    // 如果提供了回调函数，直接使用流式模式
    if (callbacks) {
      return this.sendChatRequest(chatRequest, callbacks);
    }

    // 否则使用同步模式，收集所有消息后返回
    let fullContent = '';
    let lastMessageData: SSEMessageDelta | null = null;

    return new Promise((resolve, reject) => {
      // 设置内部回调函数
      const internalCallbacks: ChatCallbacks = {
        onMessage: (content: string, data: SSEMessageDelta) => {
          fullContent += content;
          lastMessageData = data;

          // 调用用户提供的回调
          if (options.callbacks?.onMessage) {
            options.callbacks.onMessage(content, data);
          }
        },

        onComplete: (data: SSEChatCompleted) => {
          // 调用用户提供的回调
          if (options.callbacks?.onComplete) {
            options.callbacks.onComplete(data);
          }

          // 构建最终响应
          const response: ChatResponse = {
            content: fullContent,
            messageId: data.id,
            parentMessageId: data.parentMsgId,
            conversationId: data.conversation_id,
            requestId: lastMessageData?.requestId || ''
          };

          resolve(response);
        },

        onError: (error: Error) => {
          // 调用用户提供的错误回调
          if (options.callbacks?.onError) {
            options.callbacks.onError(error);
          }

          reject(error);
        }
      };

      // 发送聊天请求并处理SSE响应
      this.sendChatRequest(chatRequest, internalCallbacks).catch(reject);
    });
  }

  /**
   * 发送聊天消息（同步响应）
   * 等待完整响应后返回
   * 
   * @param options 聊天选项
   * @returns 完整的响应内容
   */
  public async chatSync(options: ChatOptions): Promise<string> {
    const response = await this.chat(options);
    return response.content;
  }

  /**
   * 构建聊天请求参数
   *
   * @param options 聊天选项
   * @param messageId 消息ID
   * @returns 聊天请求对象
   */
  private async buildChatRequest(options: ChatOptions, messageId: string): Promise<ChatRequest> {
    // 将options参数转换为userAction字符串
    // 根据调用流程.md中的规范：deep_thinking -> "deep", online_search -> "online"
    const userAction = convertOptionsToUserAction(options.options);

    console.log(`[ChatService] 构建聊天请求 - options: ${JSON.stringify(options.options)}, userAction: "${userAction}"`);

    return {
      stream: true,
      botCode: options.botCode || 'AI_SEARCH',
      conversationId: options.conversationId,
      question: options.question,
      model: options.model || 'doubao-1_6-thinking',
      chatOption: {
        searchKnowledge: options.chatOption?.searchKnowledge || false,
        searchAllKnowledge: options.chatOption?.searchAllKnowledge || false,
        searchSharedKnowledge: options.chatOption?.searchSharedKnowledge || false
      },
      knowledgeList: [],
      anonymousKey: '',
      uuid: messageId,
      chatId: messageId,
      files: [],
      reference: [],
      role: 'user',
      status: 'local',
      content: options.question,
      userAction: userAction, // 使用转换后的userAction值
      agentId: ''
    };
  }

  /**
   * 发送聊天请求并处理SSE响应
   *
   * @param chatRequest 聊天请求
   * @param callbacks 回调函数
   */
  private async sendChatRequest(
    chatRequest: ChatRequest,
    callbacks: ChatCallbacks
  ): Promise<void> {
    try {
      console.log('[ChatService] 发送聊天请求，使用改进的 SSE 处理器');

      // 重置处理器状态
      this.sseProcessor.reset();

      // 发送POST请求并获取流式响应
      const stream = await this.httpClient.postStream('/ai-search/chatApi/v2/chat', chatRequest);

      // 使用改进的 SSE 处理器处理流式数据
      await this.sseProcessor.processStream(stream, callbacks);

    } catch (error) {
      // 特殊处理v2接口错误
      const chatUrl = `${this.httpClient['baseURL']}/ai-search/chatApi/v2/chat`;
      const v2ErrorInfo = V2ErrorHandler.analyzeV2Error(error, chatUrl, chatRequest);

      // 在调试模式下输出详细的v2错误分析
      if (process.env['NODE_ENV'] === 'development') {
        const errorReport = V2ErrorHandler.generateErrorReport(v2ErrorInfo);
        console.log(errorReport);
      }

      const apiError = error instanceof DangbeiApiError ? error :
        new DangbeiApiError(
          `聊天请求失败: ${error instanceof Error ? error.message : '未知错误'}`,
          ErrorType.NETWORK_ERROR
        );

      if (callbacks.onError) {
        callbacks.onError(apiError);
      } else {
        throw apiError;
      }
    }
  }





  /**
   * 停止当前聊天
   */
  public stopChat(): void {
    this.sseClient.close();
  }

  /**
   * 检查聊天服务状态
   * 
   * @returns 服务状态信息
   */
  public getStatus(): {
    isConnected: boolean;
    readyState: string;
  } {
    return {
      isConnected: this.sseClient.isConnectedToStream(),
      readyState: this.sseClient.getReadyState()
    };
  }

  /**
   * 验证聊天选项
   * 
   * @param options 聊天选项
   * @returns 验证结果
   */
  public validateChatOptions(options: ChatOptions): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!options.conversationId || !options.conversationId.trim()) {
      errors.push('对话ID不能为空');
    }

    if (!options.question || !options.question.trim()) {
      errors.push('问题内容不能为空');
    }

    if (options.question && options.question.length > 10000) {
      errors.push('问题内容过长，最大支持10000字符');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
