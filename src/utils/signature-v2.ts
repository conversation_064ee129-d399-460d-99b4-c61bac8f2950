/**
 * v2接口签名生成工具
 * 专门处理当贝AI v2接口的签名算法
 */

import { createHash } from 'crypto';
import fs from 'fs';
import path from 'path';
import { SignatureParams, DeviceConfig } from '../types';
import { WasmSignatureEmulator } from './wasm-signature-emulator';

/**
 * WASM签名结果类型
 */
interface WasmSignatureResult {
  nonce: string;
  sign: string;
  timestamp: number;
}

/**
 * v2接口签名生成器
 * 基于WebAssembly模块分析，实现兼容的签名算法
 */
export class SignatureV2Utils {
  // ========================= WASM 原生调用（与 _app chunk 一致）=========================
  // 关键静态状态：仅在首次使用时同步初始化
  private static _wasmInited = false; // 是否已初始化原生WASM
  private static wasmInstance: any;    // WebAssembly.Instance
  private static wasmMemory: WebAssembly.Memory; // 共享内存
  private static textEncoder = new TextEncoder();
  private static textDecoder = new TextDecoder();
  private static lastStringLength = 0; // 模拟浏览器端全局变量 p，用于记录最近一次写入长度

  // ========================= WASM 模拟器（降级方案）=========================
  private static wasmEmulator: WasmSignatureEmulator;

  /**
   * 初始化WASM签名模拟器（降级用）
   */
  private static initWasmEmulator(): void {
    if (!this.wasmEmulator) {
      this.wasmEmulator = new WasmSignatureEmulator({
        debug: process.env['NODE_ENV'] === 'development',
        strategy: 'hybrid',
        secretKey: 'dangbei_ai_v2_secret_2024'
      });
    }
  }

  /**
   * 同步初始化 WebAssembly 实例（读取项目根目录 sign_bg.wasm）
   * 注意：使用 WebAssembly.Module 和 WebAssembly.Instance 实现同步加载，避免打破现有同步签名接口
   */
  private static initNativeWasmSync(): void {
    if (this._wasmInited) return;

    // 解析 WASM 路径：dist 产物位于 dist/utils，WASM 文件位于项目根目录
    // 这里采用与 src/wasm/unified-signature.js 一致的路径策略
    const wasmPath = path.join(__dirname, '../wasm/sign_bg.wasm');

    if (!fs.existsSync(wasmPath)) {
      throw new Error(`未找到 sign_bg.wasm，期望路径: ${wasmPath}`);
    }

    // 读取并同步实例化 WASM 模块
    const wasmBuffer = fs.readFileSync(wasmPath);

    // 构造导入对象（与 src/wasm/dangbei-signature-wasm.js 保持一致）
    const imports = this.createWasmImports();

    const module = new WebAssembly.Module(wasmBuffer);
    const instance = new WebAssembly.Instance(module, imports);

    this.wasmInstance = instance;
    this.wasmMemory = (instance.exports as any).memory as WebAssembly.Memory;

    // 调用模块启动函数
    if ((instance.exports as any).__wbindgen_start) {
      (instance.exports as any).__wbindgen_start();
    }

    this._wasmInited = true;

    if (process.env['NODE_ENV'] === 'development') {
      console.log('🧩 WASM 原生模块已初始化(同步)', {
        wasmPath,
        memorySize: this.wasmMemory.buffer.byteLength
      });
    }
  }

  /**
   * 构造 WASM 导入对象（与 dangbei-signature-wasm.js 基本一致）
   * 说明：以下导入函数用于配合 wasm-bindgen 在纯 WASM 环境下正确创建/传递 JS 值
   */
  private static createWasmImports() {
    const self = this;
    return {
      wbg: {
        // 内存相关
        __wbindgen_memory: () => self.wasmMemory,

        // 字符串与错误
        __wbindgen_string_new: (ptr: number, len: number) => self.getStringFromMemory(ptr, len),
        __wbindgen_error_new: (ptr: number, len: number) => new Error(self.getStringFromMemory(ptr, len)),
        __wbindgen_throw: (ptr: number, len: number) => { throw new Error(self.getStringFromMemory(ptr, len)); },
        __wbindgen_debug_string: (ptr: number, len: number) => {
          const str = self.getStringFromMemory(ptr, len);
          if (process.env['NODE_ENV'] === 'development') {
            console.log(`🐛 WASM调试: ${str}`);
          }
        },

        // 类型检查（按需返回）
        __wbindgen_is_object: (v: any) => typeof v === 'object' && v !== null,
        __wbindgen_is_string: (v: any) => typeof v === 'string',
        __wbindgen_is_function: (v: any) => typeof v === 'function',
        __wbindgen_is_undefined: (v: any) => v === undefined,
        __wbindgen_number_new: (v: number) => v,
        __wbindgen_bigint_from_i64: (v: number) => BigInt(v),
        __wbindgen_bigint_from_u64: (v: number) => BigInt(v),

        // 时间/全局对象（最小实现即可）
        __wbg_new0_f788a2397c7ca929: () => new Date(),
        __wbg_getTime_46267b1c24877e30: (date: Date) => date.getTime(),
        __wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0: () => globalThis,
        __wbg_static_accessor_SELF_37c5d418e4bf5819: () => (typeof self !== 'undefined' ? self : globalThis),
        __wbg_static_accessor_WINDOW_5de37043a91a9c40: () => (typeof window !== 'undefined' ? (window as any) : globalThis),
        __wbg_static_accessor_GLOBAL_88a902d13a557d07: () => (typeof global !== 'undefined' ? (global as any) : globalThis),

        // ===== 关键修复：实现对象属性设置，确保 get_sign 可返回包含 sign 的对象 =====
        // __wbg_set_3f1d0b984ed272ed: (obj, key, val) => { obj[key] = val; }
        // 说明：wasm-bindgen 常用的 set 导入，用于 obj[key] = val
        __wbg_set_3f1d0b984ed272ed: (obj: any, key: any, val: any) => {
          try {
            (obj as any)[key as any] = val;
            return true;
          } catch {
            return false;
          }
        },
        // __wbg_set_37837023f3d740e8: (obj, key, val) => { obj[key] = val; }
        // 说明：某些构建下会使用该变体，同样实现为通用的属性赋值
        __wbg_set_37837023f3d740e8: (obj: any, key: any, val: any) => {
          try {
            (obj as any)[key as any] = val;
          } catch {}
        },
        // __wbg_set_8fc6bf8a5b1071d1: (obj, key, val) => { obj[key] = val; }
        __wbg_set_8fc6bf8a5b1071d1: (obj: any, key: any, val: any) => {
          try {
            (obj as any)[key as any] = val;
          } catch {}
        },

        // 其他环境相关占位（返回最小可用对象，避免 LinkError）
        __wbg_instanceof_Window_def73ea0955fc569: () => false,
        __wbg_document_d249400bd7bd996d: () => null,
        __wbg_getElementById_f827f0d6648718a8: () => null,
        __wbg_crypto_ed58b8e10a292839: () => null,
        __wbg_process_5c1d670bc53614b8: () => process,
        __wbg_versions_c71aa1626a93e0a1: () => process.versions,
        __wbg_node_02999533c4ea02e3: () => process.versions.node,
        __wbg_require_79b1e9274cde3c87: () => require,
        __wbg_msCrypto_0a36e2ec3a343d26: () => null,
        __wbg_getRandomValues_bcb4912f16000dc4: () => {},
        __wbg_randomFillSync_ab2cfe79ebbf2740: () => {},
        __wbg_new_78feb108b6472713: () => ({}) /* new Object() */,
        __wbg_newnoargs_105ed471475aaf50: () => (function () { return {}; }),
        __wbg_new_5e0be73521bc8c17: () => ({}) /* new Object() */,
        __wbg_call_672a4d21634d4a24: () => ({}),
        __wbg_new_405e22f390576ce2: () => ({}) /* new Object() */,
        __wbg_call_7cccdd69e0791ae2: () => ({}),
        __wbg_buffer_609cc3eee51ed158: () => new ArrayBuffer(0),
        __wbg_newwithbyteoffsetandlength_d97e637ebe145a9a: () => new Uint8Array(0),
        __wbg_new_a12002a7f91c75be: () => new Uint8Array(0),
        __wbg_set_65595bdd868b3009: () => {},
        __wbg_newwithlength_a381634e90c276d4: () => new Uint8Array(0),
        __wbg_subarray_aa9065fa9dc5df96: () => new Uint8Array(0),
        __wbindgen_init_externref_table: () => {}
      }
    } as Record<string, any>;
  }

  /**
   * 从 WASM 共享内存读取字符串（UTF-8）
   * 对应原始 JavaScript 中的 d(e, t) 函数
   */
  private static getStringFromMemory(ptr: number, len: number): string {
    const memory = new Uint8Array(this.wasmMemory.buffer);
    const bytes = memory.slice(ptr, ptr + len);
    return this.textDecoder.decode(bytes);
  }

  /**
   * 获取 WASM 内存的 Uint8Array 视图
   * 对应原始 JavaScript 中的 f() 函数
   */
  private static getMemoryView(): Uint8Array {
    // 检查内存缓冲区是否需要更新（模拟原始代码中的缓存逻辑）
    return new Uint8Array(this.wasmMemory.buffer);
  }

  /**
   * 将字符串写入 WASM 共享内存，返回指针
   * 完整模拟原始 JavaScript 中的 g(e, t, n) 函数逻辑
   *
   * 原始函数逻辑：
   * function g(e, t, n) {
   *     if (void 0 === n) {
   *         // 简单 UTF-8 编码路径
   *         var r = h.encode(e), o = t(r.length, 1) >>> 0;
   *         return f().subarray(o, o + r.length).set(r), p = r.length, o
   *     }
   *     // ASCII 快速路径 + 混合处理
   *     // ...
   * }
   *
   * 参数对应：
   * - e: str (要写入的字符串)
   * - t: __wbindgen_malloc (内存分配函数)
   * - n: __wbindgen_realloc (内存重分配函数)
   */
  private static writeStringToMemory(str: string): number {
    if (!this.wasmInstance) throw new Error('WASM 未初始化');

    if (process.env['NODE_ENV'] === 'development') {
      console.log(`🔍 准备写入字符串: 长度=${str.length}, 字节长度=${Buffer.byteLength(str, 'utf8')}, 预览="${str.slice(0, 50)}${str.length > 50 ? '...' : ''}"`);
    }

    const malloc = (this.wasmInstance.exports as any).__wbindgen_malloc;
    const realloc = (this.wasmInstance.exports as any).__wbindgen_realloc;

    // 在 v(e, t) 函数中，总是传入了 realloc 函数，所以使用优化模式
    return this.writeStringToMemoryOptimized(str, malloc, realloc);
  }

  /**
   * 优化模式的字符串写入（对应原始 g 函数的完整逻辑）
   * 实现 ASCII 快速路径和混合 UTF-8 处理
   */
  private static writeStringToMemoryOptimized(
    str: string,
    malloc: (size: number, align: number) => number,
    realloc: (ptr: number, oldSize: number, newSize: number, align: number) => number
  ): number {
    if (!str || str.length === 0) {
      // 处理空字符串的情况
      this.lastStringLength = 0;
      if (process.env['NODE_ENV'] === 'development') {
        console.log(`📝 WASM字符串写入(空字符串): 指针=0x0, 长度=0`);
      }
      return 0;
    }

    const strLength = str.length;
    let ptr: number;
    let writtenCount = 0;

    try {
      // 初始分配内存
      ptr = malloc(strLength, 1) >>> 0;
      if (!ptr) {
        throw new Error('内存分配失败');
      }

      const memory = this.getMemoryView();

      if (process.env['NODE_ENV'] === 'development') {
        console.log(`🔍 开始ASCII快速路径检查: 字符串长度=${strLength}, 初始指针=0x${ptr.toString(16)}`);
      }

      // 第一阶段：尝试 ASCII 快速路径
      // for (var i = e.length, a = t(i, 1) >>> 0, s = f(), c = 0; c < i; c++)
      for (let c = 0; c < strLength; c++) {
        const charCode = str.charCodeAt(c);
        if (charCode > 127) {
          // 遇到非 ASCII 字符，跳出循环
          if (process.env['NODE_ENV'] === 'development') {
            console.log(`🔍 在位置${c}遇到非ASCII字符: ${str.charAt(c)} (${charCode})`);
          }
          break;
        }
        // s[a + c] = l
        memory[ptr + c] = charCode;
        writtenCount = c + 1;
      }

      // 第二阶段：处理剩余的非 ASCII 字符（如果有）
      if (writtenCount !== strLength) {
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`🔍 需要混合处理: ASCII部分=${writtenCount}, 剩余=${strLength - writtenCount}`);
        }

        let remainingStr = str;
        if (writtenCount > 0) {
          remainingStr = str.slice(writtenCount);
        }

        // 重新分配内存：c + 3 * e.length（最坏情况下每个字符3字节）
        const newSize = writtenCount + 3 * remainingStr.length;
        const newPtr = realloc(ptr, strLength, newSize, 1) >>> 0;

        if (!newPtr) {
          throw new Error('内存重分配失败');
        }
        ptr = newPtr;

        // 使用 TextEncoder 处理剩余部分
        const remainingBytes = this.textEncoder.encode(remainingStr);
        const updatedMemory = this.getMemoryView();
        updatedMemory.set(remainingBytes, ptr + writtenCount);

        writtenCount += remainingBytes.length;

        // 最终调整内存大小到实际使用的大小
        const finalPtr = realloc(ptr, newSize, writtenCount, 1) >>> 0;
        if (!finalPtr) {
          throw new Error('最终内存调整失败');
        }
        ptr = finalPtr;
      }

      // 更新全局长度变量（模拟原始代码中的 p = c）
      this.lastStringLength = writtenCount;

      if (process.env['NODE_ENV'] === 'development') {
        const asciiCount = Math.min(writtenCount, strLength);
        const hasNonAscii = writtenCount !== strLength || str.length !== asciiCount;
        console.log(`📝 WASM字符串写入(优化模式): 指针=0x${ptr.toString(16)}, 长度=${writtenCount}, ASCII字符=${asciiCount}, 包含非ASCII=${hasNonAscii}, 内容="${str.slice(0, 30)}${str.length > 30 ? '...' : ''}"`);
      }

      return ptr;

    } catch (error) {
      // 如果优化模式失败，降级到简单模式
      if (process.env['NODE_ENV'] === 'development') {
        console.warn(`⚠️ 优化模式失败，降级到简单模式:`, error);
      }
      return this.writeStringToMemorySimple(str, malloc);
    }
  }

  /**
   * 简单模式的字符串写入（降级方案）
   * 直接使用 UTF-8 编码，不进行 ASCII 优化
   */
  private static writeStringToMemorySimple(
    str: string,
    malloc: (size: number, align: number) => number
  ): number {
    // var r = h.encode(e), o = t(r.length, 1) >>> 0;
    const encoded = this.textEncoder.encode(str);
    const len = encoded.length >>> 0;
    const ptr = malloc(len, 1) >>> 0;

    if (!ptr) {
      throw new Error('简单模式内存分配失败');
    }

    // return f().subarray(o, o + r.length).set(r), p = r.length, o
    const memory = this.getMemoryView();
    memory.subarray(ptr, ptr + len).set(encoded);

    // p = r.length
    this.lastStringLength = len;

    if (process.env['NODE_ENV'] === 'development') {
      console.log(`📝 WASM字符串写入(简单模式): 指针=0x${ptr.toString(16)}, 长度=${len}, 内容="${str.slice(0, 30)}${str.length > 30 ? '...' : ''}"`);
    }

    return ptr;
  }





  /**
   * 通过原生WASM get_sign 生成签名结果
   * 严格模拟原始 JavaScript 中的 v(e, t) 函数行为
   *
   * 原始函数逻辑：
   * function v(e, t) {
   *     var n = g(e, r.__wbindgen_malloc, r.__wbindgen_realloc)  // 写入第一个字符串
   *       , o = p                                                // 获取第一个字符串长度
   *       , i = g(t, r.__wbindgen_malloc, r.__wbindgen_realloc)  // 写入第二个字符串
   *       , a = p;                                               // 获取第二个字符串长度
   *     return r.get_sign(n, o, i, a)                           // 调用 WASM 签名函数
   * }
   *
   * @returns 包含 nonce、sign、timestamp 的完整签名结果
   */
  private static getSignatureFromNativeWasm(s1: string, s2: string): WasmSignatureResult {
    this.initNativeWasmSync();

    // 第一步：写入第一个字符串到 WASM 内存（对应 g(e, r.__wbindgen_malloc, r.__wbindgen_realloc)）
    const ptr1 = this.writeStringToMemory(s1);
    const len1 = this.lastStringLength;  // 对应 o = p

    // 第二步：写入第二个字符串到 WASM 内存（对应 g(t, r.__wbindgen_malloc, r.__wbindgen_realloc)）
    const ptr2 = this.writeStringToMemory(s2);
    const len2 = this.lastStringLength;  // 对应 a = p

    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 WASM签名参数准备:', {
        s1: { ptr: `0x${ptr1.toString(16)}`, len: len1, preview: s1.slice(0, 50) },
        s2: { ptr: `0x${ptr2.toString(16)}`, len: len2, preview: s2.slice(0, 50) }
      });
    }

    // 第三步：调用 WASM 签名函数（对应 r.get_sign(n, o, i, a)）
    const result = (this.wasmInstance.exports as any).get_sign(
      ptr1 >>> 0,  // n: 第一个字符串指针
      len1 >>> 0,  // o: 第一个字符串长度
      ptr2 >>> 0,  // i: 第二个字符串指针
      len2 >>> 0   // a: 第二个字符串长度
    );

    // 验证返回结果必须是包含完整签名信息的对象
    if (!result || typeof result !== 'object') {
      throw new Error(`WASM返回值格式异常: 期望对象，实际类型=${typeof result}, 值="${result}"`);
    }

    // 提取并验证必要字段
    const { nonce, sign, timestamp } = result as any;

    if (!nonce || typeof nonce !== 'string') {
      throw new Error(`WASM返回的nonce无效: ${nonce}`);
    }

    if (!sign || typeof sign !== 'string') {
      throw new Error(`WASM返回的sign无效: ${sign}`);
    }

    if (!timestamp || typeof timestamp !== 'number') {
      throw new Error(`WASM返回的timestamp无效: ${timestamp}`);
    }

    // 验证签名格式（通常是32位十六进制字符串）
    if (!/^[A-F0-9]{32}$/i.test(sign)) {
      throw new Error(`WASM签名格式异常: 值="${sign}"`);
    }

    // 构造返回结果
    const signatureResult: WasmSignatureResult = {
      nonce: String(nonce),
      sign: String(sign).toUpperCase(),
      timestamp: Number(timestamp)
    };

    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 WASM签名生成成功:', {
        result: result,
        nonce: signatureResult.nonce,
        signature: signatureResult.sign,
        timestamp: signatureResult.timestamp,
        signaturePreview: signatureResult.sign.substring(0, 8) + '...'
      });
    }

    return signatureResult;
  }
  /**
   * 生成v2接口完整签名结果
   *
   * 基于对sign_bg.wasm的深入分析，实现多策略签名生成：
   * 1. WASM原生调用（主要策略）- 返回完整的 nonce、sign、timestamp
   * 2. WASM模拟算法（降级方案）
   * 3. 传统算法降级方案
   *
   * @param params 签名参数
   * @param extra 额外配置
   * @returns 包含 nonce、sign、timestamp 的完整签名结果
   */
  public static generateV2SignatureComplete(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): WasmSignatureResult {
    this.initWasmEmulator();

    try {
      // 策略1: 使用WASM原生调用（返回完整对象）
      const wasmResult = this.tryWasmNativeAlgorithm(params, extra);

      if (process.env['NODE_ENV'] === 'development') {
        console.log('🔐 v2接口签名生成策略:');
        console.log(`  - 策略1 (WASM原生): ${wasmResult.sign.substring(0, 8)}...`);
        console.log(`  - 选择WASM原生算法作为主要方案`);
      }

      return wasmResult;

    } catch (nativeErr) {
      console.warn('⚠️ WASM原生调用失败，降级到模拟算法:', (nativeErr as Error).message);

      // 降级策略：使用模拟算法生成签名，然后构造完整对象
      const signature = this.tryWasmEmulationAlgorithm(params, extra);

      // 生成模拟的 nonce 和 timestamp
      const timestamp = Math.floor(Date.now() / 1000);
      const nonce = this.generateNonce();

      return {
        nonce,
        sign: signature,
        timestamp
      };
    }
  }

  /**
   * 生成v2接口签名（兼容性方法）
   *
   * 基于对sign_bg.wasm的深入分析，实现多策略签名生成：
   * 1. WASM模拟算法（主要策略）
   * 2. 传统算法降级方案
   * 3. 设备相关的增强算法
   *
   * @param params 签名参数
   * @param extra 额外配置
   * @returns 签名字符串
   */
  public static generateV2Signature(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    this.initWasmEmulator();

    try {
      // 策略1: 使用WASM模拟算法（基于分析结果的最佳实现）
      const wasmSignature = this.tryWasmEmulationAlgorithm(params, extra);

      // 策略2: 传统METHOD + PATH算法（降级方案）
      const methodPathSignature = this.tryMethodPathAlgorithm(params);

      // 策略3: 简化算法（最后的降级方案）
      const simplifiedSignature = this.trySimplifiedAlgorithm(params);

      // 在调试模式下输出所有尝试的签名
      if (process.env['NODE_ENV'] === 'development') {
        console.log('🔐 v2接口签名生成策略:');
        console.log('  - 策略1 (WASM模拟):', wasmSignature.substring(0, 8) + '...');
        console.log('  - 策略2 (METHOD+PATH):', methodPathSignature.substring(0, 8) + '...');
        console.log('  - 策略3 (简化算法):', simplifiedSignature.substring(0, 8) + '...');
        console.log('  - 选择WASM模拟算法作为主要方案');
      }

      // 优先返回WASM模拟算法的结果
      return wasmSignature;

    } catch (error) {
      console.warn('⚠️ WASM模拟算法失败，使用降级方案:', (error as Error).message);

      // 降级到传统算法
      return this.tryMethodPathAlgorithm(params);
    }
  }

  /**
   * 策略1: WASM原生算法
   * 直接调用WASM的get_sign函数，返回完整的签名对象
   */
  private static tryWasmNativeAlgorithm(
    params: SignatureParams,
    _extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): WasmSignatureResult {
    const { method = 'POST', url = '', bodyRaw, data } = params;

    // 1) 准备第一个参数：请求数据
    let requestData = '';
    const methodUpper = method.toUpperCase();
    if (methodUpper === 'POST') {
      if (typeof bodyRaw === 'string' && bodyRaw.length > 0) {
        requestData = bodyRaw;
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`✅ 使用 bodyRaw: 长度=${bodyRaw.length}, 类型=${typeof bodyRaw}, 预览="${bodyRaw.slice(0, 30)}..."`);
        }
      } else if (data) {
        requestData = JSON.stringify(data);
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`✅ 使用 data: 原始=${JSON.stringify(data).slice(0, 50)}, 序列化长度=${requestData.length}`);
        }
      } else {
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`⚠️ 既没有有效的 bodyRaw 也没有 data, bodyRaw类型=${typeof bodyRaw}, bodyRaw值=${bodyRaw}`);
        }
      }
    } else if (methodUpper === 'GET') {
      const qIndex = url.indexOf('?');
      requestData = qIndex !== -1 ? url.substring(qIndex + 1) : '';
    }

    // 2) 准备第二个参数：URL 路径
    let urlPath = '';
    if (url) {
      try {
        if (url.startsWith('http://') || url.startsWith('https://')) {
          const u = new URL(url);
          urlPath = u.pathname;
        } else {
          const qIndex = url.indexOf('?');
          urlPath = qIndex !== -1 ? url.substring(0, qIndex) : url;
        }
      } catch {
        urlPath = url;
      }
    }

    if (process.env['NODE_ENV'] === 'development') {
      console.log(`🔍 请求数据准备: method=${method}(${methodUpper}), bodyRaw长度=${bodyRaw?.length || 0}, data存在=${!!data}, requestData长度=${requestData.length}, 预览="${requestData.slice(0, 50)}${requestData.length > 50 ? '...' : ''}"`);
    }

    // 直接调用WASM原生方法
    return this.getSignatureFromNativeWasm(requestData, urlPath);
  }

  /**
   * 生成随机nonce字符串
   */
  private static generateNonce(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    for (let i = 0; i < 21; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 策略2: WASM模拟算法
   * 基于对sign_bg.wasm的分析实现的模拟算法
   * 使用真实的WASM调用模式：get_sign(requestDataPtr, requestDataLen, urlPathPtr, urlPathLen)
   */
  private static tryWasmEmulationAlgorithm(
    params: SignatureParams,
    _extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    const { method = 'POST', url = '', bodyRaw, data, timestamp, nonce } = params;

    // 1) 准备第一个参数：请求数据（与浏览器端 _app 中的 g(e, ...) 一致）
    // - POST 使用原始 body 字符串（必须与实际发送完全一致）
    // - GET 使用查询字符串部分
    let requestData = '';
    const methodUpper = method.toUpperCase();
    if (methodUpper === 'POST') {
      // 修复：只有当 bodyRaw 有实际内容时才使用，否则使用 data
      if (typeof bodyRaw === 'string' && bodyRaw.length > 0) {
        requestData = bodyRaw;
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`✅ 使用 bodyRaw: 长度=${bodyRaw.length}, 类型=${typeof bodyRaw}, 预览="${bodyRaw.slice(0, 30)}..."`);
        }
      } else if (data) {
        requestData = JSON.stringify(data);
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`✅ 使用 data: 原始=${JSON.stringify(data).slice(0, 50)}, 序列化长度=${requestData.length}`);
        }
      } else {
        if (process.env['NODE_ENV'] === 'development') {
          console.log(`⚠️ 既没有有效的 bodyRaw 也没有 data, bodyRaw类型=${typeof bodyRaw}, bodyRaw值=${bodyRaw}`);
        }
      }
      // 如果既没有 bodyRaw 也没有 data，requestData 保持为空字符串
    } else if (methodUpper === 'GET') {
      const qIndex = url.indexOf('?');
      requestData = qIndex !== -1 ? url.substring(qIndex + 1) : '';
    }

    if (process.env['NODE_ENV'] === 'development') {
      console.log(`🔍 请求数据准备: method=${method}(${methodUpper}), bodyRaw长度=${bodyRaw?.length || 0}, data存在=${!!data}, requestData长度=${requestData.length}, 预览="${requestData.slice(0, 50)}${requestData.length > 50 ? '...' : ''}"`);
    }

    // 2) 准备第二个参数：URL 路径（严格对齐浏览器 _app 中 v(e,t) 第二参语义）
    //    - 原代码形如：v(e, t) { n=g(e,...); o=p; i=g(t,...); a=p; return r.get_sign(n,o,i,a) }
    //    - 其中 t 为 URL 的“路径部分”，不包含协议/主机；通常类似 "/ai-search/chatApi/v2/chat"
    let urlPath = '';
    if (url) {
      try {
        if (url.startsWith('http://') || url.startsWith('https://')) {
          const u = new URL(url);
          urlPath = u.pathname; // 仅取 pathname，不包含查询串
        } else {
          // 传入可能已是相对路径，直接使用
          const qIndex = url.indexOf('?');
          urlPath = qIndex !== -1 ? url.substring(0, qIndex) : url;
        }
      } catch {
        urlPath = url;
      }
    }

    try {
      // 优先使用“原生 WASM”方式直接调用 get_sign(ptr1,len1,ptr2,len2)
      const wasmResult = this.getSignatureFromNativeWasm(requestData, urlPath);
      const signature = wasmResult.sign;

      if (process.env['NODE_ENV'] === 'development') {
        console.log('🔐 v2签名计算成功(WASM原生)', {
          method,
          url: url.slice(0, 100) + (url.length > 100 ? '...' : ''),
          requestData: {
            content: requestData.slice(0, 100) + (requestData.length > 100 ? '...' : ''),
            length: Buffer.byteLength(requestData, 'utf8'),
            encoding: 'utf8'
          },
          urlPath: {
            content: urlPath,
            length: Buffer.byteLength(urlPath, 'utf8')
          },
          signature: {
            full: signature,
            preview: signature.substring(0, 8) + '...',
            format: 'MD5-HEX-UPPERCASE'
          },
          wasmResult: {
            nonce: wasmResult.nonce,
            timestamp: wasmResult.timestamp
          }
        });
      }

      return signature;

    } catch (nativeErr) {
      console.warn('⚠️ WASM原生调用失败，尝试WASM模拟器:', (nativeErr as Error).message);

      try {
        // 退而求其次：使用“模拟器”的兼容实现（不会严格等价，但可提供备选签名）
        this.initWasmEmulator();
        const result = this.wasmEmulator.getSignV2(requestData, urlPath);
        if (process.env['NODE_ENV'] === 'development') {
          console.log('🔐 v2签名计算(WASM模拟)', {
            method,
            url,
            requestDataPreview: requestData.slice(0, 80),
            urlPath,
            requestDataLength: Buffer.byteLength(requestData, 'utf8'),
            urlPathLength: Buffer.byteLength(urlPath, 'utf8'),
            signature: result.signature,
            strategy: result.strategy
          });
        }
        return typeof result.signature === 'string' ? result.signature : String(result.signature);
      } catch (emuErr) {
        console.warn('⚠️ WASM模拟器也失败，使用最终降级算法:', (emuErr as Error).message);

        // 最终降级：使用请求数据和URL路径的组合算法
        // 确保不同的URL路径会产生不同的签名
        const signString = `${timestamp}${requestData}${urlPath}${nonce}`;
        const signature = createHash('md5').update(signString).digest('hex').toUpperCase();

        if (process.env['NODE_ENV'] === 'development') {
          console.log('🔐 v2签名计算(降级算法)', {
            method,
            url,
            requestDataPreview: requestData.slice(0, 80),
            urlPath,
            timestamp,
            nonce,
            signStringPreview: signString.slice(0, 80),
            signature
          });
        }

        return signature;
      }
    }
  }

  /**
   * 策略1: METHOD + PATH算法
   * 基于调用流程.md中的描述
   */
  private static tryMethodPathAlgorithm(params: SignatureParams): string {
    const { timestamp, nonce, method = 'POST', url = '' } = params;
    
    // 规范化串采用 METHOD + 空格 + PATH 的格式
    const normalized = `${method.toUpperCase()} ${url}`;
    const signString = `${timestamp}${normalized}${nonce}`;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 策略2: 简化算法
   * 仅使用timestamp + nonce，适用于某些特殊情况
   */
  private static trySimplifiedAlgorithm(params: SignatureParams): string {
    const { timestamp, nonce } = params;
    
    const signString = `${timestamp}${nonce}`;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 策略3: 设备相关算法
   * 包含设备ID或其他设备信息
   */
  private static tryDeviceBasedAlgorithm(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    const { timestamp, nonce, deviceId } = params;
    
    // 如果有设备ID，尝试包含在签名中
    let signString = `${timestamp}`;
    
    if (deviceId) {
      signString += deviceId;
    }
    
    if (extra?.appVersion) {
      signString += extra.appVersion;
    }
    
    signString += nonce;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 检测URL是否为v2接口
   * @param url 请求URL
   * @returns 是否为v2接口
   */
  public static isV2Interface(url: string): boolean {
    // 规则说明（对齐单测期望）：
    // - 命中 /v2/ 一定是 v2
    // - 命中 chatApi 一定是 v2
    // - 命中 /chat 也视作 v2，但 /v1/chat 不是 v2
    const hasV2 = url.includes('/v2/');
    const hasChatApi = /(^|\/)chatApi(\/|$)/.test(url);
    const isRootChat = /(^|\/)chat(\/|$)/.test(url) && !/(^|\/)v1\/chat(\/|$)/.test(url);
    return hasV2 || hasChatApi || isRootChat;
  }

  /**
   * 获取v2接口的错误处理建议
   * @param url 请求URL
   * @returns 错误处理建议
   */
  public static getV2ErrorSuggestions(url: string): string[] {
    const suggestions = [
      '当前v2接口使用了未知的签名算法，可能需要特殊的密钥或处理逻辑',
      '建议检查当贝AI官方文档是否有更新的签名规则',
      '可以尝试使用浏览器开发者工具抓取真实的签名参数进行对比',
      '如果问题持续，建议联系当贝AI技术支持获取最新的API文档'
    ];
    
    if (url.includes('chat')) {
      suggestions.push('聊天接口可能需要特殊的流式处理或WebSocket连接');
    }
    
    return suggestions;
  }

  /**
   * 生成v2接口的调试信息
   * @param params 签名参数
   * @returns 调试信息
   */
  public static generateDebugInfo(params: SignatureParams): {
    url: string;
    isV2: boolean;
    strategies: Array<{
      name: string;
      signature: string;
      description: string;
    }>;
  } {
    const url = params.url || '';
    const isV2 = this.isV2Interface(url);
    
    const strategies = [
      {
        name: 'WASM_EMULATION',
        signature: this.tryWasmEmulationAlgorithm(params),
        description: '基于WebAssembly模块分析的模拟算法'
      },
      {
        name: 'METHOD+PATH',
        signature: this.tryMethodPathAlgorithm(params),
        description: '使用HTTP方法和路径的组合'
      },
      {
        name: 'SIMPLIFIED',
        signature: this.trySimplifiedAlgorithm(params),
        description: '仅使用时间戳和随机数'
      },
      {
        name: 'DEVICE_BASED',
        signature: this.tryDeviceBasedAlgorithm(params),
        description: '包含设备信息的算法'
      }
    ];
    
    return {
      url,
      isV2,
      strategies
    };
  }
}
