/**
 * 请求参数验证工具
 * 用于验证 ChatRequest 和 TextGenerationRequest 参数的有效性
 */

import type { ChatRequest, TextGenerationRequest, ChatMessage, TextGenerationTaskType } from '../server/types';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  /** 错误信息列表 */
  errors: string[];
  /** 警告信息列表 */
  warnings: string[];
}

/**
 * 聊天请求验证器
 */
export class ChatRequestValidator {
  /**
   * 验证聊天请求参数
   */
  static validate(request: Partial<ChatRequest>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 必填字段验证
    if (!request.messages || !Array.isArray(request.messages)) {
      errors.push('messages 字段是必填的，且必须是数组');
    } else if (request.messages.length === 0) {
      errors.push('messages 数组不能为空');
    } else {
      // 验证每个消息
      request.messages.forEach((message: ChatMessage, index: number) => {
        const messageErrors = this.validateMessage(message, index);
        errors.push(...messageErrors);
      });
    }

    if (!request.model || typeof request.model !== 'string') {
      errors.push('model 字段是必填的，且必须是字符串');
    }

    // 可选字段验证
    if (request.stream !== undefined && typeof request.stream !== 'boolean') {
      errors.push('stream 字段必须是布尔值');
    }

    if (request.conversation_id !== undefined && typeof request.conversation_id !== 'string') {
      errors.push('conversation_id 字段必须是字符串');
    }

    if (request.max_tokens !== undefined) {
      if (typeof request.max_tokens !== 'number' || request.max_tokens <= 0) {
        errors.push('max_tokens 字段必须是正整数');
      } else if (request.max_tokens > 8192) {
        warnings.push('max_tokens 设置过大，可能影响响应速度');
      }
    }

    if (request.temperature !== undefined) {
      if (typeof request.temperature !== 'number' || request.temperature < 0 || request.temperature > 1) {
        errors.push('temperature 字段必须是 0-1 之间的数字');
      }
    }

    // 验证选项
    if (request.options) {
      if (typeof request.options !== 'object') {
        errors.push('options 字段必须是对象');
      } else {
        if (request.options.deep_thinking !== undefined && typeof request.options.deep_thinking !== 'boolean') {
          errors.push('options.deep_thinking 字段必须是布尔值');
        }
        if (request.options.online_search !== undefined && typeof request.options.online_search !== 'boolean') {
          errors.push('options.online_search 字段必须是布尔值');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证单个消息
   */
  private static validateMessage(message: any, index: number): string[] {
    const errors: string[] = [];

    if (!message || typeof message !== 'object') {
      errors.push(`messages[${index}] 必须是对象`);
      return errors;
    }

    if (!message.role || !['user', 'assistant', 'system'].includes(message.role)) {
      errors.push(`messages[${index}].role 必须是 'user', 'assistant' 或 'system'`);
    }

    if (!message.content || typeof message.content !== 'string') {
      errors.push(`messages[${index}].content 是必填的，且必须是字符串`);
    } else if (message.content.trim().length === 0) {
      errors.push(`messages[${index}].content 不能为空`);
    }

    if (message.id !== undefined && typeof message.id !== 'string') {
      errors.push(`messages[${index}].id 必须是字符串`);
    }

    if (message.timestamp !== undefined && typeof message.timestamp !== 'number') {
      errors.push(`messages[${index}].timestamp 必须是数字`);
    }

    return errors;
  }
}

/**
 * 文本生成请求验证器
 */
export class TextGenerationRequestValidator {
  /**
   * 有效的任务类型
   */
  private static readonly VALID_TASK_TYPES: TextGenerationTaskType[] = [
    'creative', 'code', 'document', 'summary', 'translation', 'rewrite', 'qa', 'general'
  ];

  /**
   * 验证文本生成请求参数
   */
  static validate(request: Partial<TextGenerationRequest>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 必填字段验证
    if (!request.prompt || typeof request.prompt !== 'string') {
      errors.push('prompt 字段是必填的，且必须是字符串');
    } else if (request.prompt.trim().length === 0) {
      errors.push('prompt 不能为空');
    } else if (request.prompt.length > 10000) {
      warnings.push('prompt 过长，可能影响处理效果');
    }

    // 可选字段验证
    if (request.model !== undefined && typeof request.model !== 'string') {
      errors.push('model 字段必须是字符串');
    }

    if (request.stream !== undefined && typeof request.stream !== 'boolean') {
      errors.push('stream 字段必须是布尔值');
    }

    if (request.max_tokens !== undefined) {
      if (typeof request.max_tokens !== 'number' || request.max_tokens <= 0) {
        errors.push('max_tokens 字段必须是正整数');
      } else if (request.max_tokens > 8192) {
        warnings.push('max_tokens 设置过大，可能影响响应速度');
      }
    }

    if (request.temperature !== undefined) {
      if (typeof request.temperature !== 'number' || request.temperature < 0 || request.temperature > 1) {
        errors.push('temperature 字段必须是 0-1 之间的数字');
      }
    }

    if (request.task_type !== undefined) {
      if (!this.VALID_TASK_TYPES.includes(request.task_type)) {
        errors.push(`task_type 必须是以下值之一: ${this.VALID_TASK_TYPES.join(', ')}`);
      }
    }

    // 验证选项
    if (request.options) {
      if (typeof request.options !== 'object') {
        errors.push('options 字段必须是对象');
      } else {
        const { style, format, language, deep_thinking, online_search } = request.options;

        if (style !== undefined && typeof style !== 'string') {
          errors.push('options.style 字段必须是字符串');
        }

        if (format !== undefined && typeof format !== 'string') {
          errors.push('options.format 字段必须是字符串');
        }

        if (language !== undefined && typeof language !== 'string') {
          errors.push('options.language 字段必须是字符串');
        }

        if (deep_thinking !== undefined && typeof deep_thinking !== 'boolean') {
          errors.push('options.deep_thinking 字段必须是布尔值');
        }

        if (online_search !== undefined && typeof online_search !== 'boolean') {
          errors.push('options.online_search 字段必须是布尔值');
        }
      }
    }

    // 根据任务类型提供建议
    if (request.task_type && request.temperature !== undefined) {
      const suggestions = this.getTemperatureSuggestion(request.task_type, request.temperature);
      if (suggestions) {
        warnings.push(suggestions);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 根据任务类型获取温度参数建议
   */
  private static getTemperatureSuggestion(taskType: TextGenerationTaskType, temperature: number): string | null {
    const suggestions: Record<TextGenerationTaskType, { min: number; max: number; description: string }> = {
      creative: { min: 0.8, max: 1.0, description: '创意写作建议使用较高温度' },
      code: { min: 0.1, max: 0.3, description: '代码生成建议使用较低温度' },
      translation: { min: 0.0, max: 0.2, description: '翻译建议使用极低温度' },
      summary: { min: 0.2, max: 0.4, description: '摘要建议使用低温度' },
      document: { min: 0.4, max: 0.6, description: '文档生成建议使用中等温度' },
      rewrite: { min: 0.3, max: 0.5, description: '改写建议使用中低温度' },
      qa: { min: 0.3, max: 0.6, description: '问答建议使用中等温度' },
      general: { min: 0.4, max: 0.7, description: '通用任务建议使用中等温度' }
    };

    const suggestion = suggestions[taskType];
    if (suggestion && (temperature < suggestion.min || temperature > suggestion.max)) {
      return `${suggestion.description}（建议范围: ${suggestion.min}-${suggestion.max}）`;
    }

    return null;
  }
}

/**
 * 通用验证工具
 */
export class RequestValidator {
  /**
   * 验证聊天请求
   */
  static validateChatRequest(request: Partial<ChatRequest>): ValidationResult {
    return ChatRequestValidator.validate(request);
  }

  /**
   * 验证文本生成请求
   */
  static validateTextGenerationRequest(request: Partial<TextGenerationRequest>): ValidationResult {
    return TextGenerationRequestValidator.validate(request);
  }

  /**
   * 格式化验证结果为可读字符串
   */
  static formatValidationResult(result: ValidationResult): string {
    let output = '';

    if (result.isValid) {
      output += '✅ 验证通过\n';
    } else {
      output += '❌ 验证失败\n';
    }

    if (result.errors.length > 0) {
      output += '\n错误信息:\n';
      result.errors.forEach((error, index) => {
        output += `  ${index + 1}. ${error}\n`;
      });
    }

    if (result.warnings.length > 0) {
      output += '\n警告信息:\n';
      result.warnings.forEach((warning, index) => {
        output += `  ${index + 1}. ${warning}\n`;
      });
    }

    return output;
  }

  /**
   * 验证并返回格式化结果
   */
  static validateAndFormat(request: Partial<ChatRequest | TextGenerationRequest>, type: 'chat' | 'text'): string {
    let result: ValidationResult;

    if (type === 'chat') {
      result = this.validateChatRequest(request as Partial<ChatRequest>);
    } else {
      result = this.validateTextGenerationRequest(request as Partial<TextGenerationRequest>);
    }

    return this.formatValidationResult(result);
  }
}
