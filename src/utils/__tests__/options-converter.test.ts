/**
 * Options 转换器测试用例
 * 测试 options 参数到 userAction 参数的映射转换逻辑
 */

import {
  convertOptionsToUserAction,
  validateChatOptions,
  getSupportedOptions,
  getOptionMapping,
  ChatOptionsConfig
} from '../options-converter';

describe('Options 转换器测试', () => {
  describe('convertOptionsToUserAction 函数测试', () => {
    test('应该处理空参数', () => {
      expect(convertOptionsToUserAction()).toBe('');
      expect(convertOptionsToUserAction(undefined)).toBe('');
    });

    test('应该处理空对象', () => {
      expect(convertOptionsToUserAction({})).toBe('');
    });

    test('应该处理所有选项为 false 的情况', () => {
      const options: ChatOptionsConfig = {
        deep_thinking: false,
        online_search: false
      };
      expect(convertOptionsToUserAction(options)).toBe('');
    });

    test('应该正确转换单个 deep_thinking 选项', () => {
      const options: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: false
      };
      expect(convertOptionsToUserAction(options)).toBe('deep');
    });

    test('应该正确转换单个 online_search 选项', () => {
      const options: ChatOptionsConfig = {
        deep_thinking: false,
        online_search: true
      };
      expect(convertOptionsToUserAction(options)).toBe('online');
    });

    test('应该正确转换多个选项组合', () => {
      const options: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: true
      };
      expect(convertOptionsToUserAction(options)).toBe('deep,online');
    });

    test('应该忽略 undefined 值的选项', () => {
      const options: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: undefined
      };
      expect(convertOptionsToUserAction(options)).toBe('deep');
    });

    test('应该处理部分选项缺失的情况', () => {
      const options1: ChatOptionsConfig = {
        deep_thinking: true
      };
      expect(convertOptionsToUserAction(options1)).toBe('deep');

      const options2: ChatOptionsConfig = {
        online_search: true
      };
      expect(convertOptionsToUserAction(options2)).toBe('online');
    });
  });

  describe('validateChatOptions 函数测试', () => {
    test('应该验证有效的选项配置', () => {
      const options: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: false
      };
      const result = validateChatOptions(options);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该验证空参数', () => {
      const result = validateChatOptions();
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该验证空对象', () => {
      const result = validateChatOptions({});
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该检测 deep_thinking 参数类型错误', () => {
      const options = {
        deep_thinking: 'true' as any, // 错误的类型
        online_search: true
      };
      const result = validateChatOptions(options);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('deep_thinking参数必须是布尔值');
    });

    test('应该检测 online_search 参数类型错误', () => {
      const options = {
        deep_thinking: true,
        online_search: 1 as any // 错误的类型
      };
      const result = validateChatOptions(options);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('online_search参数必须是布尔值');
    });

    test('应该检测多个参数类型错误', () => {
      const options = {
        deep_thinking: 'true' as any,
        online_search: 1 as any
      };
      const result = validateChatOptions(options);
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors).toContain('deep_thinking参数必须是布尔值');
      expect(result.errors).toContain('online_search参数必须是布尔值');
    });
  });

  describe('getSupportedOptions 函数测试', () => {
    test('应该返回支持的选项列表', () => {
      const supportedOptions = getSupportedOptions();
      expect(supportedOptions).toContain('deep_thinking');
      expect(supportedOptions).toContain('online_search');
      expect(supportedOptions.length).toBeGreaterThan(0);
    });
  });

  describe('getOptionMapping 函数测试', () => {
    test('应该返回正确的选项映射', () => {
      expect(getOptionMapping('deep_thinking')).toBe('deep');
      expect(getOptionMapping('online_search')).toBe('online');
    });

    test('应该处理不存在的选项', () => {
      expect(getOptionMapping('non_existent' as any)).toBeUndefined();
    });
  });

  describe('实际使用场景测试', () => {
    test('应该匹配调用流程.md中的示例', () => {
      // 根据调用流程.md中的示例：userAction: "deep,online"
      const options: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: true
      };
      const result = convertOptionsToUserAction(options);
      expect(result).toBe('deep,online');
    });

    test('应该处理常见的API请求场景', () => {
      // 场景1：只启用深度思考
      const scenario1: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: false
      };
      expect(convertOptionsToUserAction(scenario1)).toBe('deep');

      // 场景2：只启用联网搜索
      const scenario2: ChatOptionsConfig = {
        deep_thinking: false,
        online_search: true
      };
      expect(convertOptionsToUserAction(scenario2)).toBe('online');

      // 场景3：都不启用
      const scenario3: ChatOptionsConfig = {
        deep_thinking: false,
        online_search: false
      };
      expect(convertOptionsToUserAction(scenario3)).toBe('');

      // 场景4：都启用
      const scenario4: ChatOptionsConfig = {
        deep_thinking: true,
        online_search: true
      };
      expect(convertOptionsToUserAction(scenario4)).toBe('deep,online');
    });
  });
});
