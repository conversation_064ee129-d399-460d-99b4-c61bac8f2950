/**
 * 数据完整性验证工具
 * 用于验证SSE流式响应中三色数据的完整性和正确性
 */

import { SSEMessageDelta, ChatCallbacks } from '../types';
import { ThreeColorDataType, threeColorDataMonitor } from './three-color-data-monitor';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 验证是否通过 */
  passed: boolean;
  /** 验证消息 */
  message: string;
  /** 详细信息 */
  details?: any;
  /** 建议修复方案 */
  suggestions?: string[];
}

/**
 * 数据流验证器
 */
export class DataIntegrityValidator {
  // private expectedSequence: ThreeColorDataType[] = []; // 暂时不使用
  private receivedSequence: ThreeColorDataType[] = [];
  private conversationId: string = '';
  private startTime: number = 0;
  private isValidating: boolean = false;

  /**
   * 开始验证会话
   */
  public startValidation(conversationId: string): void {
    this.conversationId = conversationId;
    this.startTime = Date.now();
    this.isValidating = true;
    // this.expectedSequence = [];
    this.receivedSequence = [];
    
    console.log(`[数据完整性验证] 开始验证会话: ${conversationId}`);
  }

  /**
   * 记录接收到的消息
   */
  public recordMessage(data: SSEMessageDelta): void {
    if (!this.isValidating) {
      return;
    }

    const contentType = this.getContentType(data.content_type);
    this.receivedSequence.push(contentType);
    
    console.log(`[数据完整性验证] 接收到${this.getColorName(contentType)}消息`, {
      messageId: data.id,
      contentType: data.content_type,
      contentLength: data.content.length,
      sequencePosition: this.receivedSequence.length
    });
  }

  /**
   * 结束验证并生成报告
   */
  public endValidation(): ValidationResult {
    if (!this.isValidating) {
      return {
        passed: false,
        message: '验证未开始',
        suggestions: ['请先调用startValidation()开始验证']
      };
    }

    this.isValidating = false;
    const duration = Date.now() - this.startTime;
    
    // 分析接收到的序列
    const analysis = this.analyzeSequence();
    
    console.log(`[数据完整性验证] 验证完成，耗时: ${duration}ms`);
    
    return {
      passed: analysis.isComplete,
      message: analysis.message,
      details: {
        conversationId: this.conversationId,
        duration,
        expectedTypes: this.getExpectedTypes(),
        receivedSequence: this.receivedSequence,
        missingTypes: analysis.missingTypes,
        unexpectedTypes: analysis.unexpectedTypes,
        statistics: this.getStatistics()
      },
      suggestions: analysis.suggestions
    };
  }

  /**
   * 分析接收序列
   */
  private analyzeSequence(): {
    isComplete: boolean;
    message: string;
    missingTypes: ThreeColorDataType[];
    unexpectedTypes: ThreeColorDataType[];
    suggestions: string[];
  } {
    const expectedTypes = this.getExpectedTypes();
    const receivedTypes = new Set(this.receivedSequence);
    const expectedSet = new Set(expectedTypes);
    
    const missingTypes = expectedTypes.filter(type => !receivedTypes.has(type));
    const unexpectedTypes = this.receivedSequence.filter(type => !expectedSet.has(type));
    
    let isComplete = missingTypes.length === 0;
    let message = '';
    const suggestions: string[] = [];

    if (isComplete) {
      message = '数据完整性验证通过，所有预期的三色数据类型都已接收';
    } else {
      message = `数据完整性验证失败，缺少 ${missingTypes.length} 种数据类型`;
      
      missingTypes.forEach(type => {
        const colorName = this.getColorName(type);
        suggestions.push(`检查${colorName}数据(${type})的生成和传输逻辑`);
      });
    }

    if (unexpectedTypes.length > 0) {
      suggestions.push('检查是否有未知的content_type类型需要处理');
    }

    return {
      isComplete,
      message,
      missingTypes,
      unexpectedTypes,
      suggestions
    };
  }

  /**
   * 获取预期的数据类型
   */
  private getExpectedTypes(): ThreeColorDataType[] {
    // 根据当贝AI的典型响应流程，预期的数据类型顺序
    return [
      ThreeColorDataType.PROGRESS,  // 联网搜索进度
      ThreeColorDataType.CARD,      // 搜索结果卡片
      ThreeColorDataType.THINKING,  // AI思考过程
      ThreeColorDataType.TEXT       // 正式回答
    ];
  }

  /**
   * 获取统计信息
   */
  private getStatistics(): {
    totalMessages: number;
    messagesByType: Record<string, number>;
    averageMessageLength: Record<string, number>;
  } {
    const messagesByType: Record<string, number> = {};
    const lengthsByType: Record<string, number[]> = {};
    
    // 从监控器获取详细统计
    const recentMessages = threeColorDataMonitor.getRecentMessages(1000);
    
    recentMessages.forEach(msg => {
      const typeName = msg.type.toString();
      messagesByType[typeName] = (messagesByType[typeName] || 0) + 1;
      
      if (!lengthsByType[typeName]) {
        lengthsByType[typeName] = [];
      }
      lengthsByType[typeName].push(msg.content.length);
    });

    const averageMessageLength: Record<string, number> = {};
    Object.keys(lengthsByType).forEach(type => {
      const lengths = lengthsByType[type];
      if (lengths && lengths.length > 0) {
        averageMessageLength[type] = lengths.reduce((a, b) => a + b, 0) / lengths.length;
      }
    });

    return {
      totalMessages: recentMessages.length,
      messagesByType,
      averageMessageLength
    };
  }

  /**
   * 获取内容类型
   */
  private getContentType(contentType: string): ThreeColorDataType {
    switch (contentType) {
      case 'progress':
        return ThreeColorDataType.PROGRESS;
      case 'thinking':
        return ThreeColorDataType.THINKING;
      case 'text':
        return ThreeColorDataType.TEXT;
      case 'card':
        return ThreeColorDataType.CARD;
      default:
        return ThreeColorDataType.UNKNOWN;
    }
  }

  /**
   * 获取颜色名称
   */
  private getColorName(type: ThreeColorDataType): string {
    switch (type) {
      case ThreeColorDataType.PROGRESS:
        return '蓝色';
      case ThreeColorDataType.THINKING:
        return '黄色';
      case ThreeColorDataType.TEXT:
        return '绿色';
      case ThreeColorDataType.CARD:
        return '主色调';
      default:
        return '未知';
    }
  }

  /**
   * 创建验证回调包装器
   */
  public createValidationCallbacks(originalCallbacks: ChatCallbacks): ChatCallbacks {
    return {
      onMessage: (content: string, data: SSEMessageDelta) => {
        // 记录消息到验证器
        this.recordMessage(data);
        
        // 调用原始回调
        if (originalCallbacks.onMessage) {
          originalCallbacks.onMessage(content, data);
        }
      },
      onComplete: (data) => {
        // 结束验证
        const result = this.endValidation();
        console.log('[数据完整性验证] 验证结果:', result);
        
        // 调用原始回调
        if (originalCallbacks.onComplete) {
          originalCallbacks.onComplete(data);
        }
      },
      onError: (error) => {
        // 记录错误并结束验证
        console.error('[数据完整性验证] 验证过程中出现错误:', error);
        this.isValidating = false;
        
        // 调用原始回调
        if (originalCallbacks.onError) {
          originalCallbacks.onError(error);
        }
      }
    };
  }

  /**
   * 快速验证单个消息
   */
  public static validateMessage(data: SSEMessageDelta): ValidationResult {
    
    // 检查必要字段
    const requiredFields = ['role', 'content', 'content_type', 'id', 'conversation_id'];
    const missingFields = requiredFields.filter(field => !data[field as keyof SSEMessageDelta]);
    
    if (missingFields.length > 0) {
      return {
        passed: false,
        message: `消息缺少必要字段: ${missingFields.join(', ')}`,
        details: { missingFields, data },
        suggestions: ['检查数据源是否正确设置了所有必要字段']
      };
    }

    // 检查content_type是否为已知类型
    const knownTypes = ['progress', 'thinking', 'text', 'card'];
    if (!knownTypes.includes(data.content_type)) {
      return {
        passed: false,
        message: `未知的content_type: ${data.content_type}`,
        details: { unknownType: data.content_type, knownTypes },
        suggestions: [
          '检查是否有新的content_type需要添加到系统中',
          '验证数据源是否发送了正确的content_type值'
        ]
      };
    }

    return {
      passed: true,
      message: '消息验证通过',
      details: { contentType: data.content_type, messageLength: data.content.length }
    };
  }
}

// 导出全局验证器实例
export const dataIntegrityValidator = new DataIntegrityValidator();
