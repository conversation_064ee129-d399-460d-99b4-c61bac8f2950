/**
 * WASM签名模拟器测试工具
 * 用于验证和调试WASM签名算法的实现
 */

import { WasmSignatureEmulator } from './wasm-signature-emulator';
import { SignatureV2Utils } from './signature-v2';
import { SignatureParams } from '../types';

/**
 * 测试用例接口
 */
interface TestCase {
  name: string;
  params: SignatureParams;
  expectedSignature?: string;
  description: string;
}

/**
 * WASM签名测试器
 */
export class WasmSignatureTest {
  private emulator: WasmSignatureEmulator;
  
  constructor() {
    this.emulator = new WasmSignatureEmulator({
      debug: true,
      strategy: 'hybrid'
    });
  }

  /**
   * 运行所有测试用例
   */
  public runAllTests(): void {
    console.log('🧪 开始WASM签名算法测试...\n');
    
    const testCases = this.getTestCases();
    
    for (const testCase of testCases) {
      this.runSingleTest(testCase);
    }
    
    console.log('\n✅ 所有测试完成');
  }

  /**
   * 运行单个测试用例
   */
  private runSingleTest(testCase: TestCase): void {
    console.log(`📋 测试用例: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    
    try {
      // 使用WASM模拟器生成签名
      const wasmResult = this.emulator.getSign(
        this.buildRequestData(testCase.params),
        this.buildTimestampNonce(testCase.params)
      );
      
      // 使用V2签名工具生成签名
      const v2Signature = SignatureV2Utils.generateV2Signature(testCase.params);
      
      console.log(`🔐 WASM模拟签名: ${wasmResult.signature}`);
      console.log(`🔐 V2工具签名: ${v2Signature}`);
      
      if (testCase.expectedSignature) {
        console.log(`🎯 期望签名: ${testCase.expectedSignature}`);
        
        const wasmMatch = wasmResult.signature === testCase.expectedSignature;
        const v2Match = v2Signature === testCase.expectedSignature;
        
        console.log(`✅ WASM匹配: ${wasmMatch ? '是' : '否'}`);
        console.log(`✅ V2匹配: ${v2Match ? '是' : '否'}`);
      }
      
      if (wasmResult.debug) {
        console.log(`🔍 调试信息:`);
        console.log(`  - 规范化数据: ${wasmResult.debug.normalizedData.substring(0, 50)}...`);
        console.log(`  - 签名字符串: ${wasmResult.debug.signString.substring(0, 50)}...`);
        console.log(`  - 算法: ${wasmResult.debug.algorithm}`);
      }
      
    } catch (error) {
      console.error(`❌ 测试失败: ${(error as Error).message}`);
    }
    
    console.log('─'.repeat(60));
  }

  /**
   * 构建请求数据字符串
   */
  private buildRequestData(params: SignatureParams): string {
    const { method = 'POST', url = '', bodyRaw = '', data } = params;
    
    if (method === 'GET') {
      const qIndex = url.indexOf('?');
      return qIndex !== -1 ? url.substring(qIndex + 1) : '';
    } else {
      return bodyRaw || (data ? JSON.stringify(data) : '');
    }
  }

  /**
   * 构建时间戳/nonce字符串
   */
  private buildTimestampNonce(params: SignatureParams): string {
    return `${params.timestamp}:${params.nonce}`;
  }

  /**
   * 获取测试用例
   */
  private getTestCases(): TestCase[] {
    return [
      {
        name: '真实聊天请求测试',
        description: '基于调用流程.md中的真实聊天请求数据',
        params: {
          timestamp: 1755239241,
          nonce: 'QL4MKOwQFtSmnhCOmNjde',
          deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
          method: 'POST',
          url: '/ai-search/chatApi/v2/chat',
          bodyRaw: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363022964585267589","question":"你好!","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363022965811450053","chatId":"363022965811450053","files":[],"reference":[],"role":"user","status":"local","content":"你好!","userAction":"","agentId":""}'
        },
        expectedSignature: '460D94E7C6980A6973494BC75D075905'
      },
      {
        name: '高级分析请求测试',
        description: '基于advanced-signature-analysis.js中的真实请求数据',
        params: {
          timestamp: 1755252943,
          nonce: '111g0amuaFUAic500cMI-',
          deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
          method: 'POST',
          url: '/ai-search/chatApi/v2/chat',
          data: {
            conversationList: [{
              metaData: {
                chatModelConfig: {},
                superAgentPath: "/chat"
              },
              shareId: "",
              isAnonymous: false,
              source: ""
            }]
          }
        },
        expectedSignature: '9CC214FB53DDAF31DC1BFE453D14C468'
      },
      {
        name: 'generateId API测试',
        description: '基于test-signature.js中的generateId API示例',
        params: {
          timestamp: 1755239241,
          nonce: '-un-m0ntXQIf0i-byz12f',
          deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
          method: 'POST',
          url: '/ai-search/commonApi/v1/generateId',
          data: { timestamp: 1755239241456 }
        },
        expectedSignature: '03A7FFE5DCFB0AC2C486A05ED8198142'
      },
      {
        name: '简单GET请求测试',
        description: '测试GET请求的签名生成',
        params: {
          timestamp: Math.floor(Date.now() / 1000),
          nonce: 'test_nonce_' + Math.random().toString(36).substring(2),
          deviceId: 'test_device_id',
          method: 'GET',
          url: '/api/test?param1=value1&param2=value2'
        }
      },
      {
        name: '空请求体POST测试',
        description: '测试没有请求体的POST请求',
        params: {
          timestamp: Math.floor(Date.now() / 1000),
          nonce: 'empty_post_' + Math.random().toString(36).substring(2),
          deviceId: 'test_device_id',
          method: 'POST',
          url: '/api/empty-post'
        }
      }
    ];
  }

  /**
   * 比较不同策略的签名结果
   */
  public compareStrategies(params: SignatureParams): void {
    console.log('🔄 比较不同签名策略的结果...\n');
    
    const strategies = ['standard', 'enhanced', 'hybrid'] as const;
    
    for (const strategy of strategies) {
      const emulator = new WasmSignatureEmulator({
        debug: false,
        strategy
      });
      
      try {
        const result = emulator.getSign(
          this.buildRequestData(params),
          this.buildTimestampNonce(params)
        );
        
        console.log(`📊 策略 ${strategy.toUpperCase()}:`);
        console.log(`   签名: ${result.signature}`);
        console.log(`   时间戳: ${result.timestamp}`);
        console.log('');
        
      } catch (error) {
        console.error(`❌ 策略 ${strategy} 失败: ${(error as Error).message}`);
      }
    }
  }

  /**
   * 性能测试
   */
  public performanceTest(iterations: number = 1000): void {
    console.log(`⚡ 开始性能测试 (${iterations} 次迭代)...\n`);
    
    const testParams: SignatureParams = {
      timestamp: Math.floor(Date.now() / 1000),
      nonce: 'perf_test_nonce',
      deviceId: 'perf_test_device',
      method: 'POST',
      url: '/api/performance-test',
      data: { test: 'performance data' }
    };
    
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      this.emulator.getSign(
        this.buildRequestData(testParams),
        this.buildTimestampNonce(testParams)
      );
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    const avgTime = duration / iterations;
    
    console.log(`📈 性能测试结果:`);
    console.log(`   总时间: ${duration}ms`);
    console.log(`   平均时间: ${avgTime.toFixed(2)}ms/次`);
    console.log(`   吞吐量: ${(1000 / avgTime).toFixed(0)} 次/秒`);
  }
}

/**
 * 导出便捷的测试函数
 */
export function runWasmSignatureTests(): void {
  const tester = new WasmSignatureTest();
  tester.runAllTests();
}

export function compareSignatureStrategies(params: SignatureParams): void {
  const tester = new WasmSignatureTest();
  tester.compareStrategies(params);
}

export function runPerformanceTest(iterations?: number): void {
  const tester = new WasmSignatureTest();
  tester.performanceTest(iterations);
}
