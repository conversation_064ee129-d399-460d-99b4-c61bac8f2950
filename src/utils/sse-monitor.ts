/**
 * SSE 监控和诊断工具
 * 用于监控 SSE 连接状态、性能指标和错误统计
 */

/**
 * SSE 事件类型
 */
export enum SSEEventType {
  CONNECTION_OPENED = 'CONNECTION_OPENED',
  CONNECTION_CLOSED = 'CONNECTION_CLOSED',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  MESSAGE_RECEIVED = 'MESSAGE_RECEIVED',
  MESSAGE_PARSED = 'MESSAGE_PARSED',
  MESSAGE_FAILED = 'MESSAGE_FAILED',
  RECONNECT_ATTEMPT = 'RECONNECT_ATTEMPT',
  BUFFER_OVERFLOW = 'BUFFER_OVERFLOW'
}

/**
 * SSE 事件数据
 */
export interface SSEEvent {
  type: SSEEventType;
  timestamp: number;
  data?: any;
  error?: Error;
  metadata?: Record<string, any>;
}

/**
 * SSE 性能指标
 */
export interface SSEMetrics {
  /** 连接总数 */
  totalConnections: number;
  /** 成功连接数 */
  successfulConnections: number;
  /** 失败连接数 */
  failedConnections: number;
  /** 重连次数 */
  reconnectAttempts: number;
  /** 接收到的消息总数 */
  totalMessages: number;
  /** 解析成功的消息数 */
  parsedMessages: number;
  /** 解析失败的消息数 */
  failedMessages: number;
  /** 平均消息处理时间（毫秒） */
  averageProcessingTime: number;
  /** 缓冲区溢出次数 */
  bufferOverflows: number;
  /** 最后一次活动时间 */
  lastActivity: number;
  /** 连接持续时间（毫秒） */
  connectionDuration: number;
}

/**
 * SSE 监控器
 */
export class SSEMonitor {
  private events: SSEEvent[] = [];
  private metrics: SSEMetrics = this.initializeMetrics();
  private startTime: number = Date.now();
  private lastReportTime: number = Date.now();
  private processingTimes: number[] = [];
  private maxEventHistory: number = 1000;
  private isEnabled: boolean = true;

  constructor(maxEventHistory: number = 1000) {
    this.maxEventHistory = maxEventHistory;
  }

  /**
   * 初始化指标
   */
  private initializeMetrics(): SSEMetrics {
    return {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      reconnectAttempts: 0,
      totalMessages: 0,
      parsedMessages: 0,
      failedMessages: 0,
      averageProcessingTime: 0,
      bufferOverflows: 0,
      lastActivity: Date.now(),
      connectionDuration: 0
    };
  }

  /**
   * 记录事件
   */
  public recordEvent(type: SSEEventType, data?: any, error?: Error, metadata?: Record<string, any>): void {
    if (!this.isEnabled) {
      return;
    }

    const event: SSEEvent = {
      type,
      timestamp: Date.now(),
      data,
      ...(error && { error }),
      ...(metadata && { metadata })
    };

    this.events.push(event);
    this.updateMetrics(event);

    // 限制事件历史记录大小
    if (this.events.length > this.maxEventHistory) {
      this.events = this.events.slice(-this.maxEventHistory);
    }

    // 输出调试日志
    if (process.env['NODE_ENV'] === 'development') {
      this.logEvent(event);
    }
  }

  /**
   * 更新指标
   */
  private updateMetrics(event: SSEEvent): void {
    this.metrics.lastActivity = event.timestamp;

    switch (event.type) {
      case SSEEventType.CONNECTION_OPENED:
        this.metrics.totalConnections++;
        this.metrics.successfulConnections++;
        break;

      case SSEEventType.CONNECTION_ERROR:
        this.metrics.failedConnections++;
        break;

      case SSEEventType.RECONNECT_ATTEMPT:
        this.metrics.reconnectAttempts++;
        break;

      case SSEEventType.MESSAGE_RECEIVED:
        this.metrics.totalMessages++;
        break;

      case SSEEventType.MESSAGE_PARSED:
        this.metrics.parsedMessages++;
        if (event.metadata?.['processingTime']) {
          this.processingTimes.push(event.metadata['processingTime']);
          this.updateAverageProcessingTime();
        }
        break;

      case SSEEventType.MESSAGE_FAILED:
        this.metrics.failedMessages++;
        break;

      case SSEEventType.BUFFER_OVERFLOW:
        this.metrics.bufferOverflows++;
        break;
    }

    this.metrics.connectionDuration = Date.now() - this.startTime;
  }

  /**
   * 更新平均处理时间
   */
  private updateAverageProcessingTime(): void {
    if (this.processingTimes.length === 0) {
      return;
    }

    // 只保留最近的 100 个处理时间
    if (this.processingTimes.length > 100) {
      this.processingTimes = this.processingTimes.slice(-100);
    }

    const sum = this.processingTimes.reduce((acc, time) => acc + time, 0);
    this.metrics.averageProcessingTime = sum / this.processingTimes.length;
  }

  /**
   * 记录消息处理时间
   */
  public recordMessageProcessingTime(startTime: number): void {
    const processingTime = Date.now() - startTime;
    this.recordEvent(SSEEventType.MESSAGE_PARSED, null, undefined, { processingTime });
  }

  /**
   * 获取当前指标
   */
  public getMetrics(): SSEMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取事件历史
   */
  public getEvents(limit?: number): SSEEvent[] {
    if (limit) {
      return this.events.slice(-limit);
    }
    return [...this.events];
  }

  /**
   * 获取错误事件
   */
  public getErrorEvents(): SSEEvent[] {
    return this.events.filter(event => 
      event.type === SSEEventType.CONNECTION_ERROR || 
      event.type === SSEEventType.MESSAGE_FAILED ||
      event.type === SSEEventType.BUFFER_OVERFLOW
    );
  }

  /**
   * 生成诊断报告
   */
  public generateDiagnosticReport(): string {
    const metrics = this.getMetrics();
    const errorEvents = this.getErrorEvents();
    const recentEvents = this.getEvents(10);

    let report = '\n📊 SSE 连接诊断报告\n';
    report += '=' .repeat(50) + '\n\n';

    // 基本指标
    report += '📈 基本指标:\n';
    report += `  连接总数: ${metrics.totalConnections}\n`;
    report += `  成功连接: ${metrics.successfulConnections}\n`;
    report += `  失败连接: ${metrics.failedConnections}\n`;
    report += `  重连次数: ${metrics.reconnectAttempts}\n`;
    report += `  连接持续时间: ${Math.round(metrics.connectionDuration / 1000)}秒\n\n`;

    // 消息处理指标
    report += '📨 消息处理指标:\n';
    report += `  接收消息总数: ${metrics.totalMessages}\n`;
    report += `  解析成功: ${metrics.parsedMessages}\n`;
    report += `  解析失败: ${metrics.failedMessages}\n`;
    report += `  平均处理时间: ${metrics.averageProcessingTime.toFixed(2)}ms\n`;
    report += `  缓冲区溢出: ${metrics.bufferOverflows}\n\n`;

    // 成功率计算
    const connectionSuccessRate = metrics.totalConnections > 0 ? 
      (metrics.successfulConnections / metrics.totalConnections * 100).toFixed(2) : '0';
    const messageSuccessRate = metrics.totalMessages > 0 ? 
      (metrics.parsedMessages / metrics.totalMessages * 100).toFixed(2) : '0';

    report += '📊 成功率:\n';
    report += `  连接成功率: ${connectionSuccessRate}%\n`;
    report += `  消息解析成功率: ${messageSuccessRate}%\n\n`;

    // 错误分析
    if (errorEvents.length > 0) {
      report += '❌ 错误分析:\n';
      const errorCounts = errorEvents.reduce((acc, event) => {
        acc[event.type] = (acc[event.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      Object.entries(errorCounts).forEach(([type, count]) => {
        report += `  ${type}: ${count}次\n`;
      });
      report += '\n';
    }

    // 最近事件
    if (recentEvents.length > 0) {
      report += '🕒 最近事件 (最新10条):\n';
      recentEvents.forEach(event => {
        const time = new Date(event.timestamp).toLocaleTimeString();
        report += `  [${time}] ${event.type}`;
        if (event.error) {
          report += ` - ${event.error.message}`;
        }
        report += '\n';
      });
    }

    return report;
  }

  /**
   * 输出事件日志
   */
  private logEvent(event: SSEEvent): void {
    const time = new Date(event.timestamp).toLocaleTimeString();
    let message = `[SSE Monitor ${time}] ${event.type}`;

    if (event.error) {
      message += ` - 错误: ${event.error.message}`;
    }

    if (event.metadata) {
      message += ` - 元数据: ${JSON.stringify(event.metadata)}`;
    }

    console.log(message);
  }

  /**
   * 重置监控数据
   */
  public reset(): void {
    this.events = [];
    this.metrics = this.initializeMetrics();
    this.startTime = Date.now();
    this.lastReportTime = Date.now();
    this.processingTimes = [];
  }

  /**
   * 启用/禁用监控
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 检查是否需要生成报告
   */
  public shouldGenerateReport(intervalMs: number = 60000): boolean {
    return Date.now() - this.lastReportTime > intervalMs;
  }

  /**
   * 标记报告已生成
   */
  public markReportGenerated(): void {
    this.lastReportTime = Date.now();
  }
}
