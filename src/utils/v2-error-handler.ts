/**
 * v2接口错误处理工具
 * 专门处理v2接口的错误情况和提供解决建议
 */

import { SignatureV2Utils } from './signature-v2';

/**
 * v2接口错误类型
 */
export enum V2ErrorType {
  SIGNATURE_MISMATCH = 'SIGNATURE_MISMATCH',
  UNKNOWN_ALGORITHM = 'UNKNOWN_ALGORITHM',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PARAMETER_ERROR = 'PARAMETER_ERROR'
}

/**
 * v2接口错误信息
 */
export interface V2ErrorInfo {
  type: V2ErrorType;
  message: string;
  suggestions: string[];
  debugInfo?: any;
}

/**
 * v2接口错误处理器
 */
export class V2ErrorHandler {
  /**
   * 分析v2接口错误并提供解决建议
   * @param error 错误对象
   * @param url 请求URL
   * @param params 请求参数
   * @returns 错误信息和建议
   */
  public static analyzeV2Error(
    error: any,
    url: string,
    params?: any
  ): V2ErrorInfo {
    const isV2Interface = SignatureV2Utils.isV2Interface(url);
    
    if (!isV2Interface) {
      return {
        type: V2ErrorType.PARAMETER_ERROR,
        message: '这不是v2接口',
        suggestions: ['请检查URL路径是否正确']
      };
    }

    // 分析HTTP错误状态码
    const status = error.response?.status;
    const errorCode = error.response?.data?.errCode;
    const errorMessage = error.response?.data?.errMessage;

    if (status === 400 && errorCode === '5002') {
      return this.handleSignatureMismatch(url, params);
    }

    if (status === 401 || errorCode === '4001') {
      return {
        type: V2ErrorType.SIGNATURE_MISMATCH,
        message: '认证失败，可能是签名错误',
        suggestions: [
          '检查时间戳是否正确（应为秒级Unix时间戳）',
          '检查nonce是否为21位随机字符串',
          '检查设备ID格式是否正确',
          '尝试使用浏览器开发者工具抓取真实请求进行对比'
        ]
      };
    }

    // 网络错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return {
        type: V2ErrorType.NETWORK_ERROR,
        message: '网络连接失败',
        suggestions: [
          '检查网络连接是否正常',
          '检查API服务器是否可访问',
          '尝试增加请求超时时间'
        ]
      };
    }

    // 默认错误处理
    return {
      type: V2ErrorType.UNKNOWN_ALGORITHM,
      message: errorMessage || '未知错误',
      suggestions: [
        'v2接口使用了未知的签名算法',
        '当前实现可能与官方算法不匹配',
        '建议查看最新的API文档',
        '考虑联系当贝AI技术支持获取帮助'
      ]
    };
  }

  /**
   * 处理签名不匹配错误
   */
  private static handleSignatureMismatch(_url: string, params?: any): V2ErrorInfo {
    const debugInfo = params ? SignatureV2Utils.generateDebugInfo(params) : null;
    
    return {
      type: V2ErrorType.SIGNATURE_MISMATCH,
      message: 'v2接口签名算法不匹配',
      suggestions: [
        '当前v2接口使用了与v1不同的签名算法',
        '我们尝试了多种可能的算法组合，但都无法匹配',
        '可能的原因：',
        '  - v2接口使用了专有的密钥或盐值',
        '  - 签名算法包含了我们未知的参数',
        '  - 服务端对v2接口有特殊的验证逻辑',
        '建议的解决方案：',
        '  1. 使用浏览器开发者工具抓取真实的v2请求',
        '  2. 对比我们生成的签名与真实签名的差异',
        '  3. 查看当贝AI官方文档是否有v2接口的特殊说明',
        '  4. 考虑使用v1接口的替代方案（如果可用）'
      ],
      debugInfo
    };
  }

  /**
   * 生成错误报告
   * @param errorInfo 错误信息
   * @returns 格式化的错误报告
   */
  public static generateErrorReport(errorInfo: V2ErrorInfo): string {
    let report = `\n🚨 v2接口错误报告\n`;
    report += `错误类型: ${errorInfo.type}\n`;
    report += `错误信息: ${errorInfo.message}\n\n`;
    
    report += `💡 解决建议:\n`;
    errorInfo.suggestions.forEach((suggestion, index) => {
      report += `${index + 1}. ${suggestion}\n`;
    });

    if (errorInfo.debugInfo) {
      report += `\n🔍 调试信息:\n`;
      report += `URL: ${errorInfo.debugInfo.url}\n`;
      report += `是否为v2接口: ${errorInfo.debugInfo.isV2}\n`;
      
      if (errorInfo.debugInfo.strategies) {
        report += `尝试的签名策略:\n`;
        errorInfo.debugInfo.strategies.forEach((strategy: any, index: number) => {
          report += `  ${index + 1}. ${strategy.name}: ${strategy.signature.substring(0, 8)}...\n`;
          report += `     ${strategy.description}\n`;
        });
      }
    }

    return report;
  }

  /**
   * 检查是否可以提供备用方案
   * @param url 请求URL
   * @returns 是否有备用方案
   */
  public static hasAlternative(url: string): boolean {
    // 对于聊天接口，目前没有v1的替代方案
    if (url.includes('chat')) {
      return false;
    }
    
    // 其他v2接口可能有v1的替代方案
    return true;
  }

  /**
   * 获取备用方案建议
   * @param url 请求URL
   * @returns 备用方案建议
   */
  public static getAlternativeSuggestions(url: string): string[] {
    if (url.includes('chat')) {
      return [
        '聊天功能目前只有v2接口，没有v1替代方案',
        '建议等待v2签名算法的进一步破解',
        '或者考虑使用其他AI服务提供商的API'
      ];
    }

    return [
      '尝试查找对应的v1接口',
      '检查API文档中是否有其他实现方式',
      '考虑使用不同的参数组合'
    ];
  }
}
