/**
 * 三色数据监控工具
 * 专门用于监控和诊断SSE流式响应中不同content_type的数据处理情况
 * 
 * 三色数据类型说明：
 * - progress (蓝色): 联网搜索进度信息
 * - thinking (黄色): AI思考过程
 * - text (绿色): 正式回答内容
 * - card (主色调): 搜索结果卡片
 */

import { SSEMessageDelta } from '../types';

/**
 * 三色数据类型枚举
 */
export enum ThreeColorDataType {
  PROGRESS = 'progress',    // 蓝色 - 联网搜索进度
  THINKING = 'thinking',    // 黄色 - AI思考过程
  TEXT = 'text',           // 绿色 - 正式回答
  CARD = 'card',           // 主色调 - 搜索结果卡片
  UNKNOWN = 'unknown'      // 未知类型
}

/**
 * 消息统计信息
 */
export interface MessageStats {
  /** 消息总数 */
  total: number;
  /** 成功处理数 */
  processed: number;
  /** 处理失败数 */
  failed: number;
  /** 被跳过数（重复等） */
  skipped: number;
  /** 最后接收时间 */
  lastReceived: number;
  /** 平均消息长度 */
  averageLength: number;
  /** 消息长度总和 */
  totalLength: number;
}

/**
 * 三色数据监控器
 */
export class ThreeColorDataMonitor {
  private stats: Map<ThreeColorDataType, MessageStats> = new Map();
  private messageHistory: Array<{
    type: ThreeColorDataType;
    timestamp: number;
    content: string;
    processed: boolean;
    error?: string;
    messageId?: string;
  }> = [];
  private maxHistorySize: number = 1000;
  private isEnabled: boolean = true;

  constructor(maxHistorySize: number = 1000) {
    this.maxHistorySize = maxHistorySize;
    this.initializeStats();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    Object.values(ThreeColorDataType).forEach(type => {
      this.stats.set(type, {
        total: 0,
        processed: 0,
        failed: 0,
        skipped: 0,
        lastReceived: 0,
        averageLength: 0,
        totalLength: 0
      });
    });
  }

  /**
   * 记录接收到的消息
   */
  public recordMessage(data: SSEMessageDelta, processed: boolean = true, error?: string): void {
    if (!this.isEnabled) {
      return;
    }

    const contentType = this.getContentType(data.content_type);
    const timestamp = Date.now();
    
    // 更新统计信息
    const stats = this.stats.get(contentType)!;
    stats.total++;
    stats.lastReceived = timestamp;
    stats.totalLength += data.content.length;
    stats.averageLength = stats.totalLength / stats.total;

    if (processed) {
      stats.processed++;
    } else {
      stats.failed++;
    }

    // 记录到历史
    this.messageHistory.push({
      type: contentType,
      timestamp,
      content: data.content,
      processed,
      ...(error && { error }),
      ...(data.id && { messageId: data.id })
    });

    // 限制历史记录大小
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
    }

    // 输出调试日志
    this.logMessage(contentType, data, processed, error);
  }

  /**
   * 记录跳过的消息
   */
  public recordSkippedMessage(data: SSEMessageDelta, reason: string): void {
    if (!this.isEnabled) {
      return;
    }

    const contentType = this.getContentType(data.content_type);
    const stats = this.stats.get(contentType)!;
    stats.skipped++;

    console.warn(`[三色数据监控] 跳过${this.getColorName(contentType)}消息: ${reason}`, {
      messageId: data.id,
      contentType: data.content_type,
      content: data.content.substring(0, 100) + '...'
    });
  }

  /**
   * 获取内容类型
   */
  private getContentType(contentType: string): ThreeColorDataType {
    switch (contentType) {
      case 'progress':
        return ThreeColorDataType.PROGRESS;
      case 'thinking':
        return ThreeColorDataType.THINKING;
      case 'text':
        return ThreeColorDataType.TEXT;
      case 'card':
        return ThreeColorDataType.CARD;
      default:
        return ThreeColorDataType.UNKNOWN;
    }
  }

  /**
   * 获取颜色名称
   */
  private getColorName(type: ThreeColorDataType): string {
    switch (type) {
      case ThreeColorDataType.PROGRESS:
        return '蓝色';
      case ThreeColorDataType.THINKING:
        return '黄色';
      case ThreeColorDataType.TEXT:
        return '绿色';
      case ThreeColorDataType.CARD:
        return '主色调';
      default:
        return '未知';
    }
  }

  /**
   * 获取颜色图标
   */
  private getColorIcon(type: ThreeColorDataType): string {
    switch (type) {
      case ThreeColorDataType.PROGRESS:
        return '🔍';
      case ThreeColorDataType.THINKING:
        return '🤔';
      case ThreeColorDataType.TEXT:
        return '💬';
      case ThreeColorDataType.CARD:
        return '📋';
      default:
        return '❓';
    }
  }

  /**
   * 输出消息日志
   */
  private logMessage(type: ThreeColorDataType, data: SSEMessageDelta, processed: boolean, error?: string): void {
    const icon = this.getColorIcon(type);
    const colorName = this.getColorName(type);
    const status = processed ? '✅ 已处理' : '❌ 处理失败';
    
    console.log(`[三色数据监控] ${icon} ${colorName}消息 ${status}`, {
      messageId: data.id,
      contentType: data.content_type,
      contentLength: data.content.length,
      content: data.content.substring(0, 50) + (data.content.length > 50 ? '...' : ''),
      error: error || undefined,
      timestamp: new Date().toLocaleTimeString()
    });
  }

  /**
   * 获取统计报告
   */
  public getStatsReport(): string {
    let report = '\n=== 三色数据统计报告 ===\n';
    
    Object.values(ThreeColorDataType).forEach(type => {
      const stats = this.stats.get(type)!;
      const colorName = this.getColorName(type);
      const icon = this.getColorIcon(type);
      
      if (stats.total > 0) {
        const successRate = ((stats.processed / stats.total) * 100).toFixed(1);
        const lastReceived = stats.lastReceived > 0 
          ? new Date(stats.lastReceived).toLocaleTimeString() 
          : '无';
        
        report += `\n${icon} ${colorName}数据 (${type}):\n`;
        report += `  总数: ${stats.total}\n`;
        report += `  已处理: ${stats.processed}\n`;
        report += `  失败: ${stats.failed}\n`;
        report += `  跳过: ${stats.skipped}\n`;
        report += `  成功率: ${successRate}%\n`;
        report += `  平均长度: ${Math.round(stats.averageLength)} 字符\n`;
        report += `  最后接收: ${lastReceived}\n`;
      }
    });
    
    return report;
  }

  /**
   * 获取问题诊断
   */
  public getDiagnostics(): Array<{
    level: 'info' | 'warning' | 'error';
    message: string;
    suggestion: string;
  }> {
    const diagnostics: Array<{
      level: 'info' | 'warning' | 'error';
      message: string;
      suggestion: string;
    }> = [];

    Object.values(ThreeColorDataType).forEach(type => {
      const stats = this.stats.get(type)!;
      const colorName = this.getColorName(type);

      if (stats.total === 0 && type !== ThreeColorDataType.UNKNOWN) {
        diagnostics.push({
          level: 'warning',
          message: `未接收到任何${colorName}数据`,
          suggestion: `检查数据源是否正常发送${type}类型的消息`
        });
      } else if (stats.failed > 0) {
        const failureRate = (stats.failed / stats.total) * 100;
        if (failureRate > 10) {
          diagnostics.push({
            level: 'error',
            message: `${colorName}数据处理失败率过高: ${failureRate.toFixed(1)}%`,
            suggestion: `检查${type}类型消息的解析逻辑和错误处理`
          });
        }
      }

      if (stats.skipped > stats.processed && stats.total > 0) {
        diagnostics.push({
          level: 'warning',
          message: `${colorName}数据跳过数量过多`,
          suggestion: `检查消息处理逻辑，当前版本不进行消息去重`
        });
      }
    });

    return diagnostics;
  }

  /**
   * 重置统计信息
   */
  public reset(): void {
    this.initializeStats();
    this.messageHistory = [];
    console.log('[三色数据监控] 统计信息已重置');
  }

  /**
   * 启用/禁用监控
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log(`[三色数据监控] 监控已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取最近的消息历史
   */
  public getRecentMessages(limit: number = 50): Array<{
    type: ThreeColorDataType;
    timestamp: number;
    content: string;
    processed: boolean;
    error?: string;
    messageId?: string;
  }> {
    return this.messageHistory.slice(-limit);
  }
}

// 导出全局监控实例
export const threeColorDataMonitor = new ThreeColorDataMonitor();
