/**
 * WebAssembly签名算法模拟器
 * 基于对sign_bg.wasm的分析，实现兼容的签名算法
 */

import { createHash, createHmac } from 'crypto';

/**
 * WASM签名模拟器配置
 */
export interface WasmSignatureConfig {
  /** 是否启用调试日志 */
  debug?: boolean;
  /** 时间偏移量（毫秒） */
  timeOffset?: number;
  /** 签名算法策略 */
  strategy?: 'standard' | 'enhanced' | 'hybrid';
  /** 自定义密钥 */
  secretKey?: string;
}

/**
 * 签名结果
 */
export interface SignatureResult {
  /** 生成的签名 */
  signature: string;
  /** 使用的时间戳 */
  timestamp: number;
  /** 使用的策略 */
  strategy: string;
  /** 调试信息 */
  debug?: {
    normalizedData: string;
    signString: string;
    algorithm: string;
  };
}

/**
 * WebAssembly签名算法模拟器
 * 
 * 基于对原始WASM模块的分析，这个类实现了多种可能的签名策略：
 * 1. 标准策略：基于时间戳的MD5签名
 * 2. 增强策略：包含HMAC和时间窗口验证
 * 3. 混合策略：结合多种算法的复合签名
 */
export class WasmSignatureEmulator {
  private config: Required<WasmSignatureConfig>;
  private timeBase: number;
  
  constructor(config: WasmSignatureConfig = {}) {
    this.config = {
      debug: false,
      timeOffset: 0,
      strategy: 'hybrid',
      secretKey: 'dangbei_ai_secret_2024',
      ...config
    };
    
    // 设置时间基准（模拟WASM中的时间处理）
    this.timeBase = 1640995200; // 2022-01-01 00:00:00 UTC
    
    if (this.config.debug) {
      console.log('🔧 WASM签名模拟器已初始化', {
        strategy: this.config.strategy,
        timeOffset: this.config.timeOffset
      });
    }
  }

  /**
   * 模拟原始get_sign函数
   * @param requestData 请求数据字符串
   * @param timestampOrNonce 时间戳或nonce字符串
   * @returns 签名结果
   */
  public getSign(requestData: string, timestampOrNonce: string): SignatureResult {
    const startTime = Date.now();
    
    try {
      // 解析输入参数
      const { normalizedData, timestamp } = this.parseInputs(requestData, timestampOrNonce);
      
      // 根据策略生成签名
      let result: SignatureResult;
      
      switch (this.config.strategy) {
        case 'standard':
          result = this.generateStandardSignature(normalizedData, timestamp);
          break;
        case 'enhanced':
          result = this.generateEnhancedSignature(normalizedData, timestamp);
          break;
        case 'hybrid':
        default:
          result = this.generateHybridSignature(normalizedData, timestamp);
          break;
      }
      
      if (this.config.debug) {
        console.log('🔐 签名生成完成', {
          strategy: result.strategy,
          signature: result.signature.substring(0, 8) + '...',
          timestamp: result.timestamp,
          duration: Date.now() - startTime + 'ms'
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ 签名生成失败:', error);
      throw new Error(`WASM签名模拟失败: ${(error as Error).message}`);
    }
  }

  /**
   * v2接口专用签名方法
   * 基于真实WASM调用模式：get_sign(requestDataPtr, requestDataLen, urlPathPtr, urlPathLen)
   *
   * @param requestData 请求数据字符串（对应WASM中的第一个参数）
   * @param urlPath URL路径字符串（对应WASM中的第三个参数）
   * @returns 签名结果
   */
  public getSignV2(requestData: string, urlPath: string): SignatureResult {
    const startTime = Date.now();

    if (this.config.debug) {
      console.log('🔧 开始v2签名生成', {
        // 这里输出字节长度，便于与抓包的 o=548、a=16 对齐核对
        requestDataLength: Buffer.byteLength(requestData, 'utf8'),
        urlPath: urlPath,
        urlPathLength: Buffer.byteLength(urlPath, 'utf8'),
        strategy: this.config.strategy
      });
    }

    try {
      // 模拟WASM中的内存分配和指针操作
      // 注意：WASM 层的长度为字节长度，这里按 UTF-8 字节数计算，避免中文字符导致长度不一致
      const requestDataLength = Buffer.byteLength(requestData, 'utf8');
      const urlPathLength = Buffer.byteLength(urlPath, 'utf8');

      // 生成基于真实WASM调用模式的签名
      const result = this.generateWasmV2Signature(requestData, urlPath, requestDataLength, urlPathLength);

      if (this.config.debug) {
        console.log('🔐 v2签名生成完成', {
          signature: result.signature.substring(0, 8) + '...',
          requestDataLen: requestDataLength,
          urlPathLen: urlPathLength,
          duration: Date.now() - startTime + 'ms'
        });
      }

      return result;

    } catch (error) {
      console.error('❌ v2签名生成失败:', error);
      throw new Error(`WASM v2签名模拟失败: ${(error as Error).message}`);
    }
  }

  /**
   * 解析输入参数
   */
  private parseInputs(requestData: string, timestampOrNonce: string): {
    normalizedData: string;
    timestamp: number;
  } {
    // 尝试解析时间戳
    let timestamp: number;
    
    if (/^\d{10,13}$/.test(timestampOrNonce)) {
      // 如果是数字字符串，作为时间戳处理
      timestamp = parseInt(timestampOrNonce, 10);
      if (timestamp > 1e12) {
        timestamp = Math.floor(timestamp / 1000); // 转换为秒
      }
    } else {
      // 否则使用当前时间
      timestamp = Math.floor((Date.now() + this.config.timeOffset) / 1000);
    }
    
    // 模拟WASM中的时间处理逻辑
    timestamp = this.processTimestamp(timestamp);
    
    return {
      normalizedData: requestData,
      timestamp
    };
  }

  /**
   * 处理时间戳（模拟WASM中的复杂时间计算）
   */
  private processTimestamp(timestamp: number): number {
    // 模拟WASM中观察到的时间处理逻辑
    const daySeconds = 86400;
    const baseTimestamp = this.timeBase;
    
    // 计算相对天数
    const relativeDays = Math.floor((timestamp - baseTimestamp) / daySeconds);
    
    // 应用一些观察到的数学运算
    const processedTimestamp = baseTimestamp + (relativeDays * daySeconds) + 
                              (timestamp % daySeconds);
    
    return processedTimestamp;
  }

  /**
   * 标准签名策略
   */
  private generateStandardSignature(data: string, timestamp: number): SignatureResult {
    const signString = `${timestamp}${data}`;
    const signature = createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
    
    const result: SignatureResult = {
      signature,
      timestamp,
      strategy: 'standard'
    };

    if (this.config.debug) {
      result.debug = {
        normalizedData: data,
        signString,
        algorithm: 'MD5'
      };
    }

    return result;
  }

  /**
   * 增强签名策略（包含HMAC）
   */
  private generateEnhancedSignature(data: string, timestamp: number): SignatureResult {
    // 生成动态密钥
    const dynamicKey = this.generateDynamicKey(timestamp);
    
    // 构建签名字符串
    const signString = `${timestamp}:${data}:${dynamicKey}`;
    
    // 使用HMAC-MD5
    const signature = createHmac('md5', this.config.secretKey)
      .update(signString)
      .digest('hex')
      .toUpperCase();
    
    const result: SignatureResult = {
      signature,
      timestamp,
      strategy: 'enhanced'
    };

    if (this.config.debug) {
      result.debug = {
        normalizedData: data,
        signString,
        algorithm: 'HMAC-MD5'
      };
    }

    return result;
  }

  /**
   * 混合签名策略（模拟WASM的复杂算法）
   */
  private generateHybridSignature(data: string, timestamp: number): SignatureResult {
    // 第一轮：基础哈希
    const baseHash = createHash('md5')
      .update(`${timestamp}${data}`)
      .digest('hex');
    
    // 第二轮：时间相关的变换
    const timeHash = this.generateTimeBasedHash(timestamp);
    
    // 第三轮：组合哈希
    const combinedData = `${baseHash}:${timeHash}:${data.length}`;
    const signature = createHash('md5')
      .update(combinedData)
      .digest('hex')
      .toUpperCase();
    
    const result: SignatureResult = {
      signature,
      timestamp,
      strategy: 'hybrid'
    };

    if (this.config.debug) {
      result.debug = {
        normalizedData: data,
        signString: combinedData,
        algorithm: 'Multi-round MD5'
      };
    }

    return result;
  }

  /**
   * 生成基于真实WASM调用模式的v2签名
   * 模拟WASM函数：get_sign(requestDataPtr, requestDataLen, urlPathPtr, urlPathLen)
   *
   * @param requestData 请求数据字符串
   * @param urlPath URL路径字符串
   * @param requestDataLength 请求数据长度
   * @param urlPathLength URL路径长度
   * @returns 签名结果
   */
  private generateWasmV2Signature(
    requestData: string,
    urlPath: string,
    requestDataLength: number,
    urlPathLength: number
  ): SignatureResult {
    const timestamp = Math.floor(Date.now() / 1000);

    if (this.config.debug) {
      console.log('🔧 WASM v2签名算法详细信息', {
        requestData: requestData.substring(0, 100) + '...',
        urlPath,
        requestDataLength,
        urlPathLength
      });
    }

    // 第一轮：模拟WASM中的内存布局和指针操作
    // 在真实WASM中，数据会被分配到特定的内存地址
    // 注：根据最新抓包对比，WASM get_sign 的最终签名未体现指针/内存校验和影响，因此此处不再引入内存校验和

    // 使用原始字节拼接而非字符串拼接，更贴近WASM内存处理
    const reqBytes = Buffer.from(requestData, 'utf8');
    const pathBytes = Buffer.from(urlPath, 'utf8');

    // 小端32位长度前缀（WASM中长度多为u32）
    const oBuf = Buffer.allocUnsafe(4);
    oBuf.writeUInt32LE(requestDataLength, 0);
    const aBuf = Buffer.allocUnsafe(4);
    aBuf.writeUInt32LE(urlPathLength, 0);

    // 组合：o(4) + a(4) + requestBytes + pathBytes
    const payload = Buffer.concat([oBuf, aBuf, reqBytes, pathBytes]);

    const signature = createHash('md5')
      .update(payload)
      .digest('hex')
      .toUpperCase();

    const result: SignatureResult = {
      signature,
      // 由于最终签名与请求数据/路径严格绑定，这里仍返回当前时间戳用于外层透传，但签名本身不依赖时间
      timestamp,
      strategy: 'wasm-v2'
    };

    if (this.config.debug) {
      result.debug = {
        normalizedData: `requestData(${requestDataLength}) + urlPath(${urlPathLength})`,
        signString: `u32le(${requestDataLength}),u32le(${urlPathLength}),bytes(body),bytes(path)`,
        algorithm: 'WASM-V2-Simulation (u32len + raw-bytes md5)'
      };

      console.log('🔐 WASM v2签名计算过程', {
        scheme: 'u32le(o),u32le(a),bodyBytes,pathBytes',
        o: requestDataLength,
        a: urlPathLength,
        finalSignature: signature
      });
    }

    return result;
  }

  /**
   * 模拟WASM内存布局
   * 在真实WASM中，数据会被分配到特定的内存地址
   *
   * @param requestData 请求数据
   * @param urlPath URL路径
   * @returns 内存布局信息
   */
  // 说明：此前为模拟WASM内存布局而保留此方法，但根据真实对比，get_sign 结果与指针值无强耦合；
  // 如需再次引入，可在此处恢复计算并返回校验信息。
  // 注意：为了通过严格的TS检查，暂时移除此方法的实现。如需再次模拟内存布局，请恢复此方法并在调用处使用。

  /**
   * 生成动态密钥
   */
  private generateDynamicKey(timestamp: number): string {
    // 基于时间戳生成动态密钥（模拟WASM中的复杂计算）
    const timeSlot = Math.floor(timestamp / 300); // 5分钟时间窗口
    return createHash('sha256')
      .update(`${this.config.secretKey}:${timeSlot}`)
      .digest('hex')
      .substring(0, 16);
  }

  /**
   * 生成基于时间的哈希
   */
  private generateTimeBasedHash(timestamp: number): string {
    // 模拟WASM中观察到的时间相关计算
    const dayOfYear = Math.floor((timestamp - this.timeBase) / 86400) % 365;
    const hourOfDay = Math.floor((timestamp % 86400) / 3600);
    
    return createHash('md5')
      .update(`${dayOfYear}:${hourOfDay}:${this.config.secretKey}`)
      .digest('hex')
      .substring(0, 8);
  }

  /**
   * 验证签名是否有效
   */
  public verifySignature(signature: string, requestData: string, timestamp: number): boolean {
    try {
      const result = this.getSign(requestData, timestamp.toString());
      return result.signature === signature;
    } catch {
      return false;
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<WasmSignatureConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.config.debug) {
      console.log('🔧 WASM签名模拟器配置已更新', newConfig);
    }
  }
}

/**
 * 默认的WASM签名模拟器实例
 */
export const defaultWasmEmulator = new WasmSignatureEmulator({
  debug: process.env['NODE_ENV'] === 'development',
  strategy: 'hybrid'
});
