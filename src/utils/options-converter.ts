/**
 * 选项转换工具
 * 负责将chat接口的options参数转换为当贝API所需的userAction参数格式
 */

/**
 * 聊天选项接口
 * 定义chat接口支持的选项参数
 */
export interface ChatOptionsConfig {
  /** 是否启用深度思考模式（标准键） */
  deep_thinking?: boolean;
  /** 是否启用联网搜索功能（标准键） */
  online_search?: boolean;
  /** 兼容键：深度思考（与 deep_thinking 同义） */
  deep?: boolean;
  /** 兼容键：联网搜索（与 online_search 同义） */
  online?: boolean;
}

/**
 * 用户操作映射表
 * 定义options选项到userAction字符串的映射关系
 */
const USER_ACTION_MAPPING = {
  /** 深度思考选项对应的userAction值（标准键） */
  deep_thinking: 'deep',
  /** 联网搜索选项对应的userAction值（标准键） */
  online_search: 'online',
  /** 兼容键：深度思考（与 deep_thinking 同义） */
  deep: 'deep',
  /** 兼容键：联网搜索（与 online_search 同义） */
  online: 'online'
} as const;

/**
 * 将chat接口的options参数转换为当贝API的userAction参数
 * 
 * 转换规则：
 * - deep_thinking: true -> 添加 "deep"
 * - online_search: true -> 添加 "online"  
 * - 多个启用的选项用逗号分隔
 * - 如果没有任何选项启用，返回空字符串
 * 
 * 示例：
 * - { deep_thinking: false, online_search: false } -> ""
 * - { deep_thinking: true, online_search: false } -> "deep"
 * - { deep_thinking: false, online_search: true } -> "online"
 * - { deep_thinking: true, online_search: true } -> "deep,online"
 * 
 * @param options 聊天选项配置对象
 * @returns 转换后的userAction字符串
 */
export function convertOptionsToUserAction(options?: ChatOptionsConfig | undefined): string {
  // 如果没有提供options参数，返回空字符串
  if (!options) {
    return '';
  }

  // 存储启用的用户操作
  const enabledActions: string[] = [];

  // 兼容处理：允许使用 deep/online 作为简写键
  const deepEnabled = options.deep_thinking === true || (options as any).deep === true;
  const onlineEnabled = options.online_search === true || (options as any).online === true;

  // 检查深度思考选项
  if (deepEnabled) {
    enabledActions.push(USER_ACTION_MAPPING.deep_thinking);
  }

  // 检查联网搜索选项
  if (onlineEnabled) {
    enabledActions.push(USER_ACTION_MAPPING.online_search);
  }

  // 将启用的操作用逗号连接并返回
  return enabledActions.join(',');
}

/**
 * 验证options参数的有效性
 * 
 * @param options 待验证的选项配置
 * @returns 验证结果对象，包含是否有效和错误信息
 */
export function validateChatOptions(options?: ChatOptionsConfig | undefined): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (options) {
    // 检查deep_thinking参数类型（或兼容键 deep）
    if (options.deep_thinking !== undefined && typeof options.deep_thinking !== 'boolean') {
      errors.push('deep_thinking参数必须是布尔值');
    }
    if ((options as any).deep !== undefined && typeof (options as any).deep !== 'boolean') {
      errors.push('deep参数必须是布尔值');
    }

    // 检查online_search参数类型（或兼容键 online）
    if (options.online_search !== undefined && typeof options.online_search !== 'boolean') {
      errors.push('online_search参数必须是布尔值');
    }
    if ((options as any).online !== undefined && typeof (options as any).online !== 'boolean') {
      errors.push('online参数必须是布尔值');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 获取支持的选项列表
 * 
 * @returns 支持的选项名称数组
 */
export function getSupportedOptions(): string[] {
  return Object.keys(USER_ACTION_MAPPING);
}

/**
 * 获取选项的userAction映射值
 * 
 * @param optionName 选项名称
 * @returns 对应的userAction值，如果不存在则返回undefined
 */
export function getOptionMapping(optionName: keyof ChatOptionsConfig): string | undefined {
  // 为了兼容，允许 deep/online 两个兼容键
  const key = optionName as keyof typeof USER_ACTION_MAPPING;
  return USER_ACTION_MAPPING[key];
}
