/**
 * WASM v2签名算法测试工具
 * 基于真实的WASM调用参数进行测试和验证
 */

import { WasmSignatureEmulator } from './wasm-signature-emulator';
import { SignatureV2Utils } from './signature-v2';
import { SignatureParams } from '../types';

/**
 * 测试用例接口
 */
interface TestCase {
  name: string;
  requestData: string;
  urlPath: string;
  expectedLength: number;
  description: string;
}

/**
 * 真实WASM调用参数的测试用例
 */
const REAL_WASM_TEST_CASES: TestCase[] = [
  {
    name: 'v2_chat_real_case',
    requestData: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"364120755967492485","question":"使用Agno框架进行多Agent协作时如何进行共享上下文？","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"364133138684711109","chatId":"364133138684711109","files":[],"reference":[],"role":"user","status":"local","content":"使用Agno框架进行多Agent协作时如何进行共享上下文？","userAction":"","agentId":""}',
    urlPath: '/chatApi/v2/chat',
    expectedLength: 32,
    description: '真实的v2/chat接口调用参数'
  },
  {
    name: 'v2_chat_simple_case',
    requestData: '{"question":"你好","model":"kimi-k2-0711-preview"}',
    urlPath: '/chatApi/v2/chat',
    expectedLength: 32,
    description: '简化的v2/chat接口调用参数'
  },
  {
    name: 'v2_other_endpoint',
    requestData: '{"action":"test"}',
    urlPath: '/chatApi/v2/status',
    expectedLength: 32,
    description: '其他v2接口端点测试'
  }
];

/**
 * 运行WASM v2签名测试
 */
export function runWasmV2SignatureTests(): void {
  console.log('🧪 开始WASM v2签名算法测试');
  console.log('=' .repeat(60));

  // 创建测试用的WASM模拟器
  const emulator = new WasmSignatureEmulator({
    debug: true,
    strategy: 'hybrid',
    secretKey: 'dangbei_ai_v2_test_2024'
  });

  let passedTests = 0;
  let totalTests = REAL_WASM_TEST_CASES.length;

  for (const testCase of REAL_WASM_TEST_CASES) {
    console.log(`\n🔬 测试用例: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`📊 请求数据长度: ${testCase.requestData.length}`);
    console.log(`📊 URL路径长度: ${testCase.urlPath.length}`);

    try {
      // 使用新的getSignV2方法测试
      const result = emulator.getSignV2(testCase.requestData, testCase.urlPath);
      
      console.log(`✅ 签名生成成功:`);
      console.log(`   - 签名: ${result.signature}`);
      console.log(`   - 策略: ${result.strategy}`);
      console.log(`   - 时间戳: ${result.timestamp}`);
      
      // 验证签名格式
      if (result.signature.length === testCase.expectedLength && /^[A-F0-9]+$/.test(result.signature)) {
        console.log(`✅ 签名格式验证通过`);
        passedTests++;
      } else {
        console.log(`❌ 签名格式验证失败`);
      }

      // 显示调试信息
      if (result.debug) {
        console.log(`🔍 调试信息:`);
        console.log(`   - 规范化数据: ${result.debug.normalizedData}`);
        console.log(`   - 签名字符串: ${result.debug.signString.substring(0, 50)}...`);
        console.log(`   - 算法: ${result.debug.algorithm}`);
      }

    } catch (error) {
      console.log(`❌ 测试失败: ${error}`);
    }
  }

  console.log('\n' + '=' .repeat(60));
  console.log(`🏁 测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试用例通过！');
  } else {
    console.log('⚠️  部分测试用例失败，需要进一步调试');
  }
}

/**
 * 测试SignatureV2Utils集成
 */
export function testSignatureV2Integration(): void {
  console.log('\n🔧 测试SignatureV2Utils集成');
  console.log('=' .repeat(60));

  const testParams: SignatureParams = {
    timestamp: Math.floor(Date.now() / 1000),
    nonce: 'test_nonce_v2',
    deviceId: 'test_device_v2',
    method: 'POST',
    url: '/ai-search/chatApi/v2/chat',
    data: {
      stream: true,
      botCode: 'AI_SEARCH',
      question: '测试问题',
      model: 'kimi-k2-0711-preview'
    }
  };

  try {
    console.log('🔧 测试参数:');
    console.log(`   - 时间戳: ${testParams.timestamp}`);
    console.log(`   - 随机数: ${testParams.nonce}`);
    console.log(`   - 设备ID: ${testParams.deviceId}`);
    console.log(`   - 方法: ${testParams.method}`);
    console.log(`   - URL: ${testParams.url}`);

    const signature = SignatureV2Utils.generateV2Signature(testParams);
    
    console.log(`✅ 集成测试成功:`);
    console.log(`   - 生成的签名: ${signature}`);
    console.log(`   - 签名长度: ${signature.length}`);
    console.log(`   - 签名格式: ${/^[A-F0-9]{32}$/.test(signature) ? '有效' : '无效'}`);

  } catch (error) {
    console.log(`❌ 集成测试失败: ${error}`);
  }
}

/**
 * 比较不同签名策略的结果
 */
export function compareV2SignatureStrategies(): void {
  console.log('\n🔄 比较不同签名策略');
  console.log('=' .repeat(60));

  const testData = REAL_WASM_TEST_CASES[0]; // 使用真实的测试数据
  const strategies = ['standard', 'enhanced', 'hybrid'] as const;

  if (!testData) {
    console.log('❌ 没有可用的测试数据');
    return;
  }

  for (const strategy of strategies) {
    console.log(`\n📊 策略: ${strategy.toUpperCase()}`);

    try {
      const emulator = new WasmSignatureEmulator({
        debug: false,
        strategy,
        secretKey: `dangbei_ai_${strategy}_2024`
      });

      const result = emulator.getSignV2(testData.requestData, testData.urlPath);
      
      console.log(`   - 签名: ${result.signature}`);
      console.log(`   - 策略: ${result.strategy}`);
      
    } catch (error) {
      console.log(`   - 错误: ${error}`);
    }
  }
}

/**
 * 性能测试
 */
export function runV2PerformanceTest(iterations: number = 1000): void {
  console.log(`\n⚡ 性能测试 (${iterations} 次迭代)`);
  console.log('=' .repeat(60));

  const emulator = new WasmSignatureEmulator({
    debug: false,
    strategy: 'hybrid'
  });

  const testData = REAL_WASM_TEST_CASES[0];

  if (!testData) {
    console.log('❌ 没有可用的测试数据');
    return;
  }

  const startTime = Date.now();

  for (let i = 0; i < iterations; i++) {
    emulator.getSignV2(testData.requestData, testData.urlPath);
  }

  const endTime = Date.now();
  const duration = endTime - startTime;
  const avgTime = duration / iterations;
  const throughput = Math.round(iterations / (duration / 1000));

  console.log(`📊 性能指标:`);
  console.log(`   - 总耗时: ${duration}ms`);
  console.log(`   - 平均耗时: ${avgTime.toFixed(2)}ms`);
  console.log(`   - 吞吐量: ${throughput} 次/秒`);
}

/**
 * 运行所有测试
 */
export function runAllV2Tests(): void {
  console.log('🚀 开始完整的WASM v2签名测试套件');
  console.log('=' .repeat(80));

  try {
    // 基础功能测试
    runWasmV2SignatureTests();
    
    // 集成测试
    testSignatureV2Integration();
    
    // 策略比较
    compareV2SignatureStrategies();
    
    // 性能测试
    runV2PerformanceTest(100);
    
    console.log('\n🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试套件执行失败:', error);
  }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllV2Tests();
}
