/**
 * 模型列表控制器
 * 处理模型相关的 HTTP 请求
 */

import { Request, Response } from 'express';
import { ModelService } from '../services/model-service';
import { ModelTransformService } from '../services/model-transform-service';
import { ApiResponse, ModelsResponse } from '../types/api';

/**
 * 模型控制器类
 * 提供模型列表相关的 API 端点处理
 */
export class ModelsController {
  private readonly modelService: ModelService;
  private readonly transformService: ModelTransformService;

  constructor() {
    this.modelService = new ModelService();
    this.transformService = new ModelTransformService();
  }

  /**
   * 获取所有支持的模型列表
   * GET /api/models
   * 
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public getModels = async (_req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();
    
    try {
      console.log(`[${requestId}] 处理获取模型列表请求`);
      
      // 获取模型列表数据
      const modelsData = this.modelService.getModels();
      
      // 构建成功响应
      const response: ApiResponse<ModelsResponse> = {
        success: true,
        data: modelsData,
        requestId,
        timestamp: Date.now()
      };

      console.log(`[${requestId}] 成功返回 ${modelsData.total} 个模型`);
      
      // 设置响应头
      res.set({
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
        'X-Request-ID': requestId
      });

      res.status(200).json(response);
    } catch (error) {
      console.error(`[${requestId}] 获取模型列表失败:`, error);
      
      const errorResponse: ApiResponse = {
        success: false,
        error: {
          code: 'MODELS_FETCH_ERROR',
          message: '获取模型列表失败',
          details: error instanceof Error ? error.message : '未知错误'
        },
        requestId,
        timestamp: Date.now()
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * 根据ID获取特定模型信息
   * GET /api/models/:modelId
   * 
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public getModelById = async (req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();
    const { modelId } = req.params;
    
    try {
      console.log(`[${requestId}] 处理获取模型详情请求: ${modelId}`);
      
      if (!modelId || !modelId.trim()) {
        const errorResponse: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_MODEL_ID',
            message: '模型ID不能为空'
          },
          requestId,
          timestamp: Date.now()
        };
        
        res.status(400).json(errorResponse);
        return;
      }

      // 获取特定模型信息
      const modelInfo = this.modelService.getModelById(modelId);
      
      if (!modelInfo) {
        console.log(`[${requestId}] 模型不存在: ${modelId}`);
        
        const errorResponse: ApiResponse = {
          success: false,
          error: {
            code: 'MODEL_NOT_FOUND',
            message: `模型 '${modelId}' 不存在`
          },
          requestId,
          timestamp: Date.now()
        };
        
        res.status(404).json(errorResponse);
        return;
      }

      // 构建成功响应
      const response: ApiResponse = {
        success: true,
        data: modelInfo,
        requestId,
        timestamp: Date.now()
      };

      console.log(`[${requestId}] 成功返回模型详情: ${modelInfo.name}`);
      
      // 设置响应头
      res.set({
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
        'X-Request-ID': requestId
      });

      res.status(200).json(response);
    } catch (error) {
      console.error(`[${requestId}] 获取模型详情失败:`, error);
      
      const errorResponse: ApiResponse = {
        success: false,
        error: {
          code: 'MODEL_FETCH_ERROR',
          message: '获取模型详情失败',
          details: error instanceof Error ? error.message : '未知错误'
        },
        requestId,
        timestamp: Date.now()
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * 获取推荐模型列表
   * GET /api/models/recommended
   * 
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public getRecommendedModels = async (_req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();
    
    try {
      console.log(`[${requestId}] 处理获取推荐模型列表请求`);
      
      // 获取推荐模型列表
      const recommendedModels = this.modelService.getRecommendedModels();
      
      // 构建成功响应
      const response: ApiResponse = {
        success: true,
        data: {
          models: recommendedModels,
          total: recommendedModels.length
        },
        requestId,
        timestamp: Date.now()
      };

      console.log(`[${requestId}] 成功返回 ${recommendedModels.length} 个推荐模型`);
      
      // 设置响应头
      res.set({
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
        'X-Request-ID': requestId
      });

      res.status(200).json(response);
    } catch (error) {
      console.error(`[${requestId}] 获取推荐模型列表失败:`, error);
      
      const errorResponse: ApiResponse = {
        success: false,
        error: {
          code: 'RECOMMENDED_MODELS_FETCH_ERROR',
          message: '获取推荐模型列表失败',
          details: error instanceof Error ? error.message : '未知错误'
        },
        requestId,
        timestamp: Date.now()
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * 重新加载模型数据
   * POST /api/models/reload
   * 
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public reloadModels = async (_req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();
    
    try {
      console.log(`[${requestId}] 处理重新加载模型数据请求`);
      
      // 重新加载模型数据
      this.modelService.reloadModelsData();
      
      // 获取更新后的状态
      const status = this.modelService.getStatus();
      
      // 构建成功响应
      const response: ApiResponse = {
        success: true,
        data: {
          message: '模型数据重新加载成功',
          status
        },
        requestId,
        timestamp: Date.now()
      };

      console.log(`[${requestId}] 模型数据重新加载成功，共 ${status.modelsCount} 个模型`);
      
      res.status(200).json(response);
    } catch (error) {
      console.error(`[${requestId}] 重新加载模型数据失败:`, error);
      
      const errorResponse: ApiResponse = {
        success: false,
        error: {
          code: 'MODELS_RELOAD_ERROR',
          message: '重新加载模型数据失败',
          details: error instanceof Error ? error.message : '未知错误'
        },
        requestId,
        timestamp: Date.now()
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * OpenAI兼容的模型列表接口
   * GET /v1/models
   *
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public getModelsOpenAI = async (_req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();

    try {
      console.log(`[${requestId}] 处理OpenAI兼容模型列表请求`);

      // 获取模型列表数据
      const modelsData = this.modelService.getModels();

      // 转换为OpenAI兼容格式
      const openaiResponse = this.transformService.transformToOpenAI(modelsData.models);

      console.log(`[${requestId}] 成功返回 ${openaiResponse.data.length} 个模型 (OpenAI格式)`);

      // 设置响应头
      res.set({
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
        'X-Request-ID': requestId
      });

      res.status(200).json(openaiResponse);
    } catch (error) {
      console.error(`[${requestId}] 获取OpenAI兼容模型列表失败:`, error);

      // OpenAI格式的错误响应
      const errorResponse = {
        error: {
          message: '获取模型列表失败',
          type: 'server_error',
          code: 'models_fetch_error',
          details: error instanceof Error ? error.message : '未知错误'
        }
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * OpenAI兼容的单个模型信息接口
   * GET /v1/models/{modelId}
   *
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public getModelByIdOpenAI = async (req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();
    const { modelId } = req.params;

    try {
      console.log(`[${requestId}] 处理OpenAI兼容模型详情请求: ${modelId}`);

      if (!modelId || !modelId.trim()) {
        const errorResponse = {
          error: {
            message: '模型ID不能为空',
            type: 'invalid_request',
            code: 'invalid_model_id'
          }
        };

        res.status(400).json(errorResponse);
        return;
      }

      // 获取特定模型信息
      const modelInfo = this.modelService.getModelById(modelId);

      if (!modelInfo) {
        console.log(`[${requestId}] 模型不存在: ${modelId}`);

        const errorResponse = {
          error: {
            message: `模型 '${modelId}' 不存在`,
            type: 'invalid_request',
            code: 'model_not_found'
          }
        };

        res.status(404).json(errorResponse);
        return;
      }

      // 转换为OpenAI兼容格式
      const openaiResponse = this.transformService.transformToOpenAI([modelInfo]);
      const singleModel = openaiResponse.data[0];

      console.log(`[${requestId}] 成功返回模型详情: ${modelInfo.name} (OpenAI格式)`);

      // 设置响应头
      res.set({
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
        'X-Request-ID': requestId
      });

      res.status(200).json(singleModel);
    } catch (error) {
      console.error(`[${requestId}] 获取OpenAI兼容模型详情失败:`, error);

      const errorResponse = {
        error: {
          message: '获取模型详情失败',
          type: 'server_error',
          code: 'model_fetch_error',
          details: error instanceof Error ? error.message : '未知错误'
        }
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * 生成请求ID
   *
   * @returns 唯一的请求ID
   * @private
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
