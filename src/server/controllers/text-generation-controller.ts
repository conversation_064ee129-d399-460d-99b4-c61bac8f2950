/**
 * 文本生成控制器
 * 处理文本生成相关的 HTTP 请求
 */

import { Request, Response } from 'express';
import { DangbeiProvider } from '../../providers';
import { ModelService } from '../services/model-service';
import {
  ApiResponse,
  TextGenerationRequest,
  TextGenerationResponse,
  TextGenerationStreamChunk,
  TextGenerationTaskType,
  ErrorResponse,
  ModelInfo
} from '../types/api';
import { ChatCallbacks, SSEMessageDelta, SSEChatCompleted } from '../../types';

/**
 * 文本生成控制器类
 * 提供文本生成相关的 API 端点处理
 */
export class TextGenerationController {
  private readonly dangbeiProvider: DangbeiProvider;
  private readonly modelService: ModelService;

  constructor() {
    // 初始化当贝 Provider，启用调试模式
    this.dangbeiProvider = new DangbeiProvider({
      debug: true
    });
    
    this.modelService = new ModelService();
  }

  /**
   * 文本生成接口处理函数
   * 支持流式和非流式响应
   * 
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public generate = async (req: Request, res: Response): Promise<void> => {
    const requestId = res.get('X-Request-ID') || `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      console.log(`[${requestId}] 收到文本生成请求`);
      
      // 验证请求体
      const textRequest = this.validateTextGenerationRequest(req.body);
      if (!textRequest.valid) {
        const errorResponse: ApiResponse<ErrorResponse> = {
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: '请求参数无效',
            details: textRequest.errors
          },
          requestId,
          timestamp: Date.now()
        };
        
        res.status(400).json(errorResponse);
        return;
      }

      const generationRequest = textRequest.data!;
      
      // 验证模型是否存在
      if (generationRequest.model) {
        const modelExists = this.modelService.isModelSupported(generationRequest.model);
        if (!modelExists) {
          const errorResponse: ApiResponse<ErrorResponse> = {
            success: false,
            error: {
              code: 'MODEL_NOT_FOUND',
              message: `模型 '${generationRequest.model}' 不存在`
            },
            requestId,
            timestamp: Date.now()
          };
          
          res.status(404).json(errorResponse);
          return;
        }
      }

      console.log(`[${requestId}] 文本生成参数:`, {
        prompt: generationRequest.prompt.substring(0, 100) + '...',
        model: generationRequest.model || 'default',
        taskType: generationRequest.task_type || 'general',
        stream: generationRequest.stream || false
      });

      // 处理流式响应
      if (generationRequest.stream) {
        await this.handleStreamGeneration(generationRequest, res, requestId);
      } else {
        await this.handleNormalGeneration(generationRequest, res, requestId);
      }

    } catch (error) {
      console.error(`[${requestId}] 文本生成请求处理失败:`, error);
      
      if (!res.headersSent) {
        const errorResponse: ApiResponse<ErrorResponse> = {
          success: false,
          error: {
            code: 'GENERATION_ERROR',
            message: '文本生成请求处理失败',
            details: error instanceof Error ? error.message : '未知错误'
          },
          requestId,
          timestamp: Date.now()
        };

        res.status(500).json(errorResponse);
      }
    }
  };

  /**
   * 处理普通文本生成请求（非流式）
   * 
   * @param generationRequest 文本生成请求
   * @param res Express 响应对象
   * @param requestId 请求ID
   * @private
   */
  private async handleNormalGeneration(
    generationRequest: TextGenerationRequest, 
    res: Response, 
    requestId: string
  ): Promise<void> {
    console.log(`[${requestId}] 处理非流式文本生成请求`);
    
    // 创建新对话用于文本生成
    console.log(`[${requestId}] 创建文本生成对话`);
    const conversation = await this.dangbeiProvider.createConversation();

    // 构建优化的提示词
    const optimizedPrompt = this.buildOptimizedPrompt(generationRequest);
    
    // 构建聊天选项
    const chatOptions = {
      conversationId: conversation.conversationId,
      question: optimizedPrompt,
      model: generationRequest.model || 'deepseek', // 使用默认推荐模型
      chatOption: {
        searchKnowledge: false,
        searchAllKnowledge: false,
        searchSharedKnowledge: false
      }
    };

    console.log(`[${requestId}] 发送文本生成请求到模型: ${chatOptions.model}`);

    // 发送文本生成请求
    const response = await this.dangbeiProvider.chat(chatOptions);
    
    // 构建响应
    const generationResponse: TextGenerationResponse = {
      text: response.content,
      model: generationRequest.model || 'deepseek',
      task_type: generationRequest.task_type || 'general',
      finish_reason: 'stop',
      generation_id: response.messageId,
      request_id: response.requestId,
      metadata: {
        generation_time: Date.now() - parseInt(requestId.split('_')[1] || '0'),
        parameters: {
          ...(generationRequest.temperature !== undefined && { temperature: generationRequest.temperature }),
          ...(generationRequest.max_tokens !== undefined && { max_tokens: generationRequest.max_tokens })
        }
      }
    };

    const apiResponse: ApiResponse<TextGenerationResponse> = {
      success: true,
      data: generationResponse,
      requestId,
      timestamp: Date.now()
    };

    console.log(`[${requestId}] 非流式文本生成请求处理完成，生成长度: ${response.content.length}`);
    
    res.set({
      'Content-Type': 'application/json',
      'X-Request-ID': requestId
    });
    
    res.status(200).json(apiResponse);
  }

  /**
   * 处理流式文本生成请求
   * 
   * @param generationRequest 文本生成请求
   * @param res Express 响应对象
   * @param requestId 请求ID
   * @private
   */
  private async handleStreamGeneration(
    generationRequest: TextGenerationRequest, 
    res: Response, 
    requestId: string
  ): Promise<void> {
    console.log(`[${requestId}] 处理流式文本生成请求`);
    
    // 设置 SSE 响应头
    res.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'X-Request-ID': requestId
    });

    // 创建新对话用于文本生成
    console.log(`[${requestId}] 创建流式文本生成对话`);
    const conversation = await this.dangbeiProvider.createConversation();

    // 构建优化的提示词
    const optimizedPrompt = this.buildOptimizedPrompt(generationRequest);

    // 构建聊天选项和回调
    const callbacks: ChatCallbacks = {
      onMessage: (content: string, data: SSEMessageDelta) => {
        const chunk: TextGenerationStreamChunk = {
          id: data.id,
          object: 'text.generation.chunk',
          created: Math.floor(data.created_at / 1000),
          model: generationRequest.model || 'deepseek',
          choices: [{
            index: 0,
            delta: {
              text: content
            },
            finish_reason: null
          }],
          task_type: generationRequest.task_type || 'general'
        };
        
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      },

      onComplete: (data: SSEChatCompleted) => {
        // 发送完成标记
        const finalChunk: TextGenerationStreamChunk = {
          id: data.id,
          object: 'text.generation.chunk',
          created: Math.floor(Date.now() / 1000),
          model: generationRequest.model || 'deepseek',
          choices: [{
            index: 0,
            delta: {},
            finish_reason: 'stop'
          }],
          task_type: generationRequest.task_type || 'general'
        };
        
        res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
        
        console.log(`[${requestId}] 流式文本生成完成`);
      },

      onError: (error: Error) => {
        console.error(`[${requestId}] 流式文本生成错误:`, error);
        
        const errorChunk = {
          error: {
            code: 'GENERATION_ERROR',
            message: error.message
          }
        };
        
        res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
      }
    };

    // 构建聊天选项
    const chatOptions = {
      conversationId: conversation.conversationId,
      question: optimizedPrompt,
      model: generationRequest.model || 'deepseek',
      chatOption: {
        searchKnowledge: false,
        searchAllKnowledge: false,
        searchSharedKnowledge: false
      },
      callbacks
    };

    console.log(`[${requestId}] 开始流式文本生成，模型: ${chatOptions.model}`);

    // 发送流式文本生成请求
    await this.dangbeiProvider.chat(chatOptions, callbacks);
  }

  /**
   * 构建优化的提示词
   * 根据任务类型和选项优化提示词
   *
   * @param request 文本生成请求
   * @returns 优化后的提示词
   * @private
   */
  private buildOptimizedPrompt(request: TextGenerationRequest): string {
    let prompt = request.prompt;

    // 根据任务类型添加前缀指导
    const taskPrefixes: Record<TextGenerationTaskType, string> = {
      creative: '请进行创意写作，发挥想象力和创造力：',
      code: '请生成代码，确保代码质量和可读性：',
      document: '请生成专业文档，确保结构清晰、内容准确：',
      summary: '请对以下内容进行摘要总结：',
      translation: '请进行翻译，确保准确性和流畅性：',
      rewrite: '请对以下内容进行改写，保持原意的同时提升表达：',
      qa: '请回答以下问题，提供准确和有用的信息：',
      general: ''
    };

    const taskType = request.task_type || 'general';
    if (taskPrefixes[taskType]) {
      prompt = `${taskPrefixes[taskType]}\n\n${prompt}`;
    }

    // 添加格式要求
    if (request.options?.format) {
      prompt += `\n\n请以${request.options.format}格式输出。`;
    }

    // 添加语言要求
    if (request.options?.language && request.options.language !== 'zh') {
      prompt += `\n\n请使用${request.options.language}语言回答。`;
    }

    // 添加风格要求
    if (request.options?.style) {
      prompt += `\n\n请采用${request.options.style}的写作风格。`;
    }

    return prompt;
  }

  /**
   * 验证文本生成请求参数
   *
   * @param body 请求体
   * @returns 验证结果
   * @private
   */
  private validateTextGenerationRequest(body: any): {
    valid: boolean;
    errors?: string[];
    data?: TextGenerationRequest;
  } {
    const errors: string[] = [];

    // 检查必需参数
    if (!body.prompt || typeof body.prompt !== 'string' || !body.prompt.trim()) {
      errors.push('prompt 参数是必需的，且不能为空');
    }

    // 检查提示词长度
    if (body.prompt && body.prompt.length > 10000) {
      errors.push('prompt 长度不能超过10000字符');
    }

    // 检查可选参数类型
    if (body.model && typeof body.model !== 'string') {
      errors.push('model 参数必须是字符串类型');
    }

    if (body.stream !== undefined && typeof body.stream !== 'boolean') {
      errors.push('stream 参数必须是布尔类型');
    }

    if (body.max_tokens !== undefined) {
      if (typeof body.max_tokens !== 'number' || body.max_tokens <= 0 || body.max_tokens > 4000) {
        errors.push('max_tokens 参数必须是1-4000之间的正整数');
      }
    }

    if (body.temperature !== undefined) {
      if (typeof body.temperature !== 'number' || body.temperature < 0 || body.temperature > 1) {
        errors.push('temperature 参数必须是0-1之间的数字');
      }
    }

    // 检查任务类型
    const validTaskTypes: TextGenerationTaskType[] = [
      'creative', 'code', 'document', 'summary', 'translation', 'rewrite', 'qa', 'general'
    ];
    if (body.task_type && !validTaskTypes.includes(body.task_type)) {
      errors.push(`task_type 参数必须是以下值之一: ${validTaskTypes.join(', ')}`);
    }

    if (errors.length > 0) {
      return { valid: false, errors };
    }

    // 构建验证后的请求对象
    const validatedRequest: TextGenerationRequest = {
      prompt: body.prompt.trim(),
      model: body.model,
      stream: body.stream || false,
      max_tokens: body.max_tokens,
      temperature: body.temperature,
      task_type: body.task_type || 'general',
      options: body.options || {}
    };

    return { valid: true, data: validatedRequest };
  }

  /**
   * 获取支持文本生成的模型列表
   *
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public getModels = async (_req: Request, res: Response): Promise<void> => {
    const requestId = res.get('X-Request-ID') || `models_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      console.log(`[${requestId}] 获取文本生成模型列表`);

      // 获取所有模型
      const modelsData = this.modelService.getModels();

      // 转换为文本生成模型格式
      const textGenerationModels = modelsData.models.map((model: ModelInfo) => ({
        ...model,
        supported_tasks: this.getSupportedTasksForModel(model.id),
        max_input_length: this.getMaxInputLengthForModel(model.id),
        max_output_length: this.getMaxOutputLengthForModel(model.id),
        supports_streaming: true // 所有模型都支持流式生成
      }));

      // 构建推荐模型映射
      const recommendations = this.buildTaskRecommendations(textGenerationModels);

      const response = {
        defaultModel: modelsData.defaultModel,
        models: textGenerationModels,
        total: textGenerationModels.length,
        recommendations
      };

      const apiResponse: ApiResponse<typeof response> = {
        success: true,
        data: response,
        requestId,
        timestamp: Date.now()
      };

      console.log(`[${requestId}] 返回${textGenerationModels.length}个文本生成模型`);

      res.set({
        'Content-Type': 'application/json',
        'X-Request-ID': requestId
      });

      res.status(200).json(apiResponse);

    } catch (error) {
      console.error(`[${requestId}] 获取文本生成模型列表失败:`, error);

      const errorResponse: ApiResponse<ErrorResponse> = {
        success: false,
        error: {
          code: 'MODELS_ERROR',
          message: '获取模型列表失败',
          details: error instanceof Error ? error.message : '未知错误'
        },
        requestId,
        timestamp: Date.now()
      };

      res.status(500).json(errorResponse);
    }
  };

  /**
   * 获取模型支持的任务类型
   *
   * @param modelId 模型ID
   * @returns 支持的任务类型列表
   * @private
   */
  private getSupportedTasksForModel(modelId: string): TextGenerationTaskType[] {
    // 根据模型特性返回支持的任务类型
    const modelTaskMap: Record<string, TextGenerationTaskType[]> = {
      'deepseek': ['code', 'document', 'qa', 'general'],
      'deepseek-v3': ['code', 'document', 'qa', 'general'],
      'doubao-1_6-thinking': ['creative', 'document', 'summary', 'qa', 'general'],
      'doubao': ['creative', 'document', 'summary', 'general'],
      'glm-4-5': ['creative', 'document', 'translation', 'rewrite', 'general'],
      'glm-4-plus': ['creative', 'document', 'translation', 'rewrite', 'general'],
      'qwen-plus': ['document', 'summary', 'qa', 'general'],
      'qwen3-235b-a22b': ['document', 'summary', 'qa', 'general'],
      'kimi-k2-0711-preview': ['code', 'document', 'qa', 'general'],
      'moonshot-v1-32k': ['document', 'summary', 'qa', 'general']
    };

    return modelTaskMap[modelId] || ['general'];
  }

  /**
   * 获取模型的最大输入长度
   *
   * @param modelId 模型ID
   * @returns 最大输入长度
   * @private
   */
  private getMaxInputLengthForModel(modelId: string): number {
    const modelLengthMap: Record<string, number> = {
      'moonshot-v1-32k': 32000,
      'qwen-long': 100000,
      'kimi-k2-0711-preview': 32000,
      'MiniMax-M1': 100000
    };

    return modelLengthMap[modelId] || 8000;
  }

  /**
   * 获取模型的最大输出长度
   *
   * @param modelId 模型ID
   * @returns 最大输出长度
   * @private
   */
  private getMaxOutputLengthForModel(_modelId: string): number {
    // 大部分模型的输出长度限制
    return 4000;
  }

  /**
   * 构建任务推荐模型映射
   *
   * @param models 模型列表
   * @returns 任务推荐映射
   * @private
   */
  private buildTaskRecommendations(_models: any[]): Record<TextGenerationTaskType, string[]> {
    return {
      creative: ['doubao-1_6-thinking', 'glm-4-5', 'doubao'],
      code: ['deepseek', 'deepseek-v3', 'kimi-k2-0711-preview'],
      document: ['deepseek', 'doubao-1_6-thinking', 'glm-4-5'],
      summary: ['doubao-1_6-thinking', 'qwen-plus', 'moonshot-v1-32k'],
      translation: ['glm-4-5', 'glm-4-plus'],
      rewrite: ['glm-4-5', 'doubao-1_6-thinking'],
      qa: ['deepseek', 'qwen-plus', 'moonshot-v1-32k'],
      general: ['deepseek', 'doubao-1_6-thinking', 'glm-4-5']
    };
  }
}
