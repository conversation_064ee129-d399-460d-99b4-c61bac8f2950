<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当贝 Provider API 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .api-section {
            margin-bottom: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .api-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .api-header:hover {
            background: #e9ecef;
        }
        
        .api-header h3 {
            color: #333;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .method-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .method-get {
            background: #28a745;
            color: white;
        }
        
        .method-post {
            background: #007bff;
            color: white;
        }
        
        .api-body {
            padding: 20px;
            display: none;
        }
        
        .api-body.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .response-area {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        
        .response-area h4 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .response-content {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.active {
            display: block;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .toggle-icon {
            transition: transform 0.3s;
        }
        
        .toggle-icon.rotated {
            transform: rotate(180deg);
        }
        
        .config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .config-section h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 当贝 Provider API 测试</h1>
            <p>测试和调试 HTTP API 接口的交互式工具</p>
        </div>
        
        <div class="content">
            <!-- 全局配置区域 -->
            <div class="config-section">
                <h3>🔧 全局配置</h3>
                <div class="config-grid">
                    <div class="form-group">
                        <label for="baseUrl">API 基础地址:</label>
                        <input type="text" id="baseUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
                    </div>
                    <div class="form-group">
                        <label for="apiKey">API 密钥:</label>
                        <input type="text" id="apiKey" placeholder="your-api-key-here">
                    </div>
                </div>
            </div>
            
            <!-- 模型列表接口 -->
            <div class="api-section">
                <div class="api-header" onclick="toggleSection('models')">
                    <h3>
                        📋 获取模型列表
                        <span class="method-badge method-get">GET</span>
                        <span class="toggle-icon" id="models-icon">▼</span>
                    </h3>
                    <p>获取所有可用的AI模型列表</p>
                </div>
                <div class="api-body" id="models-body">
                    <div class="form-group">
                        <label>请求地址:</label>
                        <input type="text" id="models-url" value="/api/models" readonly>
                    </div>
                    
                    <button class="btn" onclick="testModels()">🚀 发送请求</button>
                    <button class="btn btn-secondary" onclick="clearResponse('models')">🗑️ 清空响应</button>
                    
                    <div class="loading" id="models-loading">
                        <div class="spinner"></div>
                        <p>正在请求模型列表...</p>
                    </div>
                    
                    <div class="response-area" id="models-response" style="display: none;">
                        <h4>📤 响应结果:</h4>
                        <div class="response-content" id="models-content"></div>
                    </div>
                </div>
            </div>
            
            <!-- 聊天接口 -->
            <div class="api-section">
                <div class="api-header" onclick="toggleSection('chat')">
                    <h3>
                        💬 聊天对话
                        <span class="method-badge method-post">POST</span>
                        <span class="toggle-icon" id="chat-icon">▼</span>
                    </h3>
                    <p>发送聊天消息并获取AI回复</p>
                </div>
                <div class="api-body" id="chat-body">
                    <div class="form-group">
                        <label>请求地址:</label>
                        <input type="text" id="chat-url" value="/api/chat" readonly>
                    </div>

                    <div class="form-group">
                        <label for="chat-model">选择模型:</label>
                        <select id="chat-model">
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="claude-3">Claude-3</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="chat-message">用户消息:</label>
                        <textarea id="chat-message" placeholder="请输入您的问题...">你好，请介绍一下你自己。</textarea>
                    </div>

                    <div class="form-group">
                        <label for="chat-system">系统提示词 (可选):</label>
                        <textarea id="chat-system" placeholder="系统角色设定...">你是一个有用的AI助手，请用中文回答问题。</textarea>
                    </div>

                    <div class="config-grid">
                        <div class="form-group">
                            <label for="chat-temperature">温度参数 (0-1):</label>
                            <input type="number" id="chat-temperature" value="0.7" min="0" max="1" step="0.1">
                        </div>
                        <div class="form-group">
                            <label for="chat-max-tokens">最大令牌数:</label>
                            <input type="number" id="chat-max-tokens" value="2000" min="1" max="4000">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="chat-stream"> 启用流式响应
                        </label>
                        <label>
                            <input type="checkbox" id="chat-deep-thinking" checked> 启用深度思考
                        </label>
                        <label>
                            <input type="checkbox" id="chat-online-search"> 启用联网搜索
                        </label>
                    </div>

                    <button class="btn" onclick="testChat()">💬 发送聊天</button>
                    <button class="btn btn-secondary" onclick="clearResponse('chat')">🗑️ 清空响应</button>

                    <div class="loading" id="chat-loading">
                        <div class="spinner"></div>
                        <p>AI正在思考中...</p>
                    </div>

                    <div class="response-area" id="chat-response" style="display: none;">
                        <h4>🤖 AI回复:</h4>
                        <div class="response-content" id="chat-content"></div>
                    </div>
                </div>
            </div>

            <!-- 文本生成接口 -->
            <div class="api-section">
                <div class="api-header" onclick="toggleSection('generate')">
                    <h3>
                        ✍️ 文本生成
                        <span class="method-badge method-post">POST</span>
                        <span class="toggle-icon" id="generate-icon">▼</span>
                    </h3>
                    <p>基于提示词生成各种类型的文本内容</p>
                </div>
                <div class="api-body" id="generate-body">
                    <div class="form-group">
                        <label>请求地址:</label>
                        <input type="text" id="generate-url" value="/api/generate" readonly>
                    </div>

                    <div class="form-group">
                        <label for="generate-prompt">生成提示词:</label>
                        <textarea id="generate-prompt" placeholder="请输入生成提示词...">请为一家科技公司写一份产品介绍，产品是智能家居控制系统。</textarea>
                    </div>

                    <div class="config-grid">
                        <div class="form-group">
                            <label for="generate-model">选择模型:</label>
                            <select id="generate-model">
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                <option value="claude-3">Claude-3</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="generate-task-type">任务类型:</label>
                            <select id="generate-task-type">
                                <option value="general">通用生成</option>
                                <option value="creative">创意写作</option>
                                <option value="code">代码生成</option>
                                <option value="document" selected>文档生成</option>
                                <option value="summary">摘要生成</option>
                                <option value="translation">翻译</option>
                                <option value="rewrite">改写</option>
                                <option value="qa">问答</option>
                            </select>
                        </div>
                    </div>

                    <div class="config-grid">
                        <div class="form-group">
                            <label for="generate-temperature">温度参数 (0-1):</label>
                            <input type="number" id="generate-temperature" value="0.6" min="0" max="1" step="0.1">
                        </div>
                        <div class="form-group">
                            <label for="generate-max-tokens">最大令牌数:</label>
                            <input type="number" id="generate-max-tokens" value="1500" min="1" max="4000">
                        </div>
                    </div>

                    <div class="config-grid">
                        <div class="form-group">
                            <label for="generate-style">写作风格:</label>
                            <input type="text" id="generate-style" value="专业商务" placeholder="如：专业商务、轻松幽默、学术严谨">
                        </div>
                        <div class="form-group">
                            <label for="generate-format">输出格式:</label>
                            <select id="generate-format">
                                <option value="plain">纯文本</option>
                                <option value="markdown" selected>Markdown</option>
                                <option value="html">HTML</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="generate-language">输出语言:</label>
                        <select id="generate-language">
                            <option value="中文" selected>中文</option>
                            <option value="English">English</option>
                            <option value="日本語">日本語</option>
                            <option value="한국어">한국어</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="generate-stream"> 启用流式响应
                        </label>
                        <label>
                            <input type="checkbox" id="generate-deep-thinking" checked> 启用深度思考
                        </label>
                        <label>
                            <input type="checkbox" id="generate-online-search"> 启用联网搜索
                        </label>
                    </div>

                    <button class="btn" onclick="testGenerate()">✍️ 开始生成</button>
                    <button class="btn btn-secondary" onclick="clearResponse('generate')">🗑️ 清空响应</button>

                    <div class="loading" id="generate-loading">
                        <div class="spinner"></div>
                        <p>正在生成内容...</p>
                    </div>

                    <div class="response-area" id="generate-response" style="display: none;">
                        <h4>📝 生成结果:</h4>
                        <div class="response-content" id="generate-content"></div>
                    </div>
                </div>
            </div>

            <!-- OpenAI兼容接口 -->
            <div class="api-section">
                <div class="api-header" onclick="toggleSection('openai')">
                    <h3>
                        🔗 OpenAI 兼容接口
                        <span class="method-badge method-get">GET</span>
                        <span class="toggle-icon" id="openai-icon">▼</span>
                    </h3>
                    <p>OpenAI API 格式的模型列表接口</p>
                </div>
                <div class="api-body" id="openai-body">
                    <div class="form-group">
                        <label>请求地址:</label>
                        <input type="text" id="openai-url" value="/v1/models" readonly>
                    </div>

                    <button class="btn" onclick="testOpenAI()">🔗 获取模型列表</button>
                    <button class="btn btn-secondary" onclick="clearResponse('openai')">🗑️ 清空响应</button>

                    <div class="loading" id="openai-loading">
                        <div class="spinner"></div>
                        <p>正在获取OpenAI格式模型列表...</p>
                    </div>

                    <div class="response-area" id="openai-response" style="display: none;">
                        <h4>📋 OpenAI格式响应:</h4>
                        <div class="response-content" id="openai-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换API区域显示/隐藏
        function toggleSection(sectionId) {
            const body = document.getElementById(sectionId + '-body');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (body.classList.contains('active')) {
                body.classList.remove('active');
                icon.classList.remove('rotated');
            } else {
                body.classList.add('active');
                icon.classList.add('rotated');
            }
        }
        
        // 获取基础配置
        function getBaseConfig() {
            return {
                baseUrl: document.getElementById('baseUrl').value.trim(),
                apiKey: document.getElementById('apiKey').value.trim()
            };
        }
        
        // 显示加载状态
        function showLoading(sectionId) {
            document.getElementById(sectionId + '-loading').classList.add('active');
            document.getElementById(sectionId + '-response').style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading(sectionId) {
            document.getElementById(sectionId + '-loading').classList.remove('active');
        }
        
        // 显示响应结果
        function showResponse(sectionId, content, isError = false) {
            hideLoading(sectionId);
            const responseArea = document.getElementById(sectionId + '-response');
            const responseContent = document.getElementById(sectionId + '-content');
            
            responseContent.textContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
            responseContent.className = 'response-content ' + (isError ? 'error' : 'success');
            responseArea.style.display = 'block';
        }
        
        // 清空响应
        function clearResponse(sectionId) {
            document.getElementById(sectionId + '-response').style.display = 'none';
            hideLoading(sectionId);
        }
        
        // 测试模型列表接口
        async function testModels() {
            const config = getBaseConfig();
            if (!config.baseUrl) {
                alert('请先设置API基础地址');
                return;
            }
            
            showLoading('models');
            
            try {
                const response = await fetch(config.baseUrl + '/api/models', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });
                
                const data = await response.json();
                showResponse('models', data, !response.ok);
            } catch (error) {
                showResponse('models', `请求失败: ${error.message}`, true);
            }
        }
        
        // 测试聊天接口
        async function testChat() {
            const config = getBaseConfig();
            if (!config.baseUrl) {
                alert('请先设置API基础地址');
                return;
            }

            const message = document.getElementById('chat-message').value.trim();
            if (!message) {
                alert('请输入聊天消息');
                return;
            }

            showLoading('chat');

            // 构建请求体
            const messages = [];
            const systemMessage = document.getElementById('chat-system').value.trim();
            if (systemMessage) {
                messages.push({
                    role: 'system',
                    content: systemMessage,
                    id: 'msg-system-' + Date.now(),
                    timestamp: Date.now()
                });
            }

            messages.push({
                role: 'user',
                content: message,
                id: 'msg-user-' + Date.now(),
                timestamp: Date.now()
            });

            const requestBody = {
                messages: messages,
                model: document.getElementById('chat-model').value,
                stream: document.getElementById('chat-stream').checked,
                max_tokens: parseInt(document.getElementById('chat-max-tokens').value),
                temperature: parseFloat(document.getElementById('chat-temperature').value),
                options: {
                    deep_thinking: document.getElementById('chat-deep-thinking').checked,
                    online_search: document.getElementById('chat-online-search').checked
                }
            };

            try {
                const response = await fetch(config.baseUrl + '/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                showResponse('chat', data, !response.ok);
            } catch (error) {
                showResponse('chat', `请求失败: ${error.message}`, true);
            }
        }

        // 测试文本生成接口
        async function testGenerate() {
            const config = getBaseConfig();
            if (!config.baseUrl) {
                alert('请先设置API基础地址');
                return;
            }

            const prompt = document.getElementById('generate-prompt').value.trim();
            if (!prompt) {
                alert('请输入生成提示词');
                return;
            }

            showLoading('generate');

            // 构建请求体
            const requestBody = {
                prompt: prompt,
                model: document.getElementById('generate-model').value,
                stream: document.getElementById('generate-stream').checked,
                max_tokens: parseInt(document.getElementById('generate-max-tokens').value),
                temperature: parseFloat(document.getElementById('generate-temperature').value),
                task_type: document.getElementById('generate-task-type').value,
                options: {
                    style: document.getElementById('generate-style').value.trim(),
                    format: document.getElementById('generate-format').value,
                    language: document.getElementById('generate-language').value,
                    deep_thinking: document.getElementById('generate-deep-thinking').checked,
                    online_search: document.getElementById('generate-online-search').checked
                }
            };

            try {
                const response = await fetch(config.baseUrl + '/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                showResponse('generate', data, !response.ok);
            } catch (error) {
                showResponse('generate', `请求失败: ${error.message}`, true);
            }
        }

        // 测试OpenAI兼容接口
        async function testOpenAI() {
            const config = getBaseConfig();
            if (!config.baseUrl) {
                alert('请先设置API基础地址');
                return;
            }

            showLoading('openai');

            try {
                const response = await fetch(config.baseUrl + '/v1/models', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });

                const data = await response.json();
                showResponse('openai', data, !response.ok);
            } catch (error) {
                showResponse('openai', `请求失败: ${error.message}`, true);
            }
        }
        
        // 页面加载完成后默认展开第一个区域
        document.addEventListener('DOMContentLoaded', function() {
            toggleSection('models');
        });
    </script>
</body>
</html>
