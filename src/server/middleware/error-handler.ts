/**
 * 错误处理中间件
 * 统一处理应用程序中的错误
 */

import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types/api';

/**
 * 自定义错误类
 */
export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

/**
 * 404 错误处理中间件
 * 处理未找到的路由
 * 
 * @param req Express 请求对象
 * @param res Express 响应对象
 * @param next Express next 函数
 */
export function notFoundHandler(
  req: Request,
  _res: Response,
  next: NextFunction
): void {
  const error = new ApiError(
    `路由 ${req.method} ${req.path} 不存在`,
    404,
    'ROUTE_NOT_FOUND'
  );
  
  next(error);
}

/**
 * 全局错误处理中间件
 * 统一处理所有错误并返回标准格式的错误响应
 * 
 * @param error 错误对象
 * @param req Express 请求对象
 * @param res Express 响应对象
 * @param next Express next 函数
 */
export function errorHandler(
  error: Error | ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 如果响应已经发送，则交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  // 生成请求ID（如果没有的话）
  const requestId = res.get('X-Request-ID') || 
    `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // 确定错误状态码和代码
  let statusCode = 500;
  let errorCode = 'INTERNAL_ERROR';
  let errorMessage = '内部服务器错误';
  let errorDetails: any = undefined;

  if (error instanceof ApiError) {
    statusCode = error.statusCode;
    errorCode = error.code;
    errorMessage = error.message;
    errorDetails = error.details;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    errorCode = 'VALIDATION_ERROR';
    errorMessage = '请求参数验证失败';
    errorDetails = error.message;
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    statusCode = 400;
    errorCode = 'INVALID_JSON';
    errorMessage = '请求体JSON格式无效';
  } else if (error.message) {
    errorMessage = error.message;
  }

  // 记录错误日志
  console.error(`[${requestId}] 错误处理:`, {
    method: req.method,
    url: req.url,
    statusCode,
    errorCode,
    errorMessage,
    stack: error.stack,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });

  // 构建错误响应
  const errorResponse: ApiResponse = {
    success: false,
    error: {
      code: errorCode,
      message: errorMessage,
      ...(errorDetails && { details: errorDetails })
    },
    requestId,
    timestamp: Date.now()
  };

  // 在开发环境下包含错误堆栈
  if (process.env['NODE_ENV'] === 'development' && error.stack) {
    (errorResponse.error as any).stack = error.stack;
  }

  // 设置响应头
  res.set({
    'Content-Type': 'application/json',
    'X-Request-ID': requestId
  });

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理器，自动捕获异步错误
 * 
 * @param fn 异步处理函数
 * @returns 包装后的处理函数
 */
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 请求超时处理中间件
 * 
 * @param timeoutMs 超时时间（毫秒）
 * @returns 中间件函数
 */
export function timeoutHandler(timeoutMs: number = 30000) {
  return (_req: Request, res: Response, next: NextFunction) => {
    // 设置请求超时
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        const error = new ApiError(
          '请求超时',
          408,
          'REQUEST_TIMEOUT'
        );
        next(error);
      }
    }, timeoutMs);

    // 清理超时定时器
    res.on('finish', () => {
      clearTimeout(timeout);
    });

    res.on('close', () => {
      clearTimeout(timeout);
    });

    next();
  };
}
