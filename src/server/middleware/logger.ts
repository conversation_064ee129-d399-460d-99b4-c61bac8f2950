/**
 * 日志记录中间件
 * 记录HTTP请求和响应信息
 */

import { Request, Response, NextFunction } from 'express';

/**
 * 请求日志记录中间件
 * 记录每个HTTP请求的详细信息
 * 
 * @param req Express 请求对象
 * @param res Express 响应对象
 * @param next Express next 函数
 */
export function requestLogger(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const startTime = Date.now();
  const requestId = `req_${startTime}_${Math.random().toString(36).substr(2, 9)}`;
  
  // 设置请求ID到响应头
  res.set('X-Request-ID', requestId);
  
  // 记录请求开始
  console.log(`[${requestId}] --> ${req.method} ${req.url}`, {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    ip: req.ip,
    timestamp: new Date().toISOString()
  });

  // 监听响应完成
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const statusCode = res.statusCode;
    
    // 根据状态码确定状态表情
    const statusEmoji = statusCode >= 500 ? '❌' :
                       statusCode >= 400 ? '⚠️' : '✅';
    
    console.log(`[${requestId}] <-- ${statusEmoji} ${statusCode} ${req.method} ${req.url} (${duration}ms)`, {
      method: req.method,
      url: req.url,
      statusCode,
      duration,
      contentLength: res.get('Content-Length'),
      timestamp: new Date().toISOString()
    });
  });

  // 监听响应错误
  res.on('error', (error) => {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] <-- ❌ ERROR ${req.method} ${req.url} (${duration}ms)`, {
      method: req.method,
      url: req.url,
      error: error.message,
      duration,
      timestamp: new Date().toISOString()
    });
  });

  next();
}

/**
 * 性能监控中间件
 * 监控API响应时间并记录慢请求
 * 
 * @param slowThreshold 慢请求阈值（毫秒），默认1000ms
 * @returns 中间件函数
 */
export function performanceMonitor(slowThreshold: number = 1000) {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = process.hrtime.bigint();
    
    res.on('finish', () => {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
      
      if (duration > slowThreshold) {
        const requestId = res.get('X-Request-ID') || 'unknown';
        console.warn(`[${requestId}] 🐌 慢请求警告: ${req.method} ${req.url} 耗时 ${duration.toFixed(2)}ms`, {
          method: req.method,
          url: req.url,
          duration: Math.round(duration),
          threshold: slowThreshold,
          statusCode: res.statusCode,
          timestamp: new Date().toISOString()
        });
      }
    });
    
    next();
  };
}

/**
 * 请求体大小监控中间件
 * 监控请求体大小并记录大请求
 * 
 * @param sizeThreshold 大请求阈值（字节），默认1MB
 * @returns 中间件函数
 */
export function requestSizeMonitor(sizeThreshold: number = 1024 * 1024) {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.get('Content-Length') || '0', 10);
    
    if (contentLength > sizeThreshold) {
      const requestId = res.get('X-Request-ID') || 'unknown';
      console.warn(`[${requestId}] 📦 大请求警告: ${req.method} ${req.url} 请求体大小 ${(contentLength / 1024 / 1024).toFixed(2)}MB`, {
        method: req.method,
        url: req.url,
        contentLength,
        threshold: sizeThreshold,
        timestamp: new Date().toISOString()
      });
    }
    
    next();
  };
}

/**
 * API 使用统计中间件
 * 统计API端点的使用情况
 */
export class ApiUsageStats {
  private stats: Map<string, {
    count: number;
    totalDuration: number;
    errors: number;
    lastAccess: Date;
  }> = new Map();

  /**
   * 获取统计中间件
   * 
   * @returns 中间件函数
   */
  public getMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const endpoint = `${req.method} ${req.route?.path || req.path}`;
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        const isError = res.statusCode >= 400;
        
        // 更新统计信息
        const current = this.stats.get(endpoint) || {
          count: 0,
          totalDuration: 0,
          errors: 0,
          lastAccess: new Date()
        };
        
        current.count++;
        current.totalDuration += duration;
        current.lastAccess = new Date();
        
        if (isError) {
          current.errors++;
        }
        
        this.stats.set(endpoint, current);
      });
      
      next();
    };
  }

  /**
   * 获取统计信息
   * 
   * @returns 统计数据
   */
  public getStats() {
    const result: any = {};
    
    for (const [endpoint, stats] of this.stats.entries()) {
      result[endpoint] = {
        ...stats,
        averageDuration: stats.count > 0 ? Math.round(stats.totalDuration / stats.count) : 0,
        errorRate: stats.count > 0 ? Math.round((stats.errors / stats.count) * 100) : 0
      };
    }
    
    return result;
  }

  /**
   * 重置统计信息
   */
  public resetStats() {
    this.stats.clear();
  }
}

// 创建全局统计实例
export const apiUsageStats = new ApiUsageStats();
