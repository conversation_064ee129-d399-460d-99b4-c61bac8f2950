/**
 * 文本生成相关路由
 * 定义文本生成相关的 API 端点
 */

import { Router } from 'express';
import { TextGenerationController } from '../controllers/text-generation-controller';

/**
 * 创建文本生成路由
 * 
 * @returns Express 路由实例
 */
export function createTextGenerationRouter(): Router {
  const router = Router();
  const textGenerationController = new TextGenerationController();

  /**
   * POST /api/text/generate
   * 文本生成接口
   * 支持流式和非流式响应
   * 
   * 请求格式：
   * {
   *   "prompt": "请写一首关于春天的诗",
   *   "model": "deepseek",
   *   "stream": false,
   *   "task_type": "creative",
   *   "max_tokens": 1000,
   *   "temperature": 0.7,
   *   "options": {
   *     "style": "现代诗",
   *     "format": "markdown",
   *     "language": "zh",
   *     "deep_thinking": true,
   *     "online_search": false
   *   }
   * }
   * 
   * 响应格式（非流式）：
   * {
   *   "success": true,
   *   "data": {
   *     "text": "生成的文本内容...",
   *     "model": "deepseek",
   *     "task_type": "creative",
   *     "finish_reason": "stop",
   *     "generation_id": "gen_123456",
   *     "request_id": "req_123456",
   *     "usage": {
   *       "prompt_tokens": 20,
   *       "completion_tokens": 150,
   *       "total_tokens": 170
   *     },
   *     "metadata": {
   *       "generation_time": 2500,
   *       "parameters": {
   *         "temperature": 0.7,
   *         "max_tokens": 1000
   *       }
   *     }
   *   },
   *   "requestId": "gen_1234567890_abc123",
   *   "timestamp": 1640995200000
   * }
   * 
   * 响应格式（流式）：
   * Content-Type: text/event-stream
   * 
   * data: {"id":"gen_123","object":"text.generation.chunk","created":1640995200,"model":"deepseek","choices":[{"index":0,"delta":{"text":"春"},"finish_reason":null}],"task_type":"creative"}
   * 
   * data: {"id":"gen_123","object":"text.generation.chunk","created":1640995200,"model":"deepseek","choices":[{"index":0,"delta":{"text":"风"},"finish_reason":null}],"task_type":"creative"}
   * 
   * data: {"id":"gen_123","object":"text.generation.chunk","created":1640995200,"model":"deepseek","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"task_type":"creative"}
   * 
   * data: [DONE]
   */
  router.post('/generate', textGenerationController.generate);

  /**
   * GET /api/text/models
   * 获取支持文本生成的模型列表
   * 
   * 响应格式：
   * {
   *   "success": true,
   *   "data": {
   *     "defaultModel": "deepseek",
   *     "models": [
   *       {
   *         "id": "deepseek",
   *         "name": "DeepSeek-R1最新版",
   *         "description": "专注逻辑推理与深度分析",
   *         "options": [...],
   *         "recommended": true,
   *         "pinned": true,
   *         "supported_tasks": ["code", "document", "qa", "general"],
   *         "max_input_length": 8000,
   *         "max_output_length": 4000,
   *         "supports_streaming": true
   *       }
   *     ],
   *     "total": 15,
   *     "recommendations": {
   *       "creative": ["doubao-1_6-thinking", "glm-4-5"],
   *       "code": ["deepseek", "deepseek-v3"],
   *       "document": ["deepseek", "doubao-1_6-thinking"],
   *       "summary": ["doubao-1_6-thinking", "qwen-plus"],
   *       "translation": ["glm-4-5", "glm-4-plus"],
   *       "rewrite": ["glm-4-5", "doubao-1_6-thinking"],
   *       "qa": ["deepseek", "qwen-plus"],
   *       "general": ["deepseek", "doubao-1_6-thinking"]
   *     }
   *   },
   *   "requestId": "models_1234567890_abc123",
   *   "timestamp": 1640995200000
   * }
   */
  router.get('/models', textGenerationController.getModels);

  return router;
}
