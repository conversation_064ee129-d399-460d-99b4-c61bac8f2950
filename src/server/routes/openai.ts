/**
 * OpenAI兼容路由
 * 提供与OpenAI API兼容的端点
 */

import { Router } from 'express';
import { ModelsController } from '../controllers/models-controller';
import { ChatController } from '../controllers/chat-controller';

/**
 * 创建OpenAI兼容路由
 * 实现 /v1/* 端点，完全兼容OpenAI API格式
 * 
 * @returns Express 路由实例
 */
export function createOpenAIRouter(): Router {
  const router = Router();
  const modelsController = new ModelsController();
  const chatController = new ChatController();

  // ==================== 模型相关端点 ====================

  /**
   * GET /v1/models
   * 获取所有可用模型列表（OpenAI兼容格式）
   * 
   * 响应格式：
   * {
   *   "object": "list",
   *   "data": [
   *     {
   *       "id": "deepseek",
   *       "object": "model",
   *       "created": 1640995200,
   *       "owned_by": "dangbei",
   *       ...
   *     }
   *   ]
   * }
   */
  router.get('/models', modelsController.getModelsOpenAI);

  /**
   * GET /v1/models/{model_id}
   * 获取特定模型信息（OpenAI兼容格式）
   */
  router.get('/models/:modelId', modelsController.getModelByIdOpenAI);

  // ==================== 聊天相关端点 ====================

  /**
   * POST /v1/chat/completions
   * 聊天补全接口（OpenAI兼容格式）
   * 
   * 请求格式：
   * {
   *   "model": "deepseek",
   *   "messages": [
   *     {"role": "user", "content": "Hello!"}
   *   ],
   *   "stream": false
   * }
   */
  router.post('/chat/completions', chatController.chat);

  // ==================== 其他兼容端点 ====================

  /**
   * GET /v1/models/permissions/{model_id}
   * 获取模型权限信息（OpenAI兼容，返回空权限）
   */
  router.get('/models/permissions/:modelId', (_req, res) => {
    res.json({
      object: "model_permission",
      id: _req.params.modelId,
      created: Math.floor(Date.now() / 1000),
      allow_create_engine: false,
      allow_sampling: true,
      allow_logprobs: false,
      allow_search_indices: false,
      allow_view: true,
      allow_fine_tuning: false,
      organization: "*",
      group: null,
      is_blocking: false
    });
  });

  /**
   * GET /v1/engines
   * 引擎列表接口（OpenAI兼容，重定向到模型列表）
   */
  router.get('/engines', (_req, res) => {
    res.redirect('/v1/models');
  });

  /**
   * GET /v1/engines/{engine_id}
   * 特定引擎信息（OpenAI兼容，重定向到模型信息）
   */
  router.get('/engines/:engineId', (req, res) => {
    res.redirect(`/v1/models/${req.params.engineId}`);
  });

  // ==================== API信息端点 ====================

  /**
   * GET /v1
   * API根端点，返回API信息
   */
  router.get('/', (_req, res) => {
    res.json({
      message: "当贝AI Provider - OpenAI兼容API",
      version: "v1",
      documentation: "/api/info",
      endpoints: {
        models: "GET /v1/models",
        chat: "POST /v1/chat/completions",
        model_info: "GET /v1/models/{model_id}"
      },
      compatibility: "OpenAI API v1",
      timestamp: Date.now()
    });
  });

  return router;
}
