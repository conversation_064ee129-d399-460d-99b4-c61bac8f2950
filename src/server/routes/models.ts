/**
 * 模型相关路由
 * 定义模型列表相关的 API 端点
 */

import { Router } from 'express';
import { ModelsController } from '../controllers/models-controller';

/**
 * 创建模型路由
 * 
 * @returns Express 路由实例
 */
export function createModelsRouter(): Router {
  const router = Router();
  const modelsController = new ModelsController();

  /**
   * GET /api/models
   * 获取所有支持的模型列表
   */
  router.get('/', modelsController.getModels);

  /**
   * GET /api/models/recommended
   * 获取推荐模型列表
   */
  router.get('/recommended', modelsController.getRecommendedModels);

  /**
   * POST /api/models/reload
   * 重新加载模型数据
   */
  router.post('/reload', modelsController.reloadModels);

  /**
   * GET /api/models/:modelId
   * 根据ID获取特定模型信息
   */
  router.get('/:modelId', modelsController.getModelById);

  return router;
}
