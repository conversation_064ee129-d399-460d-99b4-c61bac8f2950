/**
 * 聊天相关路由
 * 定义聊天对话相关的 API 端点
 */

import { Router } from 'express';
import { ChatController } from '../controllers/chat-controller';

/**
 * 创建聊天路由
 * 
 * @returns Express 路由实例
 */
export function createChatRouter(): Router {
  const router = Router();
  const chatController = new ChatController();

  /**
   * POST /api/chat
   * 处理聊天对话请求
   * 支持流式和非流式响应
   */
  router.post('/', chatController.chat);

  return router;
}
