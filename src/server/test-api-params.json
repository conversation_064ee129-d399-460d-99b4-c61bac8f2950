{"description": "HTTP API 测试页面请求参数示例", "version": "1.0.0", "endpoints": {"models": {"description": "获取模型列表", "method": "GET", "url": "/api/models", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-api-key-here"}, "queryParams": {}, "expectedResponse": {"success": true, "data": {"defaultModel": "gpt-4", "models": [{"id": "gpt-4", "name": "GPT-4", "description": "最新的GPT-4模型，具有强大的推理能力", "options": [{"name": "temperature", "value": "0.7", "enabled": true, "selected": true}], "recommended": true, "pinned": true, "icon": "https://example.com/gpt4-icon.png", "banner": "https://example.com/gpt4-banner.png", "badge": "推荐"}], "total": 1}, "timestamp": 1703123456789}}, "chat": {"description": "聊天对话接口", "method": "POST", "url": "/api/chat", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-api-key-here"}, "requestBody": {"messages": [{"role": "system", "content": "你是一个有用的AI助手，请用中文回答问题。", "id": "msg-system-001", "timestamp": 1703123456789}, {"role": "user", "content": "你好，请介绍一下你自己。", "id": "msg-user-001", "timestamp": 1703123456790}], "model": "gpt-4", "stream": false, "conversation_id": "conv-12345", "max_tokens": 2000, "temperature": 0.7, "options": {"deep_thinking": true, "online_search": false}}, "expectedResponse": {"success": true, "data": {"message": {"role": "assistant", "content": "你好！我是一个AI助手，很高兴为您服务。我可以帮助您回答问题、提供信息、协助解决问题等。有什么我可以帮助您的吗？", "id": "msg-assistant-001", "timestamp": 1703123456791}, "conversation_id": "conv-12345", "message_id": "msg-assistant-001", "parent_message_id": "msg-user-001", "request_id": "req-67890", "model": "gpt-4", "finish_reason": "stop", "usage": {"prompt_tokens": 45, "completion_tokens": 32, "total_tokens": 77}}, "timestamp": 1703123456791}}, "chatStream": {"description": "流式聊天对话接口", "method": "POST", "url": "/api/chat", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-api-key-here", "Accept": "text/event-stream"}, "requestBody": {"messages": [{"role": "user", "content": "请写一首关于春天的诗。", "id": "msg-user-002", "timestamp": 1703123456792}], "model": "gpt-4", "stream": true, "max_tokens": 1000, "temperature": 0.8, "options": {"deep_thinking": false, "online_search": false}}, "expectedStreamResponse": [{"id": "chunk-001", "object": "chat.completion.chunk", "created": 1703123456793, "model": "gpt-4", "choices": [{"index": 0, "delta": {"role": "assistant"}, "finish_reason": null}]}, {"id": "chunk-002", "object": "chat.completion.chunk", "created": 1703123456794, "model": "gpt-4", "choices": [{"index": 0, "delta": {"content": "春风轻拂"}, "finish_reason": null}]}, {"id": "chunk-003", "object": "chat.completion.chunk", "created": 1703123456795, "model": "gpt-4", "choices": [{"index": 0, "delta": {"content": "柳絮飞，\n"}, "finish_reason": "stop"}]}]}, "textGeneration": {"description": "文本生成接口", "method": "POST", "url": "/api/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-api-key-here"}, "requestBody": {"prompt": "请为一家科技公司写一份产品介绍，产品是智能家居控制系统。", "model": "gpt-4", "stream": false, "max_tokens": 1500, "temperature": 0.6, "task_type": "document", "options": {"style": "专业商务", "format": "markdown", "language": "中文", "deep_thinking": true, "online_search": false}}, "expectedResponse": {"success": true, "data": {"text": "# 智能家居控制系统\n\n我们的智能家居控制系统是一款革命性的产品...", "model": "gpt-4", "task_type": "document", "finish_reason": "stop", "generation_id": "gen-12345", "request_id": "req-67891", "usage": {"prompt_tokens": 28, "completion_tokens": 456, "total_tokens": 484}, "metadata": {"generation_time": 3500, "parameters": {"temperature": 0.6, "max_tokens": 1500}}}, "timestamp": 1703123456796}}, "textGenerationStream": {"description": "流式文本生成接口", "method": "POST", "url": "/api/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-api-key-here", "Accept": "text/event-stream"}, "requestBody": {"prompt": "写一个关于人工智能发展历程的简短总结。", "model": "gpt-4", "stream": true, "max_tokens": 800, "temperature": 0.5, "task_type": "summary", "options": {"style": "学术", "format": "plain", "language": "中文", "deep_thinking": false, "online_search": true}}, "expectedStreamResponse": [{"id": "gen-chunk-001", "object": "text.generation.chunk", "created": 1703123456797, "model": "gpt-4", "choices": [{"index": 0, "delta": {"text": "人工智能的发展"}, "finish_reason": null}], "task_type": "summary"}, {"id": "gen-chunk-002", "object": "text.generation.chunk", "created": 1703123456798, "model": "gpt-4", "choices": [{"index": 0, "delta": {"text": "可以追溯到20世纪50年代..."}, "finish_reason": "stop"}], "task_type": "summary"}]}, "openaiModels": {"description": "OpenAI兼容的模型列表接口", "method": "GET", "url": "/v1/models", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-api-key-here"}, "queryParams": {}, "expectedResponse": {"object": "list", "data": [{"id": "gpt-4", "object": "model", "created": 1703123456799, "owned_by": "dangbei-provider", "permission": [], "root": "gpt-4", "parent": null, "display_name": "GPT-4", "description": "最新的GPT-4模型，具有强大的推理能力", "context_length": 8192, "capabilities": {"chat": true, "completion": true, "embeddings": false, "fine_tuning": false}, "pricing": {"input": 0.03, "output": 0.06}, "metadata": {"recommended": true, "pinned": true, "icon": "https://example.com/gpt4-icon.png", "banner": "https://example.com/gpt4-banner.png", "badge": "推荐", "options": [{"name": "temperature", "display_name": "创造性", "enabled": true, "default": true}, {"name": "max_tokens", "display_name": "最大令牌数", "enabled": true, "default": false}]}}]}}}, "errorExamples": {"invalidRequest": {"success": false, "error": {"code": "INVALID_REQUEST", "message": "请求参数无效", "details": {"field": "messages", "reason": "消息列表不能为空"}}, "timestamp": 1703123456800}, "authenticationError": {"success": false, "error": {"code": "AUTHENTICATION_ERROR", "message": "身份验证失败", "details": {"reason": "API密钥无效或已过期"}}, "timestamp": 1703123456801}, "rateLimitError": {"success": false, "error": {"code": "RATE_LIMIT_EXCEEDED", "message": "请求频率超限", "details": {"limit": 100, "remaining": 0, "reset_time": 1703123516801}}, "timestamp": 1703123456802}, "serverError": {"success": false, "error": {"code": "INTERNAL_SERVER_ERROR", "message": "服务器内部错误", "details": {"error_id": "err-12345", "message": "模型服务暂时不可用"}}, "timestamp": 1703123456803}}}