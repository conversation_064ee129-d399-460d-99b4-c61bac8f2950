# HTTP API 测试工具使用指南

## 📋 工具概览

本目录包含了完整的 HTTP API 测试工具套件，用于测试和验证当贝 Provider 的各种 API 接口。

### 🛠️ 工具文件

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `api-test-page.html` | 交互式测试页面 | 浏览器中手动测试 API |
| `api-test-script.js` | 自动化测试脚本 | 命令行批量测试 |
| `test-api-params.json` | 请求参数示例 | 参考标准请求格式 |
| `types/api.ts` | TypeScript 类型定义 | 开发时的类型约束 |
| `API-测试文档.md` | 详细文档 | 完整的 API 说明 |

## 🚀 快速开始

### 1. 交互式测试（推荐新手）

```bash
# 在浏览器中打开测试页面
open src/server/api-test-page.html
# 或者直接双击文件
```

**使用步骤：**
1. 设置 API 基础地址（默认：`http://localhost:3000`）
2. 配置 API 密钥（如果需要）
3. 选择要测试的接口
4. 填写请求参数
5. 点击发送请求查看结果

### 2. 命令行自动化测试

```bash
# 基础测试
npm run test:api

# 详细输出测试
npm run test:api:verbose

# 测试本地服务器
npm run test:api:local

# 开发模式测试（详细输出 + 本地服务器）
npm run test:api:dev
```

**自定义配置：**
```bash
# 使用自定义 API 地址
API_BASE_URL=https://your-api.com npm run test:api

# 使用 API 密钥
API_KEY=your-secret-key npm run test:api

# 组合使用
API_BASE_URL=https://your-api.com API_KEY=your-key npm run test:api:verbose
```

## 🧪 测试用例说明

### 自动化测试包含以下用例：

1. **📋 模型列表测试**
   - 验证 `/api/models` 接口
   - 检查返回的模型数据结构
   - 确保至少有一个可用模型

2. **💬 聊天对话测试**
   - 验证 `/api/chat` 接口
   - 发送简单的测试消息
   - 检查 AI 回复格式

3. **✍️ 文本生成测试**
   - 验证 `/api/generate` 接口
   - 测试基础文本生成功能
   - 验证生成内容格式

4. **🔗 OpenAI 兼容测试**
   - 验证 `/v1/models` 接口
   - 检查 OpenAI 格式兼容性
   - 确保数据结构正确

## 📊 测试结果解读

### 成功示例
```
🧪 执行测试: 获取模型列表
✅ 测试通过 (245ms)

🧪 执行测试: 聊天对话测试
✅ 测试通过 (1832ms)

📊 测试报告
==================================================
✅ 通过 获取模型列表        245ms
✅ 通过 聊天对话测试       1832ms
✅ 通过 文本生成测试        987ms
✅ 通过 OpenAI兼容接口测试   156ms
==================================================
总计: 4 个测试
通过: 4 个
总耗时: 3220ms
```

### 失败示例
```
🧪 执行测试: 聊天对话测试
❌ 测试失败 (5000ms): 状态码不匹配: 期望 200, 实际 500

📊 测试报告
==================================================
✅ 通过 获取模型列表        245ms
❌ 失败 聊天对话测试       5000ms
     错误: 状态码不匹配: 期望 200, 实际 500
==================================================
总计: 2 个测试
通过: 1 个
失败: 1 个
总耗时: 5245ms
```

## 🔧 高级配置

### 环境变量

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `API_BASE_URL` | API 基础地址 | `http://localhost:3000` | `https://api.example.com` |
| `API_KEY` | API 密钥 | 空 | `sk-1234567890abcdef` |

### 命令行参数

| 参数 | 描述 |
|------|------|
| `--verbose` / `-v` | 显示详细输出 |
| `--help` / `-h` | 显示帮助信息 |

### 自定义测试用例

编辑 `api-test-script.js` 中的 `testCases` 数组来添加新的测试用例：

```javascript
const testCases = [
  {
    name: '自定义测试',
    method: 'POST',
    path: '/api/custom',
    body: {
      // 请求体
    },
    expectedStatus: 200,
    validate: (response) => {
      // 自定义验证逻辑
      return response.data.success;
    }
  }
];
```

## 🐛 故障排除

### 常见问题

1. **连接被拒绝**
   ```
   错误: connect ECONNREFUSED 127.0.0.1:3000
   ```
   **解决方案：** 确保服务器正在运行
   ```bash
   npm run server:dev
   ```

2. **API 密钥错误**
   ```
   错误: 状态码不匹配: 期望 200, 实际 401
   ```
   **解决方案：** 检查 API 密钥是否正确设置

3. **请求超时**
   ```
   错误: 请求超时
   ```
   **解决方案：** 检查网络连接或增加超时时间

4. **模块未找到**
   ```
   错误: Cannot find module 'xxx'
   ```
   **解决方案：** 安装依赖
   ```bash
   npm install
   ```

### 调试技巧

1. **启用详细输出**
   ```bash
   npm run test:api:verbose
   ```

2. **检查服务器日志**
   ```bash
   # 在另一个终端窗口中
   npm run server:dev
   ```

3. **使用浏览器开发者工具**
   - 打开 `api-test-page.html`
   - 按 F12 打开开发者工具
   - 查看 Network 标签页的请求详情

## 📝 最佳实践

### 测试前准备

1. **确保服务器运行**
   ```bash
   npm run server:dev
   ```

2. **验证基础连接**
   ```bash
   curl http://localhost:3000/api/models
   ```

3. **准备测试数据**
   - 确保有有效的 API 密钥
   - 准备测试用的提示词和消息

### 持续集成

在 CI/CD 流水线中使用：

```yaml
# .github/workflows/api-test.yml
- name: 运行 API 测试
  run: |
    npm run server &
    sleep 5  # 等待服务器启动
    npm run test:api
  env:
    API_BASE_URL: http://localhost:3000
    API_KEY: ${{ secrets.API_KEY }}
```

### 性能监控

定期运行测试来监控 API 性能：

```bash
# 每日性能测试
0 9 * * * cd /path/to/project && npm run test:api:verbose >> logs/daily-test.log 2>&1
```

## 🔗 相关资源

- [完整 API 文档](./API-测试文档.md)
- [TypeScript 类型定义](./types/api.ts)
- [请求参数示例](./test-api-params.json)
- [服务器源码](./index.ts)

---

**版本：** 1.0.0  
**更新时间：** 2024-12-25  
**维护者：** 当贝 Provider 开发团队
