/**
 * 模型服务类
 * 负责解析 models.json 文件并提供模型信息
 */

import * as fs from 'fs';
import * as path from 'path';
import { ModelInfo, ModelOption, ModelsResponse } from '../types/api';

/**
 * models.json 文件中的原始模型数据结构
 */
interface RawModelData {
  success: boolean;
  data: {
    model: string;
    modelList: Array<{
      value: string;
      title: string;
      hoverText: string;
      icon?: string;
      banner?: string;
      badge?: string;
      innerBadgeText?: string;
      recommend: boolean;
      pinned: boolean;
      option: Array<{
        title: string;
        value: string;
        disable: boolean;
        selected: boolean;
      }>;
    }>;
  };
}

/**
 * 模型服务类
 * 提供模型信息的读取、解析和格式化功能
 */
export class ModelService {
  private modelsData: RawModelData | null = null;
  private readonly modelsFilePath: string;

  constructor() {
    // 获取 models.json 文件的绝对路径
    this.modelsFilePath = path.resolve(process.cwd(), 'models.json');
    this.loadModelsData();
  }

  /**
   * 加载 models.json 文件数据
   * 
   * @private
   */
  private loadModelsData(): void {
    try {
      console.log(`正在加载模型数据文件: ${this.modelsFilePath}`);
      
      if (!fs.existsSync(this.modelsFilePath)) {
        throw new Error(`模型数据文件不存在: ${this.modelsFilePath}`);
      }

      const fileContent = fs.readFileSync(this.modelsFilePath, 'utf-8');
      this.modelsData = JSON.parse(fileContent);
      
      if (!this.modelsData?.success || !this.modelsData?.data?.modelList) {
        throw new Error('模型数据文件格式不正确');
      }

      console.log(`成功加载 ${this.modelsData.data.modelList.length} 个模型`);
    } catch (error) {
      console.error('加载模型数据失败:', error);
      throw new Error(`无法加载模型数据: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 重新加载模型数据
   * 用于在运行时更新模型信息
   */
  public reloadModelsData(): void {
    console.log('重新加载模型数据...');
    this.loadModelsData();
  }

  /**
   * 将原始模型选项转换为标准格式
   * 
   * @param rawOptions 原始选项数据
   * @returns 标准化的模型选项
   * @private
   */
  private transformOptions(rawOptions: any[]): ModelOption[] {
    return rawOptions.map(option => ({
      name: option.title,
      value: option.value,
      enabled: !option.disable,
      selected: option.selected
    }));
  }

  /**
   * 将原始模型数据转换为标准格式
   * 
   * @param rawModel 原始模型数据
   * @returns 标准化的模型信息
   * @private
   */
  private transformModel(rawModel: any): ModelInfo {
    return {
      id: rawModel.value,
      name: rawModel.title,
      description: rawModel.hoverText || rawModel.title,
      options: this.transformOptions(rawModel.option || []),
      recommended: rawModel.recommend || false,
      pinned: rawModel.pinned || false,
      icon: rawModel.icon,
      banner: rawModel.banner,
      badge: rawModel.innerBadgeText || rawModel.badge
    };
  }

  /**
   * 获取所有支持的模型列表
   * 
   * @returns 模型列表响应
   */
  public getModels(): ModelsResponse {
    if (!this.modelsData) {
      throw new Error('模型数据未加载');
    }

    const models = this.modelsData.data.modelList.map(model => 
      this.transformModel(model)
    );

    // 按照推荐和置顶状态排序
    models.sort((a, b) => {
      // 置顶的排在前面
      if (a.pinned && !b.pinned) return -1;
      if (!a.pinned && b.pinned) return 1;
      
      // 推荐的排在前面
      if (a.recommended && !b.recommended) return -1;
      if (!a.recommended && b.recommended) return 1;
      
      // 按名称排序
      return a.name.localeCompare(b.name);
    });

    return {
      defaultModel: this.modelsData.data.model,
      models,
      total: models.length
    };
  }

  /**
   * 根据模型ID获取特定模型信息
   * 
   * @param modelId 模型ID
   * @returns 模型信息，如果不存在返回null
   */
  public getModelById(modelId: string): ModelInfo | null {
    if (!this.modelsData) {
      throw new Error('模型数据未加载');
    }

    const rawModel = this.modelsData.data.modelList.find(
      model => model.value === modelId
    );

    return rawModel ? this.transformModel(rawModel) : null;
  }

  /**
   * 检查模型是否存在
   * 
   * @param modelId 模型ID
   * @returns 是否存在
   */
  public isModelSupported(modelId: string): boolean {
    return this.getModelById(modelId) !== null;
  }

  /**
   * 获取推荐的模型列表
   * 
   * @returns 推荐模型列表
   */
  public getRecommendedModels(): ModelInfo[] {
    const allModels = this.getModels();
    return allModels.models.filter(model => model.recommended);
  }

  /**
   * 获取置顶的模型列表
   * 
   * @returns 置顶模型列表
   */
  public getPinnedModels(): ModelInfo[] {
    const allModels = this.getModels();
    return allModels.models.filter(model => model.pinned);
  }

  /**
   * 获取默认模型ID
   * 
   * @returns 默认模型ID
   */
  public getDefaultModel(): string {
    if (!this.modelsData) {
      throw new Error('模型数据未加载');
    }
    return this.modelsData.data.model;
  }

  /**
   * 获取服务状态信息
   * 
   * @returns 服务状态
   */
  public getStatus(): {
    loaded: boolean;
    modelsCount: number;
    defaultModel: string;
    lastUpdated: Date;
  } {
    return {
      loaded: this.modelsData !== null,
      modelsCount: this.modelsData?.data?.modelList?.length || 0,
      defaultModel: this.modelsData?.data?.model || '',
      lastUpdated: new Date()
    };
  }
}
