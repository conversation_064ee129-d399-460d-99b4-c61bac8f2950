/**
 * 模型格式转换服务
 * 负责在不同API格式之间转换模型数据
 */

import { ModelInfo, OpenAIModel, OpenAIModelsResponse } from '../types/api';

/**
 * 模型转换服务类
 * 提供各种模型数据格式之间的转换功能
 */
export class ModelTransformService {
  
  /**
   * 将内部模型格式转换为OpenAI兼容格式
   * 
   * @param models 内部模型列表
   * @returns OpenAI兼容的模型列表响应
   */
  public transformToOpenAI(models: ModelInfo[]): OpenAIModelsResponse {
    console.log(`正在转换 ${models.length} 个模型为OpenAI兼容格式`);
    
    return {
      object: "list",
      data: models.map(model => this.transformSingleModelToOpenAI(model))
    };
  }

  /**
   * 将单个内部模型转换为OpenAI格式
   * 
   * @param model 内部模型信息
   * @returns OpenAI兼容的模型信息
   * @private
   */
  private transformSingleModelToOpenAI(model: ModelInfo): OpenAIModel {
    // 生成创建时间戳（使用当前时间，实际项目中可能需要从数据库获取）
    const created = Math.floor(Date.now() / 1000);
    
    // 转换模型选项格式
    const transformedOptions = model.options.map(option => ({
      name: this.normalizeOptionName(option.value),
      display_name: option.name,
      enabled: option.enabled,
      default: option.selected || false
    }));

    // 根据模型ID推断能力（实际项目中应该从配置或数据库获取）
    const capabilities = this.inferModelCapabilities(model.id);
    
    // 根据模型ID推断定价（实际项目中应该从配置或数据库获取）
    const pricing = this.inferModelPricing(model.id);
    
    // 推断上下文长度
    const contextLength = this.inferContextLength(model.id);

    const result: OpenAIModel = {
      id: model.id,
      object: "model",
      created,
      owned_by: "dangbei",
      permission: [], // OpenAI格式要求，当前为空数组
      root: model.id,
      parent: null, // 大多数模型没有父模型
      display_name: model.name,
      description: model.description,
      context_length: contextLength,
      metadata: {
        recommended: model.recommended,
        pinned: model.pinned,
        ...(model.icon && { icon: model.icon }),
        ...(model.banner && { banner: model.banner }),
        ...(model.badge && { badge: model.badge }),
        options: transformedOptions
      }
    };

    // 添加可选字段
    if (capabilities) {
      result.capabilities = capabilities;
    }
    if (pricing) {
      result.pricing = pricing;
    }

    return result;
  }

  /**
   * 标准化选项名称
   * 将中文选项名称转换为标准的英文名称
   * 
   * @param optionValue 选项值
   * @returns 标准化的选项名称
   * @private
   */
  private normalizeOptionName(optionValue: string): string {
    const optionMap: Record<string, string> = {
      'deep': 'deep_thinking',
      'online': 'online_search',
      'search': 'web_search',
      'knowledge': 'knowledge_base',
      'shared': 'shared_knowledge'
    };

    return optionMap[optionValue] || optionValue;
  }

  /**
   * 根据模型ID推断模型能力
   * 
   * @param modelId 模型ID
   * @returns 模型能力配置
   * @private
   */
  private inferModelCapabilities(modelId: string): OpenAIModel['capabilities'] {
    // 基于模型ID的能力推断逻辑
    const capabilities = {
      chat: true,
      completion: true,
      embeddings: false,
      fine_tuning: false
    };

    // 特定模型的能力配置
    if (modelId.includes('embedding')) {
      capabilities.embeddings = true;
      capabilities.chat = false;
      capabilities.completion = false;
    }

    if (modelId.includes('fine-tune') || modelId.includes('ft-')) {
      capabilities.fine_tuning = true;
    }

    return capabilities;
  }

  /**
   * 根据模型ID推断定价信息
   * 
   * @param modelId 模型ID
   * @returns 定价信息
   * @private
   */
  private inferModelPricing(modelId: string): OpenAIModel['pricing'] {
    // 默认定价（实际项目中应该从配置文件或数据库获取）
    const defaultPricing = {
      input: 0.0015,  // 每1K tokens的输入价格
      output: 0.002   // 每1K tokens的输出价格
    };

    // 基于模型ID的定价策略
    const pricingMap: Record<string, { input: number; output: number }> = {
      'deepseek': { input: 0.0014, output: 0.0028 },
      'doubao': { input: 0.0008, output: 0.002 },
      'qwen': { input: 0.001, output: 0.002 },
      'glm': { input: 0.001, output: 0.002 }
    };

    // 查找匹配的定价
    for (const [key, pricing] of Object.entries(pricingMap)) {
      if (modelId.toLowerCase().includes(key)) {
        return pricing;
      }
    }

    return defaultPricing;
  }

  /**
   * 根据模型ID推断上下文长度
   * 
   * @param modelId 模型ID
   * @returns 上下文长度
   * @private
   */
  private inferContextLength(modelId: string): number {
    // 基于模型ID的上下文长度推断
    const contextLengthMap: Record<string, number> = {
      'deepseek': 32768,
      'doubao': 32768,
      'qwen': 32768,
      'glm': 32768,
      'claude': 200000,
      'gpt-4': 128000,
      'gpt-3.5': 16384
    };

    // 查找匹配的上下文长度
    for (const [key, length] of Object.entries(contextLengthMap)) {
      if (modelId.toLowerCase().includes(key)) {
        return length;
      }
    }

    // 默认上下文长度
    return 8192;
  }

  /**
   * 将OpenAI格式转换回内部格式
   * 用于处理客户端发送的OpenAI格式数据
   * 
   * @param openaiModel OpenAI格式的模型信息
   * @returns 内部模型格式
   */
  public transformFromOpenAI(openaiModel: OpenAIModel): ModelInfo {
    return {
      id: openaiModel.id,
      name: openaiModel.display_name || openaiModel.id,
      description: openaiModel.description || '',
      options: openaiModel.metadata?.options.map(option => ({
        name: option.display_name,
        value: option.name,
        enabled: option.enabled,
        selected: option.default
      })) || [],
      recommended: openaiModel.metadata?.recommended || false,
      pinned: openaiModel.metadata?.pinned || false,
      ...(openaiModel.metadata?.icon && { icon: openaiModel.metadata.icon }),
      ...(openaiModel.metadata?.banner && { banner: openaiModel.metadata.banner }),
      ...(openaiModel.metadata?.badge && { badge: openaiModel.metadata.badge })
    };
  }

  /**
   * 验证OpenAI格式的模型数据
   * 
   * @param data 待验证的数据
   * @returns 是否为有效的OpenAI模型格式
   */
  public validateOpenAIFormat(data: any): data is OpenAIModel {
    return (
      typeof data === 'object' &&
      typeof data.id === 'string' &&
      data.object === 'model' &&
      typeof data.created === 'number' &&
      typeof data.owned_by === 'string' &&
      Array.isArray(data.permission)
    );
  }

  /**
   * 获取支持的转换格式列表
   * 
   * @returns 支持的格式列表
   */
  public getSupportedFormats(): string[] {
    return [
      'internal',    // 内部格式
      'openai',      // OpenAI兼容格式
      'anthropic',   // Anthropic格式（未来支持）
      'google'       // Google AI格式（未来支持）
    ];
  }

  /**
   * 获取转换服务的统计信息
   * 
   * @returns 统计信息
   */
  public getStats(): {
    totalTransformations: number;
    supportedFormats: string[];
    lastTransformTime: Date | null;
  } {
    // 这里可以添加实际的统计逻辑
    return {
      totalTransformations: 0,
      supportedFormats: this.getSupportedFormats(),
      lastTransformTime: null
    };
  }
}
