/**
 * 当贝AI Provider主类
 * 整合所有服务，提供简洁易用的公共API接口
 */

import { 
  DeviceConfig, 
  CreateConversationResponse,
  ChatOptions, 
  ChatResponse,
  ChatCallbacks,
  DangbeiApiError,
  ErrorType
} from '../types';
import { 
  HttpClient, 
  ConversationService, 
  CommonService, 
  ChatService 
} from '../services';


/**
 * 当贝AI Provider配置选项
 */
export interface DangbeiProviderOptions {
  /** 设备配置 */
  deviceConfig?: Partial<DeviceConfig>;
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 当贝AI Provider主类
 * 提供完整的当贝AI API调用功能
 */
export class DangbeiProvider {
  private readonly httpClient: HttpClient;
  private readonly conversationService: ConversationService;
  private readonly commonService: CommonService;
  private readonly chatService: ChatService;
  private readonly options: DangbeiProviderOptions;

  /**
   * 构造函数
   * 
   * @param options 配置选项
   */
  constructor(options: DangbeiProviderOptions = {}) {
    this.options = {
      timeout: 30000,
      retries: 3,
      debug: false,
      ...options
    };

    // 初始化HTTP客户端（传递调试模式参数）
    this.httpClient = new HttpClient(options.deviceConfig, this.options.debug);
    
    // 初始化各个服务
    this.conversationService = new ConversationService(this.httpClient);
    this.commonService = new CommonService(this.httpClient);
    this.chatService = new ChatService(this.httpClient);

    if (this.options.debug) {
      console.log('当贝AI Provider已初始化', {
        deviceId: this.httpClient.getDeviceConfig().deviceId,
        options: this.options
      });
    }
  }

  /**
   * 创建新的对话会话
   * 
   * @param options 创建选项
   * @returns 对话信息
   */
  public async createConversation(options?: {
    superAgentPath?: string;
    isAnonymous?: boolean;
    source?: string;
  }): Promise<CreateConversationResponse> {
    try {
      if (this.options.debug) {
        console.log('创建对话会话', options);
      }

      const result = await this.conversationService.createConversation(options);
      
      if (this.options.debug) {
        console.log('对话会话创建成功', {
          conversationId: result.conversationId,
          title: result.title
        });
      }

      return result;
    } catch (error) {
      if (this.options.debug) {
        console.error('创建对话会话失败', error);
      }
      throw error;
    }
  }

  /**
   * 发送聊天消息
   *
   * @param options 聊天选项
   * @param callbacks 回调函数（可选）
   * @returns 聊天响应或void
   */
  public async chat(options: ChatOptions): Promise<ChatResponse>;
  public async chat(options: ChatOptions, callbacks: ChatCallbacks): Promise<void>;
  public async chat(options: ChatOptions, callbacks?: ChatCallbacks): Promise<ChatResponse | void> {
    try {
      // 验证聊天选项
      const validation = this.chatService.validateChatOptions(options);
      if (!validation.valid) {
        throw new DangbeiApiError(
          `聊天参数验证失败: ${validation.errors.join(', ')}`,
          ErrorType.PARAMETER_ERROR
        );
      }

      if (this.options.debug) {
        console.log('发送聊天消息', {
          conversationId: options.conversationId,
          question: options.question.substring(0, 100) + '...',
          model: options.model,
          streamMode: !!callbacks
        });
      }

      // 如果提供了回调函数，使用流式模式
      if (callbacks) {
        return await this.chatService.chat(options, callbacks);
      }

      // 否则使用同步模式
      const result = await this.chatService.chat(options);
      
      if (this.options.debug) {
        console.log('聊天响应接收完成', {
          messageId: result.messageId,
          contentLength: result.content.length
        });
      }

      return result;
    } catch (error) {
      if (this.options.debug) {
        console.error('聊天消息发送失败', error);
      }
      throw error;
    }
  }

  /**
   * 发送聊天消息（同步等待完整响应）
   * 
   * @param options 聊天选项
   * @returns 完整的响应内容
   */
  public async chatSync(options: ChatOptions): Promise<string> {
    const response = await this.chat(options);
    return response.content;
  }

  /**
   * 生成唯一ID
   * 
   * @param timestamp 时间戳（可选）
   * @returns 生成的ID
   */
  public async generateId(timestamp?: number): Promise<string> {
    try {
      return await this.commonService.generateId(timestamp);
    } catch (error) {
      if (this.options.debug) {
        console.warn('服务器ID生成失败，使用本地ID', error);
      }
      // 如果服务器ID生成失败，使用本地生成的ID
      return this.commonService.generateLocalId();
    }
  }

  /**
   * 停止当前聊天
   */
  public stopChat(): void {
    this.chatService.stopChat();
    
    if (this.options.debug) {
      console.log('聊天已停止');
    }
  }

  /**
   * 获取设备配置
   * 
   * @returns 当前设备配置
   */
  public getDeviceConfig(): DeviceConfig {
    return this.httpClient.getDeviceConfig();
  }

  /**
   * 更新设备配置
   * 
   * @param config 新的设备配置
   */
  public updateDeviceConfig(config: Partial<DeviceConfig>): void {
    this.httpClient.updateDeviceConfig(config);
    
    if (this.options.debug) {
      console.log('设备配置已更新', config);
    }
  }

  /**
   * 获取服务状态
   * 
   * @returns 服务状态信息
   */
  public getStatus(): {
    deviceId: string;
    chatStatus: {
      isConnected: boolean;
      readyState: string;
    };
    serviceAvailable: boolean;
  } {
    return {
      deviceId: this.httpClient.getDeviceConfig().deviceId,
      chatStatus: this.chatService.getStatus(),
      serviceAvailable: true // 可以添加实际的服务可用性检查
    };
  }

  /**
   * 检查服务可用性
   * 
   * @returns 服务是否可用
   */
  public async checkServiceAvailability(): Promise<boolean> {
    try {
      return await this.commonService.checkServiceAvailability();
    } catch (error) {
      if (this.options.debug) {
        console.warn('服务可用性检查失败', error);
      }
      return false;
    }
  }

  /**
   * 创建快速聊天会话
   * 自动创建对话并发送消息
   * 
   * @param question 问题内容
   * @param callbacks 回调函数
   * @returns 聊天响应
   */
  public async quickChat(
    question: string,
    callbacks?: ChatCallbacks
  ): Promise<ChatResponse> {
    // 创建新对话
    const conversation = await this.createConversation();

    // 发送聊天消息
    const chatOptions: ChatOptions = {
      conversationId: conversation.conversationId,
      question
    };

    if (callbacks) {
      chatOptions.callbacks = callbacks;
    }

    return await this.chat(chatOptions);
  }

  /**
   * 销毁Provider实例
   * 清理资源
   */
  public destroy(): void {
    this.stopChat();
    
    if (this.options.debug) {
      console.log('当贝AI Provider已销毁');
    }
  }
}
