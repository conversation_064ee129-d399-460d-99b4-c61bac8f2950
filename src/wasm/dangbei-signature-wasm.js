/**
 * 当贝AI WebAssembly 签名模块 Node.js 包装器
 * 
 * 这个模块提供了在 Node.js 环境中调用当贝AI WebAssembly签名算法的功能
 * 基于对 ai.dangbei.com/_next/static/chunks/pages/_app-72ae859153e99355.js 的逆向分析
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');

/**
 * 当贝AI WebAssembly 签名器类
 * 负责加载和管理 WebAssembly 模块，提供签名生成功能
 */
class DangbeiSignatureWasm {
  constructor() {
    this.wasmInstance = null;
    this.wasmMemory = null;
    this.textEncoder = new TextEncoder();
    this.textDecoder = new TextDecoder();
    
    // 全局变量，用于存储字符串长度（模拟原始 JS 中的 p 变量）
    this.lastStringLength = 0;
    
    // 调试模式
    this.debug = process.env.NODE_ENV === 'development';
  }

  /**
   * 初始化 WebAssembly 模块
   * 加载 sign_bg.wasm 文件并设置必要的导入
   * 
   * @param {string} wasmPath - WASM 文件路径
   * @returns {Promise<void>}
   */
  async initialize(wasmPath = './sign_bg.wasm') {
    try {
      this.log('🚀 开始初始化当贝AI WebAssembly签名模块...');
      
      // 检查 WASM 文件是否存在
      if (!fs.existsSync(wasmPath)) {
        throw new Error(`WASM 文件不存在: ${wasmPath}`);
      }

      // 读取 WASM 文件
      const wasmBuffer = fs.readFileSync(wasmPath);
      this.log(`📁 已读取 WASM 文件: ${wasmPath} (${wasmBuffer.length} 字节)`);

      // 创建 WebAssembly 导入对象
      const imports = this.createWasmImports();

      // 编译和实例化 WebAssembly 模块
      const wasmModule = await WebAssembly.compile(wasmBuffer);
      this.wasmInstance = await WebAssembly.instantiate(wasmModule, imports);
      
      // 获取内存引用
      this.wasmMemory = this.wasmInstance.exports.memory;
      
      // 初始化 WebAssembly 模块（如果有 start 函数）
      if (this.wasmInstance.exports.__wbindgen_start) {
        this.wasmInstance.exports.__wbindgen_start();
      }

      this.log('✅ WebAssembly 模块初始化成功');
      this.log(`📊 内存大小: ${this.wasmMemory.buffer.byteLength} 字节`);
      
    } catch (error) {
      this.log(`❌ WebAssembly 模块初始化失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建 WebAssembly 导入对象
   * 提供 WASM 模块所需的所有导入函数
   * 
   * @returns {Object} 导入对象
   */
  createWasmImports() {
    const self = this;
    
    return {
      wbg: {
        // 错误处理
        __wbindgen_error_new: (ptr, len) => {
          const message = this.getStringFromMemory(ptr, len);
          return new Error(message);
        },

        // 类型检查函数
        __wbindgen_is_object: (value) => {
          return typeof value === 'object' && value !== null;
        },

        __wbindgen_is_string: (value) => {
          return typeof value === 'string';
        },

        __wbindgen_is_function: (value) => {
          return typeof value === 'function';
        },

        __wbindgen_is_undefined: (value) => {
          return value === undefined;
        },

        // 数字和字符串创建
        __wbindgen_number_new: (value) => {
          return value;
        },

        __wbindgen_string_new: (ptr, len) => {
          return this.getStringFromMemory(ptr, len);
        },

        // BigInt 支持
        __wbindgen_bigint_from_i64: (value) => {
          return BigInt(value);
        },

        __wbindgen_bigint_from_u64: (value) => {
          return BigInt(value);
        },

        // 内存管理
        __wbindgen_memory: () => {
          return this.wasmMemory;
        },

        // 调试支持
        __wbindgen_debug_string: (ptr, len) => {
          const str = this.getStringFromMemory(ptr, len);
          this.log(`🐛 Debug: ${str}`);
        },

        // 异常处理
        __wbindgen_throw: (ptr, len) => {
          const message = this.getStringFromMemory(ptr, len);
          throw new Error(message);
        },

        // 外部引用表初始化
        __wbindgen_init_externref_table: () => {
          // 初始化外部引用表（如果需要）
        },

        // 时间相关函数
        __wbg_new0_f788a2397c7ca929: () => {
          return new Date();
        },

        __wbg_getTime_46267b1c24877e30: (date) => {
          return date.getTime();
        },

        // 全局对象访问器
        __wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0: () => {
          return globalThis;
        },

        __wbg_static_accessor_SELF_37c5d418e4bf5819: () => {
          return typeof self !== 'undefined' ? self : globalThis;
        },

        __wbg_static_accessor_WINDOW_5de37043a91a9c40: () => {
          return typeof window !== 'undefined' ? window : globalThis;
        },

        __wbg_static_accessor_GLOBAL_88a902d13a557d07: () => {
          return typeof global !== 'undefined' ? global : globalThis;
        },

        // 其他可能需要的函数（根据实际 WASM 需求添加）
        __wbg_set_3f1d0b984ed272ed: () => {},
        __wbg_instanceof_Window_def73ea0955fc569: () => false,
        __wbg_document_d249400bd7bd996d: () => null,
        __wbg_getElementById_f827f0d6648718a8: () => null,
        __wbg_crypto_ed58b8e10a292839: () => null,
        __wbg_process_5c1d670bc53614b8: () => process,
        __wbg_versions_c71aa1626a93e0a1: () => process.versions,
        __wbg_node_02999533c4ea02e3: () => process.versions.node,
        __wbg_require_79b1e9274cde3c87: () => require,
        __wbg_msCrypto_0a36e2ec3a343d26: () => null,
        __wbg_getRandomValues_bcb4912f16000dc4: () => {},
        __wbg_randomFillSync_ab2cfe79ebbf2740: () => {},
        __wbg_new_78feb108b6472713: () => ({}),
        __wbg_newnoargs_105ed471475aaf50: () => ({}),
        __wbg_new_5e0be73521bc8c17: () => ({}),
        __wbg_call_672a4d21634d4a24: () => ({}),
        __wbg_new_405e22f390576ce2: () => ({}),
        __wbg_set_37837023f3d740e8: () => {},
        __wbg_call_7cccdd69e0791ae2: () => ({}),
        __wbg_set_8fc6bf8a5b1071d1: () => ({}),
        __wbg_buffer_609cc3eee51ed158: () => new ArrayBuffer(0),
        __wbg_newwithbyteoffsetandlength_d97e637ebe145a9a: () => new Uint8Array(0),
        __wbg_new_a12002a7f91c75be: () => new Uint8Array(0),
        __wbg_set_65595bdd868b3009: () => {},
        __wbg_newwithlength_a381634e90c276d4: () => new Uint8Array(0),
        __wbg_subarray_aa9065fa9dc5df96: () => new Uint8Array(0)
      }
    };
  }

  /**
   * 从 WebAssembly 内存中读取字符串
   * 
   * @param {number} ptr - 内存指针
   * @param {number} len - 字符串长度
   * @returns {string} 解码后的字符串
   */
  getStringFromMemory(ptr, len) {
    const memory = new Uint8Array(this.wasmMemory.buffer);
    const bytes = memory.slice(ptr, ptr + len);
    return this.textDecoder.decode(bytes);
  }

  /**
   * 将字符串写入 WebAssembly 内存
   * 模拟原始 JavaScript 中的 g() 函数功能
   * 
   * @param {string} str - 要编码的字符串
   * @returns {number} 内存指针
   */
  writeStringToMemory(str) {
    if (!this.wasmInstance) {
      throw new Error('WebAssembly 模块未初始化');
    }

    // 编码字符串为 UTF-8 字节
    const encoded = this.textEncoder.encode(str);
    const len = encoded.length;

    // 分配内存
    const ptr = this.wasmInstance.exports.__wbindgen_malloc(len, 1);
    
    // 写入内存
    const memory = new Uint8Array(this.wasmMemory.buffer);
    memory.set(encoded, ptr);

    // 保存长度（模拟原始代码中的全局变量 p）
    this.lastStringLength = len;

    this.log(`📝 字符串写入内存: "${str}" -> 指针=${ptr}, 长度=${len}`);
    
    return ptr;
  }

  /**
   * 生成签名
   * 模拟原始 JavaScript 中的 v() 函数功能
   * 
   * @param {string} str1 - 第一个字符串参数
   * @param {string} str2 - 第二个字符串参数
   * @returns {string} 生成的签名
   */
  generateSignature(str1, str2) {
    if (!this.wasmInstance) {
      throw new Error('WebAssembly 模块未初始化');
    }

    try {
      this.log(`🔐 开始生成签名...`);
      this.log(`📥 输入参数1: "${str1}"`);
      this.log(`📥 输入参数2: "${str2}"`);

      // 将第一个字符串写入内存
      const ptr1 = this.writeStringToMemory(str1);
      const len1 = this.lastStringLength;

      // 将第二个字符串写入内存
      const ptr2 = this.writeStringToMemory(str2);
      const len2 = this.lastStringLength;

      // 调用 WebAssembly 的 get_sign 函数
      const result = this.wasmInstance.exports.get_sign(ptr1, len1, ptr2, len2);

      this.log(`✅ 签名生成成功: ${result}`);
      
      return result;

    } catch (error) {
      this.log(`❌ 签名生成失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 日志输出函数
   * 
   * @param {string} message - 日志消息
   */
  log(message) {
    if (this.debug) {
      console.log(`[DangbeiSignatureWasm] ${message}`);
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.wasmInstance = null;
    this.wasmMemory = null;
    this.log('🧹 WebAssembly 模块资源已清理');
  }
}

module.exports = DangbeiSignatureWasm;
