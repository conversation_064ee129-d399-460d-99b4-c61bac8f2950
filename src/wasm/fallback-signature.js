/**
 * 当贝AI 签名算法备用实现
 * 
 * 当 WebAssembly 模块不可用时，提供基于 JavaScript 的备用签名算法
 * 基于对原始签名逻辑的分析和逆向工程
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const crypto = require('crypto');

/**
 * 备用签名生成器类
 * 提供与 WebAssembly 模块兼容的签名算法实现
 */
class FallbackSignature {
  constructor(options = {}) {
    this.debug = options.debug || process.env.NODE_ENV === 'development';
    this.algorithm = options.algorithm || 'md5';
    this.encoding = options.encoding || 'hex';
    this.secretKey = options.secretKey || 'dangbei_ai_fallback_2024';
  }

  /**
   * 生成签名
   * 模拟 WebAssembly 模块的 get_sign 函数
   * 
   * @param {string} param1 - 第一个参数（通常是请求数据）
   * @param {string} param2 - 第二个参数（通常是时间戳和nonce）
   * @returns {string} 生成的签名
   */
  generateSignature(param1, param2) {
    try {
      this.log(`🔄 使用备用算法生成签名...`);
      this.log(`📥 参数1: ${param1.substring(0, 100)}${param1.length > 100 ? '...' : ''}`);
      this.log(`📥 参数2: ${param2}`);

      // 策略1: 基础MD5签名
      const basicSignature = this.generateBasicSignature(param1, param2);
      
      // 策略2: 增强签名（包含密钥）
      const enhancedSignature = this.generateEnhancedSignature(param1, param2);
      
      // 策略3: 复合签名（多轮哈希）
      const compositeSignature = this.generateCompositeSignature(param1, param2);

      // 根据参数特征选择最合适的签名策略
      const signature = this.selectBestSignature(param1, param2, {
        basic: basicSignature,
        enhanced: enhancedSignature,
        composite: compositeSignature
      });

      this.log(`✅ 备用签名生成成功: ${signature}`);
      return signature;

    } catch (error) {
      this.log(`❌ 备用签名生成失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 基础MD5签名算法
   * 简单的字符串拼接后MD5哈希
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {string} MD5签名
   */
  generateBasicSignature(param1, param2) {
    const combined = param1 + param2;
    const hash = crypto.createHash('md5').update(combined).digest('hex');
    return hash.toUpperCase();
  }

  /**
   * 增强签名算法
   * 包含密钥和时间戳的HMAC签名
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {string} HMAC签名
   */
  generateEnhancedSignature(param1, param2) {
    // 提取时间戳（如果param2包含时间戳）
    const timestamp = this.extractTimestamp(param2);
    
    // 构建签名字符串
    const signString = `${timestamp}${param1}${param2}${this.secretKey}`;
    
    // 使用HMAC-SHA256
    const hmac = crypto.createHmac('sha256', this.secretKey);
    hmac.update(signString);
    const signature = hmac.digest('hex');
    
    // 转换为MD5格式以保持兼容性
    const md5Hash = crypto.createHash('md5').update(signature).digest('hex');
    return md5Hash.toUpperCase();
  }

  /**
   * 复合签名算法
   * 多轮哈希计算，模拟复杂的WebAssembly逻辑
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {string} 复合签名
   */
  generateCompositeSignature(param1, param2) {
    // 第一轮：基于长度的哈希
    const lengthHash = this.generateLengthBasedHash(param1, param2);
    
    // 第二轮：基于内容的哈希
    const contentHash = this.generateContentBasedHash(param1, param2);
    
    // 第三轮：组合哈希
    const combinedHash = this.generateCombinedHash(lengthHash, contentHash, param1, param2);
    
    // 最终签名
    const finalSignature = crypto.createHash('md5')
      .update(combinedHash)
      .digest('hex')
      .toUpperCase();
    
    return finalSignature;
  }

  /**
   * 基于长度的哈希计算
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {string} 长度哈希
   */
  generateLengthBasedHash(param1, param2) {
    const len1 = param1.length;
    const len2 = param2.length;
    const lengthString = `${len1}:${len2}:${len1 + len2}`;
    
    return crypto.createHash('md5').update(lengthString).digest('hex');
  }

  /**
   * 基于内容的哈希计算
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {string} 内容哈希
   */
  generateContentBasedHash(param1, param2) {
    // 计算内容特征
    const checksum1 = this.calculateChecksum(param1);
    const checksum2 = this.calculateChecksum(param2);
    
    const contentString = `${checksum1}${param1.substring(0, Math.min(10, param1.length))}${checksum2}${param2}`;
    
    return crypto.createHash('sha1').update(contentString).digest('hex');
  }

  /**
   * 组合哈希计算
   * 
   * @param {string} lengthHash - 长度哈希
   * @param {string} contentHash - 内容哈希
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {string} 组合哈希
   */
  generateCombinedHash(lengthHash, contentHash, param1, param2) {
    const timestamp = this.extractTimestamp(param2);
    const combinedString = `${lengthHash.substring(0, 8)}${contentHash.substring(0, 8)}${timestamp}${this.secretKey}`;
    
    return crypto.createHash('sha256').update(combinedString).digest('hex');
  }

  /**
   * 计算字符串校验和
   * 
   * @param {string} str - 输入字符串
   * @returns {number} 校验和
   */
  calculateChecksum(str) {
    let checksum = 0;
    for (let i = 0; i < str.length; i++) {
      checksum += str.charCodeAt(i);
    }
    return checksum % 65536;
  }

  /**
   * 从参数中提取时间戳
   * 
   * @param {string} param - 参数字符串
   * @returns {number} 时间戳
   */
  extractTimestamp(param) {
    // 尝试从param2中提取时间戳
    const timestampMatch = param.match(/(\d{10})/);
    if (timestampMatch) {
      return parseInt(timestampMatch[1]);
    }
    
    // 如果没有找到，使用当前时间戳
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 选择最佳签名策略
   * 根据输入参数的特征选择最合适的签名算法
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @param {Object} signatures - 各种签名结果
   * @returns {string} 选择的签名
   */
  selectBestSignature(param1, param2, signatures) {
    // 根据参数特征选择策略
    if (param1.length > 1000 || param2.includes(':')) {
      // 大数据或包含特殊格式，使用复合签名
      this.log('🎯 选择复合签名策略');
      return signatures.composite;
    } else if (param1.startsWith('{') && param1.endsWith('}')) {
      // JSON数据，使用增强签名
      this.log('🎯 选择增强签名策略');
      return signatures.enhanced;
    } else {
      // 简单数据，使用基础签名
      this.log('🎯 选择基础签名策略');
      return signatures.basic;
    }
  }

  /**
   * 验证签名
   * 验证给定的签名是否正确
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @param {string} expectedSignature - 期望的签名
   * @returns {boolean} 是否匹配
   */
  verifySignature(param1, param2, expectedSignature) {
    try {
      const generatedSignature = this.generateSignature(param1, param2);
      const isValid = generatedSignature === expectedSignature;
      
      this.log(`🔍 签名验证: ${isValid ? '✅ 通过' : '❌ 失败'}`);
      this.log(`   期望: ${expectedSignature}`);
      this.log(`   实际: ${generatedSignature}`);
      
      return isValid;
      
    } catch (error) {
      this.log(`❌ 签名验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取签名算法信息
   * 
   * @returns {Object} 算法信息
   */
  getAlgorithmInfo() {
    return {
      name: 'FallbackSignature',
      version: '1.0.0',
      algorithm: this.algorithm,
      encoding: this.encoding,
      strategies: ['basic', 'enhanced', 'composite'],
      description: '当贝AI签名算法备用实现'
    };
  }

  /**
   * 日志输出函数
   * 
   * @param {string} message - 日志消息
   */
  log(message) {
    if (this.debug) {
      console.log(`[FallbackSignature] ${message}`);
    }
  }
}

/**
 * 创建备用签名实例的工厂函数
 * 
 * @param {Object} options - 配置选项
 * @returns {FallbackSignature} 签名实例
 */
function createFallbackSignature(options = {}) {
  return new FallbackSignature(options);
}

/**
 * 快速签名函数
 * 提供简单的签名生成接口
 * 
 * @param {string} param1 - 第一个参数
 * @param {string} param2 - 第二个参数
 * @param {Object} options - 配置选项
 * @returns {string} 生成的签名
 */
function quickSign(param1, param2, options = {}) {
  const signer = new FallbackSignature(options);
  return signer.generateSignature(param1, param2);
}

module.exports = {
  FallbackSignature,
  createFallbackSignature,
  quickSign
};
