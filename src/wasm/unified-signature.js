/**
 * 当贝AI 统一签名接口
 * 
 * 提供统一的签名生成接口，自动选择最佳的签名实现方式：
 * 1. 优先使用 WebAssembly 模块（如果可用）
 * 2. 降级到 JavaScript 备用实现
 * 3. 提供错误处理和重试机制
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const DangbeiSignatureWasm = require('./dangbei-signature-wasm');
const { FallbackSignature } = require('./fallback-signature');
const path = require('path');

/**
 * 统一签名管理器类
 * 负责协调不同的签名实现方式
 */
class UnifiedSignature {
  constructor(options = {}) {
    this.options = {
      wasmPath: options.wasmPath || path.join(__dirname, '../../sign_bg.wasm'),
      preferWasm: options.preferWasm !== false, // 默认优先使用 WASM
      enableFallback: options.enableFallback !== false, // 默认启用备用方案
      retryAttempts: options.retryAttempts || 3,
      debug: options.debug || process.env.NODE_ENV === 'development',
      ...options
    };

    this.wasmSigner = null;
    this.fallbackSigner = null;
    this.currentMode = null; // 'wasm' | 'fallback' | null
    this.initializationPromise = null;
    this.isInitialized = false;
  }

  /**
   * 初始化签名器
   * 尝试初始化 WebAssembly 模块，失败时降级到备用实现
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  /**
   * 执行初始化逻辑
   * 
   * @private
   * @returns {Promise<void>}
   */
  async _doInitialize() {
    this.log('🚀 开始初始化统一签名接口...');

    // 尝试初始化 WebAssembly 模块
    if (this.options.preferWasm) {
      try {
        this.log('🔧 尝试初始化 WebAssembly 模块...');
        this.wasmSigner = new DangbeiSignatureWasm();
        await this.wasmSigner.initialize(this.options.wasmPath);
        
        this.currentMode = 'wasm';
        this.log('✅ WebAssembly 模块初始化成功');
        
      } catch (error) {
        this.log(`⚠️ WebAssembly 模块初始化失败: ${error.message}`);
        
        if (!this.options.enableFallback) {
          throw error;
        }
      }
    }

    // 如果 WASM 初始化失败或未启用，使用备用实现
    if (!this.wasmSigner && this.options.enableFallback) {
      this.log('🔄 初始化备用签名实现...');
      this.fallbackSigner = new FallbackSignature({
        debug: this.options.debug,
        secretKey: this.options.secretKey
      });
      
      this.currentMode = 'fallback';
      this.log('✅ 备用签名实现初始化成功');
    }

    // 检查是否有可用的签名器
    if (!this.wasmSigner && !this.fallbackSigner) {
      throw new Error('无法初始化任何签名实现');
    }

    this.isInitialized = true;
    this.log(`🎯 统一签名接口初始化完成，当前模式: ${this.currentMode}`);
  }

  /**
   * 生成签名
   * 自动选择最佳的签名实现方式
   * 
   * @param {string} param1 - 第一个参数
   * @param {string} param2 - 第二个参数
   * @returns {Promise<string>} 生成的签名
   */
  async generateSignature(param1, param2) {
    // 确保已初始化
    if (!this.isInitialized) {
      await this.initialize();
    }

    let lastError = null;
    
    // 尝试使用当前模式生成签名
    for (let attempt = 1; attempt <= this.options.retryAttempts; attempt++) {
      try {
        this.log(`🔐 第 ${attempt} 次尝试生成签名 (模式: ${this.currentMode})...`);
        
        if (this.currentMode === 'wasm' && this.wasmSigner) {
          return this.wasmSigner.generateSignature(param1, param2);
        } else if (this.currentMode === 'fallback' && this.fallbackSigner) {
          return this.fallbackSigner.generateSignature(param1, param2);
        } else {
          throw new Error('没有可用的签名实现');
        }
        
      } catch (error) {
        this.log(`❌ 第 ${attempt} 次尝试失败: ${error.message}`);
        lastError = error;
        
        // 如果是 WASM 失败且有备用方案，切换到备用方案
        if (this.currentMode === 'wasm' && this.fallbackSigner && attempt === 1) {
          this.log('🔄 切换到备用签名实现...');
          this.currentMode = 'fallback';
        }
        
        // 如果不是最后一次尝试，等待一段时间后重试
        if (attempt < this.options.retryAttempts) {
          await this.sleep(100 * attempt); // 递增延迟
        }
      }
    }

    // 所有尝试都失败了
    throw new Error(`签名生成失败，已尝试 ${this.options.retryAttempts} 次。最后错误: ${lastError?.message}`);
  }

  /**
   * 批量生成签名
   * 
   * @param {Array<{param1: string, param2: string}>} requests - 签名请求数组
   * @returns {Promise<Array<string>>} 签名结果数组
   */
  async generateSignatureBatch(requests) {
    this.log(`📦 开始批量生成签名，共 ${requests.length} 个请求...`);
    
    const results = [];
    const errors = [];
    
    for (let i = 0; i < requests.length; i++) {
      try {
        const { param1, param2 } = requests[i];
        const signature = await this.generateSignature(param1, param2);
        results.push(signature);
        
      } catch (error) {
        this.log(`❌ 第 ${i + 1} 个请求失败: ${error.message}`);
        errors.push({ index: i, error: error.message });
        results.push(null);
      }
    }
    
    if (errors.length > 0) {
      this.log(`⚠️ 批量签名完成，${errors.length} 个请求失败`);
    } else {
      this.log(`✅ 批量签名全部成功`);
    }
    
    return results;
  }

  /**
   * 获取当前状态信息
   * 
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      currentMode: this.currentMode,
      wasmAvailable: !!this.wasmSigner,
      fallbackAvailable: !!this.fallbackSigner,
      options: { ...this.options }
    };
  }

  /**
   * 切换签名模式
   * 
   * @param {string} mode - 目标模式 ('wasm' | 'fallback')
   * @returns {boolean} 是否切换成功
   */
  switchMode(mode) {
    if (mode === 'wasm' && this.wasmSigner) {
      this.currentMode = 'wasm';
      this.log('🔄 已切换到 WebAssembly 模式');
      return true;
    } else if (mode === 'fallback' && this.fallbackSigner) {
      this.currentMode = 'fallback';
      this.log('🔄 已切换到备用实现模式');
      return true;
    } else {
      this.log(`❌ 无法切换到模式: ${mode}`);
      return false;
    }
  }

  /**
   * 性能测试
   * 
   * @param {number} iterations - 测试迭代次数
   * @returns {Promise<Object>} 性能测试结果
   */
  async performanceTest(iterations = 100) {
    this.log(`🏃 开始性能测试 (${iterations} 次迭代)...`);
    
    const testData1 = '{"test": "performance"}';
    const testData2 = `${Math.floor(Date.now() / 1000)}:perf_test`;
    
    const results = {};
    
    // 测试当前模式
    if (this.currentMode) {
      const startTime = Date.now();
      let successCount = 0;
      
      for (let i = 0; i < iterations; i++) {
        try {
          await this.generateSignature(testData1, `${testData2}_${i}`);
          successCount++;
        } catch (error) {
          // 忽略错误，继续测试
        }
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      results[this.currentMode] = {
        totalTime,
        averageTime: totalTime / iterations,
        successRate: successCount / iterations,
        throughput: (successCount / totalTime) * 1000
      };
    }
    
    this.log(`📊 性能测试完成`);
    return results;
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.wasmSigner) {
      this.wasmSigner.destroy();
      this.wasmSigner = null;
    }
    
    this.fallbackSigner = null;
    this.currentMode = null;
    this.isInitialized = false;
    this.initializationPromise = null;
    
    this.log('🧹 统一签名接口资源已清理');
  }

  /**
   * 睡眠函数
   * 
   * @param {number} ms - 睡眠时间（毫秒）
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 日志输出函数
   * 
   * @param {string} message - 日志消息
   */
  log(message) {
    if (this.options.debug) {
      console.log(`[UnifiedSignature] ${message}`);
    }
  }
}

/**
 * 创建统一签名实例的工厂函数
 * 
 * @param {Object} options - 配置选项
 * @returns {UnifiedSignature} 统一签名实例
 */
function createUnifiedSignature(options = {}) {
  return new UnifiedSignature(options);
}

/**
 * 全局单例实例
 */
let globalInstance = null;

/**
 * 获取全局单例实例
 * 
 * @param {Object} options - 配置选项（仅在首次调用时有效）
 * @returns {UnifiedSignature} 全局实例
 */
function getGlobalInstance(options = {}) {
  if (!globalInstance) {
    globalInstance = new UnifiedSignature(options);
  }
  return globalInstance;
}

/**
 * 快速签名函数
 * 使用全局实例生成签名
 *
 * @param {string} param1 - 第一个参数
 * @param {string} param2 - 第二个参数
 * @param {Object} options - 配置选项
 * @returns {Promise<string>} 生成的签名
 */
async function quickSign(param1, param2, options = {}) {
  const instance = getGlobalInstance({
    preferWasm: false, // 默认使用备用实现，更可靠
    enableFallback: true,
    ...options
  });

  // 确保实例已初始化
  if (!instance.isInitialized) {
    await instance.initialize();
  }

  return instance.generateSignature(param1, param2);
}

module.exports = {
  UnifiedSignature,
  createUnifiedSignature,
  getGlobalInstance,
  quickSign
};
