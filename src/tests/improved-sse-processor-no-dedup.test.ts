/**
 * 测试改进的 SSE 处理器 - 验证去重逻辑已移除
 */

import { ImprovedSSEProcessor } from '../services/improved-sse-processor';
import { ChatCallbacks } from '../types';
import { Readable } from 'stream';

describe('ImprovedSSEProcessor - 无去重测试', () => {
  let processor: ImprovedSSEProcessor;
  let mockCallbacks: ChatCallbacks;
  let receivedMessages: any[];

  beforeEach(() => {
    processor = new ImprovedSSEProcessor();
    receivedMessages = [];
    
    mockCallbacks = {
      onMessage: jest.fn((content, data) => {
        receivedMessages.push({ content, data });
      }),
      onComplete: jest.fn(),
      onError: jest.fn()
    };
  });

  test('应该处理重复的消息而不跳过', async () => {
    // 创建包含重复消息的 SSE 数据流
    const sseData = [
      'event: conversation.message.delta',
      'data: {"id":"msg1","content":"Hello","content_type":"text","role":"assistant"}',
      '',
      'event: conversation.message.delta', 
      'data: {"id":"msg1","content":"Hello","content_type":"text","role":"assistant"}',
      '',
      'event: conversation.message.delta',
      'data: {"id":"msg1","content":"World","content_type":"text","role":"assistant"}',
      '',
      'event: conversation.chat.completed',
      'data: {"id":"chat1","conversation_id":"conv1"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证所有消息都被处理，包括重复的消息
    expect(receivedMessages).toHaveLength(3);
    expect(receivedMessages[0].content).toBe('Hello');
    expect(receivedMessages[1].content).toBe('Hello'); // 重复消息也应该被处理
    expect(receivedMessages[2].content).toBe('World');

    // 验证完成回调被调用（可能被调用多次，这是正常的）
    expect(mockCallbacks.onComplete).toHaveBeenCalled();
  });

  test('应该处理相同ID但不同content_type的消息', async () => {
    const sseData = [
      'event: conversation.message.delta',
      'data: {"id":"msg1","content":"思考中...","content_type":"thinking","role":"assistant"}',
      '',
      'event: conversation.message.delta',
      'data: {"id":"msg1","content":"Hello","content_type":"text","role":"assistant"}',
      '',
      'event: conversation.message.delta',
      'data: {"id":"msg1","content":"进度更新","content_type":"progress","role":"assistant"}',
      '',
      'event: conversation.chat.completed',
      'data: {"id":"chat1","conversation_id":"conv1"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证所有不同类型的消息都被处理
    expect(receivedMessages).toHaveLength(3);
    expect(receivedMessages[0].data.content_type).toBe('thinking');
    expect(receivedMessages[1].data.content_type).toBe('text');
    expect(receivedMessages[2].data.content_type).toBe('progress');
  });

  test('应该处理完全相同的消息多次', async () => {
    const messageData = '{"id":"msg1","content":"重复内容","content_type":"text","role":"assistant"}';
    
    const sseData = [
      'event: conversation.message.delta',
      `data: ${messageData}`,
      '',
      'event: conversation.message.delta',
      `data: ${messageData}`,
      '',
      'event: conversation.message.delta',
      `data: ${messageData}`,
      '',
      'event: conversation.chat.completed',
      'data: {"id":"chat1","conversation_id":"conv1"}',
      ''
    ].join('\n');

    const stream = Readable.from([sseData]);

    await processor.processStream(stream, mockCallbacks);

    // 验证完全相同的消息被处理了3次
    expect(receivedMessages).toHaveLength(3);
    receivedMessages.forEach(msg => {
      expect(msg.content).toBe('重复内容');
      expect(msg.data.id).toBe('msg1');
      expect(msg.data.content_type).toBe('text');
    });
  });

  test('重置处理器后应该能正常处理消息', () => {
    // 重置处理器
    processor.reset();

    // 验证重置后处理器状态正常
    expect(() => processor.reset()).not.toThrow();
  });
});
