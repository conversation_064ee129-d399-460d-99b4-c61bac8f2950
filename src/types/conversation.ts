/**
 * 对话相关类型定义
 * 定义对话创建、管理和配置相关的数据结构
 */

/**
 * 聊天模型配置
 */
export interface ChatModelConfig {
  /** 模型名称 */
  model: string;
  /** 模型选项 */
  options: unknown[];
}

/**
 * 多模型布局配置
 */
export interface MultiModelLayout {
  /** 活跃对话列表 */
  activeConversation: Array<{
    /** 对话ID */
    conversationId: string;
    /** 是否可见 */
    isVisible: boolean;
    /** 排序顺序 */
    order: number;
  }>;
  /** 最后更新时间 */
  lastUpdateTime: string;
}

/**
 * 对话元数据
 */
export interface ConversationMetaData {
  /** 写代码相关配置 */
  writeCode: string;
  /** 聊天模型配置 */
  chatModelConfig: ChatModelConfig;
  /** 多模型布局配置 */
  multiModelLayout: MultiModelLayout | null;
  /** 超级代理路径 */
  superAgentPath: string;
  /** 页面类型 */
  pageType: string | null;
}

/**
 * 对话信息
 */
export interface ConversationInfo {
  /** 对话唯一标识符 */
  conversationId: string;
  /** 对话类型 */
  conversationType: number;
  /** 对话标题 */
  title: string;
  /** 用户ID */
  userId: string | null;
  /** 设备ID */
  deviceId: string;
  /** 标题摘要标志 */
  titleSummaryFlag: number;
  /** 对话元数据 */
  metaData: ConversationMetaData;
  /** 是否匿名 */
  isAnonymous: boolean;
  /** 匿名密钥 */
  anonymousKey: string;
  /** 最后使用的聊天模型 */
  lastChatModel: string | null;
  /** 子对话列表 */
  conversationList: ConversationInfo[] | null;
}

/**
 * 创建对话请求参数
 */
export interface CreateConversationRequest {
  /** 对话列表 */
  conversationList: Array<{
    /** 元数据 */
    metaData: {
      /** 聊天模型配置 */
      chatModelConfig: Record<string, unknown>;
      /** 超级代理路径 */
      superAgentPath: string;
    };
    /** 分享ID */
    shareId: string;
    /** 是否匿名 */
    isAnonymous: boolean;
    /** 来源 */
    source: string;
  }>;
}

/**
 * 创建对话响应数据
 */
export interface CreateConversationResponse {
  /** 对话信息 */
  conversationId: string;
  /** 对话类型 */
  conversationType: number;
  /** 对话标题 */
  title: string;
  /** 用户ID */
  userId: string | null;
  /** 设备ID */
  deviceId: string;
  /** 标题摘要标志 */
  titleSummaryFlag: number;
  /** 对话元数据 */
  metaData: ConversationMetaData;
  /** 是否匿名 */
  isAnonymous: boolean;
  /** 匿名密钥 */
  anonymousKey: string;
  /** 最后使用的聊天模型 */
  lastChatModel: string | null;
  /** 对话列表 */
  conversationList: ConversationInfo[];
}
