/**
 * 当贝 AI /v2/chat 签名候选枚举脚本（多样本）
 * - 基于 wasm get_sign(n,o,i,a) 入口与真实抓包样本，自动尝试多种组合
 * - 目标：找出对多样本都成立的公式
 */

const crypto = require('crypto');

function u32le(n) {
  const b = Buffer.allocUnsafe(4);
  b.writeUInt32LE(n >>> 0, 0);
  return b;
}

function i32le(n) {
  const b = Buffer.allocUnsafe(4);
  b.writeInt32LE(n | 0, 0);
  return b;
}

function md5Upp(input) {
  if (Buffer.isBuffer(input)) return crypto.createHash('md5').update(input).digest('hex').toUpperCase();
  return crypto.createHash('md5').update(String(input)).digest('hex').toUpperCase();
}
function md5Low(input) {
  if (Buffer.isBuffer(input)) return crypto.createHash('md5').update(input).digest('hex');
  return crypto.createHash('md5').update(String(input)).digest('hex');
}

// 样本集合（可继续添加）
const samples = [
  {
    name: '样本#1',
    urlPath: '/chatApi/v2/chat',
    requestData: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"364120755967492485","question":"使用Agno框架进行多Agent协作时如何进行共享上下文？","model":"kimi-k2-0711-preview","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"364270491870957765","chatId":"364270491870957765","files":[],"reference":[],"role":"user","status":"local","content":"使用Agno框架进行多Agent协作时如何进行共享上下文？","userAction":"","agentId":""}',
    timestamp: 1755834473,
    nonce: 'nhwYHmTJgwTiQtchXghtA',
    expected: '63334E59ECA2FC0ABDBC3BFF6CD4A30D',
    // 根据上游提供的多处样本，指针可能如下两种之一（取并集尝试）
    ptrCandidates: [
      { n: 1115144, i: 1114120 },
      { n: 1114120, i: 1114672 }
    ]
  },
  {
    name: '样本#2',
    urlPath: '/chatApi/v2/chat',
    requestData: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"364297814148776325","question":"怎么实现一个不依赖大模型能力的 agent, 也就是支持各种大模型","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"364297832057802949","chatId":"364297832057802949","files":[],"reference":[],"role":"user","status":"local","content":"怎么实现一个不依赖大模型能力的 agent, 也就是支持各种大模型","userAction":"deep","agentId":""}',
    timestamp: 1755847191,
    nonce: 'FuJA-9WY_YJ7F4CyWPMdf',
    expected: '03664C2C56D8ADE666D2DB54C0570981'
  }
];

function runSample(s) {
  const ptrs = s.ptrCandidates || [{n:0,i:0}];
  const { urlPath, requestData, timestamp, nonce, expected } = s;
  const oLen = Buffer.byteLength(requestData, 'utf8');
  const aLen = Buffer.byteLength(urlPath, 'utf8');
  const bodyBytes = Buffer.from(requestData, 'utf8');
  const pathBytes = Buffer.from(urlPath, 'utf8');

  const lstr = `${oLen}:${aLen}`;
  const md5Body = md5Low(requestData);
  const md5Path = md5Low(urlPath);
  const md5BodyPath = md5Low(requestData + urlPath);

  const tsNonceBytes = Buffer.from(String(timestamp) + nonce, 'utf8');

  const tries = {}; // name -> sig

  // 指针相关候选：在 payload 中夹入指针（u32le）
  for (const {n,i} of ptrs) {
    const nBuf = u32le(n >>> 0);
    const iBuf = u32le(i >>> 0);
    tries[`MD5([u32(n),u32(o),u32(i),u32(a),body,path])`] = md5Upp(Buffer.concat([nBuf, u32le(oLen), iBuf, u32le(aLen), bodyBytes, pathBytes]));
    tries[`MD5([u32(o),u32(a),u32(n),u32(i),body,path])`] = md5Upp(Buffer.concat([u32le(oLen), u32le(aLen), nBuf, iBuf, bodyBytes, pathBytes]));
    tries[`MD5([u32(n),u32(i),u32(o),u32(a),body,path])`] = md5Upp(Buffer.concat([nBuf, iBuf, u32le(oLen), u32le(aLen), bodyBytes, pathBytes]));
  }

  // 基础字符串组合
  tries['MD5(body+path)'] = md5Upp(requestData + urlPath);
  tries['MD5(path+body)'] = md5Upp(urlPath + requestData);
  tries['MD5(lstr + ":" + md5(body+path))'] = md5Upp(lstr + ':' + md5BodyPath);
  tries['MD5(md5(body) + md5(path))'] = md5Upp(md5Body + md5Path);
  tries['MD5(lstr + md5(body+path))'] = md5Upp(lstr + md5BodyPath);

  // 加入 ts/nonce（字符串拼接）
  tries['MD5(md5(body+path) + ts + nonce)'] = md5Upp(md5BodyPath + timestamp + nonce);
  tries['MD5(lstr + md5(body+path) + ts + nonce)'] = md5Upp(lstr + md5BodyPath + timestamp + nonce);
  tries['MD5(lstr + ":" + md5(body+path) + ":" + ts + ":" + nonce)'] = md5Upp(`${lstr}:${md5BodyPath}:${timestamp}:${nonce}`);
  tries['MD5(body+path+ts+nonce)'] = md5Upp(requestData + urlPath + timestamp + nonce);
  tries['MD5(path+body+ts+nonce)'] = md5Upp(urlPath + requestData + timestamp + nonce);

  // 原始字节组合：小端长度 + 原始字节
  tries['MD5([u32le(o),u32le(a),body,path])'] = md5Upp(Buffer.concat([u32le(oLen), u32le(aLen), bodyBytes, pathBytes]));
  tries['MD5([u32le(o),body,u32le(a),path])'] = md5Upp(Buffer.concat([u32le(oLen), bodyBytes, u32le(aLen), pathBytes]));
  tries['MD5([body,u32le(o),path,u32le(a)])'] = md5Upp(Buffer.concat([bodyBytes, u32le(oLen), pathBytes, u32le(aLen)]));

  // 加入 ts/nonce 到末尾（ASCII 字节）
  tries['MD5([u32le(o),u32le(a),body,path,ts+nonce])'] = md5Upp(Buffer.concat([u32le(oLen), u32le(aLen), bodyBytes, pathBytes, tsNonceBytes]));
  tries['MD5([u32le(o),body,u32le(a),path,ts+nonce])'] = md5Upp(Buffer.concat([u32le(oLen), bodyBytes, u32le(aLen), pathBytes, tsNonceBytes]));
  tries['MD5([body,path,ts+nonce])'] = md5Upp(Buffer.concat([bodyBytes, pathBytes, tsNonceBytes]));

  // 引入 i32 变体
  tries['MD5([i32le(o),i32le(a),body,path])'] = md5Upp(Buffer.concat([i32le(oLen), i32le(aLen), bodyBytes, pathBytes]));

  // 二次 MD5 串联
  const h1 = md5Low(Buffer.concat([u32le(oLen), u32le(aLen), bodyBytes, pathBytes]));
  tries['MD5(h1 + ts + nonce)'] = md5Upp(h1 + timestamp + nonce);
  tries['MD5(h1 + ":" + ts + ":" + nonce)'] = md5Upp(`${h1}:${timestamp}:${nonce}`);

  // 仅 body / 长度
  tries['MD5([u32le(o),u32le(a),body])'] = md5Upp(Buffer.concat([u32le(oLen), u32le(aLen), bodyBytes]));
  tries['MD5([u32le(o),body])'] = md5Upp(Buffer.concat([u32le(oLen), bodyBytes]));
  tries['MD5([body])'] = md5Upp(bodyBytes);

  // 其他套路
  tries['MD5(ts + body + nonce)'] = md5Upp(String(timestamp) + requestData + nonce);
  tries['MD5(ts + md5(body) + nonce)'] = md5Upp(String(timestamp) + md5Body + nonce);
  tries['MD5(ts + md5(body+path) + nonce)'] = md5Upp(String(timestamp) + md5BodyPath + nonce);

  // 拆段 md5 再拼：hBody/hPath
  const hBody_u32 = md5Low(Buffer.concat([u32le(oLen), bodyBytes]));
  const hPath_u32 = md5Low(Buffer.concat([u32le(aLen), pathBytes]));
  const hBody_raw = md5Low(bodyBytes);
  const hPath_raw = md5Low(pathBytes);

  tries['MD5(hBody_u32 + hPath_u32 + ts + nonce)'] = md5Upp(hBody_u32 + hPath_u32 + timestamp + nonce);
  tries['MD5(hBody_u32 + hPath_u32)'] = md5Upp(hBody_u32 + hPath_u32);
  tries['MD5(hBody_raw + hPath_raw + ts + nonce)'] = md5Upp(hBody_raw + hPath_raw + timestamp + nonce);
  tries['MD5(hBody_raw + hPath_raw)'] = md5Upp(hBody_raw + hPath_raw);
  tries['MD5(hBody_u32 + ":" + hPath_u32 + ":" + ts + ":" + nonce)'] = md5Upp(`${hBody_u32}:${hPath_u32}:${timestamp}:${nonce}`);
  tries['MD5(hBody_raw + ":" + hPath_raw + ":" + ts + ":" + nonce)'] = md5Upp(`${hBody_raw}:${hPath_raw}:${timestamp}:${nonce}`);

  // 引入 'v2' 作为常量
  tries['MD5(hBody_u32 + hPath_u32 + v2 + ts + nonce)'] = md5Upp(hBody_u32 + hPath_u32 + 'v2' + timestamp + nonce);
  tries['MD5(h1 + v2 + ts + nonce)'] = md5Upp(h1 + 'v2' + timestamp + nonce);

  const hits = Object.entries(tries).filter(([, sig]) => sig === expected);
  console.log(`\n[${s.name}] oLen=${oLen}, aLen=${aLen}, ts=${timestamp}, nonce=${nonce}`);
  console.log('  期望:', expected);
  if (hits.length === 0) {
    console.log('  ⚠️ 未命中');
  } else {
    for (const [name, sig] of hits) {
      console.log('  ✅ 命中:', name, '=>', sig);
    }
  }
}

console.log('==== v2/chat 多样本签名候选枚举 ====' );
for (const s of samples) runSample(s);
console.log('==== 枚举完成 ====' );

