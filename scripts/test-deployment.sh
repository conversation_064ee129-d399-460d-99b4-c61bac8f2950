#!/bin/bash
# 当贝AI聊天界面部署测试快速运行脚本
# 提供简化的测试命令和选项

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_DIR="$PROJECT_ROOT/tests/deployment"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
当贝AI聊天界面部署测试快速运行脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -a, --all               运行所有测试 (默认)
  -s, --static            仅运行静态部署测试
  -c, --config            仅运行配置文件测试
  -q, --quick             快速测试 (跳过性能和CDN测试)
  -v, --verbose           详细输出模式
  --clean                 清理测试结果
  --setup                 设置测试环境
  --check                 检查测试依赖

测试类型:
  static                  静态文件结构和HTTP服务测试
  config                  Caddy和Nginx配置文件验证
  all                     运行所有测试并生成报告

示例:
  $0                      运行所有测试
  $0 --static             仅测试静态部署
  $0 --config             仅测试配置文件
  $0 --quick              快速测试
  $0 --clean              清理测试结果
  $0 --setup              设置测试环境

EOF
}

# 检查测试依赖
check_dependencies() {
    log_header "🔍 检查测试依赖..."
    
    local missing_deps=()
    
    # 检查必需工具
    local required_tools=("curl" "python3" "bash")
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_info "✅ $tool 已安装"
        else
            log_error "❌ $tool 未安装"
            missing_deps+=("$tool")
        fi
    done
    
    # 检查可选工具
    local optional_tools=("node" "bc" "caddy" "nginx")
    for tool in "${optional_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_info "✅ $tool 已安装 (可选)"
        else
            log_warn "⚠️  $tool 未安装 (可选，某些测试可能跳过)"
        fi
    done
    
    # 检查项目结构
    if [ ! -d "$PROJECT_ROOT/public/chat" ]; then
        log_error "❌ 项目结构不正确，找不到 public/chat 目录"
        missing_deps+=("project-structure")
    else
        log_info "✅ 项目结构正确"
    fi
    
    if [ ! -d "$PROJECT_ROOT/deployment" ]; then
        log_error "❌ 找不到 deployment 目录"
        missing_deps+=("deployment-configs")
    else
        log_info "✅ 部署配置目录存在"
    fi
    
    # 检查测试脚本
    if [ ! -f "$TEST_DIR/run-all-tests.sh" ]; then
        log_error "❌ 找不到测试运行器"
        missing_deps+=("test-runner")
    else
        log_info "✅ 测试脚本存在"
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        echo ""
        log_info "安装建议:"
        echo "  # Ubuntu/Debian"
        echo "  sudo apt update && sudo apt install curl python3 bc"
        echo ""
        echo "  # CentOS/RHEL"
        echo "  sudo yum install curl python3 bc"
        echo ""
        echo "  # macOS"
        echo "  brew install curl python3 bc"
        return 1
    else
        log_info "✅ 所有必需依赖已满足"
        return 0
    fi
}

# 设置测试环境
setup_test_environment() {
    log_header "🔧 设置测试环境..."
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 确保测试脚本可执行
    chmod +x "$TEST_DIR"/*.sh
    log_info "✅ 测试脚本权限已设置"
    
    # 创建测试结果目录
    mkdir -p test-results
    log_info "✅ 测试结果目录已创建"
    
    # 检查端口占用
    if lsof -Pi :8888 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 8888 被占用，测试可能会自动处理"
    fi
    
    log_info "✅ 测试环境设置完成"
}

# 清理测试结果
clean_test_results() {
    log_header "🧹 清理测试结果..."
    
    cd "$PROJECT_ROOT"
    
    if [ -d "test-results" ]; then
        rm -rf test-results
        log_info "✅ 测试结果目录已清理"
    else
        log_info "测试结果目录不存在，无需清理"
    fi
    
    # 停止可能的测试服务器
    pkill -f "python.*8888" 2>/dev/null || true
    log_info "✅ 测试服务器已停止"
}

# 运行静态部署测试
run_static_test() {
    log_header "🌐 运行静态部署测试..."
    
    cd "$PROJECT_ROOT"
    
    if [ -f "$TEST_DIR/test-static-deployment.sh" ]; then
        "$TEST_DIR/test-static-deployment.sh"
    else
        log_error "静态部署测试脚本不存在"
        return 1
    fi
}

# 运行配置文件测试
run_config_test() {
    log_header "⚙️ 运行配置文件测试..."
    
    cd "$PROJECT_ROOT"
    
    if [ -f "$TEST_DIR/test-deployment-configs.sh" ]; then
        "$TEST_DIR/test-deployment-configs.sh"
    else
        log_error "配置文件测试脚本不存在"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    log_header "🚀 运行所有部署测试..."
    
    cd "$PROJECT_ROOT"
    
    if [ -f "$TEST_DIR/run-all-tests.sh" ]; then
        "$TEST_DIR/run-all-tests.sh" "$@"
    else
        log_error "测试运行器不存在"
        return 1
    fi
}

# 快速测试 (跳过耗时的测试)
run_quick_test() {
    log_header "⚡ 运行快速测试..."
    
    log_info "运行配置文件测试..."
    if run_config_test; then
        log_info "✅ 配置文件测试通过"
    else
        log_error "❌ 配置文件测试失败"
        return 1
    fi
    
    log_info "运行基础静态测试..."
    # 这里可以添加简化的静态测试逻辑
    # 暂时使用完整的静态测试
    if run_static_test; then
        log_info "✅ 静态部署测试通过"
    else
        log_error "❌ 静态部署测试失败"
        return 1
    fi
    
    log_info "✅ 快速测试完成"
}

# 显示测试结果
show_test_results() {
    log_header "📊 测试结果摘要"
    
    cd "$PROJECT_ROOT"
    
    if [ -d "test-results" ]; then
        echo "测试结果目录: test-results/"
        
        if [ -f "test-results/test-report.md" ]; then
            log_info "📄 详细报告: test-results/test-report.md"
        fi
        
        # 显示日志文件
        for log_file in test-results/*.log; do
            if [ -f "$log_file" ]; then
                local test_name=$(basename "$log_file" .log)
                if grep -q "所有测试通过\|所有配置测试通过" "$log_file" 2>/dev/null; then
                    log_info "✅ $test_name: 通过"
                else
                    log_error "❌ $test_name: 失败"
                fi
            fi
        done
    else
        log_warn "未找到测试结果"
    fi
}

# 主函数
main() {
    local action="all"
    local verbose=false
    local quick=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                action="all"
                shift
                ;;
            -s|--static)
                action="static"
                shift
                ;;
            -c|--config)
                action="config"
                shift
                ;;
            -q|--quick)
                quick=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --clean)
                clean_test_results
                exit 0
                ;;
            --setup)
                setup_test_environment
                exit 0
                ;;
            --check)
                check_dependencies
                exit $?
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示开始信息
    log_header "🎯 当贝AI聊天界面部署测试"
    echo "项目路径: $PROJECT_ROOT"
    echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    # 检查依赖
    if ! check_dependencies; then
        log_error "依赖检查失败，请安装缺失的依赖后重试"
        exit 1
    fi
    echo ""
    
    # 设置测试环境
    setup_test_environment
    echo ""
    
    # 执行测试
    local test_result=0
    
    case $action in
        "static")
            run_static_test || test_result=1
            ;;
        "config")
            run_config_test || test_result=1
            ;;
        "all")
            if [ "$quick" = true ]; then
                run_quick_test || test_result=1
            else
                run_all_tests || test_result=1
            fi
            ;;
        *)
            log_error "未知测试类型: $action"
            exit 1
            ;;
    esac
    
    echo ""
    
    # 显示测试结果
    show_test_results
    
    # 显示最终结果
    echo ""
    if [ $test_result -eq 0 ]; then
        log_header "🎉 测试完成！所有测试通过！"
        log_info "当贝AI聊天界面已准备好进行静态部署"
        echo ""
        log_info "快速部署命令:"
        echo "  # 使用Caddy"
        echo "  sudo cp -r public/chat /var/www/dangbei-chat"
        echo "  sudo cp deployment/caddy/Caddyfile /etc/caddy/Caddyfile"
        echo "  sudo systemctl start caddy"
        echo ""
        echo "  # 使用Nginx"
        echo "  sudo cp -r public/chat /var/www/dangbei-chat"
        echo "  sudo cp deployment/nginx/dangbei-chat.conf /etc/nginx/sites-available/"
        echo "  sudo systemctl start nginx"
    else
        log_header "❌ 测试失败！"
        log_error "请检查测试日志并修复问题后重新运行"
        echo ""
        log_info "查看详细日志:"
        echo "  cat test-results/*.log"
        echo ""
        log_info "重新运行测试:"
        echo "  $0 --$action"
    fi
    
    exit $test_result
}

# 执行主函数
main "$@"
