/**
 * WebAssembly 编译脚本
 * 
 * 这个脚本用于将 WAT (WebAssembly Text) 文件编译为 WASM (WebAssembly Binary) 文件
 * 支持多种编译方式，包括使用 wabt 工具链和在线编译服务
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

/**
 * WebAssembly 编译器类
 * 提供多种方式来编译 WAT 文件为 WASM 文件
 */
class WasmCompiler {
  constructor() {
    this.debug = process.env.NODE_ENV === 'development';
  }

  /**
   * 主编译函数
   * 尝试多种编译方式，直到成功为止
   * 
   * @param {string} watPath - WAT 文件路径
   * @param {string} wasmPath - 输出的 WASM 文件路径
   */
  async compile(watPath, wasmPath) {
    this.log('🔧 开始编译 WebAssembly 文件...');
    this.log(`📁 输入文件: ${watPath}`);
    this.log(`📁 输出文件: ${wasmPath}`);

    // 检查输入文件是否存在
    if (!fs.existsSync(watPath)) {
      throw new Error(`WAT 文件不存在: ${watPath}`);
    }

    // 尝试不同的编译方法
    const methods = [
      () => this.compileWithWabt(watPath, wasmPath),
      () => this.compileWithWat2wasm(watPath, wasmPath),
      () => this.compileWithDocker(watPath, wasmPath),
      () => this.compileWithNodeWasm(watPath, wasmPath)
    ];

    let lastError = null;

    for (let i = 0; i < methods.length; i++) {
      try {
        this.log(`🔄 尝试编译方法 ${i + 1}/${methods.length}...`);
        await methods[i]();
        
        // 验证输出文件
        if (fs.existsSync(wasmPath)) {
          const stats = fs.statSync(wasmPath);
          this.log(`✅ 编译成功! 输出文件大小: ${stats.size} 字节`);
          return;
        }
        
      } catch (error) {
        this.log(`❌ 编译方法 ${i + 1} 失败: ${error.message}`);
        lastError = error;
      }
    }

    // 所有方法都失败了
    throw new Error(`所有编译方法都失败了。最后一个错误: ${lastError?.message}`);
  }

  /**
   * 使用 WABT 工具链编译
   * 需要预先安装 wabt (WebAssembly Binary Toolkit)
   * 
   * @param {string} watPath - WAT 文件路径
   * @param {string} wasmPath - 输出的 WASM 文件路径
   */
  async compileWithWabt(watPath, wasmPath) {
    this.log('🛠️ 使用 WABT 工具链编译...');
    
    try {
      // 检查 wat2wasm 是否可用
      execSync('wat2wasm --version', { stdio: 'ignore' });
      
      // 执行编译
      const command = `wat2wasm "${watPath}" -o "${wasmPath}"`;
      execSync(command, { stdio: 'pipe' });
      
      this.log('✅ WABT 编译成功');
      
    } catch (error) {
      throw new Error(`WABT 编译失败: ${error.message}`);
    }
  }

  /**
   * 使用系统安装的 wat2wasm 编译
   * 
   * @param {string} watPath - WAT 文件路径
   * @param {string} wasmPath - 输出的 WASM 文件路径
   */
  async compileWithWat2wasm(watPath, wasmPath) {
    this.log('🔧 使用系统 wat2wasm 编译...');
    
    return new Promise((resolve, reject) => {
      const process = spawn('wat2wasm', [watPath, '-o', wasmPath], {
        stdio: 'pipe'
      });

      let stderr = '';

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          this.log('✅ wat2wasm 编译成功');
          resolve();
        } else {
          reject(new Error(`wat2wasm 编译失败 (退出码: ${code}): ${stderr}`));
        }
      });

      process.on('error', (error) => {
        reject(new Error(`wat2wasm 执行失败: ${error.message}`));
      });
    });
  }

  /**
   * 使用 Docker 容器编译
   * 适用于没有本地安装 WABT 的情况
   * 
   * @param {string} watPath - WAT 文件路径
   * @param {string} wasmPath - 输出的 WASM 文件路径
   */
  async compileWithDocker(watPath, wasmPath) {
    this.log('🐳 使用 Docker 容器编译...');
    
    try {
      // 检查 Docker 是否可用
      execSync('docker --version', { stdio: 'ignore' });
      
      // 获取绝对路径
      const absoluteWatPath = path.resolve(watPath);
      const absoluteWasmPath = path.resolve(wasmPath);
      const workDir = path.dirname(absoluteWatPath);
      const watFileName = path.basename(absoluteWatPath);
      const wasmFileName = path.basename(absoluteWasmPath);
      
      // 使用 Docker 运行 WABT
      const command = `docker run --rm -v "${workDir}:/work" -w /work wabt/wabt:latest wat2wasm "${watFileName}" -o "${wasmFileName}"`;
      execSync(command, { stdio: 'pipe' });
      
      this.log('✅ Docker 编译成功');
      
    } catch (error) {
      throw new Error(`Docker 编译失败: ${error.message}`);
    }
  }

  /**
   * 使用 Node.js WASM 模块编译
   * 使用纯 JavaScript 实现的 WAT 解析器
   * 
   * @param {string} watPath - WAT 文件路径
   * @param {string} wasmPath - 输出的 WASM 文件路径
   */
  async compileWithNodeWasm(watPath, wasmPath) {
    this.log('📦 使用 Node.js WASM 模块编译...');
    
    try {
      // 尝试使用 wabt.js (如果已安装)
      const wabt = require('wabt');
      
      // 读取 WAT 文件
      const watSource = fs.readFileSync(watPath, 'utf8');
      
      // 解析和编译
      const wasmModule = wabt.parseWat('module.wat', watSource);
      const { buffer } = wasmModule.toBinary({});
      
      // 写入 WASM 文件
      fs.writeFileSync(wasmPath, buffer);
      
      this.log('✅ Node.js WASM 模块编译成功');
      
    } catch (error) {
      throw new Error(`Node.js WASM 模块编译失败: ${error.message}`);
    }
  }

  /**
   * 验证 WASM 文件
   * 检查生成的 WASM 文件是否有效
   * 
   * @param {string} wasmPath - WASM 文件路径
   * @returns {boolean} 是否有效
   */
  async validateWasm(wasmPath) {
    this.log('🔍 验证 WASM 文件...');
    
    try {
      if (!fs.existsSync(wasmPath)) {
        throw new Error('WASM 文件不存在');
      }

      // 读取 WASM 文件
      const wasmBuffer = fs.readFileSync(wasmPath);
      
      // 检查 WASM 魔数 (0x00 0x61 0x73 0x6D)
      if (wasmBuffer.length < 4) {
        throw new Error('WASM 文件太小');
      }

      const magic = wasmBuffer.slice(0, 4);
      const expectedMagic = Buffer.from([0x00, 0x61, 0x73, 0x6D]);
      
      if (!magic.equals(expectedMagic)) {
        throw new Error('WASM 文件魔数不正确');
      }

      // 尝试编译模块以验证格式
      await WebAssembly.compile(wasmBuffer);
      
      this.log('✅ WASM 文件验证成功');
      return true;
      
    } catch (error) {
      this.log(`❌ WASM 文件验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取 WASM 文件信息
   * 
   * @param {string} wasmPath - WASM 文件路径
   * @returns {Object} 文件信息
   */
  async getWasmInfo(wasmPath) {
    try {
      const wasmBuffer = fs.readFileSync(wasmPath);
      const wasmModule = await WebAssembly.compile(wasmBuffer);
      
      // 获取导出信息
      const exports = WebAssembly.Module.exports(wasmModule);
      const imports = WebAssembly.Module.imports(wasmModule);
      
      const info = {
        size: wasmBuffer.length,
        exports: exports.map(exp => ({ name: exp.name, kind: exp.kind })),
        imports: imports.map(imp => ({ module: imp.module, name: imp.name, kind: imp.kind })),
        exportsCount: exports.length,
        importsCount: imports.length
      };

      this.log('📊 WASM 文件信息:');
      this.log(`   文件大小: ${info.size} 字节`);
      this.log(`   导出数量: ${info.exportsCount}`);
      this.log(`   导入数量: ${info.importsCount}`);
      
      return info;
      
    } catch (error) {
      this.log(`❌ 获取 WASM 文件信息失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 日志输出函数
   * 
   * @param {string} message - 日志消息
   */
  log(message) {
    if (this.debug) {
      console.log(`[WasmCompiler] ${message}`);
    }
  }
}

/**
 * 主函数
 * 处理命令行参数并执行编译
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log('使用方法: node compile-wasm.js <wat-file> [wasm-file]');
    console.log('');
    console.log('参数:');
    console.log('  wat-file   输入的 WAT 文件路径');
    console.log('  wasm-file  输出的 WASM 文件路径 (可选，默认为同名 .wasm 文件)');
    console.log('');
    console.log('示例:');
    console.log('  node compile-wasm.js sign_bg.wat');
    console.log('  node compile-wasm.js sign_bg.wat sign_bg.wasm');
    process.exit(1);
  }

  const watPath = args[0];
  const wasmPath = args[1] || watPath.replace(/\.wat$/, '.wasm');

  const compiler = new WasmCompiler();

  try {
    // 编译 WASM 文件
    await compiler.compile(watPath, wasmPath);
    
    // 验证编译结果
    const isValid = await compiler.validateWasm(wasmPath);
    if (!isValid) {
      throw new Error('编译的 WASM 文件验证失败');
    }
    
    // 显示文件信息
    await compiler.getWasmInfo(wasmPath);
    
    console.log('\n🎉 编译完成!');
    console.log(`📁 输出文件: ${wasmPath}`);
    
  } catch (error) {
    console.error('\n💥 编译失败:');
    console.error(`   ${error.message}`);
    
    console.log('\n🔧 故障排除建议:');
    console.log('   1. 安装 WABT 工具链: https://github.com/WebAssembly/wabt');
    console.log('   2. 或者安装 Docker: https://www.docker.com/');
    console.log('   3. 或者安装 wabt npm 包: npm install wabt');
    console.log('   4. 检查 WAT 文件语法是否正确');
    
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = WasmCompiler;
