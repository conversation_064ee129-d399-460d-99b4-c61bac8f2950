#!/usr/bin/env node

/**
 * 当贝AI Provider SDK 构建脚本
 * 编译TypeScript代码并复制静态资源
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 递归复制目录
 */
function copyDirectory(src, dest, excludePatterns = []) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    // 检查是否应该排除此文件/目录
    const shouldExclude = excludePatterns.some(pattern => {
      if (typeof pattern === 'string') {
        return entry.name === pattern;
      } else if (pattern instanceof RegExp) {
        return pattern.test(entry.name);
      }
      return false;
    });

    if (shouldExclude) {
      continue;
    }

    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath, excludePatterns);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

/**
 * 复制文件
 */
function copyFile(src, dest) {
  const destDir = path.dirname(dest);
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }
  fs.copyFileSync(src, dest);
}

/**
 * 主构建函数
 */
async function build() {
  try {
    colorLog('cyan', '🚀 开始构建当贝AI Provider SDK...');
    
    // 1. 清理dist目录
    colorLog('blue', '📁 清理dist目录...');
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    
    // 2. 编译TypeScript
    colorLog('blue', '🔨 编译TypeScript代码...');
    execSync('npx tsc', { stdio: 'inherit' });
    
    // 3. 复制WASM静态资源
    colorLog('blue', '📦 复制WASM静态资源...');
    const wasmSrcDir = 'src/wasm';
    const wasmDestDir = 'dist/wasm';
    
    if (fs.existsSync(wasmSrcDir)) {
      // 复制整个wasm目录
      copyDirectory(wasmSrcDir, wasmDestDir);
      colorLog('green', '✅ WASM资源复制完成');
      
      // 列出复制的文件
      const wasmFiles = fs.readdirSync(wasmDestDir);
      wasmFiles.forEach(file => {
        console.log(`   - ${file}`);
      });
    } else {
      colorLog('yellow', '⚠️ 未找到WASM资源目录');
    }
    
    // 4. 复制服务器静态资源
    colorLog('blue', '🌐 复制服务器静态资源...');
    const serverStaticFiles = [
      'src/server/api-test-page.html',
      'src/server/test-api-params.json',
      'src/server/README-测试工具.md',
      'src/server/生成内容总结.md'
    ];
    
    let copiedServerFiles = 0;
    for (const file of serverStaticFiles) {
      if (fs.existsSync(file)) {
        const destFile = file.replace('src/', 'dist/');
        copyFile(file, destFile);
        console.log(`   - ${path.basename(file)}`);
        copiedServerFiles++;
      }
    }
    
    if (copiedServerFiles > 0) {
      colorLog('green', `✅ 服务器静态资源复制完成 (${copiedServerFiles}个文件)`);
    } else {
      colorLog('yellow', '⚠️ 未找到服务器静态资源');
    }
    
    // 5. 构建并复制聊天界面静态资源
    colorLog('blue', '🌐 构建并复制聊天界面静态资源...');

    let copiedPublicFiles = 0;

    // 5.1 复制基础聊天界面 (无需构建)
    const chatSrc = 'public/chat';
    const chatDest = 'dist/public/chat';
    if (fs.existsSync(chatSrc)) {
      const excludePatterns = ['.DS_Store', /\.log$/, /\.tmp$/];
      copyDirectory(chatSrc, chatDest, excludePatterns);
      const fileCount = countFilesInDirectory(chatDest);
      console.log(`   - ${chatSrc} → ${chatDest} (${fileCount}个文件)`);
      copiedPublicFiles += fileCount;
    }

    // 5.2 构建并复制Material-UI聊天界面
    const muiChatSrc = 'public/mui-chat';
    const muiChatDest = 'dist/public/mui-chat';
    if (fs.existsSync(muiChatSrc)) {
      colorLog('blue', '   🔨 构建Material-UI聊天界面...');

      try {
        // 检查是否已安装依赖
        const nodeModulesPath = path.join(muiChatSrc, 'node_modules');
        if (!fs.existsSync(nodeModulesPath)) {
          colorLog('blue', '   📦 安装Material-UI聊天界面依赖...');
          execSync('npm install', {
            cwd: muiChatSrc,
            stdio: 'pipe'
          });
        }

        // 清理旧的构建产物并构建项目
        const muiBuildSrc = path.join(muiChatSrc, 'dist');
        if (fs.existsSync(muiBuildSrc)) {
          fs.rmSync(muiBuildSrc, { recursive: true, force: true });
        }

        // 构建项目
        execSync('npm run build', {
          cwd: muiChatSrc,
          stdio: 'pipe'
        });

        // 复制构建产物
        if (fs.existsSync(muiBuildSrc)) {
          copyDirectory(muiBuildSrc, muiChatDest);
          const fileCount = countFilesInDirectory(muiChatDest);
          console.log(`   - ${muiBuildSrc} → ${muiChatDest} (${fileCount}个文件)`);
          copiedPublicFiles += fileCount;
        } else {
          colorLog('yellow', '   ⚠️ Material-UI聊天界面构建失败，未找到dist目录');
        }
      } catch (error) {
        colorLog('yellow', `   ⚠️ Material-UI聊天界面构建失败: ${error.message}`);
        colorLog('blue', '   📋 尝试复制源码文件作为备选方案...');

        // 备选方案：复制必要的源码文件
        const fallbackFiles = ['index.html', 'package.json', 'README.md'];
        let fallbackCount = 0;

        if (!fs.existsSync(muiChatDest)) {
          fs.mkdirSync(muiChatDest, { recursive: true });
        }

        for (const file of fallbackFiles) {
          const srcFile = path.join(muiChatSrc, file);
          const destFile = path.join(muiChatDest, file);
          if (fs.existsSync(srcFile)) {
            fs.copyFileSync(srcFile, destFile);
            fallbackCount++;
          }
        }

        console.log(`   - ${muiChatSrc} → ${muiChatDest} (${fallbackCount}个备选文件)`);
        copiedPublicFiles += fallbackCount;
      }
    }

    // 5.3 复制API测试工具 (无需构建)
    const apiTesterSrc = 'public/api-tester';
    const apiTesterDest = 'dist/public/api-tester';
    if (fs.existsSync(apiTesterSrc)) {
      const excludePatterns = ['node_modules', 'package-lock.json', '.DS_Store'];
      copyDirectory(apiTesterSrc, apiTesterDest, excludePatterns);
      const fileCount = countFilesInDirectory(apiTesterDest);
      console.log(`   - ${apiTesterSrc} → ${apiTesterDest} (${fileCount}个文件)`);
      copiedPublicFiles += fileCount;
    }

    if (copiedPublicFiles > 0) {
      colorLog('green', `✅ 聊天界面静态资源复制完成 (${copiedPublicFiles}个文件)`);
    } else {
      colorLog('yellow', '⚠️ 未找到聊天界面静态资源');
    }

    // 6. 复制根目录重要文件
    colorLog('blue', '📄 复制根目录重要文件...');
    const rootFiles = [
      'package.json',
      'README.md',
      'models.json'
    ];

    let copiedRootFiles = 0;
    for (const file of rootFiles) {
      if (fs.existsSync(file)) {
        copyFile(file, `dist/${file}`);
        console.log(`   - ${file}`);
        copiedRootFiles++;
      }
    }

    if (copiedRootFiles > 0) {
      colorLog('green', `✅ 根目录文件复制完成 (${copiedRootFiles}个文件)`);
    }
    
    // 7. 生成构建信息
    colorLog('blue', '📋 生成构建信息...');
    const buildInfo = {
      version: require('../package.json').version,
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      files: {
        typescript: getFileCount('dist', '.js'),
        declarations: getFileCount('dist', '.d.ts'),
        sourceMaps: getFileCount('dist', '.js.map'),
        wasm: fs.existsSync('dist/wasm') ? fs.readdirSync('dist/wasm').length : 0,
        server: copiedServerFiles,
        root: copiedRootFiles,
        public: copiedPublicFiles,
        total: getFileCount('dist', '')
      }
    };
    
    fs.writeFileSync('dist/build-info.json', JSON.stringify(buildInfo, null, 2));
    colorLog('green', '✅ 构建信息已生成');
    
    // 8. 显示构建总结
    colorLog('magenta', '\n🎉 构建完成！');
    console.log(`📦 版本: ${buildInfo.version}`);
    console.log(`⏰ 构建时间: ${buildInfo.buildTime}`);
    console.log(`📁 输出目录: dist/`);
    console.log(`📊 文件统计:`);
    console.log(`   - TypeScript编译: ${buildInfo.files.typescript} 个JS文件`);
    console.log(`   - 类型声明: ${buildInfo.files.declarations} 个.d.ts文件`);
    console.log(`   - 源码映射: ${buildInfo.files.sourceMaps} 个.map文件`);
    console.log(`   - WASM资源: ${buildInfo.files.wasm} 个文件`);
    console.log(`   - 服务器资源: ${buildInfo.files.server} 个文件`);
    console.log(`   - 聊天界面: ${buildInfo.files.public} 个文件`);
    console.log(`   - 根目录文件: ${buildInfo.files.root} 个文件`);
    console.log(`   - 总文件数: ${buildInfo.files.total} 个文件`);
    
    colorLog('green', '\n✅ 当贝AI Provider SDK 构建成功！');
    
  } catch (error) {
    colorLog('red', `❌ 构建失败: ${error.message}`);
    process.exit(1);
  }
}

/**
 * 统计指定扩展名的文件数量
 */
function getFileCount(dir, ext) {
  if (!fs.existsSync(dir)) return 0;

  let count = 0;

  function countFiles(currentDir) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);

      if (entry.isDirectory()) {
        countFiles(fullPath);
      } else if (ext === '' || entry.name.endsWith(ext)) {
        count++;
      }
    }
  }

  countFiles(dir);
  return count;
}

/**
 * 统计目录中的文件数量
 */
function countFilesInDirectory(dir) {
  if (!fs.existsSync(dir)) return 0;

  let count = 0;

  function countFiles(currentDir) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);

      if (entry.isDirectory()) {
        countFiles(fullPath);
      } else {
        count++;
      }
    }
  }

  countFiles(dir);
  return count;
}

// 如果直接运行此脚本，执行构建
if (require.main === module) {
  build().catch(error => {
    colorLog('red', `构建脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { build };
