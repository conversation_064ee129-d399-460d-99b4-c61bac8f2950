/**
 * 分析真实的当贝AI API请求参数
 * 逆向工程签名生成算法
 */

const crypto = require('crypto');

// 真实请求的参数
const realRequest = {
  timestamp: 1755252943,
  nonce: '111g0amuaFUAic500cMI-',
  deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
  sign: '9CC214FB53DDAF31DC1BFE453D14C468',
  requestBody: {
    conversationList: [{
      metaData: {
        chatModelConfig: {},
        superAgentPath: "/chat"
      },
      shareId: "",
      isAnonymous: false,
      source: ""
    }]
  }
};

console.log('=== 真实API请求参数分析 ===\n');

// 1. 分析nonce参数
console.log('1. Nonce分析:');
console.log(`   值: ${realRequest.nonce}`);
console.log(`   长度: ${realRequest.nonce.length}`);
console.log(`   字符组成: ${[...new Set(realRequest.nonce)].join('')}`);
console.log(`   包含数字: ${/\d/.test(realRequest.nonce)}`);
console.log(`   包含字母: ${/[a-zA-Z]/.test(realRequest.nonce)}`);
console.log(`   包含特殊字符: ${/[^a-zA-Z0-9]/.test(realRequest.nonce)}`);
console.log(`   特殊字符: ${realRequest.nonce.match(/[^a-zA-Z0-9]/g)?.join('') || '无'}`);

// 2. 分析timestamp
console.log('\n2. Timestamp分析:');
console.log(`   值: ${realRequest.timestamp}`);
console.log(`   格式: 秒级时间戳`);
console.log(`   对应时间: ${new Date(realRequest.timestamp * 1000).toLocaleString('zh-CN')}`);

// 3. 分析deviceId
console.log('\n3. DeviceId分析:');
console.log(`   值: ${realRequest.deviceId}`);
console.log(`   长度: ${realRequest.deviceId.length}`);
console.log(`   格式: ${realRequest.deviceId.includes('_') ? 'hash_suffix' : '单一字符串'}`);
if (realRequest.deviceId.includes('_')) {
  const parts = realRequest.deviceId.split('_');
  console.log(`   主体部分: ${parts[0]} (长度: ${parts[0].length})`);
  console.log(`   后缀部分: ${parts[1]} (长度: ${parts[1].length})`);
}

// 4. 分析sign
console.log('\n4. Sign分析:');
console.log(`   值: ${realRequest.sign}`);
console.log(`   长度: ${realRequest.sign.length}`);
console.log(`   格式: ${realRequest.sign.length === 32 ? 'MD5' : realRequest.sign.length === 40 ? 'SHA1' : realRequest.sign.length === 64 ? 'SHA256' : '未知'}`);
console.log(`   大小写: ${realRequest.sign === realRequest.sign.toUpperCase() ? '全大写' : realRequest.sign === realRequest.sign.toLowerCase() ? '全小写' : '混合'}`);

// 5. 分析请求体
console.log('\n5. 请求体分析:');
const bodyStr = JSON.stringify(realRequest.requestBody);
console.log(`   JSON字符串: ${bodyStr}`);
console.log(`   长度: ${bodyStr.length}`);
console.log(`   压缩格式: ${bodyStr.includes(' ') ? '否' : '是'}`);

console.log('\n=== 签名算法逆向分析 ===\n');

// 尝试不同的签名生成方法
function testSignatureMethod(method, signString, expectedSign) {
  const algorithms = ['md5', 'sha1', 'sha256'];
  
  console.log(`测试方法: ${method}`);
  console.log(`签名字符串: ${signString}`);
  
  for (const algo of algorithms) {
    const hash = crypto.createHash(algo).update(signString).digest('hex');
    const hashUpper = hash.toUpperCase();
    const hashLower = hash.toLowerCase();
    
    console.log(`  ${algo.toUpperCase()}: ${hashUpper}`);
    
    if (hashUpper === expectedSign) {
      console.log(`  ✅ 匹配! 算法: ${algo.toUpperCase()}`);
      return { algorithm: algo, case: 'upper', match: true };
    }
    if (hashLower === expectedSign.toLowerCase()) {
      console.log(`  ✅ 匹配! 算法: ${algo.toUpperCase()} (小写)`);
      return { algorithm: algo, case: 'lower', match: true };
    }
  }
  
  console.log(`  ❌ 无匹配`);
  return { match: false };
}

// 测试各种可能的签名组合
const testCases = [
  // 方法1: 基础参数排序
  {
    method: '基础参数排序',
    signString: `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`
  },
  
  // 方法2: 不同排序
  {
    method: '时间戳优先排序',
    signString: `timestamp=${realRequest.timestamp}&nonce=${realRequest.nonce}&deviceId=${realRequest.deviceId}`
  },
  
  // 方法3: 包含请求体
  {
    method: '包含请求体',
    signString: `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}&data=${JSON.stringify(realRequest.requestBody)}`
  },
  
  // 方法4: 直接连接
  {
    method: '直接连接',
    signString: `${realRequest.timestamp}${realRequest.nonce}${realRequest.deviceId}`
  },
  
  // 方法5: 不同分隔符
  {
    method: '管道分隔符',
    signString: `${realRequest.timestamp}|${realRequest.nonce}|${realRequest.deviceId}`
  },
  
  // 方法6: 添加固定盐值
  {
    method: '添加dangbei盐值',
    signString: `dangbei${realRequest.timestamp}${realRequest.nonce}${realRequest.deviceId}`
  },
  
  // 方法7: 不包含设备ID
  {
    method: '不包含设备ID',
    signString: `timestamp=${realRequest.timestamp}&nonce=${realRequest.nonce}`
  },
  
  // 方法8: 键值对但不同顺序
  {
    method: '字母顺序排序',
    signString: `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`
  },
  
  // 方法9: 包含请求体但紧凑格式
  {
    method: '包含紧凑请求体',
    signString: `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}&data=${JSON.stringify(realRequest.requestBody, null, 0)}`
  },
  
  // 方法10: 尝试URL编码
  {
    method: 'URL编码请求体',
    signString: `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}&data=${encodeURIComponent(JSON.stringify(realRequest.requestBody))}`
  }
];

// 执行测试
for (let i = 0; i < testCases.length; i++) {
  console.log(`\n--- 测试 ${i + 1}: ${testCases[i].method} ---`);
  const result = testSignatureMethod(testCases[i].method, testCases[i].signString, realRequest.sign);
  
  if (result.match) {
    console.log('\n🎉 找到匹配的签名算法!');
    console.log(`算法: ${result.algorithm.toUpperCase()}`);
    console.log(`大小写: ${result.case}`);
    console.log(`签名字符串: ${testCases[i].signString}`);
    break;
  }
}

console.log('\n=== 分析完成 ===');
