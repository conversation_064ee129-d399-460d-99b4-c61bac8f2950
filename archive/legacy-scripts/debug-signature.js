/**
 * 签名算法调试脚本
 * 用于验证我们的签名算法是否与调用流程.md中的真实数据匹配
 */

const crypto = require('crypto');

// 从调用流程.md中的真实聊天请求数据
const realChatRequest = {
  timestamp: 1755239241,
  nonce: 'QL4MKOwQFtSmnhCOmNjde',
  expectedSign: '460D94E7C6980A6973494BC75D075905',
  method: 'POST',
  url: '/ai-search/chatApi/v2/chat',
  body: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363022964585267589","question":"你好!","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363022965811450053","chatId":"363022965811450053","files":[],"reference":[],"role":"user","status":"local","content":"你好!","userAction":"","agentId":""}'
};

// 从调用流程.md中的真实对话创建请求数据
const realConversationRequest = {
  timestamp: 1755239240,
  nonce: 'OOZpusZl8ANtYIAXqUkgP',
  expectedSign: '0D2DA56E3D33440213FCC5B1326C959B',
  method: 'POST',
  url: '/ai-search/conversationApi/v1/batch/create',
  body: '{"conversationList":[{"metaData":{"chatModelConfig":{},"superAgentPath":"/chat"},"shareId":"","isAnonymous":false,"source":""}]}'
};

/**
 * 生成签名（按照正确的算法）
 */
function generateSignature(timestamp, method, url, body, nonce) {
  let normalized = '';

  if (method === 'GET') {
    const qIndex = url.indexOf('?');
    normalized = qIndex !== -1 ? url.substring(qIndex + 1) : '';
  } else if (method === 'POST') {
    normalized = body || '';
  }

  const signString = `${timestamp}${normalized}${nonce}`;
  const signature = crypto.createHash('md5')
    .update(signString)
    .digest('hex')
    .toUpperCase();

  return { signString, signature, normalized };
}

/**
 * 生成签名（使用 METHOD PATH 算法）
 */
function generateSignatureMethodPath(timestamp, method, url, body, nonce) {
  const normalized = `${method} ${url}`;

  const signString = `${timestamp}${normalized}${nonce}`;
  const signature = crypto.createHash('md5')
    .update(signString)
    .digest('hex')
    .toUpperCase();

  return { signString, signature, normalized };
}

console.log('=== 签名算法验证 ===\n');

// 验证对话创建请求
console.log('1. 验证对话创建请求签名:');
const convResult = generateSignature(
  realConversationRequest.timestamp,
  realConversationRequest.method,
  realConversationRequest.url,
  realConversationRequest.body,
  realConversationRequest.nonce
);

console.log('  - 时间戳:', realConversationRequest.timestamp);
console.log('  - 方法:', realConversationRequest.method);
console.log('  - URL:', realConversationRequest.url);
console.log('  - 请求体长度:', realConversationRequest.body.length);
console.log('  - Nonce:', realConversationRequest.nonce);
console.log('  - 规范化串长度:', convResult.normalized.length);
console.log('  - 签名字符串长度:', convResult.signString.length);
console.log('  - 计算的签名:', convResult.signature);
console.log('  - 期望的签名:', realConversationRequest.expectedSign);
console.log('  - 签名匹配:', convResult.signature === realConversationRequest.expectedSign ? '✅' : '❌');
console.log();

// 验证聊天请求
console.log('2. 验证聊天请求签名:');
const chatResult = generateSignature(
  realChatRequest.timestamp,
  realChatRequest.method,
  realChatRequest.url,
  realChatRequest.body,
  realChatRequest.nonce
);

console.log('  - 时间戳:', realChatRequest.timestamp);
console.log('  - 方法:', realChatRequest.method);
console.log('  - URL:', realChatRequest.url);
console.log('  - 请求体长度:', realChatRequest.body.length);
console.log('  - Nonce:', realChatRequest.nonce);
console.log('  - 规范化串长度:', chatResult.normalized.length);
console.log('  - 签名字符串长度:', chatResult.signString.length);
console.log('  - 计算的签名:', chatResult.signature);
console.log('  - 期望的签名:', realChatRequest.expectedSign);
console.log('  - 签名匹配:', chatResult.signature === realChatRequest.expectedSign ? '✅' : '❌');
console.log();

// 如果签名不匹配，输出详细的调试信息
if (convResult.signature !== realConversationRequest.expectedSign) {
  console.log('❌ 对话创建签名不匹配，调试信息:');
  console.log('  - 签名字符串:', convResult.signString.substring(0, 100) + '...');
}

if (chatResult.signature !== realChatRequest.expectedSign) {
  console.log('❌ 聊天请求签名不匹配，调试信息:');
  console.log('  - 签名字符串:', chatResult.signString.substring(0, 100) + '...');

  // 尝试使用 METHOD PATH 算法
  console.log('\n3. 尝试聊天请求使用 METHOD PATH 算法:');
  const chatMethodPathResult = generateSignatureMethodPath(
    realChatRequest.timestamp,
    realChatRequest.method,
    realChatRequest.url,
    realChatRequest.body,
    realChatRequest.nonce
  );

  console.log('  - 规范化串:', chatMethodPathResult.normalized);
  console.log('  - 签名字符串:', chatMethodPathResult.signString);
  console.log('  - 计算的签名:', chatMethodPathResult.signature);
  console.log('  - 期望的签名:', realChatRequest.expectedSign);
  console.log('  - 签名匹配:', chatMethodPathResult.signature === realChatRequest.expectedSign ? '✅' : '❌');
}

console.log('\n=== 验证完成 ===');
