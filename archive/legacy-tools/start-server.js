#!/usr/bin/env node

/**
 * 简单的服务器启动脚本
 * 用于启动当贝AI Provider HTTP API服务器和测试工具
 */

const { startServer } = require('./dist/server/index.js');

// 解析命令行参数
const args = process.argv.slice(2);
const config = {};

for (let i = 0; i < args.length; i += 2) {
  const key = args[i];
  const value = args[i + 1];

  switch (key) {
    case '--port':
    case '-p':
      if (value) config.port = parseInt(value, 10);
      break;
    case '--host':
    case '-h':
      if (value) config.host = value;
      break;
    case '--debug':
    case '-d':
      config.debug = true;
      i--; // 这个参数没有值
      break;
    case '--timeout':
    case '-t':
      if (value) config.timeout = parseInt(value, 10);
      break;
  }
}

// 显示启动横幅
console.log('');
console.log('██████╗  █████╗ ███╗   ██╗ ██████╗ ██████╗ ███████╗██╗');
console.log('██╔══██╗██╔══██╗████╗  ██║██╔════╝ ██╔══██╗██╔════╝██║');
console.log('██║  ██║███████║██╔██╗ ██║██║  ███╗██████╔╝█████╗  ██║');
console.log('██║  ██║██╔══██║██║╚██╗██║██║   ██║██╔══██╗██╔══╝  ██║');
console.log('██████╔╝██║  ██║██║ ╚████║╚██████╔╝██████╔╝███████╗██║');
console.log('╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝ ╚═════╝ ╚══════╝╚═╝');
console.log('');
console.log('🤖 当贝AI Provider HTTP API 服务器');
console.log('📦 版本:', process.env['npm_package_version'] || '1.0.0');
console.log('🔗 项目地址: https://git.atjog.com/aier/dangbei-provider');
console.log('');

// 启动服务器
startServer(config).catch((error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});
