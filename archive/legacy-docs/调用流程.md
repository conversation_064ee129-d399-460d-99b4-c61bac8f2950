## 创建对话

说明：根据 _app chunk 的拦截器实现，headers 中的 sign 由以下规则生成：
- timestamp 使用秒级 Unix 时间戳
- nonce 为随机字符串
- 规范化串 O(e,t) 近似采用: `${METHOD} ${PATH}`（例如 `POST /ai-search/conversationApi/v1/batch/create`）
- sign = MD5(`${timestamp}${O(e,t)}${nonce}`).toUpperCase()

```
curl 'https://ai-api.dangbei.net/ai-search/conversationApi/v1/batch/create' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Connection: keep-alive' \
  -H 'Origin: https://ai.dangbei.com' \
  -H 'Referer: https://ai.dangbei.com/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: cross-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0' \
  -H 'appType: 6' \
  -H 'appVersion: 1.1.17-22' \
  -H 'client-ver: 1.0.2' \
  -H 'content-type: application/json' \
  -H 'deviceId: eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm' \
  -H 'lang: zh' \
  -H 'nonce: OOZpusZl8ANtYIAXqUkgP' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sign: 0D2DA56E3D33440213FCC5B1326C959B' \
  -H 'timestamp: 1755239240' \
  -H 'token;' \
  --data-raw '{"conversationList":[{"metaData":{"chatModelConfig":{},"superAgentPath":"/chat"},"shareId":"","isAnonymous":false,"source":""}]}'
```

响应结构:

```json
{
    "success": true,
    "errCode": null,
    "errMessage": null,
    "requestId": "e209febe-4229-4a4e-960d-7a93844a6472",
    "data": {
        "conversationId": "363022964585267589",
        "conversationType": 1,
        "title": "新会话",
        "userId": null,
        "deviceId": "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm",
        "titleSummaryFlag": 0,
        "metaData": {
            "writeCode": "",
            "chatModelConfig": {
                "model": "",
                "options": []
            },
            "multiModelLayout": {
                "activeConversation": [
                    {
                        "conversationId": "363022964585267589",
                        "isVisible": true,
                        "order": 0
                    }
                ],
                "lastUpdateTime": "2025-08-15T14:27:20.804490229"
            },
            "superAgentPath": "/chat",
            "pageType": null
        },
        "isAnonymous": false,
        "anonymousKey": "",
        "lastChatModel": null,
        "conversationList": [
            {
                "conversationId": "363022964585267589",
                "conversationType": 1,
                "title": "新会话",
                "userId": null,
                "deviceId": "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm",
                "titleSummaryFlag": 0,
                "metaData": {
                    "writeCode": "",
                    "chatModelConfig": {
                        "model": "",
                        "options": []
                    },
                    "multiModelLayout": null,
                    "superAgentPath": "/chat",
                    "pageType": null
                },
                "isAnonymous": false,
                "anonymousKey": "",
                "lastChatModel": null,
                "conversationList": null
            }
        ]
    }
}
```

## 生成 id

```
curl 'https://ai-api.dangbei.net/ai-search/commonApi/v1/generateId' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Connection: keep-alive' \
  -H 'Origin: https://ai.dangbei.com' \
  -H 'Referer: https://ai.dangbei.com/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: cross-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0' \
  -H 'appType: 6' \
  -H 'appVersion: 1.1.17-22' \
  -H 'client-ver: 1.0.2' \
  -H 'content-type: application/json' \
  -H 'deviceId: eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm' \
  -H 'lang: zh' \
  -H 'nonce: -un-m0ntXQIf0i-byz12f' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sign: 03A7FFE5DCFB0AC2C486A05ED8198142' \
  -H 'timestamp: 1755239241' \
  -H 'token;' \
  --data-raw '{"timestamp":1755239241456}'
```

响应结构:

```json
{
    "success": true,
    "errCode": null,
    "errMessage": null,
    "requestId": "d2d75d46-d2bc-4dfa-9be1-bcd7edf35e8f",
    "data": "363022965811450053"
}
```

## 对话

```
curl 'https://ai-api.dangbei.net/ai-search/chatApi/v2/chat' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Connection: keep-alive' \
  -H 'Origin: https://ai.dangbei.com' \
  -H 'Referer: https://ai.dangbei.com/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: cross-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0' \
  -H 'appType: 6' \
  -H 'appVersion: 1.1.19-1' \
  -H 'client-ver: 1.0.2' \
  -H 'content-type: application/json' \
  -H 'deviceId: eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm' \
  -H 'lang: zh' \
  -H 'nonce: QL4MKOwQFtSmnhCOmNjde' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sign: 460D94E7C6980A6973494BC75D075905' \
  -H 'timestamp: 1755239241' \
  -H 'token;' \
  --data-raw '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363022964585267589","question":"你好!","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363022965811450053","chatId":"363022965811450053","files":[],"reference":[],"role":"user","status":"local","content":"你好!","userAction":"deep,online","agentId":""}'
```

sse 响应:

`conversation.message.delta` 类型

### 联网搜索

```json
{"role":"assistant","type":"answer","content":"联网搜索中...","content_type":"progress","id":"364946235703955653","parentMsgId":"364946235699761349","conversation_id":"364945829712105669","created_at":1756156327,"requestId":"39e7d59a-8e84-4938-b336-732c6f979837","supportDownload":false}
```

```json
{"role":"assistant","type":"answer","content":"{\"cardType\":\"DB-CARD-2\",\"cardInfo\":{\"initTitle\":\"理解问题\",\"cardItems\":[{\"name\":\"搜索网页\",\"type\":\"2001\",\"content\":\"[\\\"SSE 应用实例\\\",\\\"WebSocket 应用实例\\\",\\\"SSE WebSocket 实例对比\\\"]\"},{\"name\":\"引用分析 9篇 参考资料\",\"shortName\":\"引用分析 9篇 参考资料\",\"type\":\"2002\",\"content\":\"[{\\\"idIndex\\\":\\\"1\\\",\\\"name\\\":\\\"<em>使用</em> <em>SSE</em> 实现多用户在线聊天的<em>实例</em>教程-JavaScript中文网\\\",\\\"siteName\\\":\\\"JavaScript中文网\\\",\\\"snippet\\\":\\\"近年来，随着 Web 技术的不断发展，前端的功能和表现力也得到了越来越\\\",\\\"thumbnailUrl\\\":\\\"https://www.javascriptcn.com/favicon.ico\\\",\\\"url\\\":\\\"https://www.javascriptcn.com/post/67d90081a941bf713406a026\\\"},{\\\"idIndex\\\":\\\"2\\\",\\\"name\\\":\\\"【超详解】<em>SSE</em>流式输出-CSDN技术社区\\\",\\\"siteName\\\":\\\"CSDN技术社区\\\",\\\"snippet\\\":\\\"目录\\\\n一、什么是SSE流式输出？\\\\n二、SSE的实现过程\\\\n三、前端代码示\\\",\\\"thumbnailUrl\\\":\\\"https://s2.zimgs.cn/ims?kt=url&at=smstruct&key=aHR0cHM6Ly9jZG4uc20uY24vdGVtcC8yMDIzMTEwNzE2MjUyNi1tYnF0anhlZG9hZHdlbTF1bjh2aXUyZmJqOWdiaDV4aS5qcGc=&sign=yx:yQchh3-fC9nPjUvoM67lCnyns5g=&tv=400_400\\\",\\\"url\\\":\\\"https://m.blog.csdn.net/weixin_62754939/article/details/148863992\\\"},{\\\"idIndex\\\":\\\"3\\\",\\\"name\\\":\\\"    你知道AI如何通过<em>SSE</em>流式渲染到页面的吗(附带完整<em>案例</em>)-稀土掘金\\\",\\\"siteName\\\":\\\"稀土掘金\\\",\\\"snippet\\\":\\\"目的\\\\n使用 SSE + deepseekAPI + react + n\\\",\\\"thumbnailUrl\\\":\\\"https://juejin.cn/favicon.ico\\\",\\\"url\\\":\\\"https://juejin.cn/post/7515383438264385573\\\"},{\\\"idIndex\\\":\\\"4\\\",\\\"name\\\":\\\"Web实时通信的学习之旅：<em>SSE</em>（Server-Sent Events）的技术详解及简单示例演示-阿里云开发者社区\\\",\\\"siteName\\\":\\\"阿里云开发者社区\\\",\\\"snippet\\\":\\\"function supportsSSE(){ return !win\\\",\\\"thumbnailUrl\\\":\\\"https://developer.aliyun.com/favicon.ico\\\",\\\"url\\\":\\\"https://developer.aliyun.com/article/1628418\\\"},{\\\"idIndex\\\":\\\"5\\\",\\\"name\\\":\\\"Spring Boot 整合 <em>SSE</em>(Server-Sent Events)实战<em>案例</em>(全网最全)-中国共产党新闻网\\\",\\\"siteName\\\":\\\"中国共产党新闻网\\\",\\\"snippet\\\":\\\"《SpringBoot整合SSE(Server-SentEvents)\\\",\\\"thumbnailUrl\\\":\\\"http://www.cppcns.com/favicon.ico\\\",\\\"url\\\":\\\"http://www.cppcns.com/ruanjian/java/719956.html\\\"},{\\\"idIndex\\\":\\\"6\\\",\\\"name\\\":\\\"<em>SSE</em> 在前端监控系统中的<em>应用</em>：实时错误日志推送实践-CSDN技术社区\\\",\\\"siteName\\\":\\\"CSDN技术社区\\\",\\\"snippet\\\":\\\"核心概念解释SSE技术及其在前端监控中的价值详细实现方案包括服务端和客\\\",\\\"thumbnailUrl\\\":\\\"https://s2.zimgs.cn/ims?kt=url&at=smstruct&key=aHR0cHM6Ly9jZG4uc20uY24vdGVtcC8yMDIzMTEwNzE2MjUyNi1tYnF0anhlZG9hZHdlbTF1bjh2aXUyZmJqOWdiaDV4aS5qcGc=&sign=yx:yQchh3-fC9nPjUvoM67lCnyns5g=&tv=400_400\\\",\\\"url\\\":\\\"https://m.blog.csdn.net/2502_91534727/article/details/149146376\\\"},{\\\"idIndex\\\":\\\"7\\\",\\\"name\\\":\\\"秒懂LLM流式输出的<em>SSE</em>原理！一文带你搞定SSE实现和Python实战<em>案例</em>-墨天轮数据库社区\\\",\\\"siteName\\\":\\\"墨天轮数据库社区\\\",\\\"snippet\\\":\\\"今天，我们将深入探讨一种常用的流式输出实现方式： 服务器发送事件（Se\\\",\\\"thumbnailUrl\\\":\\\"https://www.modb.pro/favicon.ico\\\",\\\"url\\\":\\\"https://www.modb.pro/db/1854475717170442240\\\"},{\\\"idIndex\\\":\\\"8\\\",\\\"name\\\":\\\"eventSource（<em>SSE</em>）的实践-博客园\\\",\\\"siteName\\\":\\\"博客园\\\",\\\"snippet\\\":\\\"在新闻推送、股票行情 这种 只需要服务器发送消息给客户端场景，使用SS\\\",\\\"thumbnailUrl\\\":\\\"https://www.cnblogs.com/favicon.ico\\\",\\\"url\\\":\\\"https://www.cnblogs.com/bky419/articles/17833257.html\\\"},{\\\"idIndex\\\":\\\"9\\\",\\\"name\\\":\\\"<em>使用SSE</em>-Chat构建实时通信<em>应用</em>：一个现代Web技术的实践<em>案例</em>-CSDN技术社区\\\",\\\"siteName\\\":\\\"CSDN技术社区\\\",\\\"snippet\\\":\\\"Scala, Play Framework 2.3, AngularJ\\\",\\\"thumbnailUrl\\\":\\\"https://s2.zimgs.cn/ims?kt=url&at=smstruct&key=aHR0cHM6Ly9jZG4uc20uY24vdGVtcC8yMDIzMTEwNzE2MjUyNi1tYnF0anhlZG9hZHdlbTF1bjh2aXUyZmJqOWdiaDV4aS5qcGc=&sign=yx:yQchh3-fC9nPjUvoM67lCnyns5g=&tv=400_400\\\",\\\"url\\\":\\\"https://m.blog.csdn.net/gitblog_00046/article/details/138210182\\\"}]\"}]},\"title\":\"阅读全网相关资料 15,985篇，精选引用 9篇\",\"shortTitle\":\"引用分析 9篇 参考资料\"}","content_type":"card","id":"364946235703955653","parentMsgId":"364946235699761349","conversation_id":"364945829712105669","created_at":1756156330,"requestId":"39e7d59a-8e84-4938-b336-732c6f979837","supportDownload":false}
```

### 思考内容

```json
{"role":"assistant","type":"answer","content":"用户","content_type":"thinking","id":"364945836066476229","parentMsgId":"364945836067127685","conversation_id":"364945829712105669","created_at":1756156137,"requestId":"f8beb618-c404-4761-8d2a-14bbde790f33","supportDownload":false}
```

### 正式回答

```json
{"role":"assistant","type":"answer","content":"。","content_type":"text","id":"363022966520938885","parentMsgId":"363022966516744581","conversation_id":"363022964585267589","created_at":1755239259,"requestId":"a3855317-f6b2-4613-bb0e-c53092f182f8","supportDownload":false}
```

`conversation.chat.completed` 类型

```json
{"id":"363022966520938885","parentMsgId":"363022966516744581","conversation_id":"363022964585267589","supportDownload":false}
```

### 建议列表

等待建议列表事件 `conversation.followup.loading`

```json
{}
```

获取建议列表事件 `conversation.message.completed`

```json
{"role":"assistant","type":"follow_up","content":"如何配置HAProxy的后端服务器？","content_type":"follow_up","requestId":"aba9bbc7-ef34-47ae-bf67-e6735a2d333e","supportDownload":false}
```
