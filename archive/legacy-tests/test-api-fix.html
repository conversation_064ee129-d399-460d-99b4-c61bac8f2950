<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 参数修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>API 参数修复测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <div class="info">
            <p>此测试页面用于验证 /api/chat 接口的参数修复是否正确。</p>
            <p>主要修复内容：</p>
            <ul>
                <li>前端选项字段统一为 <code>deep_thinking</code> 和 <code>online_search</code></li>
                <li>后端兼容多种字段格式（question/prompt/content 自动转换为 messages）</li>
                <li>删除重复的 closeStream 方法</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>参数格式测试</h2>
        <button onclick="testCorrectFormat()">测试正确格式</button>
        <button onclick="testLegacyFormat()">测试兼容格式</button>
        <button onclick="testQuestionFormat()">测试 question 字段</button>
        <button onclick="testEmptyMessages()">测试空消息数组问题</button>
        <div id="testResults"></div>
    </div>

    <script>
        // 模拟 API 客户端的选项转换逻辑
        function transformOptionsForAPI(options) {
            const apiOptions = {};

            // 深度思考选项 - 转换为后端期望的 deep_thinking 字段
            if (options.deep || options.thinking || options.deep_thinking) {
                apiOptions.deep_thinking = true;
            }

            // 联网搜索选项 - 转换为后端期望的 online_search 字段
            if (options.online || options.search || options.online_search) {
                apiOptions.online_search = true;
            }

            return apiOptions;
        }

        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testCorrectFormat() {
            showResult('<h3>测试正确格式</h3>', 'info');
            
            const testData = {
                messages: [{ role: 'user', content: '你好，请介绍一下自己' }],
                model: 'doubao-1_6-thinking',
                stream: true,
                options: {
                    deep_thinking: true,
                    online_search: false
                }
            };

            const transformedOptions = transformOptionsForAPI(testData.options);
            
            showResult(`
                <strong>输入参数：</strong>
                <pre>${JSON.stringify(testData, null, 2)}</pre>
                <strong>转换后的选项：</strong>
                <pre>${JSON.stringify(transformedOptions, null, 2)}</pre>
                <strong>结果：</strong> ✅ 格式正确，无需转换
            `, 'success');
        }

        function testLegacyFormat() {
            showResult('<h3>测试兼容格式</h3>', 'info');
            
            const testData = {
                messages: [{ role: 'user', content: '请帮我搜索最新的AI技术发展' }],
                model: 'doubao-pro',
                stream: true,
                options: {
                    deep: true,      // 旧格式
                    online: true     // 旧格式
                }
            };

            const transformedOptions = transformOptionsForAPI(testData.options);
            
            showResult(`
                <strong>输入参数（旧格式）：</strong>
                <pre>${JSON.stringify(testData, null, 2)}</pre>
                <strong>转换后的选项：</strong>
                <pre>${JSON.stringify(transformedOptions, null, 2)}</pre>
                <strong>结果：</strong> ✅ 成功转换为标准格式
            `, 'success');
        }

        function testQuestionFormat() {
            showResult('<h3>测试 question 字段兼容</h3>', 'info');
            
            const testData = {
                question: '什么是人工智能？',  // 使用 question 字段而不是 messages
                model: 'doubao-1_6-thinking',
                stream: false,
                options: {
                    thinking: true,   // 另一种旧格式
                    search: false     // 另一种旧格式
                }
            };

            const transformedOptions = transformOptionsForAPI(testData.options);
            
            // 模拟后端的 messages 补全逻辑
            const finalMessages = testData.question ? 
                [{ role: 'user', content: testData.question }] : [];
            
            showResult(`
                <strong>输入参数（question 格式）：</strong>
                <pre>${JSON.stringify(testData, null, 2)}</pre>
                <strong>后端补全的 messages：</strong>
                <pre>${JSON.stringify(finalMessages, null, 2)}</pre>
                <strong>转换后的选项：</strong>
                <pre>${JSON.stringify(transformedOptions, null, 2)}</pre>
                <strong>结果：</strong> ✅ 成功兼容 question 字段并转换选项格式
            `, 'success');
        }

        function testEmptyMessages() {
            showResult('<h3>测试空消息数组问题</h3>', 'info');

            // 模拟前端发送空消息数组的情况
            const problematicData = {
                messages: [],  // 空数组 - 这是问题所在
                model: 'doubao-1_6-thinking',
                stream: true,
                conversation_id: null,
                options: {
                    deep_thinking: true,
                    online_search: true
                }
            };

            showResult(`
                <strong>问题场景（修复前）：</strong>
                <pre>${JSON.stringify(problematicData, null, 2)}</pre>
                <strong>问题分析：</strong>
                <ul>
                    <li>❌ messages 数组为空，导致后端无法处理</li>
                    <li>❌ 前端 currentSession.messages 没有同步更新</li>
                    <li>❌ 用户消息添加到存储后，内存中的会话对象未更新</li>
                </ul>
                <strong>修复方案：</strong>
                <ul>
                    <li>✅ 在 sendMessage 中添加消息后立即同步 currentSession</li>
                    <li>✅ 使用 storage.getSession() 重新获取最新会话数据</li>
                    <li>✅ 确保 messages 数组包含刚添加的用户消息</li>
                    <li>✅ 后端已有兼容性处理，支持 question 字段自动转换</li>
                </ul>
            `, 'success');
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            showResult('点击上方按钮开始测试各种参数格式的兼容性', 'info');
        };
    </script>
</body>
</html>
