<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息渲染修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .message-example {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #fff;
        }
        .message-type {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>消息渲染修复测试</h1>
    
    <div class="test-container">
        <h2>修复说明</h2>
        <div class="info">
            <p>此测试页面用于验证前端消息渲染逻辑的修复效果。</p>
            <p>主要修复内容：</p>
            <ul>
                <li>修复消息内容显示为 undefined 的问题</li>
                <li>根据 content_type 字段实现不同的消息展示样式</li>
                <li>添加调试日志查看实际接收到的消息数据</li>
                <li>优化四种消息类型的渲染逻辑和样式</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>后端消息格式示例</h2>
        <button onclick="showMessageExamples()">显示消息格式示例</button>
        <div id="messageExamples"></div>
    </div>

    <div class="test-container">
        <h2>前端处理逻辑测试</h2>
        <button onclick="testMessageHandling()">测试消息处理逻辑</button>
        <div id="testResults"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function showMessageExamples() {
            const examplesDiv = document.getElementById('messageExamples');
            
            const examples = [
                {
                    type: 'text',
                    description: '正式回答内容 - 最显眼的主要内容区域',
                    data: {
                        "role": "assistant",
                        "type": "answer",
                        "content": "这是AI的正式回答内容。",
                        "content_type": "text",
                        "id": "363022966520938885",
                        "parentMsgId": "363022966516744581",
                        "conversation_id": "363022964585267589",
                        "created_at": 1755239259,
                        "requestId": "a3855317-f6b2-4613-bb0e-c53092f182f8",
                        "supportDownload": false
                    }
                },
                {
                    type: 'thinking',
                    description: 'AI 思考过程内容 - 次要内容，可折叠',
                    data: {
                        "role": "assistant",
                        "type": "answer",
                        "content": "用户询问了一个关于技术的问题，我需要仔细分析...",
                        "content_type": "thinking",
                        "id": "364945836066476229",
                        "parentMsgId": "364945836067127685",
                        "conversation_id": "364945829712105669",
                        "created_at": 1756156137,
                        "requestId": "f8beb618-c404-4761-8d2a-14bbde790f33",
                        "supportDownload": false
                    }
                },
                {
                    type: 'progress',
                    description: '联网搜索进度提示 - 临时状态，包含动画',
                    data: {
                        "role": "assistant",
                        "type": "answer",
                        "content": "联网搜索中...",
                        "content_type": "progress",
                        "id": "364946235703955653",
                        "parentMsgId": "364946235699761349",
                        "conversation_id": "364945829712105669",
                        "created_at": 1756156327,
                        "requestId": "39e7d59a-8e84-4938-b336-732c6f979837",
                        "supportDownload": false
                    }
                },
                {
                    type: 'card',
                    description: '联网搜索结果卡片 - 卡片列表布局',
                    data: {
                        "role": "assistant",
                        "type": "answer",
                        "content": '{"cardType":"DB-CARD-2","cardInfo":{"initTitle":"理解问题","cardItems":[{"name":"搜索网页","type":"2001","content":"[\\"SSE 应用实例\\",\\"WebSocket 应用实例\\"]"},{"name":"引用分析 2篇 参考资料","type":"2002","content":"[{\\"idIndex\\":\\"1\\",\\"name\\":\\"使用 SSE 实现多用户在线聊天的实例教程\\",\\"siteName\\":\\"JavaScript中文网\\",\\"snippet\\":\\"近年来，随着 Web 技术的不断发展，前端的功能和表现力也得到了越来越\\",\\"thumbnailUrl\\":\\"https://www.javascriptcn.com/favicon.ico\\",\\"url\\":\\"https://www.javascriptcn.com/post/67d90081a941bf713406a026\\"}]"}],"title":"阅读全网相关资料 15,985篇，精选引用 2篇"}}',
                        "content_type": "card",
                        "id": "364946235703955653",
                        "parentMsgId": "364946235699761349",
                        "conversation_id": "364945829712105669",
                        "created_at": 1756156330,
                        "requestId": "39e7d59a-8e84-4938-b336-732c6f979837",
                        "supportDownload": false
                    }
                }
            ];

            let html = '';
            examples.forEach(example => {
                html += `
                    <div class="message-example">
                        <div class="message-type">${example.type.toUpperCase()} 类型</div>
                        <p><strong>描述：</strong>${example.description}</p>
                        <p><strong>示例数据：</strong></p>
                        <pre>${JSON.stringify(example.data, null, 2)}</pre>
                    </div>
                `;
            });

            examplesDiv.innerHTML = html;
        }

        function testMessageHandling() {
            showResult('<h3>测试前端消息处理逻辑</h3>', 'info');
            
            // 模拟前端消息处理函数
            function handleStreamMessage(message) {
                const messageContent = message.content || '';
                console.log('处理消息类型:', message.type, '内容:', messageContent);
                
                const result = {
                    type: message.type,
                    content: messageContent,
                    hasContent: messageContent !== '',
                    contentLength: messageContent.length
                };
                
                return result;
            }

            // 测试不同类型的消息
            const testMessages = [
                { type: 'text', content: '这是正式回答' },
                { type: 'thinking', content: '这是思考过程' },
                { type: 'progress', content: '联网搜索中...' },
                { type: 'card', content: '{"cardType":"DB-CARD-2"}' },
                { type: 'text', content: undefined }, // 测试 undefined 情况
                { type: 'text', content: '' }, // 测试空字符串情况
            ];

            let results = '<table border="1" style="width:100%; border-collapse: collapse;">';
            results += '<tr><th>消息类型</th><th>原始内容</th><th>处理后内容</th><th>是否有内容</th><th>状态</th></tr>';

            testMessages.forEach((msg, index) => {
                const result = handleStreamMessage(msg);
                const status = result.hasContent ? '✅ 正常' : '❌ 内容为空';
                const statusClass = result.hasContent ? 'success' : 'error';
                
                results += `
                    <tr>
                        <td>${result.type}</td>
                        <td>${msg.content === undefined ? 'undefined' : (msg.content === '' ? '空字符串' : msg.content)}</td>
                        <td>${result.content}</td>
                        <td>${result.hasContent}</td>
                        <td class="${statusClass}">${status}</td>
                    </tr>
                `;
            });

            results += '</table>';

            showResult(`
                <strong>消息处理测试结果：</strong><br>
                ${results}
                <br>
                <strong>修复效果：</strong>
                <ul>
                    <li>✅ 添加了 messageContent = message.content || '' 确保内容不为 undefined</li>
                    <li>✅ 添加了详细的调试日志输出</li>
                    <li>✅ 根据 content_type 实现了不同的渲染样式</li>
                    <li>✅ 优化了四种消息类型的处理逻辑</li>
                </ul>
            `, 'success');
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            showResult('点击上方按钮开始测试消息渲染修复效果', 'info');
        };
    </script>
</body>
</html>
