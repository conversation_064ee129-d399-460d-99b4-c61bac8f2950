/**
 * 测试页面请求参数示例
 * 基于 src/server/types/api.ts 中定义的类型生成
 */

import type { ChatRequest, TextGenerationRequest } from '../src/server/types';

/**
 * 聊天接口请求示例
 */
export const chatRequestExamples = {
  /**
   * 基础聊天请求示例
   * 适用于简单的单轮对话测试
   */
  basic: {
    messages: [
      {
        role: 'user' as const,
        content: '你好，请介绍一下你自己',
        id: 'msg_001',
        timestamp: Date.now()
      }
    ],
    model: 'gpt-4',
    stream: false,
    conversation_id: 'conv_12345',
    max_tokens: 2048,
    temperature: 0.7
  } as ChatRequest,

  /**
   * 带高级选项的聊天请求示例
   * 包含系统提示和高级功能选项
   */
  withOptions: {
    messages: [
      {
        role: 'system' as const,
        content: '你是一个专业的AI助手，请用友好和专业的语气回答问题。',
        id: 'msg_system_001',
        timestamp: Date.now()
      },
      {
        role: 'user' as const,
        content: '请帮我分析一下当前的市场趋势，并提供一些投资建议。',
        id: 'msg_user_001',
        timestamp: Date.now() + 1
      }
    ],
    model: 'claude-3-opus',
    stream: true,
    conversation_id: 'conv_67890',
    max_tokens: 4096,
    temperature: 0.8,
    options: {
      deep_thinking: true,  // 启用深度思考
      online_search: true   // 启用联网搜索
    }
  } as ChatRequest,

  /**
   * 多轮对话请求示例
   * 展示如何维持对话上下文
   */
  multiTurn: {
    messages: [
      {
        role: 'user' as const,
        content: '什么是机器学习？',
        id: 'msg_001',
        timestamp: Date.now() - 2000
      },
      {
        role: 'assistant' as const,
        content: '机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。',
        id: 'msg_002',
        timestamp: Date.now() - 1000
      },
      {
        role: 'user' as const,
        content: '能给我举个具体的例子吗？',
        id: 'msg_003',
        timestamp: Date.now()
      }
    ],
    model: 'gpt-3.5-turbo',
    stream: false,
    max_tokens: 1024,
    temperature: 0.6
  } as ChatRequest
};

/**
 * 文本生成接口请求示例
 */
export const textGenerationRequestExamples = {
  /**
   * 创意写作示例
   * 适用于小说、诗歌、创意文案等
   */
  creative: {
    prompt: '写一篇关于未来城市的科幻短篇小说，要求包含人工智能、环保科技和人文关怀等元素。',
    model: 'claude-3-sonnet',
    stream: false,
    max_tokens: 3000,
    temperature: 0.9,  // 高创造性
    task_type: 'creative' as const,
    options: {
      style: '科幻文学',
      format: '短篇小说',
      language: '中文',
      deep_thinking: true,
      online_search: false
    }
  } as TextGenerationRequest,

  /**
   * 代码生成示例
   * 适用于编程任务和技术文档
   */
  code: {
    prompt: '请用Python编写一个简单的Web爬虫，能够抓取指定网站的标题和链接，并保存到CSV文件中。要求包含错误处理和日志记录。',
    model: 'gpt-4-turbo',
    stream: true,
    max_tokens: 2048,
    temperature: 0.3,  // 低创造性，高准确性
    task_type: 'code' as const,
    options: {
      style: '专业代码',
      format: '完整程序',
      language: 'Python',
      deep_thinking: false,
      online_search: false
    }
  } as TextGenerationRequest,

  /**
   * 文档生成示例
   * 适用于商业文档、技术文档等
   */
  document: {
    prompt: '为一个在线教育平台编写产品需求文档，包括功能模块、用户角色、技术架构和项目时间线。',
    model: 'claude-3-opus',
    stream: false,
    max_tokens: 4096,
    temperature: 0.5,  // 中等创造性
    task_type: 'document' as const,
    options: {
      style: '正式商务',
      format: '结构化文档',
      language: '中文',
      deep_thinking: true,
      online_search: true
    }
  } as TextGenerationRequest,

  /**
   * 摘要生成示例
   * 适用于文章总结、会议纪要等
   */
  summary: {
    prompt: '请总结以下文章的主要内容：[这里是一篇关于人工智能发展趋势的长篇文章内容...]',
    model: 'gpt-3.5-turbo',
    stream: false,
    max_tokens: 1024,
    temperature: 0.2,  // 低创造性，重点准确性
    task_type: 'summary' as const,
    options: {
      style: '简洁明了',
      format: '要点总结',
      language: '中文',
      deep_thinking: false,
      online_search: false
    }
  } as TextGenerationRequest,

  /**
   * 翻译示例
   * 适用于多语言翻译任务
   */
  translation: {
    prompt: "请将以下英文文本翻译成中文：'Artificial intelligence is revolutionizing the way we work, learn, and interact with technology. From healthcare to finance, AI applications are becoming increasingly sophisticated and widespread.'",
    model: 'gpt-4',
    stream: false,
    max_tokens: 512,
    temperature: 0.1,  // 极低创造性，最高准确性
    task_type: 'translation' as const,
    options: {
      style: '准确流畅',
      format: '直译',
      language: '中文',
      deep_thinking: false,
      online_search: false
    }
  } as TextGenerationRequest,

  /**
   * 改写示例
   * 适用于文本润色、风格转换等
   */
  rewrite: {
    prompt: "请将以下文本改写得更加正式和专业：'这个项目挺不错的，我们应该考虑一下，可能会有很好的效果。'",
    model: 'claude-3-sonnet',
    stream: false,
    max_tokens: 256,
    temperature: 0.4,  // 中低创造性
    task_type: 'rewrite' as const,
    options: {
      style: '正式专业',
      format: '商务语言',
      language: '中文',
      deep_thinking: false,
      online_search: false
    }
  } as TextGenerationRequest,

  /**
   * 问答示例
   * 适用于知识问答、解释说明等
   */
  qa: {
    prompt: '什么是区块链技术？它有哪些主要应用场景？',
    model: 'gpt-4-turbo',
    stream: false,
    max_tokens: 1536,
    temperature: 0.3,  // 中低创造性
    task_type: 'qa' as const,
    options: {
      style: '教育性',
      format: '问答形式',
      language: '中文',
      deep_thinking: true,
      online_search: true
    }
  } as TextGenerationRequest,

  /**
   * 通用生成示例
   * 适用于各种通用文本生成任务
   */
  general: {
    prompt: '请为我制定一个为期一个月的健身计划，包括有氧运动、力量训练和饮食建议。',
    model: 'claude-3-haiku',
    stream: false,
    max_tokens: 2048,
    temperature: 0.6,  // 中等创造性
    task_type: 'general' as const,
    options: {
      style: '实用指导',
      format: '计划表',
      language: '中文',
      deep_thinking: false,
      online_search: false
    }
  } as TextGenerationRequest
};

/**
 * 获取随机示例的工具函数
 */
export const getRandomChatExample = (): ChatRequest => {
  const examples = Object.values(chatRequestExamples);
  return examples[Math.floor(Math.random() * examples.length)];
};

export const getRandomTextGenerationExample = (): TextGenerationRequest => {
  const examples = Object.values(textGenerationRequestExamples);
  return examples[Math.floor(Math.random() * examples.length)];
};

/**
 * 根据任务类型获取文本生成示例
 */
export const getTextGenerationExampleByTaskType = (taskType: string): TextGenerationRequest | null => {
  const example = textGenerationRequestExamples[taskType as keyof typeof textGenerationRequestExamples];
  return example || null;
};

/**
 * 生成新的对话ID
 */
export const generateConversationId = (): string => {
  return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 生成新的消息ID
 */
export const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 创建用户消息对象
 */
export const createUserMessage = (content: string, conversationId?: string) => ({
  role: 'user' as const,
  content,
  id: generateMessageId(),
  timestamp: Date.now()
});

/**
 * 温度参数建议
 */
export const temperatureRecommendations = {
  creative: { min: 0.8, max: 1.0, description: '创意写作需要高创造性' },
  code: { min: 0.1, max: 0.3, description: '代码生成需要准确性和逻辑性' },
  translation: { min: 0.0, max: 0.2, description: '翻译需要准确性' },
  summary: { min: 0.2, max: 0.4, description: '摘要需要简洁准确' },
  qa: { min: 0.3, max: 0.6, description: '问答平衡准确性和表达多样性' },
  general: { min: 0.4, max: 0.7, description: '通用任务的平衡设置' }
};

/**
 * 令牌数建议
 */
export const tokenRecommendations = {
  short: { tokens: 512, description: '短回答' },
  medium: { tokens: 1024, description: '中等回答' },
  long: { tokens: 2048, description: '长回答' },
  code: { tokens: 2048, description: '代码生成' },
  creative: { tokens: 3000, description: '创意写作' },
  document: { tokens: 4096, description: '文档生成' }
};
