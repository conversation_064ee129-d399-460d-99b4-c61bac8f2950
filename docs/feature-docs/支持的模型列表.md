# 当贝AI支持的模型列表

基于 `models.json` 文件，当贝AI平台支持以下AI模型。每个模型都有其独特的特点和适用场景。

## 🧠 推理模型（支持深度思考）

这类模型专注于逻辑推理、深度分析和复杂问题解决，响应时间较长但质量更高。

### 1. DeepSeek-R1最新版 ⭐⭐⭐⭐⭐
- **模型值**: `deepseek`
- **特点**: 专注逻辑推理与深度分析，擅长解决复杂问题，提供精准决策支持
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐⭐⭐ (HOT标签)
- **适用场景**: 逻辑推理、数学问题、复杂分析、决策支持

### 2. 豆包-1.6 ⭐⭐⭐⭐⭐
- **模型值**: `doubao-1_6-thinking`
- **特点**: 豆包最新推理模型，创作、推理、数学大幅增强
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐⭐⭐
- **适用场景**: 创意写作、数学推理、逻辑分析、内容创作

### 3. GLM-4.5 ⭐⭐⭐⭐
- **模型值**: `glm-4-5`
- **特点**: 智谱最新旗舰模型，支持思考模式切换，综合能力达到开源模型的SOTA水平
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐⭐ (NEW标签)
- **适用场景**: 综合问答、创意写作、知识问答、思考分析

### 4. 通义3-235B ⭐⭐⭐⭐
- **模型值**: `qwen3-235b-a22b`
- **特点**: 国内首个混合推理模型，达到同规模业界SOTA水平
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐⭐
- **适用场景**: 复杂推理、知识问答、专业分析

### 5. MiniMax-M1 ⭐⭐⭐
- **模型值**: `MiniMax-M1`
- **特点**: 全球领先，80K思维链 x 1M输入
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 长文本处理、复杂推理、大规模分析

### 6. 通义QwQ ⭐⭐⭐
- **模型值**: `qwq-plus`
- **特点**: 善解难题，精准表达，知识全面
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 难题解答、精准分析、知识问答

### 7. 豆包-1.5 ⭐⭐⭐
- **模型值**: `doubao-thinking`
- **特点**: 推理模型，专精数理编程，擅长创意写作
- **功能**: 深度思考 ✅、联网搜索 ✅
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 数学编程、创意写作、逻辑推理

## ⚡ 高效模型（快速响应）

这类模型注重响应速度和效率，适合需要快速回复的交互场景。

### 1. DeepSeek-V3 ⭐⭐⭐⭐
- **模型值**: `deepseek-v3`
- **特点**: 轻量高效，响应极快。擅长代码，可高效解析代码与图表
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐⭐⭐
- **适用场景**: 代码分析、快速问答、图表解析、技术咨询

### 2. Kimi K2 ⭐⭐⭐⭐
- **模型值**: `kimi-k2-0711-preview`
- **特点**: 具备更强代码能力、更擅长通用Agent任务
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐⭐⭐ (NEW标签)
- **适用场景**: 代码开发、Agent任务、自动化处理

### 3. GLM-4-Plus ⭐⭐⭐
- **模型值**: `glm-4-plus`
- **特点**: 智谱最强高智能旗舰模型
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 智能对话、快速问答、信息检索

### 4. 豆包 ⭐⭐⭐
- **模型值**: `doubao`
- **特点**: 字节全能AI，创意写作、百科解答、难题破解，随需响应
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 日常对话、创意写作、百科问答

### 5. 通义Plus ⭐⭐⭐
- **模型值**: `qwen-plus`
- **特点**: 复杂问题速解专家，知识广博，表达清晰精准
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 问题解答、知识查询、快速分析

### 6. Kimi ⭐⭐⭐
- **模型值**: `moonshot-v1-32k`
- **特点**: 高效问题解析者，多领域知识库，语言简练有力
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐⭐
- **适用场景**: 问题解析、知识问答、信息整理

### 7. 通义Long ⭐⭐
- **模型值**: `qwen-long`
- **特点**: 通义千问针对超长上下文处理场景的大语言模型
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐
- **适用场景**: 长文本处理、文档分析、上下文理解

### 8. 文心4.5 ⭐⭐
- **模型值**: `ernie-4.5-turbo-32k`
- **特点**: 广泛适用于各领域复杂任务场景
- **功能**: 联网搜索 ✅（深度思考不支持）
- **推荐指数**: ⭐⭐
- **适用场景**: 复杂任务、多领域应用、专业咨询

## 🎯 模型选择建议

### 按使用场景选择

| 场景 | 推荐模型 | 备选模型 |
|------|----------|----------|
| **逻辑推理** | DeepSeek-R1 (`deepseek`) | 豆包-1.6 (`doubao-1_6-thinking`) |
| **代码分析** | DeepSeek-V3 (`deepseek-v3`) | Kimi K2 (`kimi-k2-0711-preview`) |
| **创意写作** | 豆包-1.6 (`doubao-1_6-thinking`) | GLM-4.5 (`glm-4-5`) |
| **快速问答** | 通义Plus (`qwen-plus`) | 豆包 (`doubao`) |
| **数学编程** | 豆包-1.5 (`doubao-thinking`) | DeepSeek-R1 (`deepseek`) |
| **长文本处理** | 通义Long (`qwen-long`) | MiniMax-M1 (`MiniMax-M1`) |
| **Agent任务** | Kimi K2 (`kimi-k2-0711-preview`) | GLM-4-Plus (`glm-4-plus`) |

### 按响应时间选择

- **快速响应（3-10秒）**: DeepSeek-V3, Kimi K2, 通义Plus, 豆包
- **中等响应（10-20秒）**: GLM-4-Plus, Kimi, 通义Long, 文心4.5
- **深度思考（20-60秒）**: DeepSeek-R1, 豆包-1.6, GLM-4.5, 通义3-235B

### 按功能特性选择

- **支持深度思考**: DeepSeek-R1, 豆包-1.6, GLM-4.5, 通义3-235B, MiniMax-M1, 通义QwQ, 豆包-1.5
- **支持联网搜索**: 所有模型都支持
- **代码专家**: DeepSeek-V3, Kimi K2
- **创作专家**: 豆包-1.6, GLM-4.5, 豆包-1.5
- **推理专家**: DeepSeek-R1, 通义QwQ, 豆包-1.6

## 📊 性能对比

| 模型类别 | 平均响应时间 | 回答质量 | 适用场景 | 推荐指数 |
|----------|--------------|----------|----------|----------|
| 推理模型 | 20-60秒 | ⭐⭐⭐⭐⭐ | 复杂分析 | ⭐⭐⭐⭐⭐ |
| 高效模型 | 3-15秒 | ⭐⭐⭐⭐ | 快速交互 | ⭐⭐⭐⭐ |

## 🔧 使用示例

详细的使用示例请参考：
- [模型调用指南](./模型调用指南.md)
- [模型调用示例代码](../examples/model-usage-examples.ts)
- [快速测试脚本](../test-models.js)

## 📝 注意事项

1. **推理模型**需要更长的等待时间，建议设置60秒超时
2. **高效模型**响应快速，适合实时交互场景
3. 某些模型的**深度思考功能**可能被禁用
4. 建议根据具体需求选择合适的模型
5. 可以实现**多模型对比**来选择最适合的模型
