# 测试页面请求参数示例文档

本文档包含基于 `src/server/types/api.ts` 类型定义生成的完整测试参数示例，为测试页面提供标准化的请求参数格式。

## 📁 文件结构

```
├── test-request-examples.json      # JSON 格式的请求参数示例
├── test-request-examples.md        # 详细的参数说明文档
├── test-request-examples.ts        # TypeScript 格式的示例和工具函数
├── test-page-example.html          # 可交互的 HTML 测试页面
├── request-validator.ts            # 请求参数验证工具
└── README-测试参数示例.md           # 本说明文档
```

## 🎯 主要功能

### 1. 完整的请求参数示例

#### 聊天接口 (ChatRequest)
- **基础聊天请求**: 简单的单轮对话
- **带高级选项的请求**: 包含深度思考和联网搜索
- **多轮对话请求**: 维持对话上下文的多轮交互

#### 文本生成接口 (TextGenerationRequest)
- **创意写作** (creative): 小说、诗歌、创意文案
- **代码生成** (code): 编程任务和技术文档
- **文档生成** (document): 商业文档、技术文档
- **摘要生成** (summary): 文章总结、会议纪要
- **翻译** (translation): 多语言翻译任务
- **改写** (rewrite): 文本润色、风格转换
- **问答** (qa): 知识问答、解释说明
- **通用生成** (general): 各种通用文本生成

### 2. 参数配置建议

#### 温度参数 (temperature) 建议
| 任务类型 | 推荐范围 | 说明 |
|---------|---------|------|
| 创意写作 | 0.8-1.0 | 需要高创造性 |
| 代码生成 | 0.1-0.3 | 需要准确性和逻辑性 |
| 翻译 | 0.0-0.2 | 需要准确性 |
| 摘要 | 0.2-0.4 | 需要简洁准确 |
| 问答 | 0.3-0.6 | 平衡准确性和表达多样性 |

#### 令牌数 (max_tokens) 建议
| 内容类型 | 推荐值 | 说明 |
|---------|--------|------|
| 短回答 | 256-512 | 简短问答 |
| 中等回答 | 1024-2048 | 一般对话 |
| 长回答 | 2048-4096 | 详细分析 |
| 代码生成 | 1024-3000 | 完整程序 |
| 创意写作 | 2000-4000 | 长篇内容 |

#### 模型选择建议
| 模型 | 适用场景 |
|------|---------|
| GPT-4 | 复杂推理、代码生成、专业写作 |
| GPT-3.5-turbo | 日常对话、简单任务 |
| Claude-3-opus | 长文本处理、深度分析 |
| Claude-3-sonnet | 平衡性能和成本 |
| Claude-3-haiku | 快速响应、简单任务 |

## 🛠️ 使用方法

### 1. 在测试页面中使用

#### 方法一：直接复制 JSON
```javascript
// 从 test-request-examples.json 复制所需的示例
const chatRequest = {
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己",
      "id": "msg_001",
      "timestamp": 1703123456789
    }
  ],
  "model": "gpt-4",
  "stream": false,
  "conversation_id": "conv_12345",
  "max_tokens": 2048,
  "temperature": 0.7
};
```

#### 方法二：使用 TypeScript 工具函数
```typescript
import { chatRequestExamples, textGenerationRequestExamples } from './test-request-examples';

// 获取基础聊天示例
const basicChatExample = chatRequestExamples.basic;

// 获取创意写作示例
const creativeExample = textGenerationRequestExamples.creative;

// 生成新的对话ID
import { generateConversationId, createUserMessage } from './test-request-examples';
const newConversationId = generateConversationId();
const userMessage = createUserMessage("你好，世界！");
```

#### 方法三：使用 HTML 测试页面
1. 打开 `test-page-example.html`
2. 浏览不同类型的请求示例
3. 点击"复制 JSON"按钮直接复制到剪贴板
4. 在测试页面中粘贴使用

### 2. 参数验证

使用 `request-validator.ts` 验证请求参数：

```typescript
import { RequestValidator } from './request-validator';

// 验证聊天请求
const chatValidation = RequestValidator.validateChatRequest(yourChatRequest);
if (!chatValidation.isValid) {
  console.log('验证失败:', chatValidation.errors);
}

// 验证文本生成请求
const textValidation = RequestValidator.validateTextGenerationRequest(yourTextRequest);
console.log(RequestValidator.formatValidationResult(textValidation));
```

## 📋 字段说明

### 通用字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `model` | string | 是 | 使用的AI模型名称 |
| `stream` | boolean | 否 | 是否启用流式响应，默认false |
| `max_tokens` | number | 否 | 最大生成令牌数 |
| `temperature` | number | 否 | 温度参数，控制创造性，范围0-1 |

### 聊天接口特有字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `messages` | ChatMessage[] | 是 | 消息列表 |
| `conversation_id` | string | 否 | 对话ID，用于维持上下文 |
| `options.deep_thinking` | boolean | 否 | 启用深度思考模式 |
| `options.online_search` | boolean | 否 | 启用联网搜索 |

### 文本生成接口特有字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `prompt` | string | 是 | 生成提示词 |
| `task_type` | TextGenerationTaskType | 否 | 任务类型 |
| `options.style` | string | 否 | 写作风格 |
| `options.format` | string | 否 | 输出格式 |
| `options.language` | string | 否 | 输出语言 |

### 消息对象 (ChatMessage)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `role` | 'user' \| 'assistant' \| 'system' | 是 | 消息角色 |
| `content` | string | 是 | 消息内容 |
| `id` | string | 否 | 消息ID |
| `timestamp` | number | 否 | 时间戳 |

## 🔧 自定义和扩展

### 1. 添加新的示例

在 `test-request-examples.ts` 中添加新的示例：

```typescript
export const customExamples = {
  myCustomExample: {
    // 你的自定义示例
  } as ChatRequest
};
```

### 2. 修改默认参数

根据你的需求调整示例中的默认参数：

```typescript
// 修改默认模型
const myExample = {
  ...chatRequestExamples.basic,
  model: 'your-preferred-model'
};

// 修改默认温度
const myExample = {
  ...textGenerationRequestExamples.creative,
  temperature: 0.95
};
```

### 3. 添加验证规则

在 `request-validator.ts` 中添加自定义验证逻辑：

```typescript
// 添加自定义验证函数
static validateCustomField(value: any): string[] {
  const errors: string[] = [];
  // 你的验证逻辑
  return errors;
}
```

## 🚀 快速开始

1. **查看示例**: 打开 `test-request-examples.md` 了解所有可用示例
2. **复制参数**: 从 `test-request-examples.json` 复制所需的JSON格式参数
3. **验证参数**: 使用 `request-validator.ts` 验证参数有效性
4. **测试接口**: 在你的测试页面中使用这些参数调用API
5. **调整优化**: 根据实际需求调整参数值

## 📝 注意事项

1. **时间戳**: 示例中的时间戳需要根据实际情况更新
2. **模型名称**: 确保使用的模型名称在你的系统中可用
3. **令牌限制**: 注意不同模型的令牌数限制
4. **API兼容性**: 确保请求格式与你的API接口兼容
5. **错误处理**: 在实际使用中添加适当的错误处理逻辑

## 🤝 贡献

如果你发现示例中有错误或需要添加新的示例类型，请：

1. 检查现有的类型定义 (`src/server/types/api.ts`)
2. 添加相应的示例到相关文件
3. 更新验证规则（如果需要）
4. 更新文档说明

这些示例文件将帮助你快速构建和测试API接口，确保请求参数的正确性和完整性。
