# 开发服务器启动指南

## 🐛 问题说明

您遇到的错误是因为当前版本的 `ts-node` (v10.9.1) 不支持 `--watch` 选项。这是一个常见的版本兼容性问题。

## ✅ 解决方案

### 方案1：使用 nodemon（推荐）

我已经修复了 package.json 配置，现在可以使用以下命令：

```bash
# 开发模式（带文件监听）
npm run server:dev

# 或者直接使用
npx nodemon --exec "npx ts-node src/server/index.ts"
```

### 方案2：使用编译后的代码

```bash
# 先编译
npm run build

# 然后启动
npm run server:build
```

### 方案3：使用测试服务器

```bash
# 启动测试服务器（端口3003）
PORT=3003 node test-server.js
```

### 方案4：使用集成服务器

```bash
# 启动集成服务器（自动检测可用的控制器）
node start-chat-server.js
```

## 🔧 配置文件

### package.json 脚本已更新：

```json
{
  "scripts": {
    "server": "ts-node src/server/index.ts",
    "server:build": "npm run build && node dist/server/index.js",
    "server:dev": "nodemon --exec \"ts-node src/server/index.ts\"",
    "server:watch": "nodemon --exec \"ts-node src/server/index.ts\"",
    "server:prod": "NODE_ENV=production npm run server:build"
  }
}
```

### nodemon.json 配置：

```json
{
  "watch": [
    "src/server",
    "src/providers", 
    "src/services",
    "src/types",
    "src/utils"
  ],
  "ext": "ts,js,json",
  "ignore": [
    "node_modules",
    "dist",
    "public",
    "*.test.ts",
    "*.spec.ts"
  ],
  "exec": "ts-node src/server/index.ts",
  "env": {
    "NODE_ENV": "development"
  },
  "delay": "1000",
  "verbose": true
}
```

## 🚀 推荐的开发流程

### 1. 首次启动
```bash
# 安装依赖（如果还没有）
npm install

# 启动开发服务器
npm run server:dev
```

### 2. 如果遇到问题
```bash
# 方案A: 编译后启动
npm run build
npm run server:build

# 方案B: 使用测试服务器
PORT=3000 node test-server.js

# 方案C: 使用集成服务器
node start-chat-server.js
```

## 🔍 故障排除

### 问题1: nodemon 命令不存在
```bash
# 安装 nodemon
npm install --save-dev nodemon
```

### 问题2: ts-node 编译错误
```bash
# 检查 TypeScript 配置
npx tsc --noEmit

# 或者先编译再运行
npm run build
node dist/server/index.js
```

### 问题3: 端口被占用
```bash
# 使用不同端口
PORT=3001 npm run server:dev

# 或者杀死占用端口的进程
lsof -ti:3000 | xargs kill -9
```

## 📡 服务器访问地址

启动成功后，您可以通过以下地址访问：

- **API 服务器**: http://localhost:3000
- **聊天界面**: http://localhost:3000/chat (如果使用集成服务器)
- **健康检查**: http://localhost:3000/health
- **模型列表**: http://localhost:3000/api/models

## 🔄 文件监听

使用 `npm run server:dev` 时，nodemon 会监听以下文件变化：

- `src/server/**/*.ts` - 服务器代码
- `src/providers/**/*.ts` - Provider 代码  
- `src/services/**/*.ts` - 服务代码
- `src/types/**/*.ts` - 类型定义
- `src/utils/**/*.ts` - 工具函数

文件变化时会自动重启服务器。

## 🎯 开发建议

1. **使用 nodemon 进行开发** - 自动重启，提高开发效率
2. **定期编译检查** - 运行 `npm run build` 确保没有类型错误
3. **使用测试服务器调试** - 当主服务器有问题时的备选方案
4. **查看日志输出** - 注意控制台的错误和警告信息

## 📝 常用命令总结

```bash
# 开发模式（推荐）
npm run server:dev

# 生产模式
npm run server:prod

# 编译检查
npm run build

# 测试API
npm run test:api

# 启动聊天界面
node start-chat-server.js
```

---

**现在您可以使用 `npm run server:dev` 来启动带有文件监听功能的开发服务器了！**
