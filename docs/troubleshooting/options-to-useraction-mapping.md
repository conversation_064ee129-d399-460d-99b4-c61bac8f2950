# Options 参数到 UserAction 参数映射转换说明

## 概述

本文档说明了如何将 chat 接口中的 `options` 参数转换为当贝 API 所需的 `userAction` 参数格式。

## 背景

根据 `调用流程.md` 文件中的对话请求流程，当贝 API 的 chat 接口需要一个 `userAction` 参数来指定用户的操作类型。该参数是一个逗号分隔的字符串，用于控制 AI 的行为模式。

## 映射规则

### 基本映射关系

| Options 参数 | 值 | UserAction 对应值 | 说明 |
|-------------|----|--------------------|------|
| `deep_thinking` | `true` | `"deep"` | 启用深度思考模式 |
| `online_search` | `true` | `"online"` | 启用联网搜索功能 |

### 组合规则

多个选项启用时，使用逗号分隔：

| Options 配置 | UserAction 结果 |
|-------------|-----------------|
| `{ deep_thinking: false, online_search: false }` | `""` |
| `{ deep_thinking: true, online_search: false }` | `"deep"` |
| `{ deep_thinking: false, online_search: true }` | `"online"` |
| `{ deep_thinking: true, online_search: true }` | `"deep,online"` |

## 实现方式

### 1. 转换函数

位置：`src/utils/options-converter.ts`

```typescript
/**
 * 将chat接口的options参数转换为当贝API的userAction参数
 */
export function convertOptionsToUserAction(options?: ChatOptionsConfig): string {
  if (!options) return '';
  
  const enabledActions: string[] = [];
  
  if (options.deep_thinking === true) {
    enabledActions.push('deep');
  }
  
  if (options.online_search === true) {
    enabledActions.push('online');
  }
  
  return enabledActions.join(',');
}
```

### 2. 数据流转

```
API 请求 (ChatRequest.options) 
    ↓
Chat Controller (传递 options)
    ↓  
Chat Service (ChatOptions.options)
    ↓
buildChatRequest (调用转换函数)
    ↓
当贝 API 请求 (ChatRequest.userAction)
```

### 3. 关键修改点

1. **类型定义扩展** (`src/types/chat.ts`)
   - 在 `ChatOptions` 接口中添加 `options` 字段

2. **控制器层修改** (`src/server/controllers/chat-controller.ts`)
   - 在构建 `chatOptions` 时传递 `options` 参数

3. **服务层修改** (`src/services/chat-service.ts`)
   - 在 `buildChatRequest` 方法中使用转换函数
   - 将转换结果赋值给 `userAction` 字段

## 使用示例

### API 请求示例

```json
{
  "messages": [
    {
      "role": "user",
      "content": "请分析当前AI技术发展趋势"
    }
  ],
  "model": "deepseek",
  "stream": true,
  "options": {
    "deep_thinking": true,
    "online_search": true
  }
}
```

### 转换后的当贝API请求

```json
{
  "stream": true,
  "botCode": "AI_SEARCH",
  "conversationId": "364979999117803909",
  "question": "请分析当前AI技术发展趋势",
  "model": "deepseek",
  "chatOption": {
    "searchKnowledge": true,
    "searchAllKnowledge": false,
    "searchSharedKnowledge": false
  },
  "userAction": "deep,online",
  "role": "user",
  "status": "local",
  "content": "请分析当前AI技术发展趋势"
}
```

## 验证和测试

### 参数验证

转换工具提供了参数验证功能：

```typescript
import { validateChatOptions } from '../utils/options-converter';

const validation = validateChatOptions(options);
if (!validation.valid) {
  console.error('Options 参数验证失败:', validation.errors);
}
```

### 调试日志

在转换过程中会输出调试日志：

```
[ChatService] 构建聊天请求 - options: {"deep_thinking":true,"online_search":true}, userAction: "deep,online"
```

## 扩展性

### 添加新选项

要添加新的选项映射，需要：

1. 在 `ChatOptionsConfig` 接口中添加新字段
2. 在 `USER_ACTION_MAPPING` 中添加映射关系
3. 在 `convertOptionsToUserAction` 函数中添加转换逻辑

### 示例：添加知识库搜索选项

```typescript
// 1. 扩展接口
export interface ChatOptionsConfig {
  deep_thinking?: boolean;
  online_search?: boolean;
  knowledge_search?: boolean; // 新增
}

// 2. 扩展映射
const USER_ACTION_MAPPING = {
  deep_thinking: 'deep',
  online_search: 'online',
  knowledge_search: 'knowledge' // 新增
} as const;

// 3. 扩展转换逻辑
if (options.knowledge_search === true) {
  enabledActions.push(USER_ACTION_MAPPING.knowledge_search);
}
```

## 注意事项

1. **参数类型**：所有 options 参数都应该是布尔值类型
2. **默认值**：未提供的参数默认为 `false`
3. **空值处理**：如果没有任何选项启用，`userAction` 为空字符串
4. **顺序保持**：转换结果中选项的顺序与代码中的检查顺序一致
5. **向后兼容**：现有的不使用 options 的请求仍然正常工作
