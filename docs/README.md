# 当贝AI Provider 项目文档

## 📚 文档导航

### 🎯 核心文档
- [项目总览](../README.md) - 项目介绍和快速开始
- [项目架构](../ARCHITECTURE.md) - 系统架构和设计说明
- [使用指南](../USAGE.md) - 详细使用说明
- [API文档](api.md) - API接口文档
- [开发指南](development.md) - 开发环境搭建和贡献指南

## 📁 文档结构

本目录现已重新组织，按功能分类管理文档：

### 📊 项目报告 (`project-reports/`)
- [项目综合总结](project-reports/PROJECT_COMPREHENSIVE_SUMMARY.md) - 完整的项目技术总结
- [项目状态报告](project-reports/PROJECT_STATUS_REPORT.md) - 当前项目状态和进展
- [结构重组报告](project-reports/PROJECT_STRUCTURE_REORGANIZATION_REPORT.md) - 项目结构优化记录
- [文档整理报告](project-reports/DOCUMENTATION_CLEANUP_REPORT.md) - 文档优化成果
- [测试整理方案](project-reports/TEST_SCRIPTS_CLEANUP_PLAN.md) - 测试脚本优化
- [技术成果总结](project-reports/TECHNICAL_ACHIEVEMENTS.md) - 技术实现成就

### 📝 变更日志 (`changelogs/`)
- [主要变更日志](changelogs/CHANGELOG.md) - 版本变更记录
- [聊天优化日志](changelogs/CHANGELOG_CHAT_OPTIMIZATION.md) - 聊天功能优化
- [UI修复日志](changelogs/UI_FIXES_CHANGELOG.md) - 界面问题修复
- [发布说明](changelogs/RELEASE_NOTES.md) - 版本发布说明

### 📖 使用指南 (`guides/`)
- [项目架构设计](guides/ARCHITECTURE.md) - 系统架构和设计说明
- [集成使用指南](guides/INTEGRATION_GUIDE.md) - 项目集成方法
- [基础使用指南](guides/USAGE.md) - 基本功能使用
- [聊天功能指南](guides/CHAT_FEATURES.md) - 聊天界面功能特性
- [开发服务器指南](guides/DEV_SERVER_GUIDE.md) - 开发环境配置

### 📚 API文档 (`api-docs/`)
- [API接口文档](api-docs/api.md) - 核心API接口说明
- [HTTP API文档](api-docs/HTTP_API_README.md) - HTTP API服务器文档
- [聊天界面API](api-docs/CHAT_INTERFACE_README.md) - 聊天界面功能和使用说明
- [API测试工具](api-docs/API_TESTER_README.md) - API测试工具使用说明
- [API快速参考](api-docs/API_QUICK_REFERENCE.md) - API快速参考手册

### 🔍 技术分析 (`technical-analysis/`)
- [签名算法综合分析](technical-analysis/SIGNATURE_ANALYSIS_COMPREHENSIVE.md) - 完整的签名算法逆向分析
- [V2签名实现总结](technical-analysis/V2_SIGNATURE_IMPLEMENTATION_SUMMARY.md) - V2版本签名算法
- [WASM签名使用指南](technical-analysis/WASM_SIGNATURE_USAGE.md) - WebAssembly签名模块
- [接口版本差异](technical-analysis/v1-v2-接口差异说明.md) - V1/V2接口对比
- [逆向工程记录](technical-analysis/当贝AI_v2_chat_签名逆向_对话整理.md) - 逆向工程过程记录

### 🚀 功能特性 (`feature-docs/`)
- [模型调用指南](feature-docs/模型调用指南.md) - AI模型使用说明
- [支持的模型列表](feature-docs/支持的模型列表.md) - 可用模型列表
- [流式输出功能](feature-docs/流式输出功能使用指南.md) - 流式响应实现
- [Markdown渲染](feature-docs/增强Markdown渲染功能说明.md) - Markdown渲染增强
- [界面样式调整](feature-docs/AI回答样式调整说明.md) - 样式调整说明

### 🛠️ 开发指南 (`development-guides/`)
- [开发环境搭建](development-guides/development.md) - 开发环境配置
- [浏览器调试技巧](development-guides/devtools-定位签名算法.md) - 浏览器调试方法
- [V2调试指南](development-guides/v2-browser-debug-guide.md) - V2接口调试技巧
- [网络拦截分析](development-guides/拦截器签名定位_实操手册.md) - 网络调试实操

### 🔧 故障排除 (`troubleshooting/`)
- [快速修复指南](troubleshooting/QUICK_FIX_GUIDE.md) - 常见问题快速解决
- [错误修复说明](troubleshooting/write-after-end-error-fix.md) - 具体错误修复方法
- [SSE处理优化](troubleshooting/sse-processor-deduplication-removal.md) - 流式响应问题解决
- [配置映射说明](troubleshooting/options-to-useraction-mapping.md) - 配置问题排查

### 🚀 部署方案
- [静态部署方案](静态部署方案/README.md) - 聊天界面静态部署完整方案



## 🎯 快速导航

### 新用户入门
1. 阅读 [项目总览](../README.md) 了解项目基本信息
2. 查看 [使用指南](../USAGE.md) 学习基本使用方法
3. 参考 [聊天界面使用指南](CHAT_INTERFACE_README.md) 使用聊天功能

### 开发者指南
1. 阅读 [项目架构](../ARCHITECTURE.md) 了解系统设计
2. 查看 [开发指南](development.md) 搭建开发环境
3. 参考 [API文档](api.md) 进行接口开发

### 部署运维
1. 查看 [静态部署方案](静态部署方案/README.md) 了解部署选项
2. 参考 [HTTP API文档](HTTP_API_README.md) 配置API服务
3. 使用 [开发服务器指南](../DEV_SERVER_GUIDE.md) 搭建测试环境

### 问题排查
1. 查看 [故障排除指南](API_TESTER_TROUBLESHOOTING.md) 解决常见问题
2. 参考 [快速修复指南](QUICK_FIX_GUIDE.md) 快速修复
3. 查看 [更新日志](../CHANGELOG.md) 了解已知问题和修复

## 📋 文档分类

### 按功能分类

#### 🤖 AI对话功能
- [聊天界面使用指南](CHAT_INTERFACE_README.md)
- [聊天功能说明](../CHAT_FEATURES.md)
- [模型支持列表](支持的模型列表.md)
- [流式输出功能](流式输出功能使用指南.md)

#### 🔐 签名和认证
- [WASM签名实现](WASM_SIGNATURE_IMPLEMENTATION_SUMMARY.md)
- [V2签名实现](V2_SIGNATURE_IMPLEMENTATION_SUMMARY.md)
- [签名算法分析](signature-v2-analysis.md)
- [WASM使用指南](WASM_SIGNATURE_USAGE.md)

#### 🌐 部署和运维
- [静态部署方案](静态部署方案/README.md)
- [HTTP API文档](HTTP_API_README.md)
- [开发服务器指南](../DEV_SERVER_GUIDE.md)
- [API测试工具](API_TESTER_README.md)

#### 🎨 界面和体验
- [Markdown渲染](增强Markdown渲染功能说明.md)
- [UI修复日志](../UI_FIXES_CHANGELOG.md)
- [聊天优化日志](../CHANGELOG_CHAT_OPTIMIZATION.md)

### 按用户类型分类

#### 👥 最终用户
- [项目总览](../README.md)
- [使用指南](../USAGE.md)
- [聊天界面使用指南](CHAT_INTERFACE_README.md)
- [快速修复指南](QUICK_FIX_GUIDE.md)

#### 👨‍💻 开发者
- [项目架构](../ARCHITECTURE.md)
- [开发指南](development.md)
- [API文档](api.md)
- [技术实现文档](WASM_SIGNATURE_IMPLEMENTATION_SUMMARY.md)

#### 🔧 运维人员
- [静态部署方案](静态部署方案/README.md)
- [HTTP API文档](HTTP_API_README.md)
- [故障排除指南](API_TESTER_TROUBLESHOOTING.md)
- [开发服务器指南](../DEV_SERVER_GUIDE.md)

#### 🧪 测试人员
- [API测试工具](API_TESTER_README.md)
- [API测试示例](API_TEST_EXAMPLES.md)
- [测试文档](../tests/deployment/README.md)

## 🔄 文档维护

### 文档更新原则
- 保持文档与代码同步更新
- 重要变更及时更新相关文档
- 定期检查文档的准确性和完整性
- 收集用户反馈持续改进文档

### 贡献指南
- 发现文档问题请提交Issue
- 欢迎提交Pull Request改进文档
- 新增功能请同步更新相关文档
- 遵循项目的文档规范和格式

## 📞 获取帮助

- **项目仓库**: https://git.atjog.com/aier/dangbei-provider
- **问题反馈**: 项目Issues页面
- **技术讨论**: 项目Discussions页面
- **文档问题**: 提交Issue标记为documentation

---

**当贝AI Provider** - 功能强大、文档完善的AI对话解决方案！
