# 当贝AI v1/v2接口差异说明

## 概述

通过详细的逆向工程分析和实际测试，我们发现当贝AI的v1和v2接口使用了完全不同的签名算法。本文档详细说明了两个版本之间的差异、当前的实现状态以及使用建议。

## 接口版本识别

### v1接口
- **URL特征**: 包含 `/v1/` 路径
- **典型接口**:
  - `/ai-search/commonApi/v1/generateId` - ID生成
  - `/ai-search/conversationApi/v1/batch/create` - 对话创建
- **状态**: ✅ 完全支持，签名算法已破解

### v2接口
- **URL特征**: 包含 `/v2/` 路径或 `chatApi`
- **典型接口**:
  - `/ai-search/chatApi/v2/chat` - 聊天对话
- **状态**: ⚠️ 部分支持，签名算法未完全破解

## 签名算法差异

### v1接口签名算法（已验证）

```
规范化串 = 请求体的JSON字符串（POST）或查询参数（GET）
签名字符串 = timestamp + 规范化串 + nonce
签名 = MD5(签名字符串).toUpperCase()
```

**验证状态**: ✅ 完全正确，与真实API请求100%匹配

### v2接口签名算法（未完全破解）

我们尝试了多种可能的算法：

1. **策略1: METHOD + PATH**
   ```
   规范化串 = "POST /ai-search/chatApi/v2/chat"
   签名字符串 = timestamp + 规范化串 + nonce
   签名 = MD5(签名字符串).toUpperCase()
   ```

2. **策略2: 简化算法**
   ```
   签名字符串 = timestamp + nonce
   签名 = MD5(签名字符串).toUpperCase()
   ```

3. **策略3: 设备相关算法**
   ```
   签名字符串 = timestamp + deviceId + appVersion + nonce
   签名 = MD5(签名字符串).toUpperCase()
   ```

**验证状态**: ❌ 所有策略都无法匹配真实API的签名

## 当前实现策略

### 自动版本检测

系统会自动检测接口版本并应用相应的签名算法：

```typescript
// 版本检测逻辑
function isV2Interface(url: string): boolean {
  return url.includes('/v2/') || url.includes('chatApi');
}
```

### 错误处理机制

当v2接口签名失败时，系统会：

1. **详细错误分析**: 识别错误类型和可能原因
2. **友好错误提示**: 提供具体的解决建议
3. **调试信息输出**: 在开发模式下显示详细的签名尝试过程

### 降级策略

- v1接口：正常工作，无需降级
- v2接口：提供详细的错误信息和解决建议

## 使用建议

### 对于开发者

1. **优先使用v1接口**: 如果功能可以通过v1接口实现，建议优先使用
2. **v2接口调试**: 如需使用v2接口，请参考错误报告中的建议
3. **开发模式**: 在开发时设置 `NODE_ENV=development` 以获取详细的调试信息

### 对于用户

1. **基础功能**: ID生成、对话创建等基础功能完全可用
2. **聊天功能**: 目前受限于v2签名算法问题，正在持续改进
3. **错误处理**: 系统会提供友好的错误提示和解决建议

## 技术细节

### 签名验证测试

我们使用真实的API请求数据进行了详细验证：

```javascript
// v1接口验证（成功）
const v1Request = {
  timestamp: 1755239240,
  nonce: 'OOZpusZl8ANtYIAXqUkgP',
  expectedSign: '0D2DA56E3D33440213FCC5B1326C959B',
  // ... 其他参数
};
// 结果: ✅ 签名匹配

// v2接口验证（失败）
const v2Request = {
  timestamp: 1755239241,
  nonce: 'QL4MKOwQFtSmnhCOmNjde',
  expectedSign: '460D94E7C6980A6973494BC75D075905',
  // ... 其他参数
};
// 结果: ❌ 所有尝试的算法都无法匹配
```

### 错误代码说明

- **5002**: 签名验证失败，通常表示签名算法不匹配
- **4001**: 认证失败，可能是签名或其他认证参数错误

## 未来改进计划

1. **持续分析**: 继续分析v2接口的签名算法
2. **社区合作**: 欢迎社区贡献v2签名算法的破解
3. **官方支持**: 考虑联系当贝AI获取官方的API文档

## 示例代码

### 基本使用（推荐）

```javascript
const { DangbeiProvider } = require('dangbei-provider');

const provider = new DangbeiProvider({
  debug: true // 开启调试模式
});

// v1接口 - 完全支持
const conversation = await provider.createConversation();
console.log('对话ID:', conversation.conversationId);

// v2接口 - 部分支持，会显示详细错误信息
try {
  const response = await provider.chatSync({
    conversationId: conversation.conversationId,
    question: '你好'
  });
  console.log('回复:', response);
} catch (error) {
  console.log('v2接口暂时不可用:', error.message);
  // 系统会自动显示详细的错误分析和解决建议
}
```

### 错误处理示例

```javascript
// 在开发模式下，系统会自动输出详细的错误报告
NODE_ENV=development node your-script.js

// 输出示例:
// 🚨 v2接口错误报告
// 错误类型: SIGNATURE_MISMATCH
// 错误信息: v2接口签名算法不匹配
// 💡 解决建议:
// 1. 当前v2接口使用了与v1不同的签名算法
// 2. 我们尝试了多种可能的算法组合，但都无法匹配
// ...
```

## 总结

- ✅ **v1接口**: 完全可用，签名算法已完美破解
- ⚠️ **v2接口**: 部分可用，提供详细的错误分析和解决建议
- 🔄 **持续改进**: 我们正在持续分析和改进v2接口的支持

建议开发者在当前阶段优先使用v1接口实现核心功能，同时关注项目更新以获取v2接口的最新支持状态。
