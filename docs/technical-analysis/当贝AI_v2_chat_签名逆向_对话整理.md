# 当贝 AI v2/chat 签名逆向 · 对话整理与阶段性结论

## 背景
- 目标：还原 ai.dangbei.com 对 /ai-search/chatApi/v2/chat 的 headers.sign 生成规则。
- 证据来源：
  - 浏览器构建产物 _app-72ae859153e99355.js 中的 wasm 调用链片段：g(e,t,n) → v(e,t) → r.get_sign(n, o, i, a)
  - 你提供的两组真实抓包参数（包含 e/t 及 a/o/n/i、timestamp、nonce、sign）。
  - 项目中“调用流程.md”、“docs/签名算法_来自_app_chunk_反推.md”等辅助文档。

## 关键代码语义（来自 _app chunk 的整理）
- 函数 g(e, t, n)：
  - 将字符串 e 以 UTF-8 写入 wasm 线性内存；
  - 返回内存指针；全局 p 记录写入的“字节长度”。
- 函数 v(e, t)：
  - n = g(e, __wbindgen_malloc, __wbindgen_realloc) → o = p（请求体字节长度）
  - i = g(t, __wbindgen_malloc, __wbindgen_realloc) → a = p（路径字节长度）
  - 调用 r.get_sign(n, o, i, a) 返回 sign。

> 推断：v2 的签名与“请求体、路径及其 UTF-8 字节长度”强相关；timestamp/nonce 亦参与，但组合方式未直接暴露。

## 真实样本（来自你提供的抓包）

### 样本 #1
- path: "/chatApi/v2/chat"（a=16）
- o=548
- n=1114120、i=1114672
- timestamp: 1755834473
- nonce: nhwYHmTJgwTiQtchXghtA
- sign: 63334E59ECA2FC0ABDBC3BFF6CD4A30D

### 样本 #2
- path: "/chatApi/v2/chat"（a=16）
- o=579
- n=1115216、i=1114120
- timestamp: 1755847191
- nonce: FuJA-9WY_YJ7F4CyWPMdf
- sign: 03664C2C56D8ADE666D2DB54C0570981

## 本仓库内实现与变更

### 1) SignatureV2Utils（主实现对齐“调用流程.md”规则）
- 文件：src/utils/signature-v2.ts
- 当前主策略（命名仍沿用 tryWasmEmulationAlgorithm，语义已对齐“调用流程.md”）：
  - GET：normalized = URL 中 ? 之后的 query string
  - POST：normalized = 原始 body 字符串（与发送一致）
  - sign = MD5(String(timestamp) + normalized + String(nonce)).toUpperCase()
- 调试结果：
  - v1 创建对话请求可复现签名；
  - v2/chat 的真实 sign 与该规则不一致（证明 v2 规则不同）。

### 2) WasmSignatureEmulator（实验性模拟）
- 文件：src/utils/wasm-signature-emulator.ts
- 做过两类探索性实现：
  - 用 UTF-8 字节长度替代字符串长度，靠近 wasm 语义；
  - 尝试“u32 小端长度 + 原始字节拼接 → MD5（大写）”的思路；
- 但最终与 v2 真实 sign 仍未一致，故当前不作为主路径，仅保留为研究参考。

### 3) 调试/验证脚本
- debug-signature.js：基于“调用流程.md”规则，对真实请求进行签名计算与对照输出。
- scripts/enumerate-v2-sign.js：
  - 新增的多样本枚举器；
  - 覆盖字符串级、原始字节级（u32 小端长度前缀）、指针 u32(n/i) 夹入、分步 MD5、加入 timestamp/nonce/常量“v2”等多套候选；
  - 支持多样本并发验证，搜索可同时命中两条样本的候选公式；目前未命中，提示仍有关键细节未覆盖。
- examples/v2-chat-signature.ts：最小化复算与调试输出（字节长度、算法中间值）。

## 阶段性结论
- v1 / generateId / createConversation：遵循“timestamp + normalized + nonce”（normalized=POST body 或 GET query）规则，可稳定复现。
- v2/chat：真实签名与上述通用规则不同；结合 wasm 证据，强关联“body/path/其字节长度”，但组合仍未知。
- 已构建“多样本枚举器”，尝试多类高信号组合仍未命中，后续需扩展覆盖或进行运行时 hook。

## 下一步计划
1. 扩展枚举器覆盖：
   - 变换顺序：u32(o)+body+u32(a)+path、u32(a)+path+u32(o)+body、以及混合指针 u32(n/i) 位置；
   - 段尾 0x00、BE/LE 切换、长度前后缀对齐；
   - 路径参与方式微调（仅长度、不含文本，或仅部分字节）。
2. DevTools Hook：在浏览器对 r.get_sign 调用前断点，打印：
   - 写入内存的原始字节片段（十六进制）；
   - 参与拼接的所有段（含顺序、分隔与大小端）；
   - 中间哈希与最终 sign 的输入基串。
3. 样本追加：如能提供 1～2 条不同 body 的 v2/chat 样本（含 timestamp、nonce、sign、n、o、i、a），可用“多样本交集”快速锁定规则。

## 使用说明
- 运行签名对照（调用流程规则）：
  - node debug-signature.js
- 运行 v2 多样本枚举：
  - node scripts/enumerate-v2-sign.js
- v2 主签名生成：
  - 当前以“调用流程.md 规则”作为保底；待命中真实公式后替换。

## 变更记录（本次）
- 新增：docs/当贝AI_v2_chat_签名逆向_对话整理.md（本文档）
- 新增/更新：scripts/enumerate-v2-sign.js（多样本枚举器）
- 调整：src/utils/signature-v2.ts（主实现对齐调用流程.md；v2 与 v1 行为差异已注明）
- 说明：examples/v2-chat-signature.ts、src/utils/wasm-signature-emulator.ts 仍作为探索验证用。

## 附：调试日志建议模板
```
[签名调试] method=POST url=/ai-search/chatApi/v2/chat timestamp=17558xxxxx nonce=abcDEF...
[签名调试] bodyBytesLen=579 pathBytesLen=16
[签名调试] 尝试方案: u32(o)+u32(a)+body+path+ts+nonce
[签名调试] 中间哈希: h1=... h2=...
[签名调试] 最终 sign: 03664C2C56D8ADE666D2DB54C0570981
```

