# 当贝AI 签名算法（来自 _app chunk 的静态反推）

> 目的：基于你提供的 _app-72ae859153e99355.js 与相关 runtime 片段，复原 headers.sign 的生成流程；明确 O(e,t) 与 S() 的实际职责与边界，给出可复现实现与核验要点。

---

## 1. 关键代码证据与语义化描述

- O(e,t) 的实现（语义化重构）：

```js
O = function(req, ctx) {
  if (req.method === "GET") {
    const qIndex = req.url.indexOf("?");
    if (qIndex !== -1) {
      // 仅取查询字符串（不含 ?），不包含 path
      return req.url.substring(qIndex + 1);
    }
  } else if (req.method === "POST") {
    // 取请求体原始字符串，若不存在则为空串
    return ctx.body ?? "";
  }
  // 其他方法统一返回空串
  return "";
}
```

- 签名生成两步：

```js
const a = O(e, t); // 规范化结果：GET→query string；POST→body；其他→""
const s = S()(String(o) + a + String(i)).toUpperCase(); // s 即 sign
```

- 参与字段说明：
  - `o`：timestamp（秒级 Unix 时间戳）
  - `a`：来自 O(e,t) 的规范化串（见上）
  - `i`：nonce（一次性随机串）

---

## 2. S() 的来源与哈希判定

- S() 是 webpack 的默认导出“兼容访问器”包装：`var S = r.n(mod)`，`S()` 实际返回 `mod.default`（若为 ESM）或 `mod`（若为 CJS）。
- 你提供的对应实现片段：

```js
e.exports = function (input, opts) {
  if (input == null) throw Error("Illegal argument " + input);
  const bytes = t.wordsToBytes(a(input, opts));
  // 输出形式：优先 asBytes / asString，否则默认十六进制
  return opts && opts.asBytes ? bytes
       : opts && opts.asString ? i.bytesToString(bytes)
       : t.bytesToHex(bytes);
}
```

- 配套的 `15561` 模块提供 UTF-8 与二进制编解码（`utf8.stringToBytes`, `bin.stringToBytes`, `bytesToHex` 等），说明该哈希函数按“字节→十六进制”产出。
- 结合 sign 最终调用了 `.toUpperCase()` 且线上 sign 为 32 位十六进制：高度符合 **MD5（hex）→ 大写** 的形态。

> 结论：`S()` 返回一个“字符串 → 十六进制字符串”的哈希函数实现；默认输出为小写 hex，我们再 `.toUpperCase()` 统一成大写。算法强烈指向 MD5。

---

## 3. 最终签名公式

- 规范化串：`normalized = O(e,t)`：
  - GET：仅取 URL 中 `?` 后的查询字符串（不含 `?` 与路径部分）；若无 `?` 则为空串
  - POST：取请求体原始字符串 `t.body`（不存在则空串）
  - 其他方法：空串

- 拼接顺序与算法：

```text
baseString = String(timestamp) + normalized + String(nonce)
sign       = MD5(baseString).toUpperCase()
```

---

## 4. 可复现实现（TypeScript 伪代码）

```ts
/**
 * 还原 O(e,t)：根据 method 返回 query 或 body 的原始串
 */
function normalizeForSign(req: { method: string; url: string }, ctx: { body?: string }): string {
  if (req.method === 'GET') {
    const i = req.url.indexOf('?');
    return i !== -1 ? req.url.substring(i + 1) : '';
  }
  if (req.method === 'POST') {
    return ctx.body ?? '';
  }
  return '';
}

/**
 * 生成签名：MD5( timestamp + normalized + nonce ).toUpperCase()
 * 注意：timestamp 需为“秒级”，body/qs 必须与实际发送完全一致（空白/转义/顺序）。
 */
function generateSign(params: {
  timestampSec: number; // 秒级
  method: 'GET' | 'POST' | string;
  url: string;          // 完整 URL，用于 GET 取 ? 后的查询串
  bodyRaw?: string;     // POST 原始 body 串
  nonce: string;
}): string {
  const normalized = normalizeForSign({ method: params.method, url: params.url }, { body: params.bodyRaw });
  const base = String(params.timestampSec) + normalized + String(params.nonce);
  const md5hexLower = md5(base); // 假设 md5() 返回小写 hex
  return md5hexLower.toUpperCase();
}
```

---

## 5. 实战核验要点

- 时间戳：必须为 **秒级**；如果请求体内有毫秒级字段，不应直接参与上述拼接。
- GET：仅使用 `?` 后的 **query string 原样串**；不要加入路径、协议、域名；参数顺序与编码必须与实际请求一致。
- POST：必须使用 **原始 body 字符串**（JSON 的空白/字段顺序/转义决定最终签名）。建议在序列化后缓存同一份 body 用于签名与发送，避免两次 stringify 造成差异。
- 统一大写：`.toUpperCase()` 是协议的一部分，不要遗漏。
- 其他方法：O(e,t) 返回空串，等价于对 `timestamp + "" + nonce` 取 MD5（除非后续版本扩展）。

---

## 6. 与既有推断的差异说明

- 早先推测 O(e,t) 可能包含 method/path/query/body 的组合；当前基于你提供的 **实际 O 实现**，明确：
  - 只取 GET 的查询串或 POST 的 body；未拼入 method/path。
- 因此在 SDK/Gateway 侧复刻时，应严格按本文规则实现与校验。

---

## 7. 调试日志模板（建议接入）

```text
[签名调试] method=POST url=/ai-search/... timestamp=1755252943 nonce=abcDEF-123
[签名调试] normalized(len=128)=<POST body 原文前80字节...>
[签名调试] base=1755252943<normalized>abcDEF-123
[签名调试] md5(lower)=9cc214fb53ddaf31dc1bfe453d14c468 → sign(upper)=9CC214FB53DDAF31DC1BFE453D14C468
```

---

## 8. 风险与变更监控

- 若线上后续将 method/path 纳入签名，或对 body/query 做排序/规范化，此文规则需同步更新。
- 建议在发版前通过浏览器 DevTools 断点再次核对 O(e,t) 与 S() 的实现是否有变。

