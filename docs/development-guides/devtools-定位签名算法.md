# DevTools 定位 /ai-search/chatApi/v2/chat 签名算法调试指南

> 目的：在浏览器 DevTools 中快速定位 Next 构建产物里对 `/ai-search/chatApi/v2/chat` 请求“签名生成与头注入”的代码位置，便于设置断点与动态核验。

---

## 1. 背景与关键结论

- 统一签名逻辑位于全局 HTTP 客户端的请求拦截器（axios interceptors）中，随页面初始化加载。
- 产物路径（重点）：
  - `ai.dangbei.com/_next/static/chunks/pages/_app-72ae859153e99355.js`
- 拦截器会为所有 `/ai-search/*` 请求（含 `/ai-search/chatApi/v2/chat`）注入请求头：`timestamp`、`nonce`、`sign`、`deviceId`、`appType`、`appVersion`、`client-ver`、`lang` 等。
- 核心签名公式（语义重构）：
  - `sign = MD5( String(timestampSec) + normalized + String(nonce) ).toUpperCase()`
  - normalized：
    - GET：URL 中 `?` 之后的查询串（不含 `?`）
    - POST：原始 body 字符串（与实际发送完全一致）

> 注意：`timestamp` 为“秒级”；POST 时，参与签名的 **原始 body 串** 必须与最终发送的内容完全一致（包括空白、转义、字段顺序）。

---

## 2. 在 DevTools 中的快速定位步骤

1) 打开 Sources 并格式化
- 浏览器 DevTools → Sources
- 展开：`ai.dangbei.com/_next/static/chunks/pages/_app-72ae859153e99355.js`
- 点击左下角 `{}`（Prettify/格式化），便于阅读

2) 全局搜索锚点关键词（Ctrl/Cmd + F）
- `interceptors.request.use`
- `https://ai-api.dangbei.net/ai-search` 或 `baseUrl`
- `timestamp`、`nonce`、`sign`、`md5`、`toUpperCase`
- 可能出现的函数短名：`O(e,t)`（规范化函数）、`S()`（MD5 计算）

3) 断点建议（逐步观察数据）
- 在请求拦截器内部：
  - 生成 `timestamp` 的行（确保为“秒级”）
  - 生成 `nonce` 的行
  - 计算 `normalized`（规范化请求串）的行
  - 计算 `sign` 的行（`MD5(...).toUpperCase()`）
  - 将 `timestamp`、`nonce`、`sign` 写入 `headers` 的行
- 在 XHR/Fetch 层：
  - 添加 XHR/Fetch 断点：匹配子串 `chatApi/v2/chat`

4) 触发请求并验证
- 在站内发起一次聊天（点击“发送”/提问）
- 命中断点后，按步骤查看：
  - `timestamp`（秒级数值）
  - `nonce`（随机串）
  - `normalized`：
    - GET：查看 URL 的查询串
    - POST：查看 body 原始 JSON 字符串
  - `sign`：确认是否为 `MD5(timestamp + normalized + nonce).toUpperCase()`
  - `config.headers`：是否包含 `timestamp`、`nonce`、`sign` 等

---

## 3. `/ai-search/chatApi/v2/chat` 调用点辅助定位

- 业务调用通常在页面 chunk（例如 `ai.dangbei.com/_next/static/chunks/pages/chat-*.js`）中以 `fetch` 或 `axios.post` 调用。
- 在 DevTools 全局搜索：`"/ai-search/chatApi/v2/chat"`
- 在调用处也可下断，结合调用栈（Call Stack）回溯至请求拦截器，确认签名注入链路。

---

## 4. 关键逻辑的语义化片段（便于识别）

- 规范化函数（示意）：

```js
function normalizeForSign(method, url, bodyRaw) {
  if (method === 'GET') {
    const i = url.indexOf('?');
    return i >= 0 ? url.slice(i + 1) : '';
  }
  if (method === 'POST') {
    return bodyRaw || '';
  }
  return '';
}
```

- 签名计算（示意）：

```js
const normalized = normalizeForSign(method, url, bodyRaw);
const base = String(timestampSec) + normalized + String(nonce);
const sign = md5(base).toUpperCase();
```

> 在压缩产物中，常见形态是 `const a = O(e,t); const s = S()((o) + a + (i)).toUpperCase();`

---

## 5. XHR/Fetch 断点快速配置

- 打开 DevTools → Sources → 右侧面板 “XHR Breakpoints”
- Add breakpoint: 输入 `chatApi/v2/chat`
- 重新触发请求，自动在匹配的网络请求上中断，便于查看调用栈与请求参数。

---

## 6. 常见陷阱与排障

- [签名不一致] POST 的 `body` 串若与最终发送内容在空格、转义、字段顺序上存在任何差异，`sign` 将对不上。
- [时间戳混淆] 有些代码体内保存的是“毫秒级”，签名使用时需转换为“秒级”。确认拦截器中实际参与签名的数值。
- [搜索不到符号] 生产构建会压缩变量名，优先通过关键词定位拦截器、`baseUrl` 与请求头注入逻辑，再逆向确认函数职责。

---

## 7. 实战中的中文调试日志（建议在断点处通过 Console 打印）

```js
console.log('调试-时间戳(秒):', timestamp);
console.log('调试-随机串nonce:', nonce);
console.log('调试-规范化串normalized:', normalized);
console.log('调试-签名基串:', String(timestamp) + normalized + String(nonce));
console.log('调试-计算签名sign:', sign);
console.log('调试-请求头:', config && config.headers);
```

---

## 8. 仓库内的辅助资料

- `docs/签名算法_来自_app_chunk_反推.md`：基于 _app chunk 的静态反推说明
- `docs/当贝AI签名_整合分析报告.md`：签名整体分析与证据链
- `调用流程.md`：真实请求示例（含头部与 body）

---

## 9. 变更记录

- 初始版本：新增 DevTools 调试指北，覆盖产物定位、断点建议、验证步骤与常见问题。

