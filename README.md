# 当贝AI Provider SDK

一个功能完整的当贝AI API调用SDK，提供简洁易用的TypeScript接口来访问当贝AI的对话功能。该项目经过深入的逆向工程分析，实现了完整的API流程封装和WebAssembly签名算法支持。

## 🌟 核心特性

- 🚀 **完整的API封装** - 支持对话创建、消息发送、流式响应等所有功能
- 🔐 **多层签名算法** - 支持v1/v2接口签名，集成WebAssembly高性能签名模块
- 📱 **智能设备管理** - 自动生成和管理设备标识，支持自定义配置
- 🌊 **流式响应处理** - 完整的Server-Sent Events实时消息流支持
- 🛡️ **健壮错误处理** - 完善的错误分类、重试机制和降级策略
- 📝 **TypeScript支持** - 完整的类型定义和智能提示
- 🧪 **全面测试覆盖** - 89个测试用例，覆盖单元测试和集成测试
- ⚡ **版本兼容性** - 同时支持v1和v2接口，自动处理版本差异
- 🔧 **WebAssembly集成** - 高性能WASM签名模块，支持原生和模拟器双模式

## 安装

```bash
npm install dangbei-provider
```

## 快速开始

### 基础用法

```typescript
import { DangbeiProvider } from 'dangbei-provider';

// 创建Provider实例
const provider = new DangbeiProvider({
  debug: true // 开启调试日志
});

// 快速聊天（自动创建对话）
async function quickChat() {
  try {
    const response = await provider.quickChat('你好，请介绍一下自己');
    console.log('AI回复:', response.content);
  } catch (error) {
    console.error('聊天失败:', error);
  }
}

quickChat();
```

### 多模型调用

基于 `models.json` 中的模型列表，支持多种AI模型调用：

```javascript
// 使用DeepSeek-R1进行逻辑推理
const logicResponse = await provider.chatSync({
  conversationId: 'your-conversation-id',
  question: '请分析：为什么1+1=2？请从数学逻辑角度详细解释',
  model: 'deepseek'  // DeepSeek-R1最新版 - 专注逻辑推理
});

// 使用DeepSeek-V3进行代码分析
const codeResponse = await provider.chatSync({
  conversationId: 'your-conversation-id',
  question: '请解释这段代码的功能：function fibonacci(n){if(n<=1)return n;return fibonacci(n-1)+fibonacci(n-2);}',
  model: 'deepseek-v3'  // DeepSeek-V3 - 代码专家，响应极快
});

// 使用GLM-4.5进行创意写作
const creativeResponse = await provider.chatSync({
  conversationId: 'your-conversation-id',
  question: '请写一首关于人工智能的现代诗',
  model: 'glm-4-5'  // GLM-4.5 - 智谱旗舰模型
});

// 使用豆包-1.6进行推理思考
const thinkingResponse = await provider.chatSync({
  conversationId: 'your-conversation-id',
  question: '请分析区块链技术的优缺点',
  model: 'doubao-1_6-thinking'  // 豆包-1.6 - 创作推理数学增强
});
```

### 快速测试

运行模型测试脚本：

```bash
# 快速测试主要模型
node test-models.js quick

# 深度功能测试（不同场景）
node test-models.js deep

# 流式响应测试
node test-models.js stream

# 运行所有测试
node test-models.js all
```

### 流式聊天

```typescript
import { DangbeiProvider, ChatCallbacks } from 'dangbei-provider';

const provider = new DangbeiProvider();

async function streamChat() {
  // 创建对话
  const conversation = await provider.createConversation();

  // 设置流式回调
  const callbacks: ChatCallbacks = {
    onMessage: (content, data) => {
      // 实时接收消息片段
      process.stdout.write(content);
    },
    onComplete: (data) => {
      console.log('\n聊天完成，消息ID:', data.id);
    },
    onError: (error) => {
      console.error('聊天错误:', error);
    }
  };

  // 发送消息
  await provider.chat({
    conversationId: conversation.conversationId,
    question: '请详细介绍一下人工智能的发展历史',
    callbacks
  });
}

streamChat();
```

## API文档

### DangbeiProvider

主要的Provider类，提供所有API功能。

#### 构造函数

```typescript
new DangbeiProvider(options?: DangbeiProviderOptions)
```

**参数:**
- `options.deviceConfig` - 设备配置（可选）
- `options.timeout` - 请求超时时间，默认30000ms
- `options.retries` - 重试次数，默认3次
- `options.debug` - 是否开启调试日志，默认false

#### 主要方法

##### createConversation(options?)

创建新的对话会话。

```typescript
const conversation = await provider.createConversation({
  superAgentPath: '/chat', // 可选，默认'/chat'
  isAnonymous: false,      // 可选，默认false
  source: ''               // 可选，来源标识
});
```

##### chat(options)

发送聊天消息并接收流式响应。

```typescript
const response = await provider.chat({
  conversationId: 'your-conversation-id',
  question: '你的问题',
  model: 'doubao-1_6-thinking', // 可选，默认模型
  callbacks: {
    onMessage: (content, data) => console.log(content),
    onComplete: (data) => console.log('完成'),
    onError: (error) => console.error(error)
  }
});
```

##### chatSync(options)

发送聊天消息并等待完整响应。

```typescript
const content = await provider.chatSync({
  conversationId: 'your-conversation-id',
  question: '你的问题'
});
```

##### quickChat(question, callbacks?)

快速聊天，自动创建对话并发送消息。

```typescript
const response = await provider.quickChat('你好');
```

##### generateId(timestamp?)

生成唯一ID。

```typescript
const id = await provider.generateId();
```

##### 其他方法

- `stopChat()` - 停止当前聊天
- `getDeviceConfig()` - 获取设备配置
- `updateDeviceConfig(config)` - 更新设备配置
- `getStatus()` - 获取服务状态
- `checkServiceAvailability()` - 检查服务可用性
- `destroy()` - 销毁实例

## 配置选项

### DeviceConfig

设备配置选项：

```typescript
interface DeviceConfig {
  deviceId: string;      // 设备ID
  appType: number;       // 应用类型，固定为6
  appVersion: string;    // 应用版本
  clientVersion: string; // 客户端版本
  lang: string;          // 语言，默认'zh'
  userAgent: string;     // 用户代理
}
```

### ChatOptions

聊天配置选项：

```typescript
interface ChatOptions {
  conversationId: string;           // 对话ID（必需）
  question: string;                 // 问题内容（必需）
  model?: string;                   // 模型名称
  botCode?: string;                 // 机器人代码
  chatOption?: {                    // 聊天选项
    searchKnowledge?: boolean;
    searchAllKnowledge?: boolean;
    searchSharedKnowledge?: boolean;
  };
  callbacks?: ChatCallbacks;        // 回调函数
}
```

## 错误处理

SDK提供了完善的错误处理机制：

```typescript
import { DangbeiApiError, ErrorType } from 'dangbei-provider';

try {
  await provider.chat(options);
} catch (error) {
  if (error instanceof DangbeiApiError) {
    console.log('错误类型:', error.type);
    console.log('错误代码:', error.code);
    console.log('请求ID:', error.requestId);

    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        console.log('网络连接失败');
        break;
      case ErrorType.API_ERROR:
        console.log('API调用失败');
        break;
      case ErrorType.PARAMETER_ERROR:
        console.log('参数错误');
        break;
      // ... 其他错误类型
    }
  }
}
```

## 高级用法

### 自定义设备配置

```typescript
const provider = new DangbeiProvider({
  deviceConfig: {
    deviceId: 'your-custom-device-id',
    lang: 'en',
    appVersion: '2.0.0'
  }
});
```

### 使用底层服务

```typescript
import {
  HttpClient,
  ConversationService,
  ChatService,
  DeviceUtils
} from 'dangbei-provider';

// 创建自定义HTTP客户端
const httpClient = new HttpClient({
  deviceId: DeviceUtils.generateDeviceId()
});

// 使用底层服务
const conversationService = new ConversationService(httpClient);
const chatService = new ChatService(httpClient);
```

## 开发和测试

### 安装依赖

```bash
npm install
```

### 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并监听变化
npm run test:watch

# 运行特定测试
npm test -- --testNamePattern="Provider"
```

### 构建项目

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
```

## 🔍 接口支持状态

### ✅ v1接口（完全支持）
- **接口示例**: `/ai-search/commonApi/v1/generateId`, `/ai-search/conversationApi/v1/batch/create`
- **状态**: 签名算法已完全实现，100%可用
- **功能**: ID生成、对话创建等基础功能
- **签名方式**: 标准MD5哈希算法

### 🚀 v2接口（WebAssembly增强支持）
- **接口示例**: `/ai-search/chatApi/v2/chat`
- **状态**: 通过WebAssembly模块实现高性能签名算法
- **功能**: 聊天对话功能，支持流式响应
- **签名方式**: 复杂的自定义算法，已通过逆向工程实现

### 🛠️ WebAssembly签名模块
- **原生WASM**: 基于逆向分析的原始算法实现
- **JavaScript模拟器**: 纯JS实现的备用方案
- **统一接口**: 自动选择最佳实现方式
- **性能优化**: 支持批量签名和内存管理

### 使用建议
1. **生产环境**: 推荐使用WebAssembly模块获得最佳性能
2. **开发调试**: 设置 `debug: true` 获取详细调试信息
3. **降级策略**: 自动在WASM和JavaScript实现间切换

详细说明请参考：
- [v1/v2接口差异说明](./docs/v1-v2-接口差异说明.md)
- [WebAssembly签名模块使用指南](./WASM_README.md)

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。特别欢迎对v2接口签名算法的研究和贡献。

## 📈 项目统计

- **代码行数**: 5000+ 行TypeScript/JavaScript代码
- **测试覆盖**: 89个测试用例，覆盖核心功能
- **文档完整性**: 15+ 个详细文档文件
- **WebAssembly模块**: 完整的WASM签名算法实现
- **逆向工程**: 深度分析当贝AI签名算法
- **架构设计**: 分层架构，易于维护和扩展

## 📁 项目结构

```
dangbei-provider/
├── src/                    # 源代码
│   ├── providers/         # Provider实现
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript类型定义
│   ├── server/            # HTTP API服务器
│   └── wasm/              # WebAssembly模块
├── tests/                 # 测试套件
│   ├── api/              # API和签名测试
│   ├── features/         # 功能特性测试
│   ├── tools/            # 工具函数测试
│   ├── deployment/       # 部署配置测试
│   ├── unit/             # 单元测试
│   └── integration/      # 集成测试
├── deployment/            # 静态部署方案
│   ├── caddy/            # Caddy配置
│   └── nginx/            # Nginx配置
├── docs/                  # 项目文档
│   └── 静态部署方案/      # 部署文档
├── tools/                 # 开发工具
├── scripts/               # 构建脚本
├── examples/              # 使用示例
├── public/                # 静态资源
│   └── chat/             # 聊天界面
├── archive/               # 历史文件归档
│   ├── legacy-docs/      # 历史文档
│   ├── legacy-scripts/   # 历史脚本
│   ├── legacy-tests/     # 历史测试
│   └── legacy-tools/     # 历史工具
└── dist/                  # 编译输出
```

## 📚 文档索引

### 核心文档
- [API参考文档](./docs/api.md) - 完整的API接口说明
- [模型调用指南](./docs/模型调用指南.md) - 详细的AI模型使用说明
- [开发指南](./docs/development.md) - 项目架构和开发说明
- [WebAssembly使用指南](./docs/WASM_SIGNATURE_USAGE.md) - WASM模块详细说明

### 技术分析
- [签名算法综合分析](./docs/SIGNATURE_ANALYSIS_COMPREHENSIVE.md) - 完整的逆向工程分析
- [项目综合总结](./PROJECT_COMPREHENSIVE_SUMMARY.md) - 项目完整技术总结
- [v2签名实现总结](./docs/V2_SIGNATURE_IMPLEMENTATION_SUMMARY.md) - v2接口技术细节

### 实操指南
- [DevTools定位签名算法](./docs/devtools-定位签名算法.md) - 浏览器调试技巧
- [拦截器签名定位实操手册](./docs/拦截器签名定位_实操手册.md) - 网络拦截分析

### 示例代码
- [模型调用示例](./examples/model-usage-examples.ts) - TypeScript完整示例
- [简化JavaScript示例](./examples/model-usage-simple.js) - JavaScript快速上手
- [基础功能演示](./examples/basic-usage.ts) - 基本功能使用
- [高级功能演示](./examples/advanced-usage.ts) - 高级特性展示
- [模型测试脚本](./test-models.js) - 快速测试工具

## 🔄 更新日志

### v1.0.0 (当前版本)

#### 🚀 核心功能
- 完整的当贝AI API调用封装
- v1/v2接口全面支持
- WebAssembly高性能签名模块
- 流式响应和实时消息处理

#### 🔧 技术实现
- TypeScript严格模式开发
- 89个测试用例全面覆盖
- 分层架构设计
- 完善的错误处理和重试机制

#### 📖 文档和工具
- 15+ 个详细技术文档
- 完整的逆向工程分析报告
- 丰富的示例代码和使用指南
- 开发调试工具集