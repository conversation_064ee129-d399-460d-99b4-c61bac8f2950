(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3536],{23748:e=>{e.exports={"recommend-model":"ActionGroup_recommend-model__emn4P","recommend-model-content":"ActionGroup_recommend-model-content__ssgkZ",btn:"ActionGroup_btn__BCstp"}},30427:e=>{e.exports={"deep-thinking-container":"DeepThinking_deep-thinking-container__tkFQK","deep-thinking-markdown":"DeepThinking_deep-thinking-markdown__9c_e9","md-code-block":"DeepThinking_md-code-block__a9RyV","deep-thinking-collapse":"DeepThinking_deep-thinking-collapse__C5GST","is-active":"DeepThinking_is-active__ECNL7","deep-thinking-collapse--header":"DeepThinking_deep-thinking-collapse--header__As80g","deep-thinking-collapse--body":"DeepThinking_deep-thinking-collapse--body__6pnb4"}},34890:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var r=t(23798);t(21462);var o=t(41028),i=t.n(o),a=t(92079),s=t(1450);let l=function(e){var n=e.data,t=e.imgSize,o=void 0===t?160:t,l=e.children,c=e.style,u=n.iconUrl,d=n.createShowPerson,x=n.name,m=n.description,p=(0,a.n)().isMobile;return(0,r.jsxs)("div",{className:i()["ai-entity-card"],style:c,children:[(0,r.jsxs)("div",{className:i()["ai-entity-card-header"],children:[(0,r.jsx)("img",{height:p?80:o,width:p?80:o,src:u,alt:"Icon",className:i()["ai-entity-icon"]}),(0,r.jsx)("div",{className:"text-24 mb-8",children:x}),(0,r.jsxs)("div",{className:"text-12 text-[var(--text-color-tertiary)]",children:["创建人 ",d]})]}),(0,r.jsx)("div",{className:(0,s.cx)(i()["ai-entity-card-description"],p?"text-14":"text-16"),children:m}),l]})}},35167:e=>{e.exports={"step-progress-card":"StepProgressCard_step-progress-card__VwRsc","step-progress-panel":"StepProgressCard_step-progress-panel__oT_xq"}},36882:e=>{e.exports={"container-loading-arrow":"components_container-loading-arrow__Uaw1r","loading-border":"components_loading-border__AlmCg","rotate-border":"components_rotate-border__8lbS_","model-talk-box":"components_model-talk-box__dRvSw","model-talk-box-top-item":"components_model-talk-box-top-item__thLub","model-talk-box-bottom-item":"components_model-talk-box-bottom-item__ZuLoy","model-box":"components_model-box__V378j","not-box":"components_not-box__XRXCz",title:"components_title__8_SrL"}},39735:(e,n,t)=>{"use strict";t.d(n,{t:()=>j,A:()=>A});var r=t(23798),o=t(21462),i=t(42420),a=t(31635),s=t(1450),l=t(29935),c=t(12694),u=t(82643),d=t(81549),x=t(67657),m=t(78454),p=t(41209),h=t(34566),f=t(35654),v=t(3217),y=t(34890),g=t(35476),b=t(92079);let k=function(e){var n,t=e.data,i=e.onClearContext,a=(0,b.n)().isTabletOrMobile,k=(n=(0,l._)(function(){return(0,u.YH)(this,function(e){switch(e.label){case 0:return[4,(0,g.m1e)({body:{agentId:t.agentId}})];case 1:return e.sent(),d.Ay.success("已清除上下文"),i(),[2]}})}),function(){return n.apply(this,arguments)}),_=(0,c._)((0,o.useState)(!1),2),w=_[0],j=_[1],A=function(){return(0,r.jsx)(y.A,{data:t,imgSize:120,children:(0,r.jsx)("div",{className:"ai-entity-card-delete",children:(0,r.jsx)(x.Ay,{className:"!bg-transparent text-[var(--text-color-secondary)]",variant:"outlined",block:!0,icon:(0,r.jsx)(h.A,{type:"icon-DeleteOutlined"}),onClick:k,children:"清除上下文"})})})};return a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.A,{style:{fontSize:16},component:v.A,onClick:function(){j(!0)}}),(0,r.jsxs)(m.A,{placement:"bottom",open:w,classNames:{body:"scrollbar-none"},styles:{content:{borderRadius:"16px 16px 0 0"},body:{position:"relative",padding:8,paddingTop:32},header:{display:"none"}},maskClosable:!0,onClose:function(){return j(!1)},children:[(0,r.jsx)(h.A,{type:"icon-CloseOutlined",className:"text-16 absolute right-[20px] top-[20px]",onClick:function(){return j(!1)}}),(0,r.jsx)(A,{})]})]}):(0,r.jsx)(p.A,{content:(0,r.jsx)("div",{style:{width:368,padding:"20px 4px 4px"},children:(0,r.jsx)(A,{})}),placement:"bottomRight",trigger:"click",color:"#ffffff",arrow:!1,align:{offset:[-4,16]},children:(0,r.jsx)("button",{className:(0,s.cx)("hover:bg-[var(--bg-color-secondary)] bg-transparent mr-8 !border-0 w-32 h-32 rounded-8 cursor-pointer"),children:(0,r.jsx)(h.A,{type:"icon-MoreOutlined",className:"text-20 text-[var(--text-color-primary)]"})})})};var _=t(78182),w=t.n(_),j=(0,o.createContext)({agentInfo:void 0});function A(e){var n=e.agentInfo,t=(0,o.useRef)(null),l=(null==n?void 0:n.conversationId)||"local_"+(null==n?void 0:n.agentId);return n&&(null==n?void 0:n.agentId)?(0,r.jsx)(j.Provider,{value:{agentInfo:n},children:(0,r.jsx)(a.A,{className:(0,s.cx)(w()["chat-page"],"!max-w-[100%]"),pageTitle:null==n?void 0:n.name,avatar:null==n?void 0:n.iconUrl,headerRight:n&&(0,r.jsx)(k,{data:n,onClearContext:function(){var e;null===(e=t.current)||void 0===e||e.onClearContext()}}),children:(0,r.jsx)(i.A,{ref:t,id:l,agentInfo:n})})}):null}},41028:e=>{e.exports={"ai-entity-card":"AiEntityCard_ai-entity-card__8YXBd","ai-entity-card-header":"AiEntityCard_ai-entity-card-header__0vYfW","ai-entity-card-description":"AiEntityCard_ai-entity-card-description__4KLD3","ai-entity-card-actions":"AiEntityCard_ai-entity-card-actions__yZ9D8","ai-entity-icon":"AiEntityCard_ai-entity-icon__rZAGf"}},42420:(e,n,t)=>{"use strict";t.d(n,{t:()=>eo,A:()=>er});var r=t(29935),o=t(17844),i=t(93629),a=t(12694),s=t(82643),l=t(23798),c=t(21462),u=t(99782),d=t(33794),x=t(36645),m=t(1450),p=t(94069),h=t.n(p),f=t(35476),v=t(91013),y=t(93489),g=t(30054),b=t(51062),k=t(81549),_=t(80721),w=t(80667),j=t(67657),A=t(54435),I=t(8448),N=t(86105),C=t(17459),S=t(38204),R=t(31541),T=t(74023),L=t(67297),M=t(23555),E=function(e){var n,t,l=e.roomConfig,u=e.agentId,d=void 0===u?"":u,x=e.isTourism,p=void 0!==x&&x,h=e.anonymousKey,y=e.userAction,g=e.modelRef,b=e.onChatCompleted,k=e.defaultMessages,_=(0,M.Bd)().t,w=(0,v.A)(function(e){return e.questionIdsMap}),j=(0,v.A)(function(e){return e.setMapCenter}),A=(0,c.useMemo)(function(){return"cancel_".concat(_("common.ansTer"),"_cancel")},[_]),I=(0,a._)((0,c.useState)(!1),2),E=I[0],D=I[1],O="",F=(0,c.useRef)(),z=(0,c.useRef)(function(){}),B=(0,c.useCallback)((n=(0,r._)(function(e,n){var t,r,a,c,u,x,h,v,y,k,_,I,C,R,T,M,E,B,P,q,H,U,V,W,Y,K,J,$,Z;return(0,s.YH)(this,function(Q){switch(Q.label){case 0:t=e.message,r=n.onSuccess,a=n.onUpdate,O=u=(0,L.Ak)(6),x=g.current,y={role:"ai",uuid:u,chatId:null==t?void 0:t.chatId,pid:null==t?void 0:t.uuid,status:"loading",isDeep:null==t?void 0:null===(c=t.userAction)||void 0===c?void 0:c.includes("deep"),question:(null==t?void 0:t.question)||"",content:"",supportDownload:!1,networkContent:void 0,tourismContent:void 0,deepThinking:void 0,suggestion:[],files:null!==(h=null==t?void 0:t.files)&&void 0!==h?h:[],knowledgeList:null!==(v=null==t?void 0:t.knowledgeList)&&void 0!==v?v:void 0,model:null!=x?x:void 0,current:0,conversationId:l.conversationId,chatCmd:(0,i._)((0,o._)({},null!=t?t:{}),{conversationId:l.conversationId}),travelResultReferences:void 0},k=function(){y.networkContent&&(y.networkContent.loading=!1,y.networkContent.dataSource?y.networkContent.dataSource.status=2:y.networkContent=void 0),"success"!==y.status&&(y.status="success"),r(y)},Q.label=1;case 1:return Q.trys.push([1,6,,7]),_=function(){var e,n,t,r,o,i,c,u,d,p,h,f,v;return(0,s.YH)(this,function(s){switch(s.label){case 0:return[4,B.read()];case 1:n=(e=s.sent()).value,t=e.done;try{if(o=(r=null!=n?n:{}).data,i=r.event,u=(c=(0,m.jU)(o)?JSON.parse(o):{}).type,d=c.content,p=c.content_type,h=c.supportDownload,t)return null==b||b(l.conversationId,y),Y=-2,k(),[2,"break"];switch(i){case"conversation.message.delta":y.msgId||P(c.id,c.parentMsgId),"answer"===u&&("progress_card_item"===p||"progress"===p||"card"===p||"travel_result_card"===p||"progress_card_title"===p||"travel_location"===p||"travel_intent"===p||"travel_plan"===p)&&q(p,d),"answer"===u&&"thinking"===p&&H(d),"answer"===u&&"text"===p&&U(d,h,x);break;case"conversation.followup.loading":y.status="followupLoading",y.suggestion=y.suggestion||[];break;case"conversation.chat.failed":"answer"===u&&"text"===p&&V(d);break;case"conversation.message.completed":W(u,d)}"ai"===y.role&&(v=[],null===(f=G.current)||void 0===f||f.forEach(function(e){e.message.chatId===y.chatId&&"ai"===e.message.role&&(v=e.message.answerList||[],Y<0&&(Y=v.findIndex(function(e){return e.id===y.msgId})),Y>-1?v[Y]={status:"loading",message:y,id:y.msgId}:v.push({status:"loading",message:y,id:y.msgId}))}),y.answerList=0===v.length?void 0:v,y.current=Math.max(v.length-1,0)),a(y)}catch(e){throw Error(e)}return[2]}})},D(!0),I=new AbortController,z.current=function(){I.abort()},C=(0,i._)((0,o._)({},l,t),{agentId:d||""}),d?(R=f.$tQ,C.userAction="retry"===C.userAction?"dd":""):p?(R=f.tBT,C.userAction="retry"===C.userAction?"retry":""):R=f.Zw4,[4,R({body:C,parseAs:"stream",signal:I.signal})];case 2:if(M=(T=Q.sent()).response,E=T.error,200!==M.status)throw Error(null==E?void 0:E.errMessage);B=(0,S.A)({readableStream:M.body}).getReader(),z.current=function(){I.abort(),y.content||(y.content=A),null==B||B.cancel()},P=function(e,n){y.msgId=e,(null==t?void 0:t.uuid)&&w.set(null==t?void 0:t.uuid,n)},q=function(e,n){if("travel_result_card"===e){var t=JSON.parse(n);y.travelResultReferences=t;return}if("progress_card_item"===e){var r=JSON.parse(n),o=r.type,i=r.name,a=r.content;if(y.tourismContent){if("2004"==o){if(""===i){var s,c=null!==(s=y.tourismContent.cardItems)&&void 0!==s?s:[];c[(null==c?void 0:c.length)-1].content?c[(null==c?void 0:c.length)-1].content+=a:c[(null==c?void 0:c.length)-1].content=a,y.tourismContent.cardItems=(0,N._)(c)}else y.tourismContent.cardItems=(0,N._)(y.tourismContent.cardItems||[]).concat([r])}else y.tourismContent.cardItems=(0,N._)(y.tourismContent.cardItems||[]).concat([r])}else y.tourismContent={cardItems:[r]};r.isStart&&(y.tourismContent.status="loading"),r.isEnd&&(y.tourismContent.status="success");return}if("progress_card_title"===e){null==b||b(l.conversationId,y,!0),y.tourismContent?y.tourismContent.title=n:y.tourismContent={title:n};return}if("travel_location"===e){j(null==n?void 0:n.split(","));return}if(y.networkContent||(y.networkContent={}),"progress"===e){y.networkContent.loading=!0,y.networkContent.title=n,y.networkContent.titleShort=n,y.status="updating";return}if("card"===e){var u=JSON.parse(n);y.networkContent.dataSource=u.cardInfo,y.networkContent.cardType=u.cardType,y.networkContent.loading&&u.cardInfo&&(clearTimeout(F.current),F.current=setTimeout(function(){y.networkContent&&(y.networkContent.loading=!1)},1e3))}},H=function(e){y.deepThinking||(y.deepThinking={content:"",loading:!0}),y.deepThinking.content+=null!=e?e:""},U=function(e,n,t){y.content+=e,y.supportDownload=n,y.model=t,y.networkContent&&y.networkContent.loading&&(y.networkContent.loading=!1,y.networkContent.dataSource||(y.networkContent=void 0)),y.deepThinking&&(y.deepThinking.loading=!1),"loading"===y.status&&(y.status="updating")},V=function(e){y.content+=e,"loading"===y.status&&(y.status="updating")},W=function(e,n){if("follow_up"===e){var t;null===(t=y.suggestion)||void 0===t||t.push(n)}},Y=-2,Q.label=3;case 3:if(!B)return[3,5];return[5,(0,s.Ju)(_())];case 4:if("break"===Q.sent())return[3,5];return[3,3];case 5:return u==O&&D(!1),[3,7];case 6:if(K=Q.sent(),D(!1),"AbortError"===(J=K).name)return y.content=A,k(),[2];if("TypeError"===J.name&&"Failed to fetch"===J.message){if((null==y?void 0:null===($=y.deepThinking)||void 0===$?void 0:$.content)||(null==y?void 0:null===(Z=y.networkContent)||void 0===Z?void 0:Z.dataSource)||(null==y?void 0:y.content))return k(),[2];return y.content=navigator.onLine?"<i><span>服务器繁忙，请稍后再试！</span></i>":"<i><span>当前你的网络不稳定</span></i>",y.status="success",k(),[2]}throw J;case 7:return[2]}})}),function(e,t){return n.apply(this,arguments)}),[l,b]),P=(0,a._)((0,R.A)({request:B}),1)[0],q=(0,c.useRef)(!1);q.current||(q.current=!0);var H=(0,T.A)({agent:P,defaultMessages:k,requestPlaceholder:{role:"ai",content:"Waiting...",status:"loading"},requestFallback:function(){return{role:"error",question:"服务器繁忙，请稍后再试！",content:"服务器繁忙，请稍后再试！"}}}),U=H.onRequest,V=H.messages,W=H.setMessages,G=(0,c.useRef)(V);(0,c.useEffect)(function(){G.current=V},[V]);var Y=(0,c.useCallback)(function(e){for(var n,t=(0,N._)(G.current),r=-1,o=t.length-1;o>-1;o--)if(t[o].message.uuid===e||t[o].id===e||(null===(n=t[o].message.answerList)||void 0===n?void 0:n.some(function(n){return n.message.uuid===e}))){r=o;break}if(-1!==r){var i=t[r].message,a=i.current,s=i.answerList;s&&(null==s?void 0:s.length)!==1?(void 0!==a&&(null==s||s.splice(a,1),t[r].message.current=a>s.length-1?s.length-1:a),t[r].message.answerList=s):t.splice(r,1),W(t)}},[W]),K=(0,c.useCallback)((t=(0,r._)(function(e){var n,t,r;return(0,s.YH)(this,function(a){return U((0,i._)((0,o._)({},e),{role:"user",uuid:e.chatId,status:"local",anonymousKey:h,model:g.current,content:e.question||"",userAction:d?null===(n=e.userAction)||void 0===n?void 0:n.join(","):(0,N._)(null!==(t=y.current)&&void 0!==t?t:[]).concat((0,N._)(null!==(r=e.userAction)&&void 0!==r?r:[])).join(","),chatId:e.chatId})),[2]})}),function(e){return t.apply(this,arguments)}),[U,h,g,d,y]),J=(0,c.useCallback)(function(e){for(var n=e.chatId,t=(0,C._)(e,["chatId"]),r=(0,N._)(G.current),a=-1,s=r.length-1;s>-1;s--)if(r[s].message.chatId===n&&"ai"===r[s].message.role){a=s;break}r[a].message.userAction="retry",W(r),K((0,i._)((0,o._)({chatId:n},t),{userAction:["retry"]}))},[K,W]),$=(0,c.useCallback)(function(e,n){for(var t=(0,N._)(G.current),r=-1,o=t.length-1;o>-1;o--)if(t[o].message.chatId===e&&"ai"===t[o].message.role){r=o;break}-1!==r&&(t[r].message.current=n,W(t))},[W]),Z=(0,c.useCallback)(function(){z.current()},[]);return(0,c.useEffect)(function(){return function(){return z.current()}},[]),{loading:E,setLoading:D,onRequest:U,messages:V,questionIdsMap:w,setMessages:W,onCancelReply:Z,onChatSubmit:K,onRetryGenerate:J,onRemoveChatHistroy:Y,onSwitchAnswer:$}},D=t(67931),O=t(3335),F=t(43218),z=t(77177),B=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(t?(0,N._)(e).reverse():(0,N._)(e)).map(function(e){var t={role:"user"===e.role?"user":"ai",msgId:e.msgId,chatId:e.chatId,uuid:(0,L.Ak)(6),typing:!1,question:null==e?void 0:e.question,isDeep:null===(r=e.ext)||void 0===r?void 0:r.isDeep,content:null!==(i=e.content)&&void 0!==i?i:"",status:"success",supportDownload:null===(o=e.ext)||void 0===o?void 0:o.supportDownload,recommendStatus:e.recommendStatus,conversationId:e.conversationId};if("user"===e.role){if(t.question=e.content,(null===(a=e.ext)||void 0===a?void 0:a.chatCmd)&&(0,m.jU)(null===(s=e.ext)||void 0===s?void 0:s.chatCmd)){var r,o,i,a,s,l,c,u=JSON.parse(null===(c=e.ext)||void 0===c?void 0:c.chatCmd)||{};t.userAction=u.userAction,t.chatOption=u.chatOption,t.knowledgeList=u.knowledgeList||[]}(null===(l=e.ext)||void 0===l?void 0:l.references)&&(t.reference=JSON.parse(e.ext.references)),e.files&&e.files.length>0&&(t.files=e.files)}if("assistant"===e.role){var d,x=null,p=null,h=null,f=null;if(null===(v=e.ext)||void 0===v?void 0:v.searchReferences){var v,y,g,b,k,_,w,j,A,I,N=JSON.parse(e.ext.searchReferences),C=N.cardInfo,S=N.cardType,R=N.shortTitle,T=N.title,M=C.cardItems.find(function(e){return"2002"===e.type||"2003"===e.type});x={dataSource:C,loading:!1,title:T||(null==M?void 0:M.name),titleShort:R?R||T:null!==(I=null==M?void 0:M.shortName)&&void 0!==I?I:null==M?void 0:M.name,cardType:S}}if(null===(y=e.ext)||void 0===y?void 0:y.travelProcessReferences){var E=JSON.parse(e.ext.travelProcessReferences);p={cardItems:E.cardItems,title:E.title,cardType:E.cardType,status:"success"}}if((null===(g=e.ext)||void 0===g?void 0:g.travelResultReferences)&&(f=JSON.parse(e.ext.travelResultReferences)),(null===(b=e.ext)||void 0===b?void 0:b.thinking)&&(h={loading:!1,content:null===(d=e.ext)||void 0===d?void 0:d.thinking}),t.uuid=(0,L.Ak)(6),t.networkContent=x,t.deepThinking=h,t.isDeep=null===(k=e.ext)||void 0===k?void 0:k.isDeep,t.ctxClearFlag=null===(_=e.ext)||void 0===_?void 0:_.ctxClearFlag,t.content=e.content,t.model=e.model||"",t.current=Math.max((e.answerList||[]).length-1,0),t.chatCmd=JSON.parse((null===(w=e.ext)||void 0===w?void 0:w.chatCmd)||"{}"),t.tourismContent=p,t.travelResultReferences=f,e.answerList&&(t.answerList=B(e.answerList,n,!1)),(null===(j=e.ext)||void 0===j?void 0:j.followUp)&&!(null===(A=e.ext)||void 0===A?void 0:A.ctxClearFlag)){var D=JSON.parse(e.ext.followUp);D.length&&(t.suggestion=D)}}return{id:e.msgId,message:t,status:"success"}})},P=(0,c.forwardRef)(function(e,n){var t,u,d,x,m=e.index,p=e.conversationItem,h=e.onInited,y=e.setConversationList,b=e.agentInfo,k=e.isTourism,_=e.defaultMessagesRef,w=e.isSearch,j=p.conversationId,I=void 0===j?"":j,C=p.anonymousKey,S=(0,g.vS)(),R=S.globalModelConfig,T=S.isDisabledDeepSearch,M=S.isAnonymous,P=S.privateLibSearch,q=S.allLibSearch,H=S.sharedLibSearch,U=S.validImageTypes,V=S.showFile,W=S.isFile,G=S.modelNum,Y=S.isSearchDeep,K=(0,v.A)(function(e){return e.shareId}),J=(0,v.A)(function(e){return e.setJourneyId}),$=(0,v.A)(function(e){return e.setMsgId}),Z=(0,z.A)().updateChatList,Q=(0,v.A)(function(e){return e.setIsStreamDisplayEnabled}),X=(0,v.A)(function(e){return e.setShowTourism}),ee=(0,c.useContext)(F.M).setIsCollapsed,en=(0,c.useRef)(""),et=(0,c.useRef)([]);(0,c.useEffect)(function(){var e,n;1===G?en.current=null==R?void 0:null===(e=R[0])||void 0===e?void 0:e.model:en.current=null==R?void 0:null===(n=R[m])||void 0===n?void 0:n.model},[R,null==R?void 0:null===(d=R[m])||void 0===d?void 0:d.model,G]),(0,c.useEffect)(function(){if(w)et.current=[Y&&"deep"].filter(Boolean);else{var e,n,t,r,o;et.current=[!T&&(null==R?void 0:null===(n=R[m])||void 0===n?void 0:null===(e=n.options)||void 0===e?void 0:e.includes("deep"))&&!(null==R?void 0:null===(t=R[m])||void 0===t?void 0:t.option[0].disable)&&"deep",(null==R?void 0:null===(o=R[m])||void 0===o?void 0:null===(r=o.options)||void 0===r?void 0:r.includes("online"))&&"online"].filter(Boolean)}},[Y,R,null==R?void 0:null===(x=R[m])||void 0===x?void 0:x.options,T]);var er=(0,v.A)(function(e){return e.setLoading}),eo=(t=(0,r._)(function(e){var n,t,r,a,l,c,u,d,x;return(0,s.YH)(this,function(s){return n=e.value,t=e.writeCode,r=e.files,a=e.knowledges,l=e.chatId,c=e.reference,I&&(u=(0,L.Ak)(6),d={question:n,model:en.current,chatOption:{writeCode:""!==t?t:void 0,searchKnowledge:P,searchAllKnowledge:q,searchSharedKnowledge:H},knowledgeList:(a||[]).map(function(e){return(0,i._)((0,o._)({},e),{isCreator:1===e.role})}),anonymousKey:C,uuid:u,chatId:l,files:[],reference:[]},x=r.map(function(e){var n;return{type:U.includes(null!==(n=e.docType)&&void 0!==n?n:"")?"image":"file",fileId:e.docId,fileName:e.docName,fileType:e.docType,fileSize:e.docSize,wordNum:e.wordNum,fileUrl:e.fileUrl}}),V||W?d.files=x:d.reference=x,(null==c?void 0:c.length)&&(d.reference=c,d.files=[]),null==ea||ea(d),y(function(e){return e.map(function(e){return e.conversationId===I?(0,i._)((0,o._)({},e),{messageLoading:!0}):e})}),setTimeout(function(){er(!1),eR.current&&eR.current.scrollTo({top:eR.current.scrollHeight})},100)),[2]})}),function(e){return t.apply(this,arguments)}),ei=E({roomConfig:{stream:!0,botCode:A.Yr.AI_SEARCH,conversationId:I},agentId:null==b?void 0:b.agentId,isTourism:k,anonymousKey:C,userAction:et,modelRef:en,onChatCompleted:function(e,n,t){var r,a;if(k&&t&&!(null==n?void 0:null===(r=n.travelResultReferences)||void 0===r?void 0:r.journeyId)){J(""),X(!0),null==ee||ee(!0),$(n.msgId);return}k&&((null==n?void 0:null===(a=n.travelResultReferences)||void 0===a?void 0:a.journeyId)?(J(n.travelResultReferences.journeyId),$(n.msgId),X(!0),null==ee||ee(!0),null==Q||Q(!0)):(J(""),$(n.msgId),X(!1))),y(function(n){return n.map(function(n){return n.conversationId==e?(0,i._)((0,o._)({},n),{messageLoading:!1}):n})}),Z(e)},defaultMessages:0===m?_.current:[]}),ea=ei.onChatSubmit,es=ei.onCancelReply,el=ei.setMessages,ec=ei.messages,eu=ei.onRemoveChatHistroy,ed=ei.onSwitchAnswer,ex=ei.onRetryGenerate;(0,c.useEffect)(function(){K&&0!==ec.length&&(_.current=ec)},[_,ec,K]),(0,c.useImperativeHandle)(n,function(){return{conversationId:I||"",messages:ec,submit:eo,onCancelReply:es,onRemoveChatHistroy:eu,getMessageList:ev,onPostMessage:eN,clearContext:eS}});var em=(0,c.useRef)(),ep=(0,c.useRef)(!0),eh=(0,c.useRef)([]),ef=(0,v.A)(function(e){return e.recordExportConversationId}),ev=(0,c.useCallback)((0,r._)(function(){var e,n,t,r,a,l,c,u,d,x,p,v,y,g,b,k,_;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(ef||!ep.current)return[2];if(!K)return[3,2];if(0!==m)return[2];return r={order:"desc",pageSize:100,shareId:K},[4,(0,f.HNQ)({body:r})];case 1:return(null===(t=(a=s.sent()).data)||void 0===t?void 0:t.data)&&(c=(0,o._)({},null===(l=a.data)||void 0===l?void 0:l.data)).msgList&&(c.msgList.map(function(e){return e.recommendStatus=0,e}),n=null===(u=a.data)||void 0===u?void 0:u.data),[3,4];case 2:return x={order:"desc",limit:20,afterId:em.current},[4,(0,f.mS1)({body:(0,i._)((0,o._)({},x),{conversationId:I})})];case 3:(null===(d=(p=s.sent()).data)||void 0===d?void 0:d.data)&&(n=null===(v=p.data)||void 0===v?void 0:v.data,setTimeout(function(){er(!1)},500)),s.label=4;case 4:if(b=void 0===(g=(y=n||{}).msgList)?[]:g,ep.current=void 0!==(k=y.hasMore)&&k,em.current=null===(e=(_=B(b,m).map(function(e){return K&&(e.message.fromShare=!0),e}))[0])||void 0===e?void 0:e.id,eh.current=(0,N._)(_).concat((0,N._)(eh.current)),el(eh.current),h(I),(null==b?void 0:b.length)===0)return[2];return requestAnimationFrame(function(){var e=document.querySelector("#message_".concat(_[_.length-1].id));if(e){var n=e.querySelector(".action-group");n?n.scrollIntoView({block:"start"}):e.scrollIntoView({block:"start"})}else ey()}),[2]}})}),[I,eh,K,M,ef]);(0,c.useEffect)(function(){if(K){ev();return}!I||I.startsWith("local")||(M?h(I):ev())},[I,K]);var ey=function(){},eg=(0,v.A)(function(e){return e.setOnRemoveChatHistroy}),eb=(0,v.A)(function(e){return e.setOnSwitchAnswer}),ek=(0,v.A)(function(e){return e.setOnRetryGenerate}),e_=(0,v.A)(function(e){return e.setTmpShareId}),ew=(0,v.A)(function(e){return e.setRecordExportConversationId});(0,c.useEffect)(function(){!I||I.startsWith("local")||eg(I,eu)},[I,eg,eu]),(0,c.useEffect)(function(){!I||I.startsWith("local")||eb(I,ed)},[I,eb,ed]),(0,c.useEffect)(function(){!I||I.startsWith("local")||ek(I,function(e){ex(e),y(function(e){return e.map(function(e){return e.conversationId===I?(0,i._)((0,o._)({},e),{messageLoading:!0}):e})}),ey()})},[I,ek,y,ex]);var ej=(0,a._)(O.A.useForm(),1)[0],eA=(0,c.useCallback)((u=(0,r._)(function(e,n){var t,r,a,l;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(!e)return[3,4];t=(ej.getFieldValue("messages")||[]).map(function(t){return void 0===t||void 0===t.id||void 0===t.chatId?(0,i._)((0,o._)({},t),{checked:!1}):t.id===e||t.chatId===n?(0,i._)((0,o._)({},t),{checked:!0}):(0,i._)((0,o._)({},t),{checked:!1})}),null==ej||ej.setFieldValue("messages",t),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,(0,f.YAX)({body:{timestamp:Number(Date.now())}})];case 2:return(null===(r=(a=s.sent()).data)||void 0===r?void 0:r.success)&&(null==e_||e_(null!==(l=a.data.data)&&void 0!==l?l:""),ew(I)),[3,4];case 3:return console.error("创建分享链接失败:",s.sent()),[3,4];case 4:return[2]}})}),function(e,n){return u.apply(this,arguments)}),[]);(0,c.useEffect)(function(){if(ej&&!ef){var e=(ej.getFieldValue("messages")||[]).map(function(e){return(0,i._)((0,o._)({},e),{checked:!1})});ej.setFieldValue("messages",e)}},[ej,ef]);var eI=(0,v.A)(function(e){return e.setOnHandleShare});(0,c.useEffect)(function(){!I||I.startsWith("local")||eI(I,eA)},[I,eI,eA]);var eN=function(e,n){},eC=(0,v.A)(function(e){return e.setFormInstance});(0,c.useEffect)(function(){!I||I.startsWith("local")||eC(I,ej)},[I]);var eS=function(){el(function(e){if(e.length>0){var n;e[e.length-1].message.ctxClearFlag=!0,null===(n=e[e.length-1].message.answerList)||void 0===n||n.forEach(function(e){e.message.ctxClearFlag=!0})}return(0,N._)(e)})},eR=(0,c.useRef)(null);return(0,l.jsx)(O.A,{form:ej,style:{height:"100%",display:"flex"},children:(0,l.jsx)(D.A,{listRef:eR,messageList:ec,getMessageList:ev,index:m,conversationId:I,isRecordExport:ef===I,messageLoading:p.messageLoading})})}),q=t(36882),H=t.n(q),U=t(34566),V=t(50349),W=t(92079);let G=function(e){var n=e.children,t=e.modelIndex,r=e.conversationInfo,a=e.id,s=e.setConversationList,c=e.shareId,u=(0,g.vS)(),d=u.onSwitchModelChange,x=u.modelNum,p=(0,v.A)(function(e){return e.setRecordExportConversationId}),h=(0,m.bA)(),y=(0,W.n)().isMobile;return(0,l.jsxs)("div",{className:(0,m.cx)(H()["model-talk-box"]),children:[(0,l.jsxs)("div",{className:(0,m.cx)(H()["model-talk-box-top-item"]),children:[(0,l.jsx)(V.A,{modelPosition:Number(t)+1,type:"sender-custom",onChange:function(e){null==d||d({model:e,modelPosition:Number(t)+1})}}),(0,l.jsx)(U.A,{onClick:function(){var e,n,t;if(p(void 0),!r&&!c){h("/chat");return}s(function(e){return e.map(function(e){return e.conversationId===a?(0,i._)((0,o._)({},e),{hidden:!0}):e})}),r&&(0,f.xj_)({body:{parentConversationId:r.conversationId||"",multiModelLayout:{activeConversation:(null===(t=r.metaData)||void 0===t?void 0:null===(n=t.multiModelLayout)||void 0===n?void 0:null===(e=n.activeConversation)||void 0===e?void 0:e.map(function(e,n){return{conversationId:e.conversationId,isVisible:e.conversationId!==a,order:n}}))||[]}}})},type:"icon-CloseOutlined",className:"text-20"})]}),(0,l.jsx)("div",{className:1!==x||y?H()["model-talk-box-bottom-item"]:"overflow-auto h-full",children:n})]})};var Y=t(85141),K=t(29219),J=t(43857),$=t(47716),Z=t(79031),Q=t(81577),X=t(59626);let ee=function(){var e,n,t,o=(0,M.Bd)().t,i=null===(n=(0,Y.useRouter)().query)||void 0===n?void 0:n.id,u=(0,v.A)(function(e){return e.recordExportConversationId}),d=(0,v.A)(function(e){return e.objMap}),x=(0,c.useMemo)(function(){if(u)return d.get(u)},[d,u]),p=(0,v.A)(function(e){return e.tmpShareId}),h=(0,v.A)(function(e){return e.setRecordExportConversationId}),y=null!==(t=O.A.useWatch(["messages"],x))&&void 0!==t?t:[],g=y.filter(function(e){return e}).length,b=!g,_=y.filter(function(e){return null==e?void 0:e.checked}),A=!b&&_.length===g,I=(0,a._)((0,c.useState)(!1),2),N=I[0],C=I[1],S=(0,Q.i)(),R=(0,a._)((0,c.useState)(-1),2),T=R[0],L=R[1],E=(0,W.n)().isTabletOrMobile,D=(0,c.useContext)(F.M).setHideHeader,z=(0,a._)((0,c.useState)(!1),2),B=z[0],P=z[1];(0,c.useEffect)(function(){return u&&D(!0),function(){D(!1)}},[u]);var q=(e=(0,r._)(function(){var e;return(0,s.YH)(this,function(n){switch(n.label){case 0:if(0===_.length)return k.Ay.warning(o("common.selSha")),[2];C(!0),n.label=1;case 1:if(n.trys.push([1,5,6,7]),!(p&&u))return[3,4];return[4,S("".concat(window.location.origin,"/share/").concat(p))];case 2:return n.sent(),[4,(0,f.MJq)({body:{conversationId:i,shareId:p,msgIds:_.map(function(e){return e.id}),expireDate:T}})];case 3:(null===(e=n.sent().data)||void 0===e?void 0:e.success)?(k.Ay.success(o("common.linSuc")),null==h||h(void 0)):k.Ay.error(o("common.linFal")),n.label=4;case 4:return[3,7];case 5:return n.sent(),k.Ay.error(o("common.linOpe")),[3,7];case 6:return C(!1),[7];case 7:return[2]}})}),function(){return e.apply(this,arguments)});return(0,l.jsxs)("div",{className:(0,m.cx)("z-10 left-0 w-full bottom-0 py-16 bg-[var(--bg-color-primary)] border-[0px] border-t border-solid border-t-black/[0.06]",E?"!px-16":"px-24"),children:[(0,l.jsxs)(w.A,{align:"center",className:(0,m.cx)("!max-w-[840px] !mx-auto",E?"":"!pl-24 "),justify:"space-between",children:[(0,l.jsx)(w.A,{align:"center",gap:10,children:(0,l.jsx)($.A,{disabled:b,checked:A,onChange:function(e){var n=e.target.checked,t=y.map(function(e){return void 0===e||void 0===e.id||void 0===e.chatId||(e.checked=n),e});null==x||x.setFieldValue("messages",t)},children:o("common.selAll")})}),(0,l.jsxs)(w.A,{align:"center",gap:10,children:[(0,l.jsx)("span",{children:"有效期："}),(0,l.jsx)(Z.A,{style:{width:100,height:E?32:40},value:T,onChange:L,options:[{label:"永久",value:-1,key:"-1"},{label:"30天",value:30,key:"30"},{label:"7天",value:7,key:"7"},{label:"1天",value:1,key:"1"}]}),(0,l.jsx)(j.Ay,{size:E?"middle":"large",type:"primary",icon:(0,l.jsx)(U.A,{type:"icon-LinkOutlined"}),loading:N,onClick:function(){-1===T?P(!0):q()},style:{padding:E?"0 10px":"0 15px"},children:o("common.linCop")})]})]}),(0,l.jsx)(X.A,{visible:B,onOk:q,icon:(0,l.jsx)(U.A,{type:"icon-ExclamationCircleFilled",className:"text-24 text-[rgba(238,159,0,1)]"}),title:"确定该分享永久有效吗？",content:"该链接将永久有效，互联网上获得链接的人可访问，严禁分享违规内容。",onCancel:function(){return P(!1)}})]})};var en=t(78176),et=(0,c.forwardRef)(function(e,n){var t,d,x,p,N,C,S,R=e.id,T=e.agentInfo,L=e.onFilesDrop,M=e.isTourism,E=function(){e7.current.forEach(function(e,n){e&&(e.onCancelReply(),es(function(e){return e.map(function(e,t){return n===t?(0,i._)((0,o._)({},e),{messageLoading:!1}):e})}))})},D=function(e,n){return e8.apply(this,arguments)},O=null==T?void 0:T.agentId,B=(0,Y.useRouter)(),q=(0,W.n)(),H=q.isMobile,U=q.isTabletOrMobile,V=(null==B?void 0:null===(x=B.query)||void 0===x?void 0:x.source)||"",$=(null==B?void 0:null===(p=B.query)||void 0===p?void 0:p.shareId)||"",Z=(0,z.A)(),Q=Z.updateChatList,X=Z.chatList,et=(0,g.vS)(),er=et.isAnonymous,eo=et.globalModelConfig,ei=et.conversationList,ea=et.modelNum,es=et.setConversationList,el=et.setAnonymousKey,ec=et.setShowFile,eu=et.setIsFile,ed=et.setAllLibSearch,ex=et.setPrivateLibSearch,em=et.setSharedLibSearch,ep=(0,c.useContext)(F.M).pageTitle,eh=(0,v.A)(function(e){return e.reset}),ef=(0,v.A)(function(e){return e.setTitle}),ev=(0,v.A)(function(e){return e.setShareId}),ey=(0,v.A)(function(e){return e.recordExportConversationId}),eg=(0,v.A)(function(e){return e.setRecordExportConversationId}),eb=(0,v.A)(function(e){return e.conversationInfo}),ek=(0,v.A)(function(e){return e.setConversationInfo});(0,c.useEffect)(function(){ev($)},[ev,$]);var e_=(0,v.A)(function(e){return e.setLoading});function ew(){return(ew=(0,r._)(function(){var e,n,t,r,a,l,c,u,d,x;return(0,s.YH)(this,function(s){switch(s.label){case 0:return e_(!0),[4,(0,f.wIy)({body:{conversationId:R}})];case 1:if(null===(e=(n=s.sent()).data)||void 0===e?void 0:e.success){if(ek(n.data.data),null===(t=n.data.data)||void 0===t?void 0:t.conversationList){for(u=[],u=(null===(c=n.data.data)||void 0===c?void 0:null===(l=c.metaData)||void 0===l?void 0:null===(a=l.multiModelLayout)||void 0===a?void 0:a.activeConversation)?n.data.data.conversationList.map(function(e){var t,r,a,s,l;return(null===(l=n.data.data)||void 0===l?void 0:null===(s=l.metaData)||void 0===s?void 0:null===(a=s.multiModelLayout)||void 0===a?void 0:null===(r=a.activeConversation)||void 0===r?void 0:null===(t=r.find(function(n){return n.conversationId===e.conversationId}))||void 0===t?void 0:t.isVisible)||T?e:(0,i._)((0,o._)({},e),{hidden:!0})}):[n.data.data.conversationList[0]],d=window.sessionStorage.getItem("modelNum")||"1";u.length<Number(d);)u.push({conversationId:"local_".concat(u.length+1)});H&&(u=[u[0]]),es(u)}ef((null===(r=n.data.data)||void 0===r?void 0:r.title)||"")}else k.Ay.error((null===(x=n.error)||void 0===x?void 0:x.errMessage)||"");return[2]}})})).apply(this,arguments)}var ej=(0,c.useRef)(null==B?void 0:null===(N=B.query)||void 0===N?void 0:N.q),eA=(0,K.A)(function(e){return e.state}),eI=(0,K.A)(function(e){return e.clearAllState}),eN=eA.initialQuestion||ej.current,eC=eA.coverStationType||0,eS=eA.writeCode||"",eR=eA.parsedContent||"",eT=eA.multiple||!1,eL=4===eC||(null==eb?void 0:eb.conversationType)===4,eM=(0,u.vV)(),eE=eM.fileListData,eD=eM.setFileListData,eO=eM.handleUploadInterrupt,eF=(0,a._)((0,c.useState)(eA.knowledgeList||[]),2),ez=eF[0],eB=eF[1],eP=(0,a._)((0,c.useState)([{type:"paragraph",children:[{text:""}]}]),2),eq=eP[0],eH=eP[1],eU=B.pathname.split("/"),eV=eU.length>1?"/".concat(eU[1]):"",eW=(null===(C=eA.fileList)||void 0===C?void 0:C.length)>0?eA.fileList:eE,eG=(0,en.A)(),eY=eG.userInfoData,eK=eG.setIsLoginOpen,eJ=(0,v.A)(function(e){return e.setShowTourism});(0,c.useEffect)(function(){if(!eY&&ea>1){e_(!1),eK(!0);return}},[eY,ea,e_,eK,eL]),(0,c.useEffect)(function(){return M&&eJ(!1),eT?(eH(eR),eB(ez),eD(eW),requestAnimationFrame(function(){K.A.getState().clearState("multiple")})):eH([{type:"paragraph",children:[{text:""}]}]),R.startsWith("local")?eN?(ec(!1),eD(eW),eQ()):e_(!1):(null==eb?void 0:eb.conversationId)!==R&&(nt.current=[],function(){return ew.apply(this,arguments)}()),function(){R.startsWith("local")||(eI(),eB([]),ed(!1),ex(!1),em(!1),eD([]),nu.current=[],es([{conversationId:"local_1"}])),eg(void 0)}},[R]);var e$=(0,m.bA)(),eZ=(0,c.useMemo)(function(){var e;return(0,o._)({},{metaData:{writeCode:eS||(null==eb?void 0:null===(e=eb.metaData)||void 0===e?void 0:e.writeCode),chatModelConfig:{model:null==eo?void 0:eo.model,options:null==eo?void 0:eo.options},superAgentPath:eV},shareId:$,isAnonymous:er,source:V,conversationType:eL?4:void 0})},[eS,null==eb?void 0:null===(S=eb.metaData)||void 0===S?void 0:S.writeCode,null==eo?void 0:eo.model,null==eo?void 0:eo.options,eV,$,er,V,eL]),eQ=(0,c.useCallback)((t=(0,r._)(function(e){var n,t,r,a,l,c,u,d,x,m,p,h,v,y;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(eu(!0),!O)return[3,2];return[4,(0,f.WXy)({body:{agentId:O}})];case 1:return(r=s.sent()).data?n=r.data:t=r.error,[3,4];case 2:for(a=[],l=0;l<ea;)a.push(eZ),l++;return[4,(0,f.K1P)({body:{conversationList:a}})];case 3:(c=s.sent()).data?n=c.data:t=c.error,s.label=4;case 4:if(null==n?void 0:n.success)n.data&&n.data.isAnonymous&&el((null===(u=n.data)||void 0===u?void 0:u.anonymousKey)||""),Q(),O?e$("/agentChat/".concat(null===(d=n.data)||void 0===d?void 0:d.conversationId),{replace:!0,state:e}):M?(e$("/tourism/".concat(null===(x=n.data)||void 0===x?void 0:x.conversationId),{replace:!0,state:e}),ek(n.data),es(null===(p=n.data)||void 0===p?void 0:null===(m=p.conversationList)||void 0===m?void 0:m.map(function(e,n){return n<ea?e:(0,i._)((0,o._)({},e),{hidden:!0})}))):(e$("/chat/".concat(null===(h=n.data)||void 0===h?void 0:h.conversationId),{replace:!0,state:e}),ek(n.data),es(null===(y=n.data)||void 0===y?void 0:null===(v=y.conversationList)||void 0===v?void 0:v.map(function(e,n){return n<ea?e:(0,i._)((0,o._)({},e),{hidden:!0})})));else throw K.A.getState().clearAllState(),k.Ay.error((null==t?void 0:t.errMessage)||"创建会话失败"),Error("创建会话失败");return[2]}})}),function(e){return t.apply(this,arguments)}),[er,Q,O,eZ,ea]),eX=(0,b.Bd)().t,e0=eX("common.askAny"),e1=(0,c.useRef)(null),e9=(0,c.useRef)(null),e2=(0,a._)((0,c.useState)(""),2),e4=e2[0],e3=e2[1];function e8(){return(e8=(0,r._)(function(e,n){var t,r,o,i,a,l=arguments;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(t=l.length>2&&void 0!==l[2]?l[2]:eW,r=l.length>3&&void 0!==l[3]?l[3]:ez,o=l.length>4?l[4]:void 0,E(),!eb&&!$)return eQ({initialQuestion:e,fileList:t,knowledgeList:r}),[2];if(!(ea>((null==eb?void 0:eb.conversationList)||[]).length))return[3,2];return[4,nn({initialQuestion:e,fileList:t,knowledgeList:r})];case 1:return s.sent(),ej.current=e,[2];case 2:if(ne.current)return k.Ay.warning("正在创建子会话，请稍后重试"),[2];return[4,(0,f.oA4)({body:{timestamp:Date.now()}})];case 3:return a=null===(i=s.sent().data)||void 0===i?void 0:i.data,e7.current.forEach(function(i){i&&i.submit({value:e,writeCode:n,files:t,knowledges:r,chatId:a,reference:o})}),ec(!1),eu(!1),eH([{type:"paragraph",children:[{text:""}]}]),eI(),[2]}})})).apply(this,arguments)}var e6=(0,v.A)(function(e){return e.setOnChatSubmit});(0,c.useEffect)(function(){e6(D)},[D]);var e5=(0,c.useCallback)(function(e,n){return n.length>A.t5?(n[0]===e&&k.Ay.error("一次最多只能上传".concat(A.t5,"个文件")),_.A.LIST_IGNORE):null==L?void 0:L([e])},[L]),e7=(0,c.useRef)([]),ne=(0,c.useRef)(!1),nn=(d=(0,r._)(function(e){var n,t,r,i,a,l,c,u,d,x,m,p,h;return(0,s.YH)(this,function(s){switch(s.label){case 0:for(ne.current=!0,t=[],r=((null==eb?void 0:eb.conversationList)||[]).length;r<ea;)r>0&&(eZ.shareId=""),t.push((0,o._)({},eZ)),r++;return[4,(0,f.K1P)({body:{parentConversationId:(null==eb?void 0:eb.conversationId)||"",conversationList:t}})];case 1:if(i=s.sent(),ne.current=!1,null===(n=i.data)||void 0===n?void 0:n.success){if(R.startsWith("local"))return x=null===(c=i.data.data)||void 0===c?void 0:null===(l=c.metaData)||void 0===l?void 0:l.superAgentPath,e$("".concat(x,"/").concat(null===(d=i.data)||void 0===d?void 0:null===(u=d.data)||void 0===u?void 0:u.conversationId),{replace:!0,state:e}),[2];(null===(a=i.data)||void 0===a?void 0:a.success)&&(ek(null===(m=i.data)||void 0===m?void 0:m.data),es((null===(h=i.data)||void 0===h?void 0:null===(p=h.data)||void 0===p?void 0:p.conversationList)||[]))}return[2]}})}),function(e){return d.apply(this,arguments)});(0,c.useEffect)(function(){return function(){eh()}},[]);var nt=(0,c.useRef)([]),nr=function(e){!(nt.current.includes(e)||e.startsWith("local"))&&(nt.current.push(e),nt.current.length===ea&&eN&&(D(eN,eS,eW),ej.current=""))},no=(0,c.useRef)(0),ni=function(e,n){"onChatCompleted"===n.event&&(no.current+=1,no.current===ea&&(na(),Q(e))),e7.current.forEach(function(t){t&&t.conversationId!==e&&t.onPostMessage(e,n)})},na=function(){no.current=0},ns=(0,v.A)(function(e){return e.setAgentId});(0,c.useEffect)(function(){O&&ns(O)},[O]);var nl=function(){e7.current.forEach(function(e){e&&e.clearContext()})};(0,c.useEffect)(function(){if(X.length>0&&R&&!O&&!R.includes("local")){var e=X.find(function(e){return e.conversationId===R});e&&ef(e.title||eX("common.newCon"))}},[O,X,R]),(0,c.useImperativeHandle)(n,function(){return{onClearContext:nl}});var nc=B.pathname.startsWith("/chat"),nu=(0,c.useRef)([]);return(0,l.jsxs)(w.A,{vertical:!0,className:"h-full flex-1",children:[ey&&(0,l.jsxs)("div",{style:{width:"calc(100% - 40px)",maxWidth:"840px",background:"var(--bg-color-primary)",padding:U?"16px 0":"10px 0",display:"flex",alignItems:"center",justifyContent:"space-between",margin:"0 auto",borderBottom:U?"1px solid #e5e7eb":""},children:[(0,l.jsxs)("p",{style:{margin:0},className:"m-0 flex flex-col gap-4",children:[(0,l.jsx)("span",{className:"text-16",style:{fontWeight:500},children:ep}),(0,l.jsxs)("span",{className:"opacity-60 text-12",children:[eX("common.contSrc"),"AI",eX("common.gen"),"，",eX("common.notTrue")]})]}),(0,l.jsx)(j.Ay,{size:"small",type:"default",onClick:function(){return null==eg?void 0:eg(void 0)},children:eX("common.can")})]}),(0,l.jsx)(w.A,{className:(0,m.cx)("overflow-hidden flex-1 ",1===ea?"single-model":"",H||1===ea?"":"p-24 pt-60",ey?"pb-60":""),gap:24,children:ei.map(function(e,n){return e.hidden?null:(0,l.jsx)(G,{modelIndex:n,id:e.conversationId||"",conversationInfo:eb,setConversationList:es,shareId:$,children:(0,l.jsx)(P,{index:n,agentInfo:T,isTourism:M,isSearch:eL,conversationItem:e,ref:function(e){e7.current[n]=e},onInited:nr,messagePost:ni,setConversationList:es,defaultMessagesRef:nu})},e.conversationId)})}),!ey&&(0,l.jsxs)("div",{className:(0,m.cx)("relative flex-none w-full max-w-[920px] mx-auto  pb-24 ",h().chat_sender),children:[nc&&(0,l.jsx)(J.A,{editorData:e4,setSlateValue:eH,className:h()["chat-prompt-search"],position:"top",focusRef:e1}),(0,l.jsx)(y.default,{classNameContent:h().chat_sender_content,simple:!!O||M,isSearch:eL,focusRef:e1,theme:er?"dark":"default",placeholder:e0,loading:ei.some(function(e){return!0===e.messageLoading}),ref:e9,onCancelReply:E,sendHandle:D,slateValue:eq,setSlateValue:eH,fileList:eE,setFileList:eD,beforeUpload:e5,handleValueChange:function(e){e3((0,I.P)(e,!1).replace(/\[|\]/g,""))},handleUploadInterrupt:eO,knowledgeList:ez,setKnowledgeList:eB,writeCode:eS,showAiModel:!eL})]}),ey&&(0,l.jsx)(ee,{})]})});let er=et;var eo=(0,u.Ay)((0,d.A)(et,x.A))},59626:(e,n,t)=>{"use strict";t.d(n,{A:()=>x});var r=t(29935),o=t(12694),i=t(82643),a=t(23798),s=t(21462),l=t(34814),c=t(67657),u=t(92079),d=t(23555);let x=function(e){var n,t=e.title,x=e.content,m=e.visible,p=e.onOk,h=e.onCancel,f=e.icon,v=e.buttonPrimaryColor,y=void 0===v?"#1B77FF":v,g=e.buttonPrimaryText,b=void 0===g?"确定":g,k=(0,d.Bd)().t,_=(0,o._)((0,s.useState)(!1),2),w=_[0],j=_[1],A=(0,u.n)().isMobile,I=(n=(0,r._)(function(){return(0,i.YH)(this,function(e){switch(e.label){case 0:j(!0),e.label=1;case 1:return e.trys.push([1,3,4,5]),[4,p()];case 2:return e.sent(),[3,5];case 3:return console.error(e.sent()),[3,5];case 4:return j(!1),[7];case 5:return[2]}})}),function(){return n.apply(this,arguments)}),N=function(){h()};return(0,a.jsx)(l.A,{className:"prompt-modal",width:A?311:396,title:!1,open:m,centered:!0,onCancel:N,closable:!A,footer:function(e){return(0,a.jsxs)("div",{className:"".concat(A?"flex justify-center gap-16":"flex justify-end gap-8"),children:[(0,a.jsx)(c.Ay,{className:"rounded-8 ".concat(A?"w-[123px] !h-[40px]":"m-w-[76px]"),onClick:N,children:k("common.can")},"cancel"),(0,a.jsx)(c.Ay,{className:"rounded-8   ".concat(A?"w-[123px] !h-[40px]":"m-w-[76px]"),loading:w,style:{borderColor:y,backgroundColor:y,color:"#fff"},onClick:I,children:b},"submit")]})},children:(0,a.jsxs)("div",{className:"flex items-start gap-16 ".concat(A&&"justify-center mb-24"),children:[!A&&f,(0,a.jsxs)("div",{className:"vertical-top",children:[(0,a.jsx)("div",{className:"!text-16 !font-medium !mb-8 ".concat(A&&"!text-center"),children:t}),(0,a.jsx)("div",{className:"text-14 text-[var(--text-color-primary)]",children:x})]})]})})}},67931:(e,n,t)=>{"use strict";t.d(n,{A:()=>ta});var r=t(29935),o=t(17844),i=t(93629),a=t(12694),s=t(82643),l=t(23798),c=t(21462),u=t(3335),d=t(80667),x=t(67621),m=t(39735),p=t(34890),h=t(92079),f=t(30054),v=t(98505),y=t(91013),g=t(1450),b=t(36882),k=t.n(b),_=t(88839),w=t(34566),j=t(21478),A=t(71297),I=t.n(A);let N=JSON.parse('{"v":"5.12.2","fr":30,"ip":80,"op":160,"w":48,"h":48,"nm":"动画","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":4,"ty":4,"nm":"圆形 7","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":60,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":70,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":90,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":100,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":120,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":140,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":150,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":170,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":180,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":200,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":220,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":230,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":250,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":260,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":280,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":300,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":310,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":330,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":340,"s":[50],"e":[100]},{"t":360}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":1,"k":[{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":60,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":80,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":100,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":120,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":140,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":160,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":180,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":200,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":220,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":240,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":260,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":280,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":300,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":320,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":340,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-2.333,0,0]},{"t":360}],"ix":2,"l":2},"a":{"a":0,"k":[0,0,0],"ix":1,"l":2},"s":{"a":1,"k":[{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":60,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":100,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":120,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":140,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":180,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":200,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":220,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":260,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":280,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":300,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":340,"s":[80,80,100],"e":[100,100,100]},{"t":360}],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"合并路径 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[0,0,0,0.15000000596],"ix":4},"o":{"a":0,"k":24,"ix":5},"r":1,"bm":0,"nm":"填充 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[200,200],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"变换"}],"nm":"圆形 1","np":4,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":60,"op":333,"st":32,"ct":1,"bm":0},{"ddd":0,"ind":5,"ty":4,"nm":"圆形 6","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":40,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":50,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":70,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":80,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":100,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":120,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":130,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":150,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":160,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":180,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":200,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":210,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":230,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":240,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":260,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":280,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":290,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":310,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":320,"s":[50],"e":[100]},{"t":340}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":1,"k":[{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":40,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":60,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":80,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":100,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":120,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":140,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":160,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":180,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":200,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":220,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":240,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":260,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":280,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":300,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":320,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-2.333,0,0]},{"t":340}],"ix":2,"l":2},"a":{"a":0,"k":[0,0,0],"ix":1,"l":2},"s":{"a":1,"k":[{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":40,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":80,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":100,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":120,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":160,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":180,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":200,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":240,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":260,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":280,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":320,"s":[80,80,100],"e":[100,100,100]},{"t":340}],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"合并路径 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[0,0,0,0.15000000596],"ix":4},"o":{"a":0,"k":24,"ix":5},"r":1,"bm":0,"nm":"填充 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[200,200],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"变换"}],"nm":"圆形 1","np":4,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":40,"op":313,"st":12,"ct":1,"bm":0},{"ddd":0,"ind":6,"ty":4,"nm":"圆形 5","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":20,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":30,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":50,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":60,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":80,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":100,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":110,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":130,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":140,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":160,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":180,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":190,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":210,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":220,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":240,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":260,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":270,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":290,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":300,"s":[50],"e":[100]},{"t":320}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":1,"k":[{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":20,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":40,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":60,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":80,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":100,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":120,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":140,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":160,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":180,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":200,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":220,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":240,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":260,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":280,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":300,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-2.333,0,0]},{"t":320}],"ix":2,"l":2},"a":{"a":0,"k":[0,0,0],"ix":1,"l":2},"s":{"a":1,"k":[{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":20,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":60,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":80,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":100,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":140,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":160,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":180,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":220,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":240,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":260,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":300,"s":[80,80,100],"e":[100,100,100]},{"t":320}],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"合并路径 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[0,0,0,0.15000000596],"ix":4},"o":{"a":0,"k":24,"ix":5},"r":1,"bm":0,"nm":"填充 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[200,200],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"变换"}],"nm":"圆形 1","np":4,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":20,"op":293,"st":-8,"ct":1,"bm":0},{"ddd":0,"ind":7,"ty":4,"nm":"圆形 1","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":0,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":10,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":30,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":40,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":60,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":80,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":90,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":110,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":120,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":140,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":160,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":170,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":190,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":200,"s":[50],"e":[100]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":220,"s":[100],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":240,"s":[50],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":250,"s":[0],"e":[0]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":270,"s":[0],"e":[50]},{"i":{"x":[0.999],"y":[1]},"o":{"x":[0.001],"y":[0]},"t":280,"s":[50],"e":[100]},{"t":300}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":1,"k":[{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":0,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":20,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":40,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":60,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":80,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":100,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":120,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":140,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":160,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":180,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":200,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-4.667,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":220,"s":[24,24,0],"e":[38,24,0],"to":[4.667,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":240,"s":[38,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":260,"s":[24,24,0],"e":[10,24,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.999,"y":1},"o":{"x":0.001,"y":0},"t":280,"s":[10,24,0],"e":[24,24,0],"to":[0,0,0],"ti":[-2.333,0,0]},{"t":300}],"ix":2,"l":2},"a":{"a":0,"k":[0,0,0],"ix":1,"l":2},"s":{"a":1,"k":[{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":0,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":40,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":60,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":80,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":120,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":140,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":160,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":200,"s":[80,80,100],"e":[100,100,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":220,"s":[100,100,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":240,"s":[80,80,100],"e":[80,80,100]},{"i":{"x":[0.999,0.999,0.999],"y":[1,1,1]},"o":{"x":[0.001,0.001,0.001],"y":[0,0,0]},"t":280,"s":[80,80,100],"e":[100,100,100]},{"t":300}],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0],[1.657,0],[0,1.657],[-1.657,0],[0,-1.657]],"o":[[0,1.657],[-1.657,0],[0,-1.657],[1.657,0],[0,0]],"v":[[3,0],[0,3],[-3,0],[0,-3],[3,0]],"c":true},"ix":2},"nm":"路径 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"合并路径 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[0,0,0,0.15000000596],"ix":4},"o":{"a":0,"k":24,"ix":5},"r":1,"bm":0,"nm":"填充 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[200,200],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"变换"}],"nm":"圆形 1","np":4,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":273,"st":-28,"ct":1,"bm":0},{"ddd":0,"ind":8,"ty":4,"nm":"动画","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[24,24,0],"ix":2,"l":2},"a":{"a":0,"k":[0,0,0],"ix":1,"l":2},"s":{"a":0,"k":[100,100,100],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ty":"rc","d":1,"s":{"a":0,"k":[24,24],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"r":{"a":0,"k":0,"ix":4},"nm":"矩形路径 1","mn":"ADBE Vector Shape - Rect","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[200,200],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"变换"}],"nm":"动画","np":1,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":150,"st":0,"ct":1,"bm":0}],"markers":[],"props":{}}');var C=I()(function(){return Promise.all([t.e(7957),t.e(5566)]).then(t.bind(t,55566))},{loadableGenerated:{webpack:function(){return[55566]}},ssr:!1}),S={renderer:"svg",loop:!0,autoplay:!0,animationData:N},R=c.forwardRef(function(e){var n=e.height,t=e.width;return(0,l.jsx)(C,{options:S,style:{pointerEvents:"none",margin:0},width:void 0===t?24:t,height:void 0===n?24:n})});R.displayName="BubbleLoading";var T=t(86105),L=t(49539),M=t(81549),E=t(7263),D=t.n(E),O=function(e){for(var n="",t=0;t<e.length;t++){var r=e[t];if(-1!==r.indexOf("filename")){n=(n=r.replace("filename=","")).replace("filename*=utf-8''","");break}}return n};let F=function(e){var n,t=e||{},o=t.request,i=t.fileName,l=t.ext,u=(0,a._)((0,c.useState)(!1),2),d=u[0],x=u[1],m=(0,c.useCallback)((n=(0,r._)(function(e){var n,t,r,a,c,u,m;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(d)return[2,!1];return x(!0),n=new Set([e,{responseType:"blob",getResponse:!0}]),[4,o.apply(null,(0,T._)(n).filter(function(e){return!!e}))];case 1:if(r=(t=s.sent()).data,a=t.response,x(!1),a&&200===a.status)return c=(a.headers.get("content-disposition")||"").split(";"),u=a.headers.get("content-length")||0,m=decodeURI(void 0===i?O(c)+(l?".".concat(l):""):i),D()(r,m),[2,u];return M.Ay.error("文件下载失败！"),[2,!1]}})}),function(e){return n.apply(this,arguments)}),[o,i]);return[d,m]};var z=t(35476),B=t(31602),P=t(80162);let q=function(e){var n,t=e.id,o=e.title,i=e.num,u=e.className,d=(0,a._)(F({request:z.s$L}),2),x=d[0],m=d[1],p=(0,a._)((0,c.useState)(0),2),h=p[0],f=p[1],v=(n=(0,r._)(function(e){var n;return(0,s.YH)(this,function(t){switch(t.label){case 0:if(!e||x)return[2];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,m({body:{msgId:e}})];case 2:return(n=t.sent())&&f(n),[3,4];case 3:return t.sent(),[3,4];case 4:return[2]}})}),function(e){return n.apply(this,arguments)});return(0,l.jsx)("div",{className:(0,P.cx)(u+" w-[240px]"),onClick:function(){return v(t)},children:(0,l.jsx)(B.I3,{docSize:h,docType:"doc",wordNum:i,docName:o})})};var H=t(17459),U=t(67657),V=t(39652),W=t(81577),G=t(51062),Y=c.forwardRef(function(e,n){var t=e.children,r=e.copyText,s=e.className,u=(0,H._)(e,["children","copyText","className"]),d=(0,g.cx)("!text-[var(--text-color-tertiary)]",s),x=(0,G.Bd)().t,m=(0,a._)((0,c.useState)(!1),2),p=m[0],f=m[1],v=(0,h.n)().isTabletOrMobile,y=(0,W.i)(),b=(0,c.useMemo)(function(){return!r},[r]),k=function(){var e=(0,g.pr)(null!=r?r:"");f(!0),y(e),setTimeout(function(){f(!1)},1e3)},_=function(){return(0,l.jsx)(U.Ay,(0,i._)((0,o._)({ref:n,color:"default",variant:"text",disabled:b||p,className:d,size:"small",icon:p?(0,l.jsx)(w.A,{type:"icon-CheckOutlined",className:"!text-16"}):(0,l.jsx)(w.A,{type:"icon-CopyOutlined",className:"!text-16"}),onClick:k},u),{children:t}))};return v?_():(0,l.jsx)(V.A,{title:x("common.cop"),children:_()})});Y.displayName="CopyButton";let K=(0,c.memo)(Y);var J=t(41055),$=I()(function(){return Promise.all([t.e(7957),t.e(8361)]).then(t.t.bind(t,90742,23))},{loadableGenerated:{webpack:function(){return[90742]}},ssr:!1});let Z=function(){var e=(0,c.useRef)(null),n=(0,a._)((0,c.useState)(null),2),t=n[0],o=n[1];return(0,c.useEffect)(function(){var e;(e=(0,r._)(function(){return(0,s.YH)(this,function(e){switch(e.label){case 0:return e.trys.push([0,3,,4]),[4,fetch("/assets/data.json")];case 1:return[4,e.sent().json()];case 2:return o(e.sent()),[3,4];case 3:return console.error("Error loading animation data:",e.sent()),[3,4];case 4:return[2]}})}),function(){return e.apply(this,arguments)})()},[]),(0,l.jsx)("div",{ref:e,children:t?(0,l.jsx)("div",{className:"w-22 h-22",children:(0,l.jsx)($,{animationData:t})}):(0,l.jsx)(w.A,{type:"icon-LightupFilled",className:"!text-16 w-22 flex justify-center"})})};var Q=t(78176),X=t(89987),ee=t(34814),en=t(23748),et=t.n(en),er=t(95121),eo=t.n(er),ei=t(95495),ea=t(33089),es=t.n(ea);function el(e){var n=e.data,t=e.isError,r=void 0===t?0:t;return(0,l.jsx)(d.A,{gap:8,vertical:!0,className:eo()["share-card"],children:r?(0,l.jsx)("div",{className:eo()["error-text"],children:1===r?"当前对话无正文，请选择其他对话点亮":"当前对话无法点亮，请选择其他对话点亮"}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h3",{className:"line-clamp-1",children:n.title||"无标题"}),(0,l.jsx)("p",{className:"text-14 text-[var(--text-color-secondary)] line-clamp-3 min-h-[66px]",children:(0,g.Nc)(n.answerContent||"")}),(0,l.jsx)(d.A,{justify:"space-between",children:(0,l.jsxs)(d.A,{align:"center",children:[(0,l.jsx)(ei.A,{className:"!w-20 !h-20",style:{position:"unset"},src:n.avatar}),(0,l.jsx)("span",{className:"text-[var(--text-color-secondary)] text-12 ml-6 ",children:n.nickname}),(0,l.jsx)(L.A,{type:"vertical",style:{height:"1em",top:0}}),(0,l.jsx)("span",{className:"text-[var(--text-color-secondary)] text-12",children:es()(n.createTime).format("YYYY-MM-DD")})]})})]})})}let ec=function(){var e=(0,h.n)().isMobile;return(0,l.jsx)(d.A,{gap:8,vertical:!0,className:eo()["share-card"],children:(0,l.jsx)(X.A,{active:!0,className:"custom-skeleton w-[130px]",paragraph:{rows:e?4:3}})})};var eu=t(62410);let ed=function(e){var n,t,u=e.id,x=e.uuid,m=e.conversationId,p=e.recommendStatus,h=(0,a._)((0,c.useState)(!1),2),f=h[0],v=h[1],y=(0,a._)((0,c.useState)({}),2),b=y[0],k=y[1],_=(0,a._)((0,c.useState)(!1),2),j=_[0],A=_[1],I=(0,a._)((0,c.useState)(!1),2),N=I[0],C=I[1],S=(0,a._)((0,c.useState)(!1),2),R=S[0],T=S[1],L=(0,a._)((0,c.useState)(0),2),E=L[0],D=L[1],O=(0,a._)((0,c.useState)(1===p),2),F=O[0],B=O[1],P=(0,Q.A)(),q=P.userInfoData,H=P.setIsLoginOpen,G=(0,eu.A)(),Y=(0,W.i)(),K=function(){T(!0)},J=(n=(0,r._)(function(e,n){var t,r,a,l;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(!e||!n)return[2];if(!(null==q?void 0:q.userId))return H(!0),[2];return K(),v(!0),[4,(0,z.tJb)({body:{msgId:e}})];case 1:return(t=s.sent()).response.ok?k((0,i._)((0,o._)({},null!==(l=null===(a=t.data)||void 0===a?void 0:a.data)&&void 0!==l?l:{}),{id:e,uuid:n})):(null===(r=t.error)||void 0===r?void 0:r.errMessage)=="点亮失败，请选择其他内容点亮"?D(2):D(1),v(!1),[2]}})}),function(e,t){return n.apply(this,arguments)}),$=(t=(0,r._)(function(e,n,t){var r,o,i,a,l;return(0,s.YH)(this,function(s){switch(s.label){case 0:if(s.trys.push([0,9,,10]),!e||!n||N)return[2];if(!m)throw Error("conversationId is required but is undefined.");if(C(!0),0!=t)return[3,2];return[4,(0,z.Nej)({body:{msgId:e,title:null!==(o=b.title)&&void 0!==o?o:""}})];case 1:return r=s.sent(),[3,4];case 2:return[4,(0,z.cV_)({body:{msgId:e}})];case 3:r=s.sent(),s.label=4;case 4:if(r.response.ok)return[3,5];return M.Ay.error(null===(i=r.error)||void 0===i?void 0:i.errMessage),[3,8];case 5:if(a=0==t?(0,g.mU)()?"已点亮":"已点亮并复制分享链接":"已取消点亮",M.Ay.success(a),B(!F),0!=t)return[3,8];if(l=r.data.data,(0,g.mU)())return[3,7];return[4,Y(window.location.origin+"/ask/"+l)];case 6:s.sent(),s.label=7;case 7:A(!0),setTimeout(function(){A(!1)},800),s.label=8;case 8:return T(!1),C(!1),[3,10];case 9:return console.error("接口错误:",s.sent()),[3,10];case 10:return[2]}})}),function(e,n,r){return t.apply(this,arguments)}),en=function(){return(0,l.jsx)(U.Ay,{color:"default",variant:"text",className:(0,g.cx)("!h-24 !px-4 !gap-0 !hover:bg-[rgba(255,255,255,1)] !mt-[-1px] "+(F?"!text-[#FF2742]":"!text-[var(--text-color-tertiary)]")),size:"small",onClick:function(){F?$(null!=u?u:"",null!=x?x:"",1):J(null!=u?u:"",null!=x?x:"")},icon:j?(0,l.jsx)(Z,{}):(0,l.jsx)(w.A,{type:F?"icon-LightupFilled":"icon-LightOutlined",className:"!text-16 w-22 flex justify-center"}),children:"点亮"})};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(ee.A,{footer:null,centered:!0,closable:!1,open:R,className:(0,g.cx)(et()["recommend-model"]),onCancel:function(){return T(!1)},children:(0,l.jsx)(function(){return(0,l.jsxs)(d.A,{gap:24,vertical:!0,className:(0,g.cx)(et()["recommend-model-content"]),children:[(0,l.jsx)(d.A,{vertical:!0,align:"center",gap:4,children:(0,l.jsxs)(d.A,{gap:8,className:"text-16 text-[var(--text-color-primary)] font-medium",children:[(0,l.jsx)(w.A,{type:E?"icon-ExclamationCircleOutlined":"icon-CommunityOutlined",className:"text-[20px]"}),E?"请点亮其他对话":"点亮到广场"]})}),f?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(ec,{}),(0,l.jsx)(d.A,{justify:"center",children:(0,l.jsx)(X.A.Node,{active:!0,className:"custom-skeleton",style:{width:200,height:40,marginBottom:30}})})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(el,{data:b,isError:E}),(0,l.jsx)(d.A,{vertical:!0,align:"center",gap:8,children:0!==E?(0,l.jsx)(U.Ay,{disabled:f,className:et().btn,onClick:function(){T(!1)},children:"确定"}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.A,{gap:8,className:"text-16 text-[var(--text-color-primary)] font-medium",children:(0,l.jsx)(U.Ay,{disabled:f,className:"text-[16px] !w-[200px] !leading-[18px] !border-0 !flex !gap-2 !h-[40px] text-[#ffffff] !bg-[#FF2742] !rounded-8 hover:!bg-[#FF324C] ",onClick:function(){var e,n;$(null!==(e=b.id)&&void 0!==e?e:"",null!==(n=b.uuid)&&void 0!==n?n:"",0)},icon:(0,l.jsx)(w.A,{type:"icon-LightupFilled",className:"!text-18 "}),children:"点亮"})}),(0,l.jsx)("span",{className:"text-14 text-[var(--text-color-tertiary)]",children:"点亮后会发布到广场并推荐给其他用户"})]})})]})]})},{})}),G?en():(0,l.jsx)(V.A,{title:F?"已推荐给其他用户":"点亮后会推荐给其他用户",children:en()})]})};var ex=t(41209);let em=function(e){var n,t=e.id,o=e.uuid,i=(0,a._)((0,c.useState)(!1),2),u=i[0],x=i[1],m=(0,a._)(F({request:z.s$L}),2),p=m[0],f=m[1],v=(0,a._)(F({request:z.swW}),2),y=v[0],g=v[1],b=(0,h.n)().isTabletOrMobile,k=(n=(0,r._)(function(e,n){var t;return(0,s.YH)(this,function(r){try{if(!e||p||y)return[2];t={msgId:e};try{"PDF"===n?g({body:t}):f({body:t}),x(!1)}catch(e){}}catch(e){}return[2]})}),function(e,t){return n.apply(this,arguments)});return(0,l.jsx)(ex.A,{content:function(){return(0,l.jsxs)(d.A,{vertical:!0,gap:0,children:[(0,l.jsxs)("div",{onClick:function(){return t&&o&&k(t,"Word")},className:"hover:bg-[var(--bg-color-primary-hover)] hover:rounded-4 w-full h-32 flex gap-8 px-12 items-center cursor-pointer box-border",children:[(0,l.jsx)(w.A,{type:"icon-FileWordOutlined",className:"!text-16"}),"Word"]}),(0,l.jsxs)("div",{onClick:function(){return t&&o&&k(t,"PDF")},className:"hover:bg-[var(--bg-color-primary-hover)] hover:rounded-4 w-full h-32 flex gap-8 px-12 items-center cursor-pointer box-border",children:[(0,l.jsx)(w.A,{type:"icon-FilePdfOutlined",className:"!text-16"}),"PDF"]})]})},title:"",trigger:b?"click":"hover",placement:"bottom",arrow:!1,open:u,onOpenChange:function(e){x(e)},styles:{body:{width:"160px",borderRadius:"8px",padding:"8px",boxShadow:"0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important"}},children:(0,l.jsx)(U.Ay,{color:"default",variant:"text",className:"!text-[var(--text-color-tertiary)]",size:"small",icon:(0,l.jsx)(w.A,{type:"icon-DownloadOutlined",className:"!text-16"})})})},ep=(0,c.memo)(function(e){var n,t=e.isSquare,o=e.chatId,i=void 0===o?"":o,a=e.id,u=void 0===a?"":a,d=e.conversationId,x=(0,y.A)(function(e){return e.fnMap}),m=(0,c.useMemo)(function(){if(d)return null==x?void 0:x.get("".concat(d,"_onShare"))},[x,d]),p=(0,W.i)(),h=(0,G.Bd)().t,f=(0,eu.A)(),v=(n=(0,r._)(function(){return(0,s.YH)(this,function(e){switch(e.label){case 0:if(!t)return[3,5];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,p(window.location.href)];case 2:return e.sent(),M.Ay.success("已复制分享链接"),[3,4];case 3:return console.error("复制分享链接失败:",e.sent()),[3,4];case 4:return[2];case 5:return null==m||m(u,i),[2]}})}),function(){return n.apply(this,arguments)}),g=function(){return(0,l.jsx)(U.Ay,{color:"default",variant:"text",className:"!text-[var(--text-color-tertiary)]",size:"small",onClick:v,icon:(0,l.jsx)(w.A,{type:"icon-ShareOutlined",className:"!text-16"})})};return f?g():(0,l.jsx)(V.A,{title:h("common.sha"),children:g()})}),eh=(0,c.memo)(function(e){var n,t=e.conversationId,o=e.id,i=e.uuid,u=(0,y.A)(function(e){return e.fnMap}),d=(0,c.useMemo)(function(){if(t)return null==u?void 0:u.get("".concat(t,"_onRemove"))},[u,t]),x=(0,a._)((0,c.useState)(!1),2),m=x[0],p=x[1],h=(0,eu.A)(),f=(0,G.Bd)().t,v=(n=(0,r._)(function(e,n){var t;return(0,s.YH)(this,function(r){switch(r.label){case 0:if(r.trys.push([0,2,,3]),!e||!n)return[2];return p(!0),[4,(0,z.DiX)({body:{msgId:e}})];case 1:return(null==(t=r.sent().data)?void 0:t.success)&&(null==d||d(n)),p(!1),[3,3];case 2:return console.error(r.sent()),p(!1),[3,3];case 3:return[2]}})}),function(e,t){return n.apply(this,arguments)}),g=function(){return(0,l.jsx)(U.Ay,{color:"default",disabled:!o||!i,loading:m,variant:"text",className:"!text-[var(--text-color-tertiary)]",size:"small",onClick:function(){return v(null!=o?o:"",null!=i?i:"")},icon:(0,l.jsx)(w.A,{type:"icon-DeleteOutlined",className:"!text-16"})})};return h?g():(0,l.jsx)(V.A,{title:f("common.del"),children:g()})}),ef=function(e){var n=e.chatCmd,t=n.conversationId,r=(0,y.A)(function(e){return e.fnMap}),i=(0,c.useMemo)(function(){if(t)return r.get("".concat(t,"_onRetry"))},[r,t]),a=(0,eu.A)(),s=(0,G.Bd)().t,u=function(){return(0,l.jsx)(U.Ay,{color:"default",variant:"text",className:"!text-[var(--text-color-tertiary)] regenerate-btn",size:"small",onClick:function(){null==i||i((0,o._)({},n))},icon:(0,l.jsx)(w.A,{type:"icon-SyncOutlined",className:"!text-16"})})};return a?u():(0,l.jsx)(V.A,{title:s("common.reGen"),children:u()})};var ev=t(85141),ey=t(67297);let eg=(0,c.memo)(function(e){var n,t=e.chatCmd,a=e.uuid,u=e.id,x=e.fromShare,m=e.chatId,p=e.content,h=e.ctxClearFlag,f=e.showDownload,v=e.recommendStatus,b=e.postId,k=e.isOwn,_=e.isAnonymous,j=e.conversationId,A=e.messageLoading,I=(0,y.A)(function(e){return e.shareId}),N=(0,y.A)(function(e){return e.agentId}),C=(0,y.A)(function(e){return e.recordExportConversationId}),S=(0,ev.useRouter)(),R=S.pathname.startsWith("/share"),T=S.pathname.startsWith("/ask"),E=S.pathname.startsWith("/tourism"),D=(0,g.bA)(),O=(0,J.A)(["zh"]),F=(0,c.useMemo)(function(){return null==j?void 0:j.startsWith("local_")},[j]),B=(0,c.useMemo)(function(){return!h&&!A&&!R&&!x&&!T},[h,A,R,x,T]),P=(0,c.useMemo)(function(){return!R&&!N&&!_&&O&&!x},[_,N,R,O,x]),q=(n=(0,r._)(function(){var e,n,t,r;return(0,s.YH)(this,function(o){switch(o.label){case 0:if(!k)return[3,1];return D("/chat/".concat(j)),[3,3];case 1:if(I)return D("/chat/local_".concat((0,ey.Ak)(8),"?shareId=").concat(I)),[2];if(!b)return[2];return[4,(0,z.ZQK)({body:{postId:b}})];case 2:(e=o.sent()).response.ok?D("/chat/local_".concat(j,"?shareId=").concat(null!==(r=null===(t=e.data)||void 0===t?void 0:t.data)&&void 0!==r?r:""),{}):M.Ay.error(null===(n=e.error)||void 0===n?void 0:n.errMessage),o.label=3;case 3:return[2]}})}),function(){return n.apply(this,arguments)});return(0,l.jsxs)(d.A,{className:"action-group h-30",justify:"space-between",align:"center",children:[(0,l.jsxs)(d.A,{align:"center",gap:8,className:"h-full",children:[p&&(0,l.jsx)(K,{copyText:p}),B&&t&&"{}"!==JSON.stringify(t)&&(0,l.jsx)(ef,{chatCmd:(0,i._)((0,o._)({},t),{chatId:m})}),!R&&!x&&!F&&!T&&(0,l.jsx)(eh,{id:u,uuid:a,conversationId:j}),!A&&!C&&P&&(0,l.jsx)(ep,{id:u,conversationId:j,chatId:m,isSquare:T}),f&&!R&&!x&&!T&&(0,l.jsx)(em,{uuid:null!=a?a:"",id:null!=u?u:""}),!A&&!F&&!T&&P&&!E&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(L.A,{type:"vertical",className:"!mx-[6px]"}),(0,l.jsx)(ed,{id:null!=u?u:"",uuid:null!=a?a:"",conversationId:null!=j?j:"",recommendStatus:null!=v?v:0})]})]}),T&&(0,l.jsxs)(d.A,{className:"text-12 text-[var(--text-color-secondary)] cursor-pointer hover:bg-[#0000000a] h-[24px] px-4 rounded-4",gap:4,align:"center",onClick:function(){return q()},children:[(0,l.jsx)(w.A,{className:"text-16",type:"icon-AiMessageOutlined"}),k?"查看原对话":"和当贝继续聊"]})]})});var eb=t(12300),ek=t(82403),e_=t.n(ek);let ew=function(e){var n=e.suggestion,t=e.files,r=e.status,o=e.knowledgeList,i=(0,y.A)(function(e){return e.onChatSubmit});return(0,l.jsx)(eb.A,{size:10,direction:"vertical",className:(0,g.cx)("!max-w-full w-full box-border",e_()["suggestion-message"]),children:"followupLoading"===r?(0,l.jsx)(l.Fragment,{children:[1,2,3].map(function(e){return(0,l.jsx)(X.A.Node,{className:"!py-8 !text-14 !h-38 !border-none !rounded-8 !justify-start max-w-full ",style:{width:200+30*e,opacity:.6},active:!0},e)})}):(0,l.jsx)(l.Fragment,{children:(void 0===n?[]:n).map(function(e){return(0,l.jsxs)(U.Ay,{className:"!py-8 !text-14 !h-38 !border-none !rounded-8 !justify-start max-w-full overflow-hidden !bg-[var(--bg-color-secondary)] hover:!bg-[var(--bg-color-primary-active)] hover:!text-[var(--text-color-primary)]",style:{boxShadow:"none"},variant:"filled",onClick:function(){return null==i?void 0:i(e,"",null!=t?t:[],(null==t?void 0:t.length)!==0&&o||[],null!=t?t:[])},children:[(0,l.jsx)("span",{className:"truncate",children:e}),(0,l.jsx)(w.A,{type:"icon-ArrowRightOutlined"})]},e)})})})};var ej=t(43218),eA=t(29219);let eI=function(e){var n,t=e.pic,o=e.journeyId,i=e.tags,a=e.title,u=e.isShare,x=e.msgId,m=(0,h.n)().isMobile,p=(0,ev.useRouter)(),f=(0,eA.A)(function(e){return e.state}).shouldExpandCard||!1,v=(0,c.useContext)(ej.M).setIsCollapsed,g=(0,y.A)(function(e){return e.setJourneyId}),b=(0,y.A)(function(e){return e.journeyId}),k=(0,y.A)(function(e){return e.setMsgId}),_=(0,y.A)(function(e){return e.setShowTourism}),j=(0,y.A)(function(e){return e.showTourism});(0,c.useEffect)(function(){f&&(_(!0),null==v||v(!0),g(o),k(x),requestAnimationFrame(function(){eA.A.getState().clearState("shouldExpandCard")}))},[]);var A=(n=(0,r._)(function(){var e,n,t;return(0,s.YH)(this,function(r){switch(r.label){case 0:if(!o||!x||u)return[2];if(!m)return[3,2];return[4,(0,z.KvM)({body:{msgId:null!=x?x:""}})];case 1:return(null===(e=(n=r.sent()).data)||void 0===e?void 0:e.data)&&p.push(null===(t=n.data)||void 0===t?void 0:t.data),[2];case 2:return g(o),k(x),null==v||v(!0),_(!0),[2]}})}),function(){return n.apply(this,arguments)});return(0,l.jsxs)(d.A,{justify:"space-between",align:"center",className:"mb-8 !bg-[var(--bg-color-primary-active)] p-12 rounded-12 border "+(o&&x?"cursor-pointer":""),onClick:function(){A()},children:[(0,l.jsxs)(d.A,{gap:12,align:"center",justify:"center",className:"",children:[(0,l.jsx)("img",{src:t,width:64,height:64,className:"rounded-8 object-cover"}),(0,l.jsxs)(d.A,{className:"w-full h-full flex-1",vertical:!0,gap:2,children:[(0,l.jsx)("div",{className:" w-full font-medium line-clamp-1 text-16 text-[var(--text-color-primary)]",children:a}),(0,l.jsx)(d.A,{gap:12,children:i.map(function(e,n){return(0,l.jsxs)("span",{className:"text-12 text-[var(--text-color-secondary)]",children:[(0,l.jsx)(w.A,{type:0==n?"icon-DateOutlined":1==n?"icon-PositionOutlined":"icon-LocationOutlined"}),e]},n)})})]})]}),!m&&!u&&(!j||j&&b!==o)&&(0,l.jsx)(U.Ay,{type:"primary",onClick:function(){A()},children:"查看详情"})]})},eN=(0,c.memo)(function(e){var n=e.chatCmd,t=e.isSquare,r=e.isShare,a=e.isOwn,s=e.isAnonymous,c=e.msgId,u=e.status,d=e.ctxClearFlag,x=e.recommendStatus,m=e.files,p=e.suggestion,h=e.postId,f=e.content,v=e.uuid,g=e.fromShare,b=e.chatId,k=e.knowledgeList,_=e.supportDownload,w=e.conversationId,j=e.messageLoading,A=e.travelResultReferences,I=(0,y.A)(function(e){return e.title}),N="success"===u||"followupLoading"===u,C=N&&_&&!r&&!t&&!g;return(0,l.jsxs)(l.Fragment,{children:[A&&(0,l.jsx)(eI,(0,i._)((0,o._)({},A),{msgId:c,isShare:r})),C&&(0,l.jsx)(q,{className:"mb-12",num:Number(f.length),title:I||"Untitled",id:c||""}),N&&(0,l.jsx)(eg,{id:c,chatCmd:n,uuid:v,chatId:b,conversationId:w,fromShare:g,content:f,ctxClearFlag:d,showDownload:_,recommendStatus:x,isSquare:t,postId:h,isOwn:a,isAnonymous:s,messageLoading:j}),!t&&!g&&!r&&(0,l.jsx)(ew,{status:u,suggestion:p,files:m,knowledgeList:k}),d&&(0,l.jsx)(L.A,{style:{fontSize:14,color:"var(--text-color-tertiary)"},children:(0,l.jsx)("div",{children:"聊聊新话题"})})]})});var eC=t(47716),eS=t(58229);let eR=(0,c.memo)(function(e){var n,t,r=e.value,o=e.index,i=e.chatId,a=e.children,s=e.formInstance,d=e.isRecordExport,x=(0,c.useRef)(null),m=(0,c.useRef)(null),p=(0,eu.A)(),h=null!==(t=u.A.useWatch(["messages"],s))&&void 0!==t?t:[],f=null===(n=h[o])||void 0===n?void 0:n.checked,v="checkbox_"+r;return((0,c.useEffect)(function(){r&&s&&s.setFieldValue(["messages",o,"id"],r)},[r,s]),(0,c.useEffect)(function(){i&&s&&s.setFieldValue(["messages",o,"chatId"],i)},[i,s]),d)?(0,l.jsxs)("div",{ref:x,className:"block",onClick:function(e){e.stopPropagation()},children:[(0,l.jsx)(u.A.Item,{name:["messages",o,"checked"],valuePropName:"checked",noStyle:!0,children:(0,l.jsx)(eC.A,{id:v,checked:f,className:(0,g.cx)("!absolute !top-1/2 !-translate-y-1/2",p?"!left-12":"!left-24")})}),(0,l.jsx)(u.A.Item,{name:["messages",o,"id"],hidden:!0,noStyle:!0,children:(0,l.jsx)(eS.A,{})}),(0,l.jsx)(u.A.Item,{name:["messages",o,"chatId"],hidden:!0,noStyle:!0,children:(0,l.jsx)(eS.A,{})}),(0,l.jsx)("label",{ref:m,className:"absolute top-0 left-0  cursor-pointer w-full h-full -inset-1 ",style:{zIndex:1},onClick:function(){null==s||s.setFieldValue(["messages",o,"checked"],!f);var e=h.findIndex(function(e){return e.chatId===i&&e.id!==r});e>=0&&(null==s||s.setFieldValue(["messages",e,"checked"],!f))}}),a]}):a}),eT=(0,c.memo)(function(e){var n=e.conversationId,t=e.id,r=e.uuid,o=e.chatId,i=e.loading,a=e.content,s=e.fromShare,u=e.agentId,d=(0,J.A)(["zh"]),x=(0,f.vS)().isAnonymous,m=(0,y.A)(function(e){return e.shareId}),p=(0,y.A)(function(e){return e.messageLoading}),h=(0,y.A)(function(e){return e.recordExportConversationId}),v=(0,c.useMemo)(function(){return null==n?void 0:n.startsWith("local_")},[n]),g=(0,ev.useRouter)(),b=(0,c.useMemo)(function(){return g.asPath.includes("/share")},[g]);return(0,l.jsx)("div",{className:"action-group",children:(0,l.jsxs)(eb.A,{size:8,children:[(0,l.jsx)(K,{copyText:a}),!s&&!v&&!b&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eh,{id:t,uuid:r,conversationId:n}),!p&&!u&&!m&&!i&&d&&!h&&!x&&(0,l.jsx)(ep,{conversationId:n,id:t,chatId:o})]})]})})});var eL=t(88074),eM=t(23555);function eE(e){var n=e.icontype,t=e.text;return(0,l.jsxs)("div",{color:"primary",className:"!text-primary !select-none flex whitespace-nowrap !items-center h-full",children:[(0,l.jsx)(w.A,{type:n,className:"text-primary text-20 mr-4 align-[-3px]"}),t]})}function eD(e){var n,t=e.libType,r=e.message,o=e.isTourism,i=(0,eM.Bd)().t,a=(0,J.A)(["zh"]),s=r.reference,u=void 0===s?[]:s,d=r.knowledgeList,x=void 0===d?[]:d,m=(0,c.useMemo)(function(){var e;return t===eL.x1.all?"所有知识库":t===eL.x1.private?"个人知识库":0==x.length&&0==u.length?"共享知识库":null==x?void 0:null===(e=x[0])||void 0===e?void 0:e.name},[t,u,x]);return o?null:t!==eL.x1.invalid?(0,l.jsx)(eE,{icontype:t===eL.x1.private?"icon-PersonalknowledgeOutlined":"icon-AIKnowledgeOutlined",text:m}):(null===(n=r.userAction)||void 0===n?void 0:n.includes("deep"))?(0,l.jsx)(eE,{icontype:"icon-DeepseekOutlined",text:i("common.deepTh")+(a?"思考":"")}):null}let eO=(0,c.memo)(function(e){var n,t,r,o,i,a,s=e.conversationId,u=e.messageId,d=e.index,x=e.chatId,m=e.loading,p=e.message,h=e.status,f=e.isTourism,v=e.isSearch,b=(0,y.A)(function(e){return e.agentId}),_=(0,y.A)(function(e){return e.recordExportConversationId}),w=(0,y.A)(function(e){return e.objMap}),j=(0,c.useMemo)(function(){return w.get(s)},[w,s]),A=null!==(o=p.files)&&void 0!==o?o:[],I=null!==(i=p.reference)&&void 0!==i?i:[],N=null!==(a=p.knowledgeList)&&void 0!==a?a:[],C=null==p?void 0:null===(n=p.chatOption)||void 0===n?void 0:n.searchKnowledge,S=null===(t=p.chatOption)||void 0===t?void 0:t.searchAllKnowledge,R=null===(r=p.chatOption)||void 0===r?void 0:r.searchSharedKnowledge,T=(0,c.useMemo)(function(){if(C||I.some(function(e){var n;return null===(n=e.fileId)||void 0===n?void 0:n.startsWith("doc-")})||A.some(function(e){var n;return null===(n=e.fileId)||void 0===n?void 0:n.startsWith("doc-")}))return eL.x1.private;if(N.length>0){var e;return(null===(e=N[0])||void 0===e?void 0:e.isCreator)?eL.x1.myShared:eL.x1.otherShared}return S?eL.x1.all:R?eL.x1.shared:eL.x1.invalid},[C,R,S,I,A]);return(0,l.jsx)(eR,{index:d,value:u,chatId:x,formInstance:j,isRecordExport:void 0!==_&&_===s,children:(0,l.jsxs)("div",{className:(0,g.cx)("flex flex-col gap-12 items-end",k()["user-message-content"]),children:[(0,l.jsx)(eL.Ay,{message:p,libType:T}),p.question&&(0,l.jsxs)("div",{className:"max-w-[612px] text-14 bg-[var(--bg-color-primary-active)] flex rounded-12 px-16 py-8 gap-8 text-[var(--text-color-primary)]",children:[(0,l.jsx)(eD,{libType:T,message:p,isTourism:f,isSearch:v}),(0,l.jsx)("span",{children:p.question})]}),("success"===h||"local"===h)&&(0,l.jsx)(eT,{loading:m,chatId:x,conversationId:s,id:p.msgId||u,uuid:p.uuid,content:p.question,fromShare:p.fromShare,agentId:b})]})},d)});var eF=t(37439),ez=t(29309),eB=t(47663),eP=t(33799),eq=t(89710),eH=t(9118),eU=function(){return function(e){(0,eH.YR)(e,"element",function(e){var n,t;(null===(t=e.properties)||void 0===t?void 0:null===(n=t.className)||void 0===n?void 0:n.includes("katex"))&&(e.properties.dir="ltr")})}},eV=t(12584);function eW(){var e=(0,eV._)(["\n                --lobe-markdown-font-size: ","px;\n                --lobe-markdown-header-multiple: ",";\n                --lobe-markdown-margin-multiple: ",";\n                --lobe-markdown-line-height: ",";\n                --lobe-markdown-border-radius: ",";\n\n                /* 解决只有一个子节点时高度坍缩的问题 */\n                :first-child:not(:has(*)) {\n                    margin-block: 0;\n                }\n\n                ol,\n                ul {\n                    li {\n                        &::marker {\n                            color: "," !important;\n                        }\n\n                        li {\n                            &::marker {\n                                color: "," !important;\n                            }\n                        }\n                    }\n                }\n\n                ul {\n                    list-style: unset;\n\n                    li {\n                        &::before {\n                            content: unset;\n                            display: unset;\n                        }\n                    }\n                }\n            "]);return eW=function(){return e},e}function eG(){var e=(0,eV._)(["\n                .katex-html {\n                    overflow: auto hidden;\n                    padding: 3px;\n\n                    .base {\n                        margin-block: 0;\n                        margin-inline: auto;\n                    }\n                }\n\n                .katex-html:has(span.tag) {\n                    display: flex !important;\n                }\n\n                .katex-html > .tag {\n                    position: relative !important;\n                    float: right;\n                    margin-inline-start: 0.25rem;\n                }\n            "]);return eG=function(){return e},e}function eY(){var e=(0,eV._)(["\n                position: relative;\n                overflow: hidden;\n                max-width: 100%;\n\n                #footnote-label {\n                    display: none;\n                }\n\n                sup:has(a[aria-describedby='footnote-label']) {\n                    margin-inline: 0.2em;\n                    padding-block: 0.05em;\n                    padding-inline: 0.4em;\n\n                    font-size: 0.75em;\n                    vertical-align: super !important;\n\n                    background: ",";\n                    border: 1px solid ",";\n                    border-radius: 0.25em;\n                }\n\n                section.footnotes {\n                    padding-block: 1em;\n                    font-size: 0.875em;\n                    color: ",";\n\n                    ol {\n                        display: flex;\n                        flex-wrap: wrap;\n                        gap: 0.5em;\n\n                        margin: 0;\n                        padding: 0;\n\n                        list-style-type: none;\n                    }\n\n                    ol li {\n                        position: relative;\n\n                        overflow: hidden;\n                        display: flex;\n                        flex-direction: row;\n\n                        margin: 0 !important;\n                        padding-block: 0 !important;\n                        padding-inline: 0 0.4em !important;\n\n                        text-overflow: ellipsis;\n                        white-space: nowrap;\n\n                        border: 1px solid ",";\n                        border-radius: 0.25em;\n\n                        &::before {\n                            content: counter(list-item);\n                            counter-increment: list-item;\n\n                            display: block;\n\n                            margin-inline-end: 0.4em;\n                            padding-inline: 0.6em;\n\n                            background: ",";\n                        }\n\n                        p,\n                        a {\n                            overflow: hidden;\n\n                            margin: 0 !important;\n                            padding: 0 !important;\n\n                            text-overflow: ellipsis;\n                            white-space: nowrap;\n                        }\n                    }\n                }\n            "]);return eY=function(){return e},e}var eK=(0,P.rU)(function(e,n){var t=e.css,r=e.token,o=e.isDarkMode,i=n.fontSize,a=n.headerMultiple,s=n.marginMultiple,l=n.lineHeight,c=o?r.cyan9A:r.cyan11A;return{chat:t(eW(),void 0===i?14:i,void 0===a?.25:a,void 0===s?1:s,void 0===l?1.6:l,r.borderRadius,c,r.colorTextSecondary),latex:t(eG()),root:t(eY(),r.colorFillTertiary,r.colorBorderSecondary,r.colorTextSecondary,r.colorBorderSecondary,r.colorFillSecondary)}}),eJ=t(29979);function e$(){var e=(0,eV._)(["\n                --lobe-markdown-font-size: ","px;\n                --lobe-markdown-header-multiple: ",";\n                --lobe-markdown-margin-multiple: ",";\n                --lobe-markdown-line-height: ",";\n                --lobe-markdown-border-radius: ",";\n                --lobe-markdown-border-color: ",";\n\n                position: relative;\n\n                width: 100%;\n                max-width: 100%;\n                font-size: var(--lobe-markdown-font-size);\n                line-height: var(--lobe-markdown-line-height);\n                word-break: break-word;\n\n                svg {\n                    line-height: 1;\n                }\n            "]);return e$=function(){return e},e}function eZ(){var e=(0,eV._)(["\n                a {\n                    color: ",";\n\n                    &:hover {\n                        color: ",";\n                    }\n                }\n            "]);return eZ=function(){return e},e}function eQ(){var e=(0,eV._)(["\n                blockquote {\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 1em);\n                    margin-inline: 0;\n                    padding-block: 0;\n                    padding-inline: 1em;\n\n                    color: ",";\n\n                    border-inline-start: solid 4px ",";\n                }\n            "]);return eQ=function(){return e},e}function eX(){var e=(0,eV._)(["\n                code:not(:has(span)) {\n                    display: inline;\n\n                    margin-inline: 0.25em;\n                    padding-block: 0.2em;\n                    padding-inline: 0.4em;\n\n                    font-family: ",";\n                    font-size: 0.875em;\n                    line-height: 1;\n                    word-break: break-word;\n                    white-space: break-spaces;\n\n                    background: ",";\n                    border: 1px solid var(--lobe-markdown-border-color);\n                    border-radius: 0.25em;\n                }\n            "]);return eX=function(){return e},e}function e0(){var e=(0,eV._)(["\n                details {\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 1em);\n                    padding-block: 0.75em;\n                    padding-inline: 1em;\n\n                    background: ",";\n                    border-radius: calc(var(--lobe-markdown-border-radius) * 1px);\n                    box-shadow: 0 0 0 1px var(--lobe-markdown-border-color);\n\n                    summary {\n                        cursor: pointer;\n                        display: flex;\n                        align-items: center;\n                        list-style: none;\n\n                        &::before {\n                            content: '';\n\n                            position: absolute;\n                            inset-inline-end: 1.25em;\n                            transform: rotateZ(-45deg);\n\n                            display: block;\n\n                            width: 0.4em;\n                            height: 0.4em;\n\n                            font-family: ",";\n\n                            border-block-end: 1.5px solid ",";\n                            border-inline-end: 1.5px solid ",";\n\n                            transition: transform 200ms ",";\n                        }\n                    }\n\n                    &[open] {\n                        summary {\n                            padding-block-end: 0.75em;\n                            border-block-end: 1px dashed ",";\n\n                            &::before {\n                                transform: rotateZ(45deg);\n                            }\n                        }\n                    }\n                }\n            "]);return e0=function(){return e},e}function e1(){var e=(0,eV._)(["\n                h1,\n                h2,\n                h3,\n                h4,\n                h5,\n                h6 {\n                    margin-block: max(\n                        calc(var(--lobe-markdown-header-multiple) * var(--lobe-markdown-margin-multiple) * 0.4em),\n                        var(--lobe-markdown-font-size)\n                    );\n                    font-weight: bold;\n                    line-height: 1.25;\n                }\n\n                h1 {\n                    font-size: calc(var(--lobe-markdown-font-size) * (1 + 1.5 * var(--lobe-markdown-header-multiple)));\n                }\n\n                h2 {\n                    font-size: calc(var(--lobe-markdown-font-size) * (1 + var(--lobe-markdown-header-multiple)));\n                }\n\n                h3 {\n                    font-size: calc(var(--lobe-markdown-font-size) * (1 + 0.5 * var(--lobe-markdown-header-multiple)));\n                }\n\n                h4 {\n                    font-size: calc(var(--lobe-markdown-font-size) * (1 + 0.25 * var(--lobe-markdown-header-multiple)));\n                }\n\n                h5,\n                h6 {\n                    font-size: calc(var(--lobe-markdown-font-size) * 1);\n                }\n            "]);return e1=function(){return e},e}function e9(){var e=(0,eV._)(["\n                hr {\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 1.5em);\n\n                    border-color: ",";\n                    border-style: dashed;\n                    border-width: 1px;\n                    border-block-start: none;\n                    border-inline-start: none;\n                    border-inline-end: none;\n                }\n            "]);return e9=function(){return e},e}function e2(){var e=(0,eV._)(["\n                img {\n                    max-width: 100%;\n                }\n\n                > img,\n                > p > img {\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 1em);\n                    border-radius: calc(var(--lobe-markdown-border-radius) * 1px);\n                    box-shadow: 0 0 0 1px var(--lobe-markdown-border-color);\n                }\n            "]);return e2=function(){return e},e}function e4(){var e=(0,eV._)(["\n                kbd {\n                    cursor: default;\n\n                    display: inline-block;\n\n                    min-width: 1em;\n                    margin-inline: 0.25em;\n                    padding-block: 0.2em;\n                    padding-inline: 0.4em;\n\n                    font-family: ",";\n                    font-size: 0.875em;\n                    font-weight: 500;\n                    line-height: 1;\n                    text-align: center;\n\n                    background: ",";\n                    border: 1px solid ",";\n                    border-radius: 0.25em;\n                }\n            "]);return e4=function(){return e},e}function e3(){var e=(0,eV._)(["\n                li {\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 0.33em);\n\n                    p {\n                        display: inline;\n                    }\n                }\n\n                ul,\n                ol {\n                    margin-inline-start: 1em;\n                    padding-inline-start: 0;\n                    list-style-position: outside;\n\n                    ul,\n                    ol {\n                        margin-block: 0;\n                    }\n\n                    li {\n                        margin-inline-start: 1em;\n                    }\n                }\n\n                ol {\n                    list-style: auto;\n                }\n\n                ul {\n                    list-style-type: none;\n\n                    li {\n                        &::before {\n                            content: '-';\n                            display: inline-block;\n                            margin-inline: -1em 0.5em;\n                            opacity: 0.5;\n                        }\n                    }\n                }\n            "]);return e3=function(){return e},e}function e8(){var e=(0,eV._)(["\n                p {\n                    /* stylelint-disable declaration-block-no-redundant-longhand-properties */\n                    margin-block-start: calc(var(--lobe-markdown-margin-multiple) * 1em);\n                    margin-block-end: calc(var(--lobe-markdown-margin-multiple) * 1em);\n                    /* stylelint-enable declaration-block-no-redundant-longhand-properties */\n                    line-height: var(--lobe-markdown-line-height);\n                    letter-spacing: 0.02em;\n                }\n            "]);return e8=function(){return e},e}function e6(){var e=(0,eV._)(["\n                pre,\n                [data-code-type='highlighter'] {\n                    white-space: break-spaces;\n                    border: none;\n\n                    > code {\n                        padding: 0 !important;\n\n                        font-family: ",";\n                        font-size: 0.875em;\n                        line-height: 1.6;\n\n                        border: none !important;\n                    }\n                }\n            "]);return e6=function(){return e},e}function e5(){var e=(0,eV._)(["\n                strong {\n                    font-weight: 600;\n                }\n            "]);return e5=function(){return e},e}function e7(){var e=(0,eV._)(["\n                table {\n                    unicode-bidi: isolate;\n                    overflow: auto hidden;\n                    display: block;\n                    border-spacing: 0;\n                    border-collapse: collapse;\n\n                    box-sizing: border-box;\n                    width: max-content;\n                    max-width: 100%;\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 1em);\n\n                    text-align: start;\n                    text-indent: initial;\n                    text-wrap: pretty;\n                    word-break: auto-phrase;\n                    overflow-wrap: break-word;\n\n                    background: ",";\n                    border-radius: calc(var(--lobe-markdown-border-radius) * 1px);\n                    box-shadow: 0 0 0 1px var(--lobe-markdown-border-color);\n\n                    code {\n                        word-break: break-word;\n                    }\n\n                    thead {\n                        background: ",";\n                    }\n\n                    tr {\n                        box-shadow: 0 1px 0 var(--lobe-markdown-border-color);\n                    }\n\n                    th,\n                    td {\n                        min-width: 120px;\n                        padding-block: 0.75em;\n                        padding-inline: 1em;\n                        text-align: start;\n                    }\n                }\n            "]);return e7=function(){return e},e}function ne(){var e=(0,eV._)(["\n                > video,\n                > p > video {\n                    margin-block: calc(var(--lobe-markdown-margin-multiple) * 1em);\n                    border-radius: calc(var(--lobe-markdown-border-radius) * 1px);\n                    box-shadow: 0 0 0 1px var(--lobe-markdown-border-color);\n                }\n\n                video {\n                    max-width: 100%;\n                }\n            "]);return ne=function(){return e},e}var nn=(0,P.rU)(function(e,n){var t=e.token,r=e.isDarkMode,o=e.css,i=n.fontSize,a=n.headerMultiple,s=n.marginMultiple,l=n.lineHeight;return{__root:o(e$(),void 0===i?16:i,void 0===a?1:a,void 0===s?1.5:s,void 0===l?1.8:l,t.borderRadiusLG,r?t.colorBorderSecondary:(0,eJ.B3)(t.colorBorderSecondary,.5)),a:o(eZ(),t.colorInfoText,t.colorInfoHover),blockquote:o(eQ(),t.colorTextSecondary,t.colorBorder),code:o(eX(),t.fontFamilyCode,t.colorFillSecondary),details:o(e0(),t.colorFillTertiary,t.fontFamily,t.colorTextSecondary,t.colorTextSecondary,t.motionEaseOut,t.colorBorder),header:o(e1()),hr:o(e9(),t.colorBorderSecondary),img:o(e2()),kbd:o(e4(),t.fontFamily,t.colorBgLayout,t.colorBorderSecondary),list:o(e3()),p:o(e8()),pre:o(e6(),t.fontFamilyCode),strong:o(e5()),table:o(e7(),t.colorFillQuaternary,t.colorFillQuaternary),video:o(ne())}});let nt=function(e){var n=e.className,t=e.style,r=e.children,a=e.rehypePlugins,s=void 0===a?[]:a,u=e.remarkPlugins,d=void 0===u?[]:u,x=e.fontSize,m=e.headerMultiple,p=e.lineHeight,h=e.marginMultiple,f=(0,H._)(e,["className","style","children","rehypePlugins","remarkPlugins","fontSize","headerMultiple","lineHeight","marginMultiple"]),v=eK({fontSize:x,headerMultiple:m,lineHeight:p,marginMultiple:h}),y=v.cx,g=v.styles,b=nn({fontSize:x,headerMultiple:m,marginMultiple:h}).styles,k=(0,c.useMemo)(function(){return function(e){for(var n=0,t=0,r="",o=!1,i=!1,a=0;a<e.length;a++){var s=e[a];if("```"===e.slice(a,a+3)){o=!o,r+="```",a+=2;continue}if("`"===s){i=!i,r+="`";continue}if("*"!==s||i||o)r+=s,n=0;else{if(2==++n&&t++,n>2){r+=s;continue}if(2===n&&t%2==0){var l=a>0?e[a-2]:"",c=RegExp("[\\p{P}\\p{S}]","u").test(l);r+=a+1<e.length&&" "!==e[a+1]&&c?"* ":"*"}else r+="*"}}return r}((null!=r?r:"").replaceAll(/(```[\S\s]*?```|`.*?`)|\\\[([\S\s]*?[^\\])\\]|\\\((.*?)\\\)/g,function(e,n,t,r){return n?n:t?"$$".concat(t,"$$"):r?"$".concat(r,"$"):e}).replaceAll("$\\ce{","$\\\\ce{").replaceAll("$\\pu{","$\\\\pu{"))},[r]),_=(0,c.useMemo)(function(){return[ez.A,eU].concat((0,T._)(s)).filter(Boolean)},(0,T._)(s)),w=(0,c.useMemo)(function(){return[eP.A,eq.A,eB.A].concat((0,T._)(d)).filter(Boolean)},(0,T._)(d)),j=(0,c.useMemo)(function(){var e=k.replace(/~~([^~]*)~~/g,"###DOUBLE_TILDE###$1###DOUBLE_TILDE###");return(e=e.replace(/~/g,"\\~")).replace(/###DOUBLE_TILDE###/g,"~~")},[k]);return(0,l.jsx)("div",{className:y(g.root,b.__root,b.a,b.blockquote,b.code,b.details,b.header,b.hr,b.img,b.kbd,b.list,b.p,b.pre,b.strong,b.table,b.video,g.latex,g.chat,n),style:t,children:(0,l.jsx)(eF.$,(0,i._)((0,o._)({},f),{remarkPlugins:w,rehypePlugins:_,children:j}))})};var nr=t(16442),no=function(e){var n=e.item;return(0,l.jsxs)("a",{draggable:!1,className:"w-[268px] p-16 block hover:bg-black/[0.045]",href:null==n?void 0:n.url,target:"_blank",rel:"noreferrer noopener nofollow",children:[(0,l.jsx)("article",{className:"min-h-40 text-12 text-[var(--text-color-primary)] line-clamp-2",children:null==n?void 0:n.snippet}),(0,l.jsxs)("div",{className:"flex-shrink-0 flex items-center text-10 text-[var(--text-color-tertiary)] gap-4 mt-8 min-h-18",children:[(0,l.jsx)(ei.A,{className:"!flex-none",src:null==n?void 0:n.thumbnailUrl,size:16,icon:(0,l.jsx)(w.A,{type:"icon-AiFindOutlined"})}),(0,l.jsx)("span",{className:"inline-block flex-auto truncate",children:null==n?void 0:n.siteName}),(0,l.jsx)(w.A,{type:"icon-ArrowRightOutlined",className:"text-16"})]})]})};let ni=function(e){var n=e.index,t=e.item;return(0,l.jsx)(ex.A,{placement:"bottom",styles:{body:{paddingInline:0,paddingBlock:0,overflow:"hidden"}},content:(0,l.jsx)(no,{item:t}),arrow:!1,children:(0,l.jsx)("a",{draggable:!1,className:"align-[2px] inline-flex items-center",target:"_blank",rel:"noreferrer noopener",children:(0,l.jsx)(nr.A,{className:"markdown-badge overflow-visible",size:"small",title:"",count:n})})})};var na=t(3793),ns=t(18850),nl=t(33556),nc=t(84765),nu=t.n(nc);let nd=function(e){var n,t,u,d=e.children,x=e.className,m=(0,H._)(e,["children","className"]),p=(0,eM.Bd)().t,h=(0,a._)((0,c.useState)(!1),2),f=h[0],v=h[1],y=null!==(n=null==x?void 0:x.replace("language-",""))&&void 0!==n?n:"text",g=/language-(\w+)/.exec(x||""),b=null!==(u=String(null!==(t=null==d?void 0:d[0])&&void 0!==t?t:"").replace(/\n$/,""))&&void 0!==u?u:"",k=(0,W.i)(),_=(0,c.useCallback)((0,r._)(function(){return(0,s.YH)(this,function(e){return v(!0),k(b),setTimeout(function(){return v(!1)},2e3),[2]})}),[b]);return(0,l.jsxs)("div",{className:nu()["md-code-block"],children:[(0,l.jsx)("div",{className:nu()["md-code-block--header"],children:(0,l.jsxs)("div",{className:nu()["md-code-block--header-body"],children:[(0,l.jsx)("span",{className:nu()["code-block--language"],children:y}),(0,l.jsx)("div",{className:nu()["code-block--actions"],children:(0,l.jsx)(U.Ay,{type:"text",className:"!text-[rgb(8,8,8)] !text-12 !h-20",size:"small",onClick:_,children:p(f?"common.copSuc":"common.cop")})})]})}),(0,l.jsx)(ns.A,(0,i._)((0,o._)({language:(null==g?void 0:g[1])||"text",style:nl.Wj,wrapLines:!0,customStyle:{fontSize:14,margin:"0",whiteSpace:"break-spaces",padding:"16px 12px",borderRadius:"0 0 12px 12px",background:"#F7F7F7"},codeTagProps:{style:{whiteSpace:"break-spaces",textShadow:"white 0px 1px",tabSize:8,fontSize:14,textAlign:"left",wordBreak:"normal",wordSpacing:"normal",lineHeight:1.6,hyphens:"none"}}},m),{children:b}))]})},nx=function(e){var n=e.index,t=e.item;return(0,h.n)().isMobile?(0,l.jsx)("a",{draggable:!1,href:null==t?void 0:t.url,className:"align-[2px] inline-flex items-center",target:"_blank",rel:"noreferrer noopener",children:(0,l.jsx)(nr.A,{className:"docrefence-badge overflow-visible",size:"small",title:"",count:"string"==typeof n?n.replace("doc_",""):n})}):(0,l.jsx)(ex.A,{placement:"bottom",styles:{body:{paddingInline:0,paddingBlock:0,overflow:"hidden"}},content:(0,l.jsx)(B.I3,{docType:t.docType,docName:t.docName,docSize:t.docSize,wordNum:t.wordNum}),arrow:!1,children:(0,l.jsx)("a",{draggable:!1,href:null==t?void 0:t.url,className:"align-[2px] inline-flex items-center",target:"_blank",rel:"noreferrer noopener",children:(0,l.jsx)(nr.A,{className:"docrefence-badge overflow-visible ",size:"small",title:"",count:"string"==typeof n?n.replace("doc_",""):n})})})};var nm=t(22710),np=t(53087),nh=/cancel_(.+?)_cancel/,nf=/(https?:\/\/)?([a-zA-Z0-9-]+)(\.[a-zA-Z0-9-._~:\/#[\]@!$&'()*+,;=]+)(\?[a-zA-Z0-9-._~&=!%]+)?/g,nv=[function(){return function(e){(0,eH.YR)(e,"code",function(e){e.value=e.value.replace("{{end_dot}}",""),e.value=e.value.replace(/\\~/g,"~")})}},function(){return function(e){(0,np.VG)(e,"text",function(e,n){if(e.value.includes("{{end_dot}}")){e.value=e.value.replace("{{end_dot}}","");var t,r=n[n.length-1];if("paragraph"===r.type){var o=n[n.length-2];"listItem"===o.type&&(r=o)}r&&((t=r).data||(t.data={}),t.data.hProperties||(t.data.hProperties={}),t.data.hProperties.className="markdown-last-node")}})}},function(){return function(e){(0,np.VG)(e,"link",function(e,n){var t=n[n.length-1];if(e.url.match(nf)){var r=[],o=0;for(nf.lastIndex=0;null!==(i=nf.exec(e.url));)i.index>o&&r.push({type:"text",value:e.url.substring(o,i.index)}),r.push({type:"link",url:i[0].startsWith("http")?i[0]:"http://".concat(i[0]),children:[{type:"text",value:i[0]}]}),o=nf.lastIndex;if(o<e.url.length&&r.push({type:"text",value:e.url.substring(o)}),r.length>0){var i,a,s=t.children.indexOf(e);(a=t.children).splice.apply(a,[s,1].concat((0,T._)(r)))}}})}},function(){return function(e){(0,eH.YR)(e,"text",function(e,n,t){var r=e.value.match(nh);r&&(e.value=r[1],t.data={hProperties:{className:"markdown-cancel-text"}})})}},function(){return function(e){(0,eH.YR)(e,"text",function(e,n,t){for(var r,o,i=/\[(.*?)\]/g,s=[],l=0;null!==(r=i.exec(e.value));){var c=(0,a._)(r,2),u=c[0],d=c[1],x=r[1];r.index>l&&s.push({type:"text",value:e.value.slice(l,r.index)}),s.push({type:"customSyntax",data:{hName:"squote",hProperties:{className:"reference-syntax",dataIndex:x}},children:[{type:"text",value:d}]}),l=r.index+u.length}l<e.value.length&&s.push({type:"text",value:e.value.slice(l)}),s.length>0&&(o=t.children).splice.apply(o,[n,1].concat((0,T._)(s)))})}},function(){return function(e){(0,nm.T)(e,[/\*\*"(.*?)"\*\*/g,function(e,n){return{type:"strong",children:[{type:"text",value:'"'.concat(n,'"')}]}}]),(0,nm.T)(e,[/\*\*（(.*?)）\*\*/g,function(e,n){return{type:"strong",children:[{type:"text",value:'"'.concat(n,'"')}]}}])}}],ny=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=function(t){var r=t.children,a=(0,H._)(t,["children"]),s=(0,na.A)(a,["ordered"]);return(0,l.jsx)(e,(0,i._)((0,o._)({},s),{className:(0,g.cx)(n,s.className),target:"a"===e?"_blank":void 0,children:r}))};return t.displayName="CustomRenderer(".concat(e,")"),t},ng=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return(0,c.useMemo)(function(){return{code:function(e){var n=e.children,t=(0,H._)(e,["children"]);return(0,l.jsx)("code",(0,i._)((0,o._)({className:"markdown-custom-code"},t),{children:String(n).replace(/\s-\s/g,"-")}))},h1:ny("h1","markdown-custom-h1"),h3:ny("h3","markdown-custom-h3"),h4:ny("h4","markdown-custom-h4"),ol:ny("ol","markdown-custom-ol"),ul:ny("ul","markdown-custom-ul"),a:ny("a","markdown-custom-a"),br:ny("br","markdown-custom-br"),hr:function(){return null},p:ny("p","markdown-custom-p"),squote:function(n){var t,r=n.node,o=n.children,i=n.className,a=null==r?void 0:null===(t=r.properties)||void 0===t?void 0:t.dataIndex;if("reference-syntax"===i){var s=(e||[]).reduce(function(e,n){return e.push.apply(e,(0,T._)(n)),e},[]),c=(null==e?void 0:e.find(function(e){return e.idIndex==a}))||s.find(function(e){return e.idIndex==a});return c?c.idIndex.startsWith("doc_")?(0,l.jsx)(nx,{index:a,item:c}):(0,l.jsx)(ni,{index:a,item:c}):(0,l.jsxs)("span",{children:["[",a,"]"]})}return(0,l.jsx)("span",{children:o})},pre:function(e){var n=e.children;return(0,l.jsx)(nd,(0,o._)({},n[0].props))}}},[null==e?void 0:e.length])};let nb=(0,c.memo)(function(e){var n=e.className,t=e.content,r=ng(e.refrenceSource);return(0,l.jsx)(nt,{className:(0,g.cx)("markdown-container",n),components:r,remarkPlugins:nv,children:t})}),nk=(0,c.memo)(function(e){var n=e.id,t=e.chatId,r=e.index,o=e.searchSource,i=e.status,a=e.ctxClearFlag,s=e.content,u=void 0===s?"":s,x=e.scrollToBottom,m=e.conversationId,p=(0,y.A)(function(e){return e.recordExportConversationId}),h=(0,y.A)(function(e){return e.objMap}),f=(0,c.useMemo)(function(){return h.get(m)},[h,m]);return(0,c.useEffect)(function(){["success","followupLoading"].includes(i)&&(null==x||x())},[i]),(0,c.useEffect)(function(){null==x||x()},[u]),(0,l.jsx)(eR,{index:r,value:n,chatId:t,formInstance:f,isRecordExport:void 0!==p&&p===m,children:(0,l.jsx)(d.A,{vertical:!0,gap:20,children:(0,l.jsx)(d.A,{className:(0,g.cx)(e_()["ai-message-content"],a&&e_()["ctx-clear-flag"]),vertical:!0,children:(0,l.jsx)(nb,{content:"".concat(u).concat(u&&"updating"===i?"{{end_dot}}":""),refrenceSource:o})})})},r)});var n_=t(15093),nw=t(33319),nj=t(30427),nA=t.n(nj),nI=function(e){var n=e.title;return(0,l.jsxs)(d.A,{wrap:!1,className:"!whitespace-nowrap !min-h-20 !text-[var(--text-color-primary)] ",gap:4,align:"center",children:[(0,l.jsx)(w.A,{type:"icon-DeepseekOutlined",className:"text-16",size:16}),(0,l.jsx)("article",{children:n})]})},nN=function(e){var n=e.content,t=e.loading,r=e.status,o=e.onTyping,i=(0,c.useRef)(n?n.length:0),s=(0,c.useRef)(!1),u=(0,c.useRef)(),d=(0,c.useRef)(n||""),x=(0,a._)((0,c.useState)(n||""),2),m=x[0],p=x[1];return(0,c.useEffect)(function(){if(!t||"success"===r){clearTimeout(u.current),d.current=n,p(d.current),null==o||o();return}var e=n.length-i.current>20,a=function(e){s.current=!0,u.current=setTimeout(function(){var n=e[i.current];if(d.current+=n,i.current+=1,p(d.current),null==o||o(),e.length-i.current<20){s.current=!1,clearTimeout(u.current);return}a(e)},20)};e&&!s.current&&a(n)},[n,r,t]),(0,l.jsxs)("div",{className:(0,g.cx)(nA()["deep-thinking-container"]),children:[!m&&(0,l.jsx)(R,{}),(0,l.jsx)("div",{className:nA()["deep-thinking-markdown"],children:(0,l.jsx)(nb,{content:"".concat(m," ").concat(n&&t&&"success"!==r?"{{end_dot}}":"")})})]})};let nC=(0,c.memo)(function(e){var n=e.status,t=e.className,r=e.style,o=e.loading,i=e.content,s=e.onTyping,u=(0,eM.Bd)().t,d=(0,ev.useRouter)(),x=(0,a._)((0,c.useState)(d.pathname.startsWith("/share")||d.pathname.startsWith("/ask")?[]:["root"]),2),m=x[0],p=x[1],h=m.length>0,f=(0,c.useRef)(null),v=[{key:"root",label:(0,l.jsx)(nI,{title:o&&"success"!==n?"".concat(u("common.deepPro"),"..."):"".concat(u("common.deepCom"))}),classNames:{header:nA()["deep-thinking-collapse--header"],body:"!pt-0 !pb-[12px] !px-12"},children:(0,l.jsx)(nw.P.div,{className:"opacity-0",animate:{opacity:+!!h},children:(0,l.jsx)(nN,{status:n,content:i||"",loading:!!o,onTyping:s})})}];return(0,l.jsx)(nw.P.div,{ref:f,className:(0,g.cx)("inline-block overflow-hidden min-w-[120px] w-fit self-baseline",t,{"max-w-full":h}),style:r,animate:{width:h?"100%":"auto"},transition:{duration:.3,ease:"linear"},children:(0,l.jsx)(n_.A,{activeKey:m,expandIconPosition:"end",className:(0,g.cx)(nA()["deep-thinking-collapse"],h?nA()["is-active"]:""),items:v,expandIcon:function(e){var n=e.isActive;return(0,l.jsx)(w.A,{type:"icon-DownOutlined",className:"flex text-14 mt-2",rotate:n?-180:0})},onChange:function(e){p(e)}})})});var nS=t(16121),nR=function(e){var n=e.className,t=e.title,r=e.type,o=e.loading;return t?(0,l.jsxs)("div",{className:(0,g.cx)("flex items-center gap-4 leading-[22px]",n),children:[o?(0,l.jsx)(R,{width:16,height:16}):(0,l.jsx)(w.A,{type:"2003"===r?"icon-AIKnowledgeOutlined":"icon-CheckCircleOutlined",className:"text-16 h-16 leading-4"}),(0,l.jsx)("span",{className:"min-w-max text-14 text-[var(--text-color-primary)] font-medium",children:t})]}):null},nT=function(e){var n=e.items,t=e.className,r=e.style;return(0,l.jsx)("div",{className:(0,g.cx)("flex pl-20 gap-8 flex-wrap items-center self-stretch",t),style:r,children:(void 0===n?[]:n).map(function(e){return(0,l.jsx)("div",{className:"flex flex-col justify-center items-start text-12 text-[var(--text-color-primary)] rounded-8 px-8 py-4 bg-[var(--bg-color-primary-active)]",children:e},e)})})},nL=function(e){var n=e.className,t=e.title,r=e.children,o=e.type,i=e.loading;return(0,l.jsxs)("div",{className:(0,g.cx)("cursor-auto",n),children:[(0,l.jsx)(nR,{className:"pb-6",title:t,type:o,loading:i}),(0,l.jsx)("div",{className:"flex gap-4 w-full",onClick:function(e){return e.stopPropagation()},children:r})]})},nM=function(e){return(null==e?void 0:e.length)===0?[]:e.reduce(function(e,n){return n.content&&(0,g.jU)(n.content)&&(n.content=JSON.parse(n.content)),e.push(n),e},[])};let nE=function(e){var n,t=e.className,r=e.style,s=e.title,u=e.records,x=e.isTourism,m=e.onTyping,p=e.loading,h=(0,a._)((0,c.useState)(!1),2),f=h[0],v=h[1],y=(0,c.useRef)(!1),b=(0,c.useMemo)(function(){var e,n=null!==(e=null==u?void 0:u.cardItems)&&void 0!==e?e:[];null==m||m();var t=nM(n);return p?"2004"!==t[t.length-1].type?t[t.length-1]=(0,i._)((0,o._)({},t[t.length-1]),{content:"",loading:!0,name:t[t.length-1].name}):t[t.length-1]=(0,i._)((0,o._)({},t[t.length-1]),{loading:!0,name:t[t.length-1].name}):v(!1),t},[null==u?void 0:u.cardItems,u,p]),k=(0,c.useMemo)(function(){var e,n=null===(e=b.find(function(e){return"2002"===e.type||"2003"===e.type}))||void 0===e?void 0:e.content;return null!=n?n:[]},[b]),_=(0,c.useMemo)(function(){var e,n=null===(e=b.find(function(e){return"2003"===e.type}))||void 0===e?void 0:e.content;return void 0!==n&&(null==n?void 0:n.length)!==0},[b]);return(0,c.useEffect)(function(){var e;v(null!==(e=!!x&&p)&&void 0!==e&&e)},[x,p]),(0,l.jsxs)(nS.P.div,{className:(0,g.cx)("p-12 overflow-hidden bg-[var(--bg-color-secondary)] self-baseline box-border",t,{}),style:(0,o._)({overflowAnchor:"auto",display:"block",maxWidth:"100%"},r),onClick:function(){(k.length>0||x&&b.length>0)&&(y.current=!0,v(!f))},initial:{height:"auto",borderRadius:16},animate:{height:f?"auto":y.current?[110,"auto"]:"auto",width:f?"100%":["fit-content"]},transition:{borderRadius:{duration:.3,cubicBezier:[.4,0,.1,1]},height:{duration:.3,cubicBezier:[.4,0,.1,1]},width:{duration:.3,cubicBezier:[.4,0,.1,1]}},children:[(0,l.jsxs)("div",{className:(0,g.cx)("relative flex items-center"),children:[f?(0,l.jsx)(nR,{className:" select-none flex-auto",title:null==u?void 0:u.initTitle}):(0,l.jsxs)("span",{className:"select-none flex items-center gap-4 cursor-pointer w-full",children:[p?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(R,{}),"正在",null===(n=b[b.length-1])||void 0===n?void 0:n.name]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(w.A,{type:x?"icon-DeepseekOutlined":_?"icon-AIKnowledgeOutlined":"icon-AiSearchOutlined",className:"text-16"}),(0,l.jsxs)("div",{className:"inline-flex items-center text-14 text-[var(--text-color-primary)] font-medium text-nowrap overflow-hidden",children:[(0,l.jsx)("span",{children:s}),(0,l.jsx)(ei.A.Group,{size:22,children:k.filter(function(e,n,t){return e.thumbnailUrl&&""!==e.thumbnailUrl&&n===t.findIndex(function(n){return n.thumbnailUrl===e.thumbnailUrl})}).slice(0,5).map(function(e){return(0,l.jsx)(ei.A,{className:"!bg-[#d8d8d8] !border-[var(--border-color)] !w-20 !h-20",src:e.thumbnailUrl||void 0,icon:e.thumbnailUrl?void 0:(0,l.jsx)(w.A,{type:"icon-AiFindOutlined",className:"text-18"})},e.url)})})]})]}),(0,l.jsx)(w.A,{type:"icon-DownOutlined",className:"flex text-14"})]}),f&&(0,l.jsx)(w.A,{type:"icon-UpOutlined",className:(0,g.cx)("absolute top-1/2 -translate-y-1/2 right-0 text-16",x?"top-10":"")})]}),(0,l.jsx)(nS.P.div,{className:(0,g.cx)("w-full ",{hidden:!f,block:f}),initial:{opacity:0},animate:{opacity:+!!f},transition:{opacity:{duration:.5,cubicBezier:[.4,0,.1,1]}},children:b.map(function(e,n){var t;if("2003"===e.type&&(t=(0,l.jsx)(B.Ay,{items:e.content,notdel:!0,isFile:!0,className:"w-full"})),"2001"===e.type&&(t=(0,l.jsx)(nT,{items:e.content})),"2002"===e.type){var r=e.content.map(function(e){return{href:e.url,target:"_blank",title:e.snippet,author:e.siteName,avatar:e.thumbnailUrl,rel:"noreferrer noopener"}});t=(0,l.jsx)(B.Ay,{className:"w-full h-94 ",items:r,contentClassName:"h-94"})}if("2004"===e.type&&(t=(0,l.jsxs)("div",{className:(0,g.cx)("w-full max-h-[145px] overflow-hidden relative  ml-20 text-14 scrollbar-none",n===b.length-1?"text-[#00000000]":"text-[var(--text-color-primary-hover)] overflow-y-auto"),children:[e.content,n===b.length-1&&(0,l.jsx)("div",{className:"absolute bottom-0 right-0 w-full text-[var(--text-color-primary-hover)]",children:e.content})]})),"2005"===e.type){var o=nM(e.content).map(function(e,n){if("2003"===e.type)return(0,l.jsx)(d.A,{className:"w-full pl-20",vertical:!0,gap:8,children:(0,l.jsx)(B.Ay,{items:e.content,notdel:!0,isFile:!0,className:"w-full"})});if("2004"===e.type)return(0,l.jsxs)("div",{className:(0,g.cx)("w-full max-h-[145px] overflow-hidden relative  ml-20 text-14 scrollbar-none",n===o.length-1?"text-[#00000000]":"text-[var(--text-color-primary-hover)] overflow-y-auto"),children:[e.content,n===o.length-1&&(0,l.jsx)("div",{className:"absolute bottom-0 right-0 w-full text-[var(--text-color-primary-hover)]",children:e.content})]});if("2001"===e.type)return(0,l.jsx)(d.A,{className:"w-full mx-20 border",vertical:!0,gap:8,children:(0,l.jsx)(nT,{items:e.content,className:"pl-0"})});if("2002"===e.type){var t=e.content.map(function(e){return{href:e.url,target:"_blank",title:e.snippet,author:e.siteName,avatar:e.thumbnailUrl,rel:"noreferrer noopener"}});return(0,l.jsx)(d.A,{className:"ml-20 border",style:{width:"calc(100% - 20px)"},vertical:!0,gap:8,children:(0,l.jsx)(B.Ay,{className:"w-full border h-94 pl-0",items:t,noneRight:!0,contentClassName:"h-94"})})}});t=(0,l.jsx)(d.A,{vertical:!0,gap:4,className:"w-full",children:o})}return(0,l.jsx)(nL,{className:0===n&&x?"":"mt-12",title:e.name,type:e.type,loading:e.loading,children:t},n)})})]})};var nD=t(48772),nO=t(92168),nF=t(98661),nz=t.n(nF),nB=function(e){var n,t=null===(n=e.find(function(e){return"3002"===e.type}))||void 0===n?void 0:n.content;if("string"==typeof t&&!(0,g.jU)(t))return[];var r=JSON.parse(t);return(null!=r?r:[]).reduce(function(e,n){var t=(null!=n?n:{}).searchList;return(0,T._)(e).concat((0,T._)(null!=t?t:[]))},[])},nP=function(e){var n=e.title;return(0,l.jsxs)(d.A,{wrap:!1,className:"!whitespace-nowrap !min-h-22",gap:4,align:"center",children:[(0,l.jsx)(w.A,{type:"icon-AiSearchOutlined",className:"text-16",size:16}),(0,l.jsx)("article",{children:n})]})},nq=function(e){var n=e.title,t=e.loading;return(0,l.jsxs)(d.A,{className:"!min-h-22 !rounded-[22px]",align:"center",gap:4,children:[t?(0,l.jsx)(x.A,{indicator:(0,l.jsx)(nO.A,{spin:!0}),size:"small"}):(0,l.jsx)(w.A,{type:"icon-CheckCircleOutlined",size:16,className:"text-16"}),(0,l.jsx)("span",{children:n})]})},nH=function(e){var n=e.content,t=null==n?void 0:n.replace(/\n/g,"<br/>");return(0,l.jsx)("article",{className:"text-12 rounded-[20px] text-[var(--text-color-secondary)] px-20",dangerouslySetInnerHTML:{__html:null!=t?t:""}})},nU=function(e){var n=e.list,t=e.isCompleted,r=(0,c.useMemo)(function(){return n.filter(function(e){return 0!=e.status})},[n]);return(0,l.jsx)(nD.A,{size:"small",className:"!py-0",dataSource:r,split:!1,renderItem:function(e,n){var r,o=!t&&2!=e.status;return(0,l.jsx)(nD.A.Item,{className:"px-0 py-4",children:(0,l.jsx)(nD.A.Item.Meta,{className:nz()["collapse-child-list--meta"],title:(0,l.jsxs)(d.A,{align:"center",gap:4,children:[o?(0,l.jsx)(x.A,{indicator:(0,l.jsx)(nO.A,{spin:!0}),size:"small"}):(0,l.jsx)(w.A,{type:"icon-CheckOutlined",size:12,className:"text-12"}),(0,l.jsx)("span",{children:e.title})]}),description:(0,l.jsxs)(d.A,{gap:10,align:"center",wrap:!1,children:[e.content&&(0,l.jsx)("span",{children:e.content}),(0,l.jsx)(ei.A.Group,{size:18,children:(null!==(r=null==e?void 0:e.searchList)&&void 0!==r?r:[]).filter(function(e,n,t){return e.thumbnailUrl&&""!==e.thumbnailUrl&&n===t.findIndex(function(n){return n.thumbnailUrl===e.thumbnailUrl})}).slice(0,5).map(function(e,n){return(0,l.jsx)(ei.A,{src:e.thumbnailUrl,icon:(0,l.jsx)(w.A,{type:"icon-AiFindOutlined"})},n)})})]})})},n)}})},nV=function(e){var n=e.records,t=void 0===n?[]:n,r=e.isCompleted,o=(0,a._)((0,c.useState)(),2),i=o[0],s=o[1],u=(0,c.useMemo)(function(){return t.filter(function(e){return 0!==e.status}).map(function(e,n){var t=!r&&2!=e.status,o={key:n,label:(0,l.jsx)(nq,{loading:t,title:e.name}),showArrow:!!e.content,collapsible:e.content?void 0:"disabled",classNames:{header:nz()["deep-search-child-collapse--header"],body:nz()["deep-search-child-collapse--body"]},style:{border:"none"}};if("3001"===e.type&&e.content&&(o.children=(0,l.jsx)(nH,{content:e.content})),"3002"===e.type&&e.content){var i=(0,g.jU)(e.content)?JSON.parse(e.content):[];o.children=(0,l.jsx)(nU,{list:i,isCompleted:r})}return o})},[t,r]);return(0,c.useEffect)(function(){s(null==u?void 0:u.map(function(e){return e.key}))},[u]),(0,l.jsx)(n_.A,{activeKey:i,className:nz()["deep-search-child-collapse"],bordered:!1,items:u,expandIconPosition:"end",onChange:function(e){s(e)}})};let nW=function(e){var n,t=e.className,r=e.style,o=e.records,i=2==o.status,s=(0,a._)((0,c.useState)(i?[]:["root"]),2),u=s[0],x=s[1],m=u.length>0,p=(0,c.useRef)(null),h=o.cardItems,f=nB(h);if(1==o.status){var v=h.filter(function(e){return 1==e.status})[0];(null==v?void 0:v.name)&&(n="深度搜索：正在"+(null==v?void 0:v.name))}2==o.status&&(n=(0,l.jsxs)(d.A,{align:"center",gap:4,children:[(0,l.jsxs)("span",{children:["深度搜索：基于",f.length,"篇资料作为参考"]}),!!f.length&&(0,l.jsx)(ei.A.Group,{size:18,children:(null!=f?f:[]).filter(function(e,n,t){return e.thumbnailUrl&&""!==e.thumbnailUrl&&n===t.findIndex(function(n){return n.thumbnailUrl===e.thumbnailUrl})}).slice(0,5).map(function(e,n){return(0,l.jsx)(ei.A,{src:e.thumbnailUrl,icon:(0,l.jsx)(w.A,{type:"icon-AiFindOutlined"})},n)})})]}));var y=[{key:"root",label:(0,l.jsx)(nP,{title:m?"深度搜索":n}),classNames:{header:nz()["deep-search-collapse--header"],body:"py-10"},children:(0,l.jsx)(nw.P.div,{className:"opacity-0 hidden",animate:{opacity:+!!m,display:m?"block":"none"},transition:{display:{delay:.2*!!m,duration:0,ease:"linear"}},children:(0,l.jsx)(nV,{records:h,isCompleted:i})})}];return(0,l.jsx)(nw.P.div,{ref:p,className:(0,g.cx)("inline-block overflow-hidden min-w-[120px] w-fit self-baseline",t,{"max-w-full":m}),style:r,animate:{width:m?["50%","100%"]:"auto"},transition:{duration:.3,ease:"linear"},children:(0,l.jsx)(n_.A,{activeKey:u,expandIconPosition:"end",className:(0,g.cx)(nz()["deep-search-collapse"],m?nz()["is-active"]:""),items:y,expandIcon:function(e){var n=e.isActive;return(0,l.jsx)(w.A,{type:"icon-DownOutlined",className:"block text-16",rotate:n?-180:0})},onChange:function(e){x(e)}})})};var nG=t(11e3),nY=t(37967),nK=t(35167),nJ=t.n(nK),n$=function(e,n){var t=(0,a._)((0,c.useState)(0),2),r=t[0],o=t[1],i=(0,c.useRef)(null),s=n/e*100,l=(0,c.useRef)(!1),u=function(){o(0),i.current&&clearInterval(i.current),l.current=!1};return{progress:r,start:function(){l.current||(l.current=!0,i.current=setInterval(function(){o(function(e){var t=e+s;return t>=n?(clearInterval(i.current),l.current=!1,n):t})},100))},stop:function(){l.current&&(i.current&&clearInterval(i.current),l.current=!1)},clear:u,set:function(e){u(),o(e)}}},nZ=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e4,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:80,r=n$(n,t),o=r.progress,i=r.start,a=r.clear,s=r.set;return(0,c.useEffect)(function(){switch(e){case 0:a();break;case 1:i();break;case 2:s(100)}},[e]),{percent:Math.round(o)}},nQ=(0,c.memo)(function(e){var n=e.label,t=e.status,r=e.duration,o=e.targetProgress,i=nZ(t,void 0===r?1e4:r,void 0===o?80:o).percent;return(0,l.jsxs)("div",{className:"block flex-none w-[120px]",children:[(0,l.jsx)(nG.A,{className:"!block !h-14 !leading-[12px]",strokeColor:"#75B3FF",percent:i,showInfo:!1,size:"small"}),(0,l.jsx)("label",{className:"text-12/[20px] mt-4 text-[var(--text-color-primary)]",children:n})]})}),nX=(0,c.memo)(function(e){var n=e.index,t=nZ(e.status).percent;return(0,l.jsxs)("div",{className:"bg-[var(--bg-color-primary)] relative w-[15px] h-[15px] rounded-12 overflow-hidden",children:[(0,l.jsx)(nG.A,{className:"!block",type:"circle",percent:t,size:15,strokeColor:"#75B3FF",strokeWidth:10}),(0,l.jsx)("div",{className:"absolute top-0 left-0 w-full h-full flex justify-center items-center text-[8px]/[14px] text-[var(--text-color-secondary)]",children:n})]})}),n0=(0,c.memo)(function(e){var n=e.tasks,t=e.status,r=(0,a._)((0,c.useState)(0),2),o=r[0],i=r[1],s=(0,c.useRef)(null),u=(0,c.useRef)(0);return(0,c.useEffect)(function(){if(s.current&&clearInterval(s.current),u.current=0,2===t){i(100);return}var e=100/n.length,r=.8*e,o=r/1e4*100,a=n.filter(function(e){return 2===e.status}).length*e;i(function(e){return e<a?a:e}),n.some(function(e){return 1===e.status})&&(s.current=setInterval(function(){i(function(e){return u.current+=o,u.current>=r&&clearInterval(s.current),e+o})},100))},[n,t]),(0,l.jsx)(nG.A,{className:"block h-14 leading-[12px]",strokeColor:"#75B3FF",percent:o,showInfo:!1,size:"small"})}),n1=(0,c.memo)(function(e){var n=e.label,t=e.status,r=JSON.parse(e.data);return(0,l.jsxs)("div",{className:"block w-full",children:[(0,l.jsxs)("div",{className:"relative w-full",children:[(0,l.jsx)(n0,{tasks:r||[],status:t}),(0,l.jsxs)("div",{className:"flex justify-between items-center w-full absolute text-12/[24px] text-[var(--text-color-tertiary)] h-full select-none top-0 left-0",children:[(0,l.jsx)("div",{className:"w-[15px] h-[15px]"}),null==r?void 0:r.map(function(e,n){return(0,l.jsx)(nX,{index:n+1,status:e.status},n)})]})]}),(0,l.jsx)("label",{className:"text-12/[20px] mt-4 text-[var(--text-color-primary)]",children:n})]})}),n9=function(e){var n=e.title,t=e.secTitle,r=e.status;return(0,l.jsxs)(d.A,{align:"center",className:"!text-12/[24px] !text-[var(--text-color-tertiary)] !min-h-24 !select-none",gap:8,children:[(0,l.jsx)(x.A,{spinning:2!==r,indicator:(0,l.jsx)(nO.A,{spin:!0}),size:"small"}),(0,l.jsx)(d.A,{flex:"1 1 auto",children:(0,l.jsx)("label",{children:n})}),(0,l.jsx)("span",{children:t})]})},n2=function(e){var n=e.data;return(0,l.jsx)("div",{className:"w-full",children:(0,l.jsx)(d.A,{gap:12,children:n.map(function(e,t){return"3002"===e.type?(0,l.jsx)(n1,{status:e.status,label:e.name||"",data:e.content},e.name):(0,l.jsx)(nQ,{status:e.status,label:e.name||"",targetProgress:t+1===n.length?90:80,duration:t+1===n.length?15e3:1e4},e.name)})})})},n4=(0,c.memo)(function(e){var n=e.data;if(!n)return null;var t=[{key:"1",className:nJ()["step-progress-panel"],classNames:{header:"border-none py-0",body:"pt-8 pb-0 border-none"},label:(0,l.jsx)(n9,{title:n.title,secTitle:n.secTitle,status:n.status}),children:(0,l.jsx)(n2,{data:n.cardItems})}];return(0,l.jsx)(nY.A,{target:function(){return document.querySelector(".dbei-bubble-list")},children:(0,l.jsx)(n_.A,{expandIconPosition:"end",className:(0,g.cx)(nJ()["step-progress-card"],"!bg-[var(--bg-color-primary)] !py-16 !mb-12 !border-[var(--border-color)]"),items:t,defaultActiveKey:[1],expandIcon:function(e){var n=e.isActive;return(0,l.jsx)(w.A,{type:"icon-DownOutlined",className:"block mt-6 text-16",rotate:n?-180:0})}})})});let n3=function(e){var n=e.contentLoading,t=e.cardTyle,r=e.loading,o=e.title,i=e.data;return r&&!n?(0,l.jsxs)(eb.A,{size:8,align:"center",children:[(0,l.jsx)(R,{}),(0,l.jsx)("span",{className:"text-16 text-[var(--text-color-primary)] fade-in",children:o})]}):"DB-CARD-3"===(void 0===t?"DB-CARD-2":t)?(0,l.jsxs)(l.Fragment,{children:[[0,1].includes((null==i?void 0:i.status)||2)&&(0,l.jsx)(n4,{data:i}),(0,l.jsx)(nW,{records:i})]}):!r&&(0,l.jsx)(nE,{title:o,records:i})},n8=function(e){var n,t=e.data,r=e.onTyping,o=e.status;return(null===(n=t.cardItems)||void 0===n?void 0:n.length)?(0,l.jsx)(nE,{records:t,title:null==t?void 0:t.title,isTourism:!0,onTyping:r,loading:(null==t?void 0:t.status)==="loading"&&"loading"===o}):(0,l.jsxs)(eb.A,{size:8,align:"center",children:[(0,l.jsx)(R,{}),(0,l.jsx)("span",{className:"text-16 text-[var(--text-color-primary)] fade-in",children:"规划旅游行程中..."})]})},n6=function(e){var n=e.status,t=e.onTyping,r=e.deepThinkingLoading,o=e.deepThinkingContent,i=e.networkContent,a=e.tourismContent,s=(0,eu.A)();return o||i||a?(0,l.jsxs)(d.A,{vertical:!0,gap:8,children:[i&&(0,l.jsx)(n3,{contentLoading:r,loading:null==i?void 0:i.loading,cardTyle:null==i?void 0:i.cardType,title:s?(null==i?void 0:i.titleShort)||"":null==i?void 0:i.title,data:null==i?void 0:i.dataSource}),a&&(0,l.jsx)(n8,{data:a,status:n,onTyping:t}),o&&(0,l.jsx)(nC,{status:n,loading:r,onTyping:t,content:o})]}):null},n5=function(e){var n=e.index,t=e.id,r=e.content;return(0,l.jsx)(eR,{index:n,value:t,children:(0,l.jsx)(nb,{content:r},t)})};var n7=t(99625),te=t.n(n7);let tn=function(e){var n=e.totalPages,t=e.initialPage,r=void 0===t?0:t,o=e.onPageChange,i=e.loading,s=(0,a._)((0,c.useState)(r),2),u=s[0],d=s[1],x=(0,y.A)(function(e){return e.recordExportConversationId});return(0,c.useEffect)(function(){r!==u&&d(r)},[r]),(0,l.jsxs)("div",{className:(0,g.cx)(te()["pagination-container"]),children:[(0,l.jsx)(U.Ay,{className:(0,g.cx)(te()["pagination-button"]),onClick:function(){u>0?(d(function(e){return e-1}),null==o||o(u-1)):(d(n-1),null==o||o(n-1))},disabled:i||x,type:"text",icon:(0,l.jsx)(w.A,{type:"icon-LeftOutlined",className:"text-10"})}),(0,l.jsxs)("span",{className:(0,g.cx)(te()["pagination-info"]),children:[u+1," / ",n]}),(0,l.jsx)(U.Ay,{className:(0,g.cx)(te()["pagination-button"]),onClick:function(){u<n-1?(d(function(e){return e+1}),null==o||o(u+1)):(d(0),null==o||o(0))},disabled:i||x,type:"text",icon:(0,l.jsx)(w.A,{type:"icon-RightOutlined",className:"text-10"})})]})},tt=function(e){var n=e.answerList,t=e.current,r=e.id,o=e.conversationId,i=e.loading,a=e.unShowModel,s=(0,f.vS)().aiModelList,u=n[null!=t?t:0].message.model,x=(0,c.useMemo)(function(){return s.find(function(e){return e.value===u})},[s,u]),m=(0,y.A)(function(e){return e.fnMap}),p=(0,c.useMemo)(function(){if(o)return m.get("".concat(o,"_onSwitch"))},[m,o]);return(0,l.jsxs)(d.A,{justify:a?"flex-end":"space-between",align:"center",children:[!a&&(0,l.jsxs)(d.A,{gap:4,className:"text-[var(--text-color-primary)] font-medium",children:[(null==x?void 0:x.icon)&&(0,l.jsx)("img",{src:x.icon,width:20,height:20}),null==x?void 0:x.title]}),n.length>1&&(0,l.jsx)(tn,{initialPage:t,totalPages:n.length,onPageChange:function(e){null==p||p(r,e)},loading:i})]})};var tr=function(e){var n,t,r,o,i=null===(r=e.networkContent)||void 0===r?void 0:null===(t=r.dataSource)||void 0===t?void 0:null===(n=t.cardItems.filter(function(e){return"2002"===e.type||"3002"===e.type||"2003"===e.type}))||void 0===n?void 0:n.map(function(e){return"string"==typeof e.content&&(0,g.jU)(e.content)?JSON.parse(e.content):e.content});return"string"==typeof i&&(0,g.jU)(i)?(i=JSON.parse(i),(null===(o=e.networkContent)||void 0===o?void 0:o.cardType)==="DB-CARD-3")?i.reduce(function(e,n){var t=(null!=n?n:{}).searchList;return(0,T._)(e).concat((0,T._)(null!=t?t:[]))},[]):void 0:i},to=function(e){var n=e.agentInfo,t=e.conversationId,r=e.message.message,a=e.index,s=e.questionIdsMap,c=e.scrollToBottom,u=e.isOwn,x=e.isAnonymous,m=e.isShare,p=e.isSquare,h=e.messageLoading,f=e.isTourism,v=void 0!==f&&f,y=e.isSearch,b=void 0!==y&&y,k=r.answerList,_=r.model,w=r.current,j=r.userAction,A=r.role,I="ai"===A&&k?k[w].message:r,N=I.status,C=I.deepThinking,S=I.msgId,T=I.ctxClearFlag,L=I.chatId,M=I.content,E=I.uuid,D=I.networkContent,O=I.supportDownload,F=I.recommendStatus,z=I.tourismContent,B=I.travelResultReferences,P=tr(I),q="loading"===N&&!(null==C?void 0:C.loading),H={id:"message_".concat(S||E),messageId:S,role:A,className:(0,g.cx)("chat-bubble group/message"),loading:q,loadingRender:function(){return(0,l.jsx)(R,{})},supportDownload:"ai"==A&&O,recommendStatus:"ai"==A&&F,userAction:j,tourismContentLoading:(null==z?void 0:z.status)==="loading"||z};switch("string"==typeof M?H.content=M:H=(0,o._)({},H,M),r.role){case"user":H.messageRender=function(){return(0,l.jsx)(eO,{conversationId:t,messageId:S||(null==s?void 0:s.get(null!=E?E:"")),message:r,chatId:L,loading:h,status:N,index:a,isTourism:v,isSearch:b},r.uuid)};break;case"ai":H.classNames={content:"!w-full group/message"},H.typing=!["success","followupLoading"].includes(N)&&{step:2,interval:10},H.header=(0,l.jsxs)(d.A,{gap:8,vertical:!0,children:[k?(0,l.jsx)(tt,{answerList:k,current:w,id:null!=L?L:"",conversationId:t,loading:"success"!==N,unShowModel:!!n||v||b}):_?(0,l.jsx)(tt,{id:null!=L?L:"",conversationId:t,answerList:[{message:{model:_}}],loading:"success"!==N,unShowModel:!!n||v||b}):null,((null==C?void 0:C.content)||D||z)&&(0,l.jsx)(n6,{networkContent:D,tourismContent:z,deepThinkingLoading:null==C?void 0:C.loading,deepThinkingContent:null==C?void 0:C.content,status:N,onTyping:c})]}),H.messageRender=function(e){return(0,l.jsx)(nk,{id:null!=S?S:"",chatId:L,conversationId:t,index:a,status:N,searchSource:P,ctxClearFlag:T,content:e,scrollToBottom:c},S)},H.footer=(0,l.jsx)(eN,(0,i._)((0,o._)({},I),{conversationId:t,messageLoading:h,isShare:m,isSquare:p,isOwn:void 0!==u&&u,isAnonymous:x,travelResultReferences:B}));break;case"error":H.messageRender=function(e){return(0,l.jsx)(n5,{index:a,id:S,content:e},r.uuid)}}return H},ti=t(54435);function ta(e){var n,t,b=e.messageLoading,A=e.conversationId,I=e.className,N=e.messageList,C=e.isShare,S=void 0!==C&&C,T=e.isRecordExport,L=e.getMessageList,M=e.index,E=e.isOwn,D=e.listRef,O=(0,y.A)(function(e){return e.conversationInfo}),F=(0,y.A)(),z=F.loading,B=F.questionIdsMap,P=(0,c.useContext)(m.t).agentInfo,q=(0,f.vS)(),H=q.isAnonymous,U=q.globalModelConfig,V=q.modelNum,W=(0,y.A)(function(e){return e.objMap}),G=(0,y.A)(function(e){return e.recordExportConversationId}),Y=(0,c.useMemo)(function(){if(G)return W.get(G)},[W,G]),K=(0,h.n)().isMobile,J=(0,v.U)(D),$=(0,ev.useRouter)(),Z=(0,c.useRef)(!1),Q=(n=(0,r._)(function(e){var n;return(0,s.YH)(this,function(t){switch(t.label){case 0:if(es((n=e.target).scrollHeight-Math.abs(n.scrollTop)-n.clientHeight<=50),!(0===n.scrollTop&&!Z.current))return[3,2];return Z.current=!0,[4,null==L?void 0:L()];case 1:t.sent(),Z.current=!1,t.label=2;case 2:return[2]}})}),function(e){return n.apply(this,arguments)}),X=(0,c.useMemo)(function(){return $.pathname.startsWith("/tourism")},[$]),ee=(0,c.useMemo)(function(){return(null==O?void 0:O.conversationType)===4},[O]),en=(0,c.useMemo)(function(){var e;return(null==U?void 0:U.length)?"立刻给 "+(X?"当贝AI旅行规划师":null==U?void 0:null===(e=U[M||0])||void 0===e?void 0:e.title)+" 发消息":""},[U,M]),et=(0,a._)(c.useState(0),2),er=et[0],eo=et[1],ei=(0,a._)((0,c.useState)(!0),2),ea=ei[0],es=ei[1];(0,c.useEffect)(function(){if(null==D?void 0:D.current){var e,n;es((null==D?void 0:null===(e=D.current)||void 0===e?void 0:e.scrollHeight)-Math.abs(null==D?void 0:null===(n=D.current)||void 0===n?void 0:n.scrollTop)-(null==D?void 0:D.current.clientHeight)<=50)}},[null==D?void 0:null===(t=D.current)||void 0===t?void 0:t.clientHeight]),(0,c.useEffect)(function(){D.current&&ea&&D.current.scrollTo({top:D.current.scrollHeight})},[er]);var el=(0,c.useCallback)(function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&es(!0),eo(function(e){return e+1})},[eo,es]),ec=u.A.useWatch(["messages"],Y),eu=(0,c.useMemo)(function(){return(ec||[]).filter(function(e){return e&&e.checked}).map(function(e){return e.id})},[ec]),ed=(0,c.useMemo)(function(){return $.pathname.startsWith("/ask")},[$]),ex=(0,c.useMemo)(function(){return N.reduce(function(e,n,t){return"retry"!==n.message.userAction&&e.push(to({agentInfo:P,conversationId:A,message:n,index:t,questionIdsMap:B,scrollToBottom:el,isOwn:E,isAnonymous:H,isShare:S,isSquare:ed,messageLoading:b,isTourism:X,isSearch:ee})),e},[])},[N,P,A,B,el,E,H,S,ed,b,X,ee]);return 0===ex.length?(0,l.jsx)(d.A,{vertical:!0,justify:P?"start":"center",className:"flex-auto gap-4 scrollbar-none relative",children:z?(0,l.jsx)(x.A,{style:{padding:"15px 0"}}):!H&&P?(0,l.jsx)(p.A,{style:{maxWidth:"880px",margin:"24px auto"},data:P,imgSize:160}):en&&(0,l.jsx)("div",{className:(0,g.cx)(k()["model-box"]),children:(0,l.jsxs)("div",{className:(0,g.cx)(k()["not-box"]),children:[(0,l.jsx)("div",{className:(0,g.cx)(k().title),children:"你好"}),en]})})}):(0,l.jsxs)("div",{ref:D,className:(0,g.cx)(I,"relative w-full !overflow-x-hidden overflow-y-auto ",K||S||ed?"scrollbar-none":J?1!==V?"mb-10":"":1!==V?"mb-10":"pr-6 scrollbar-none",T||S?"":"pt-60"),onScroll:Q,children:[ti.n&&(0,l.jsxs)("div",{className:"absolute top-0 left-0",children:["conversation: ",A]}),0!==ex.length&&(0,l.jsxs)("div",{className:(0,g.cx)("chat-bubble-list !flex-auto !gap-4 min-w-[375px]",T?"selected-mode":"",K||S?"":"pr-10 pl-16"),children:[z&&ex.length>10&&(0,l.jsx)(x.A,{style:{padding:"15px 0",width:"100%"}}),ex.map(function(e){var n;return(null===(n=e.userAction)||void 0===n?void 0:n.includes("retry"))?null:(0,l.jsx)(j.A,(0,i._)((0,o._)({placement:"user"===e.role?"end":"start"},e),{className:(0,g.cx)("chat-bubble group/message",eu.includes(e.messageId)&&"selected mb-10"),loadingRender:function(){return!e.tourismContentLoading&&(0,l.jsx)(R,{})},onTypingComplete:el}),e.uuid)})]}),!ea&&(0,l.jsxs)("div",{className:k()["container-loading-arrow"],onClick:function(){return el(!0)},children:[b&&(0,l.jsx)(_.A,{className:(0,g.cx)(k()["loading-border"]),name:"FloatButtonloading"}),(0,l.jsx)(w.A,{className:"text-primary text-[22px]",type:"icon-DownOutlined"})]})]})}},78182:e=>{e.exports={"chat-page":"AgentChat_chat-page__c8D0b"}},81577:(e,n,t)=>{"use strict";t.d(n,{i:()=>i});var r=t(29935),o=t(82643);function i(){var e;return e=(0,r._)(function(e,n){var t,r,i;return(0,o.YH)(this,function(o){switch(o.label){case 0:if(o.trys.push([0,4,,5]),!navigator.clipboard)return[3,2];return[4,navigator.clipboard.writeText(e)];case 1:return o.sent(),null==n||n({success:!0}),[3,3];case 2:if((t=document.createElement("textarea")).value=e,t.setAttribute("readonly",""),t.style.cssText="position: fixed; top: -9999px; left: -9999px; opacity: 0;",document.body.appendChild(t),t.select(),t.setSelectionRange(0,e.length),document.execCommand("copy"))t.blur(),null==n||n({success:!0});else throw Error("Fallback copy command failed");document.body.contains(t)&&(null===(r=document.body)||void 0===r||r.removeChild(t)),o.label=3;case 3:return[3,5];case 4:return i=o.sent(),null==n||n({success:!1,error:i instanceof Error?i:Error("Unknown error")}),[3,5];case 5:return[2]}})}),function(n,t){return e.apply(this,arguments)}}},82403:e=>{e.exports={"chat-sender-header":"Chat_chat-sender-header__3UkSI","chat-sender-header__header":"Chat_chat-sender-header__header__9by0V","chat-sender-header__content":"Chat_chat-sender-header__content__IuBej","chat-sender-header__attachments":"Chat_chat-sender-header__attachments__60R5M","custom-sender":"Chat_custom-sender__pUQCt","custom-sender-aget":"Chat_custom-sender-aget__hV1y7","sender-custom-item":"Chat_sender-custom-item__EQezI","ai-message-content":"Chat_ai-message-content__e5MCA","suggestion-message":"Chat_suggestion-message__7hrtu","ctx-clear-flag":"Chat_ctx-clear-flag__GC19y","prompt-search-wrap":"Chat_prompt-search-wrap__wg8iK","chat-prompt-search":"Chat_chat-prompt-search__f3sVx",chat_sender:"Chat_chat_sender__hOZaG",chat_sender_content:"Chat_chat_sender_content__whCwy","rotate-border":"Chat_rotate-border__NFdck"}},84765:e=>{e.exports={"md-code-block":"codeBlock_md-code-block__Sbh76","md-code-block--header":"codeBlock_md-code-block--header__P9HHc","md-code-block--header-body":"codeBlock_md-code-block--header-body__Dp4qb","code-block--language":"codeBlock_code-block--language__r78ZB","code-block--actions":"codeBlock_code-block--actions__VjshZ"}},94069:e=>{e.exports={chat_sender:"MultipleModelChat_chat_sender__JvUrO","chat-prompt-search":"MultipleModelChat_chat-prompt-search__apsaG"}},95121:e=>{e.exports={"actionGroup-likeOutlined":"SquarePage_actionGroup-likeOutlined__967cy","share-card":"SquarePage_share-card__JXaSI","like-btn":"SquarePage_like-btn__HfARf","share-btn":"SquarePage_share-btn__WiX45","error-text":"SquarePage_error-text__Ao36y"}},98661:e=>{e.exports={"deep-search-collapse":"DeepSearchCard_deep-search-collapse__0Vr5s","is-active":"DeepSearchCard_is-active__vV_pn","deep-search-collapse--header":"DeepSearchCard_deep-search-collapse--header__Su29p","deep-search-child-collapse":"DeepSearchCard_deep-search-child-collapse__9Dbcm","deep-search-child-collapse--header":"DeepSearchCard_deep-search-child-collapse--header__6o3_2","deep-search-child-collapse--body":"DeepSearchCard_deep-search-child-collapse--body___ozr8","collapse-child-list--meta":"DeepSearchCard_collapse-child-list--meta__QoyOK"}},99625:e=>{e.exports={"pagination-container":"Pagination_pagination-container__dh865","pagination-button":"Pagination_pagination-button__A6j9N","pagination-info":"Pagination_pagination-info__ihu_Z"}}}]);