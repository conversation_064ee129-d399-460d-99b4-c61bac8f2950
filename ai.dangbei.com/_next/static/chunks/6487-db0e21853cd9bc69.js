(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6487],{8727:(e,t,n)=>{"use strict";function r(e){return"[object Object]"===Object.prototype.toString.call(e)}function a(e){var t,n;return!1!==r(e)&&(void 0===(t=e.constructor)||!1!==r(n=t.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf"))}n.d(t,{Q:()=>a})},11338:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(35726),a=n(21462);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var i=n(29236);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,r.A)({},e,{ref:t,icon:o}))})},12819:(e,t)=>{"use strict";for(var n="undefined"!=typeof window&&/Mac|iPod|iPhone|iPad/.test(window.navigator.platform),r={alt:"altKey",control:"ctrlKey",meta:"metaKey",shift:"shiftKey"},a={add:"+",break:"pause",cmd:"meta",command:"meta",ctl:"control",ctrl:"control",del:"delete",down:"arrowdown",esc:"escape",ins:"insert",left:"arrowleft",mod:n?"meta":"control",opt:"alt",option:"alt",return:"enter",right:"arrowright",space:" ",spacebar:" ",up:"arrowup",win:"meta",windows:"meta"},o={backspace:8,tab:9,enter:13,shift:16,control:17,alt:18,pause:19,capslock:20,escape:27," ":32,pageup:33,pagedown:34,end:35,home:36,arrowleft:37,arrowup:38,arrowright:39,arrowdown:40,insert:45,delete:46,meta:91,numlock:144,scrolllock:145,";":186,"=":187,",":188,"-":189,".":190,"/":191,"`":192,"[":219,"\\":220,"]":221,"'":222},i=1;i<20;i++)o["f"+i]=111+i;function l(e,t,n){!t||"byKey"in t||(n=t,t=null),Array.isArray(e)||(e=[e]);var i=e.map(function(e){return function(e,t){var n=t&&t.byKey,i={},l=(e=e.replace("++","+add")).split("+"),c=l.length;for(var d in r)i[r[d]]=!1;var u=!0,f=!1,p=void 0;try{for(var g,v=l[Symbol.iterator]();!(u=(g=v.next()).done);u=!0){var h=g.value,m=h.endsWith("?")&&h.length>1;m&&(h=h.slice(0,-1));var b=s(h),w=r[b];if(h.length>1&&!w&&!a[h]&&!o[b])throw TypeError('Unknown modifier: "'+h+'"');1!==c&&w||(n?i.key=b:i.which=function(e){return o[e=s(e)]||e.toUpperCase().charCodeAt(0)}(h)),w&&(i[w]=!m||null)}}catch(e){f=!0,p=e}finally{try{!u&&v.return&&v.return()}finally{if(f)throw p}}return i}(e,t)}),l=function(e){return i.some(function(t){return function(e,t){for(var n in e){var r=e[n],a=void 0;if(null!=r&&(null!=(a="key"===n&&null!=t.key?t.key.toLowerCase():"which"===n?91===r&&93===t.which?91:t.which:t[n])||!1!==r)&&a!==r)return!1}return!0}(t,e)})};return null==n?l:l(n)}function s(e){return e=a[e=e.toLowerCase()]||e}t.v_=l,t.Sn=function(e,t){return l(e,{byKey:!0},t)}},15212:e=>{"use strict";e.exports=function(e){return(e=String(e||""),r.test(e))?"rtl":a.test(e)?"ltr":"neutral"};var t="֑-߿יִ-﷽ﹰ-ﻼ",n="A-Za-z\xc0-\xd6\xd8-\xf6\xf8-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",r=RegExp("^[^"+n+"]*["+t+"]"),a=RegExp("^[^"+t+"]*["+n+"]")},20078:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(35726),a=n(21462),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},i=n(29236);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,r.A)({},e,{ref:t,icon:o}))})},23348:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(35726),a=n(21462);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"}}]},name:"undo",theme:"outlined"};var i=n(29236);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,r.A)({},e,{ref:t,icon:o}))})},26033:(e,t,n)=>{"use strict";n.d(t,{Fo:()=>nv,rL:()=>tz,A:()=>nk,o$:()=>nA});var r,a,o,i,l=n(15212),s=n.n(l),c=n(60929),d=n.n(c),u=n(99962),f=n.n(u),p=n(21462),g=n(25033),v=n(33665),h=n(12819),m=globalThis.Node;globalThis.Element;var b=globalThis.Text;globalThis.Range,globalThis.Selection,globalThis.StaticRange;var w=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView||null,y=e=>x(e)&&8===e.nodeType,E=e=>x(e)&&1===e.nodeType,x=e=>{var t=w(e);return!!t&&e instanceof t.Node},S=e=>{var t=e&&e.anchorNode&&w(e.anchorNode);return!!t&&e instanceof t.Selection},C=e=>x(e)&&3===e.nodeType,O=e=>e.clipboardData&&""!==e.clipboardData.getData("text/plain")&&1===e.clipboardData.types.length,k=e=>{var[t,n]=e;if(E(t)&&t.childNodes.length){var r=n===t.childNodes.length,a=r?n-1:n;for([t,a]=P(t,a,r?"backward":"forward"),r=a<n;E(t)&&t.childNodes.length;){var o=r?t.childNodes.length-1:0;t=D(t,o,r?"backward":"forward")}n=r&&null!=t.textContent?t.textContent.length:0}return[t,n]},A=e=>{for(var t=e&&e.parentNode;t;){if("[object ShadowRoot]"===t.toString())return!0;t=t.parentNode}return!1},P=(e,t,n)=>{for(var{childNodes:r}=e,a=r[t],o=t,i=!1,l=!1;(y(a)||E(a)&&0===a.childNodes.length||E(a)&&"false"===a.getAttribute("contenteditable"))&&(!i||!l);){if(o>=r.length){i=!0,o=t-1,n="backward";continue}if(o<0){l=!0,o=t+1,n="forward";continue}a=r[o],t=o,o+="forward"===n?1:-1}return[a,t]},D=(e,t,n)=>{var[r]=P(e,t,n);return r},M=e=>{var t="";if(C(e)&&e.nodeValue)return e.nodeValue;if(E(e)){for(var n of Array.from(e.childNodes))t+=M(n);var r=getComputedStyle(e).getPropertyValue("display");("block"===r||"list"===r||"BR"===e.tagName)&&(t+="\n")}return t},T=/data-slate-fragment="(.+?)"/m,B=e=>{var[,t]=e.getData("text/html").match(T)||[];return t},N=e=>null!=e.getSelection?e.getSelection():document.getSelection(),R=(e,t,n)=>{var{target:r}=t;if(E(r)&&r.matches('[contentEditable="false"]'))return!1;var{document:a}=ek.getWindow(e);if(a.contains(r))return ek.hasDOMNode(e,r,{editable:!0});var o=n.find(e=>{var{addedNodes:t,removedNodes:n}=e;for(var a of t)if(a===r||a.contains(r))return!0;for(var o of n)if(o===r||o.contains(r))return!0});return!!o&&o!==t&&R(e,o,n)},z=()=>{for(var e,t,n,r=document.activeElement;null!==(e=r)&&void 0!==e&&e.shadowRoot&&null!==(t=r.shadowRoot)&&void 0!==t&&t.activeElement;)r=null===(n=r)||void 0===n||null===(n=n.shadowRoot)||void 0===n?void 0:n.activeElement;return r},j=(e,t)=>!!(e.compareDocumentPosition(t)&m.DOCUMENT_POSITION_PRECEDING),_=(e,t)=>!!(e.compareDocumentPosition(t)&m.DOCUMENT_POSITION_FOLLOWING),F="undefined"!=typeof navigator&&"undefined"!=typeof window&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,L="undefined"!=typeof navigator&&/Mac OS X/.test(navigator.userAgent),I="undefined"!=typeof navigator&&/Android/.test(navigator.userAgent),K="undefined"!=typeof navigator&&/^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent),W="undefined"!=typeof navigator&&/AppleWebKit(?!.*Chrome)/i.test(navigator.userAgent),H="undefined"!=typeof navigator&&/Edge?\/(?:[0-6][0-9]|[0-7][0-8])(?:\.)/i.test(navigator.userAgent),X="undefined"!=typeof navigator&&/Chrome/i.test(navigator.userAgent),Y="undefined"!=typeof navigator&&/Chrome?\/(?:[0-7][0-5]|[0-6][0-9])(?:\.)/i.test(navigator.userAgent),Q=I&&"undefined"!=typeof navigator&&/Chrome?\/(?:[0-5]?\d)(?:\.)/i.test(navigator.userAgent),q="undefined"!=typeof navigator&&/^(?!.*Seamonkey)(?=.*Firefox\/(?:[0-7][0-9]|[0-8][0-6])(?:\.)).*/i.test(navigator.userAgent),V="undefined"!=typeof navigator&&/.*UCBrowser/.test(navigator.userAgent),U="undefined"!=typeof navigator&&/.*Wechat/.test(navigator.userAgent)&&!/.*MacWechat/.test(navigator.userAgent),G="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;"undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&/Version\/(\d+)/.test(navigator.userAgent)&&null!==(r=navigator.userAgent.match(/Version\/(\d+)/))&&void 0!==r&&r[1]&&parseInt(null===(a=navigator.userAgent.match(/Version\/(\d+)/))||void 0===a?void 0:a[1],10);var Z=(!Y||!Q)&&!H&&"undefined"!=typeof globalThis&&globalThis.InputEvent&&"function"==typeof globalThis.InputEvent.prototype.getTargetRanges;function J(e){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $(e,t,n){var r;return r=function(e,t){if("object"!==J(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==J(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===J(r)?r:String(r))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ee=0;class et{constructor(){$(this,"id",void 0),this.id="".concat(ee++)}}var en=new WeakMap,er=new WeakMap,ea=new WeakMap,eo=new WeakMap,ei=new WeakMap,el=new WeakMap,es=new WeakMap,ec=new WeakMap,ed=new WeakMap,eu=new WeakMap,ef=new WeakMap,ep=new WeakMap,eg=new WeakMap,ev=new WeakMap,eh=new WeakMap,em=new WeakMap,eb=new WeakMap,ew=new WeakMap,ey=new WeakMap,eE=new WeakMap,ex=new WeakMap,eS=new WeakMap,eC=Symbol("placeholder"),eO=Symbol("mark-placeholder"),ek={androidPendingDiffs:e=>ey.get(e),androidScheduleFlush:e=>{var t;null===(t=em.get(e))||void 0===t||t()},blur:e=>{var t=ek.toDOMNode(e,e),n=ek.findDocumentOrShadowRoot(e);ep.set(e,!1),n.activeElement===t&&t.blur()},deselect:e=>{var{selection:t}=e,n=N(ek.findDocumentOrShadowRoot(e));n&&n.rangeCount>0&&n.removeAllRanges(),t&&v.gB.deselect(e)},findDocumentOrShadowRoot:e=>{var t=ek.toDOMNode(e,e),n=t.getRootNode();return n instanceof Document||n instanceof ShadowRoot?n:t.ownerDocument},findEventRange:(e,t)=>{"nativeEvent"in t&&(t=t.nativeEvent);var n,{clientX:r,clientY:a,target:o}=t;if(null==r||null==a)throw Error("Cannot resolve a Slate range from a DOM event: ".concat(t));var i=ek.toSlateNode(e,t.target),l=ek.findPath(e,i);if(v.Hg.isElement(i)&&v.KE.isVoid(e,i)){var s=o.getBoundingClientRect(),c=e.isInline(i)?r-s.left<s.left+s.width-r:a-s.top<s.top+s.height-a,d=v.KE.point(e,l,{edge:c?"start":"end"}),u=c?v.KE.before(e,d):v.KE.after(e,d);if(u)return v.KE.range(e,u)}var{document:f}=ek.getWindow(e);if(f.caretRangeFromPoint)n=f.caretRangeFromPoint(r,a);else{var p=f.caretPositionFromPoint(r,a);p&&((n=f.createRange()).setStart(p.offsetNode,p.offset),n.setEnd(p.offsetNode,p.offset))}if(!n)throw Error("Cannot resolve a Slate range from a DOM event: ".concat(t));return ek.toSlateRange(e,n,{exactMatch:!1,suppressThrow:!1})},findKey:(e,t)=>{var n=ed.get(t);return n||(n=new et,ed.set(t,n)),n},findPath:(e,t)=>{for(var n=[],r=t;;){var a=ea.get(r);if(null==a){if(v.KE.isEditor(r))return n;break}var o=er.get(r);if(null==o)break;n.unshift(o),r=a}throw Error("Unable to find the path for Slate node: ".concat(v.h6.stringify(t)))},focus:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{retries:5};if(!ep.get(e)){if(t.retries<=0)throw Error("Could not set focus, editor seems stuck with pending operations");if(e.operations.length>0){setTimeout(()=>{ek.focus(e,{retries:t.retries-1})},10);return}var n=ek.toDOMNode(e,e),r=ek.findDocumentOrShadowRoot(e);if(r.activeElement!==n){if(e.selection&&r instanceof Document){var a=N(r),o=ek.toDOMRange(e,e.selection);null==a||a.removeAllRanges(),null==a||a.addRange(o)}e.selection||v.gB.select(e,v.KE.start(e,[])),ep.set(e,!0),n.focus({preventScroll:!0})}}},getWindow:e=>{var t=eo.get(e);if(!t)throw Error("Unable to find a host window element for this editor");return t},hasDOMNode:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{editable:a=!1}=r,o=ek.toDOMNode(e,e);try{n=E(t)?t:t.parentElement}catch(e){if(e instanceof Error&&!e.message.includes('Permission denied to access property "nodeType"'))throw e}return!!n&&n.closest("[data-slate-editor]")===o&&(!a||!!n.isContentEditable||"boolean"==typeof n.isContentEditable&&n.closest('[contenteditable="false"]')===o||!!n.getAttribute("data-slate-zero-width"))},hasEditableTarget:(e,t)=>x(t)&&ek.hasDOMNode(e,t,{editable:!0}),hasRange:(e,t)=>{var{anchor:n,focus:r}=t;return v.KE.hasPath(e,n.path)&&v.KE.hasPath(e,r.path)},hasSelectableTarget:(e,t)=>ek.hasEditableTarget(e,t)||ek.isTargetInsideNonReadonlyVoid(e,t),hasTarget:(e,t)=>x(t)&&ek.hasDOMNode(e,t),insertData:(e,t)=>{e.insertData(t)},insertFragmentData:(e,t)=>e.insertFragmentData(t),insertTextData:(e,t)=>e.insertTextData(t),isComposing:e=>!!eg.get(e),isFocused:e=>!!ep.get(e),isReadOnly:e=>!!ef.get(e),isTargetInsideNonReadonlyVoid:(e,t)=>{if(ef.get(e))return!1;var n=ek.hasTarget(e,t)&&ek.toSlateNode(e,t);return v.Hg.isElement(n)&&v.KE.isVoid(e,n)},setFragmentData:(e,t,n)=>e.setFragmentData(t,n),toDOMNode:(e,t)=>{var n=eu.get(e),r=v.KE.isEditor(t)?ei.get(e):null==n?void 0:n.get(ek.findKey(e,t));if(!r)throw Error("Cannot resolve a DOM node from Slate node: ".concat(v.h6.stringify(t)));return r},toDOMPoint:(e,t)=>{var[n]=v.KE.node(e,t.path),r=ek.toDOMNode(e,n);v.KE.void(e,{at:t})&&(t={path:t.path,offset:0});for(var a=Array.from(r.querySelectorAll("[data-slate-string], [data-slate-zero-width]")),o=0,i=0;i<a.length;i++){var l=a[i],s=l.childNodes[0];if(null!=s&&null!=s.textContent){var{length:c}=s.textContent,d=l.getAttribute("data-slate-length"),u=o+(null==d?c:parseInt(d,10)),f=a[i+1];if(t.offset===u&&null!=f&&f.hasAttribute("data-slate-mark-placeholder")){var p,g,h=f.childNodes[0];p=[h instanceof b?h:f,null!==(g=f.textContent)&&void 0!==g&&g.startsWith("\uFEFF")?1:0];break}if(t.offset<=u){p=[s,Math.min(c,Math.max(0,t.offset-o))];break}o=u}}if(!p)throw Error("Cannot resolve a DOM point from Slate point: ".concat(v.h6.stringify(t)));return p},toDOMRange:(e,t)=>{var{anchor:n,focus:r}=t,a=v.Q6.isBackward(t),o=ek.toDOMPoint(e,n),i=v.Q6.isCollapsed(t)?o:ek.toDOMPoint(e,r),l=ek.getWindow(e).document.createRange(),[s,c]=a?i:o,[d,u]=a?o:i,f=!!(E(s)?s:s.parentElement).getAttribute("data-slate-zero-width"),p=!!(E(d)?d:d.parentElement).getAttribute("data-slate-zero-width");return l.setStart(s,f?1:c),l.setEnd(d,p?1:u),l},toSlateNode:(e,t)=>{var n=E(t)?t:t.parentElement;n&&!n.hasAttribute("data-slate-node")&&(n=n.closest("[data-slate-node]"));var r=n?es.get(n):null;if(!r)throw Error("Cannot resolve a Slate node from DOM node: ".concat(n));return r},toSlatePoint:(e,t,n)=>{var{exactMatch:r,suppressThrow:a,searchDirection:o="backward"}=n,[i,l]=r?t:k(t),s=i.parentNode,c=null,d=0;if(s){var u=ek.toDOMNode(e,e),f=s.closest('[data-slate-void="true"]'),p=f&&u.contains(f)?f:null,g=s.closest('[contenteditable="false"]'),h=g&&u.contains(g)?g:null,m=s.closest("[data-slate-leaf]"),b=null;if(m){if(c=m.closest('[data-slate-node="text"]')){var w=ek.getWindow(e).document.createRange();w.setStart(c,0),w.setEnd(i,l);var y=w.cloneContents();[...Array.prototype.slice.call(y.querySelectorAll("[data-slate-zero-width]")),...Array.prototype.slice.call(y.querySelectorAll("[contenteditable=false]"))].forEach(e=>{if(I&&!r&&e.hasAttribute("data-slate-zero-width")&&e.textContent.length>0&&"\uFEFF"!==e.textContext){e.textContent.startsWith("\uFEFF")&&(e.textContent=e.textContent.slice(1));return}e.parentNode.removeChild(e)}),d=y.textContent.length,b=c}}else if(p){for(var E=p.querySelectorAll("[data-slate-leaf]"),x=0;x<E.length;x++){var S=E[x];if(ek.hasDOMNode(e,S)){m=S;break}}m?(c=m.closest('[data-slate-node="text"]'),d=(b=m).textContent.length,b.querySelectorAll("[data-slate-zero-width]").forEach(e=>{d-=e.textContent.length})):d=1}else if(h){var C,O,A,P,D=e=>e?e.querySelectorAll("[data-slate-leaf]:not(:scope [data-slate-editor] [data-slate-leaf])"):[],M=h.closest('[data-slate-node="element"]');(m="forward"===o?null!==(A=[...D(M),...D(null==M?void 0:M.nextElementSibling)].find(e=>_(h,e)))&&void 0!==A?A:null:null!==(P=[...D(null==M?void 0:M.previousElementSibling),...D(M)].findLast(e=>j(h,e)))&&void 0!==P?P:null)&&(c=m.closest('[data-slate-node="text"]'),b=m,"forward"===o?d=0:(d=b.textContent.length,b.querySelectorAll("[data-slate-zero-width]").forEach(e=>{d-=e.textContent.length})))}b&&d===b.textContent.length&&I&&"z"===b.getAttribute("data-slate-zero-width")&&null!==(C=b.textContent)&&void 0!==C&&C.startsWith("\uFEFF")&&(s.hasAttribute("data-slate-zero-width")||K&&null!==(O=b.textContent)&&void 0!==O&&O.endsWith("\n\n"))&&d--}if(I&&!c&&!r){var T=s.hasAttribute("data-slate-node")?s:s.closest("[data-slate-node]");if(T&&ek.hasDOMNode(e,T,{editable:!0})){var B=ek.toSlateNode(e,T),{path:N,offset:R}=v.KE.start(e,ek.findPath(e,B));return T.querySelector("[data-slate-leaf]")||(R=l),{path:N,offset:R}}}if(!c){if(a)return null;throw Error("Cannot resolve a Slate point from DOM point: ".concat(t))}var z=ek.toSlateNode(e,c);return{path:ek.findPath(e,z),offset:d}},toSlateRange:(e,t,n)=>{var r,a,o,i,l,s,{exactMatch:c,suppressThrow:d}=n;if(S(t)?t.anchorNode:t.startContainer){if(S(t)){if(K&&t.rangeCount>1){i=t.focusNode;var u=t.getRangeAt(0),f=t.getRangeAt(t.rangeCount-1);if(i instanceof HTMLTableRowElement&&u.startContainer instanceof HTMLTableRowElement&&f.startContainer instanceof HTMLTableRowElement){function p(e){return e.childElementCount>0?p(e.children[0]):e}var g=u.startContainer,h=f.startContainer,m=p(g.children[u.startOffset]),b=p(h.children[f.startOffset]);l=0,a=b.childNodes.length>0?b.childNodes[0]:b,i=m.childNodes.length>0?m.childNodes[0]:m,o=b instanceof HTMLElement?b.innerHTML.length:0}else u.startContainer===i?(a=f.endContainer,o=f.endOffset,l=u.startOffset):(a=u.startContainer,o=u.endOffset,l=f.startOffset)}else a=t.anchorNode,o=t.anchorOffset,i=t.focusNode,l=t.focusOffset;s=X&&A(a)||K?t.anchorNode===t.focusNode&&t.anchorOffset===t.focusOffset:t.isCollapsed}else a=t.startContainer,o=t.startOffset,i=t.endContainer,l=t.endOffset,s=t.collapsed}if(null==a||null==i||null==o||null==l)throw Error("Cannot resolve a Slate range from DOM range: ".concat(t));K&&null!==(r=i.textContent)&&void 0!==r&&r.endsWith("\n\n")&&l===i.textContent.length&&l--;var w=ek.toSlatePoint(e,[a,o],{exactMatch:c,suppressThrow:d});if(!w)return null;var y=j(a,i)||a===i&&l<o,x=s?w:ek.toSlatePoint(e,[i,l],{exactMatch:c,suppressThrow:d,searchDirection:y?"forward":"backward"});if(!x)return null;var C={anchor:w,focus:x};return v.Q6.isExpanded(C)&&v.Q6.isForward(C)&&E(i)&&v.KE.void(e,{at:C.focus,mode:"highest"})&&(C=v.KE.unhangRange(e,C,{voids:!0})),C}};function eA(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce((e,t)=>e.slice(0,t.start)+t.text+e.slice(t.end),e)}function eP(e,t){var{start:n,end:r,text:a}=t,o=e.slice(n,r),i=function(e,t){for(var n=Math.min(e.length,t.length),r=0;r<n;r++)if(e.charAt(r)!==t.charAt(r))return r;return n}(o,a),l=Math.min(o.length-i,a.length-i),s=function(e,t,n){for(var r=Math.min(e.length,t.length,n),a=0;a<r;a++)if(e.charAt(e.length-a-1)!==t.charAt(t.length-a-1))return a;return r}(o,a,l),c={start:n+i,end:r-s,text:a.slice(i,a.length-s)};return c.start===c.end&&0===c.text.length?null:c}function eD(e,t){var{path:n,offset:r}=t;if(!v.KE.hasPath(e,n))return null;var a=v.bP.get(e,n);if(!v.EY.isText(a))return null;var o=v.KE.above(e,{match:t=>v.Hg.isElement(t)&&v.KE.isBlock(e,t),at:n});if(!o)return null;for(;r>a.text.length;){var i=v.KE.next(e,{at:n,match:v.EY.isText});if(!i||!v.wA.isDescendant(i[1],o[1]))return null;r-=a.text.length,a=i[0],n=i[1]}return{path:n,offset:r}}function eM(e,t){var n=eD(e,t.anchor);if(!n)return null;if(v.Q6.isCollapsed(t))return{anchor:n,focus:n};var r=eD(e,t.focus);return r?{anchor:n,focus:r}:null}function eT(e,t,n){var r=ey.get(e),a=null==r?void 0:r.find(e=>{var{path:n}=e;return v.wA.equals(n,t.path)});if(!a||t.offset<=a.diff.start)return v.bR.transform(t,n,{affinity:"backward"});var{diff:o}=a;if(t.offset<=o.start+o.text.length){var i={path:t.path,offset:o.start},l=v.bR.transform(i,n,{affinity:"backward"});return l?{path:l.path,offset:l.offset+t.offset-o.start}:null}var s={path:t.path,offset:t.offset-o.text.length+o.end-o.start},c=v.bR.transform(s,n,{affinity:"backward"});return c?"split_node"===n.type&&v.wA.equals(n.path,t.path)&&s.offset<n.position&&o.start<n.position?c:{path:c.path,offset:c.offset+o.text.length-o.end+o.start}:null}function eB(e,t,n){var r=eT(e,t.anchor,n);if(!r)return null;if(v.Q6.isCollapsed(t))return{anchor:r,focus:r};var a=eT(e,t.focus,n);return a?{anchor:r,focus:a}:null}var eN=(e,t)=>{var n=(t.top+t.bottom)/2;return e.top<=n&&e.bottom>=n},eR=(e,t,n)=>{var r=ek.toDOMRange(e,t).getBoundingClientRect(),a=ek.toDOMRange(e,n).getBoundingClientRect();return eN(r,a)&&eN(a,r)},ez=(e,t)=>{var n=v.KE.range(e,v.Q6.end(t)),r=Array.from(v.KE.positions(e,{at:t})),a=0,o=r.length,i=Math.floor(o/2);if(eR(e,v.KE.range(e,r[a]),n))return v.KE.range(e,r[a],n);if(r.length<2)return v.KE.range(e,r[r.length-1],n);for(;i!==r.length&&i!==a;)eR(e,v.KE.range(e,r[i]),n)?o=i:a=i,i=Math.floor((a+o)/2);return v.KE.range(e,r[o],n)};function ej(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function e_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ej(Object(n),!0).forEach(function(t){$(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ej(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var eF=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"x-slate-fragment",{apply:n,onChange:r,deleteBackward:a,addMark:o,removeMark:i}=e;return eu.set(e,new WeakMap),e.addMark=(t,n)=>{var r,a;null===(r=em.get(e))||void 0===r||r(),!eb.get(e)&&null!==(a=ey.get(e))&&void 0!==a&&a.length&&eb.set(e,null),ew.delete(e),o(t,n)},e.removeMark=t=>{var n;!eb.get(e)&&null!==(n=ey.get(e))&&void 0!==n&&n.length&&eb.set(e,null),ew.delete(e),i(t)},e.deleteBackward=t=>{if("line"!==t)return a(t);if(e.selection&&v.Q6.isCollapsed(e.selection)){var n=v.KE.above(e,{match:t=>v.Hg.isElement(t)&&v.KE.isBlock(e,t),at:e.selection});if(n){var[,r]=n,o=v.KE.range(e,r,e.selection.anchor),i=ez(e,o);v.Q6.isCollapsed(i)||v.gB.delete(e,{at:i})}}},e.apply=t=>{var r=[],a=[],o=ey.get(e);if(null!=o&&o.length){var i=o.map(e=>(function(e,t){var{path:n,diff:r,id:a}=e;switch(t.type){case"insert_text":if(!v.wA.equals(t.path,n)||t.offset>=r.end)return e;if(t.offset<=r.start)return{diff:{start:t.text.length+r.start,end:t.text.length+r.end,text:r.text},id:a,path:n};return{diff:{start:r.start,end:r.end+t.text.length,text:r.text},id:a,path:n};case"remove_text":if(!v.wA.equals(t.path,n)||t.offset>=r.end)return e;if(t.offset+t.text.length<=r.start)return{diff:{start:r.start-t.text.length,end:r.end-t.text.length,text:r.text},id:a,path:n};return{diff:{start:r.start,end:r.end-t.text.length,text:r.text},id:a,path:n};case"split_node":if(!v.wA.equals(t.path,n)||t.position>=r.end)return{diff:r,id:a,path:v.wA.transform(n,t,{affinity:"backward"})};if(t.position>r.start)return{diff:{start:r.start,end:Math.min(t.position,r.end),text:r.text},id:a,path:n};return{diff:{start:r.start-t.position,end:r.end-t.position,text:r.text},id:a,path:v.wA.transform(n,t,{affinity:"forward"})};case"merge_node":if(!v.wA.equals(t.path,n))return{diff:r,id:a,path:v.wA.transform(n,t)};return{diff:{start:r.start+t.position,end:r.end+t.position,text:r.text},id:a,path:v.wA.transform(n,t)}}var o=v.wA.transform(n,t);return o?{diff:r,path:o,id:a}:null})(e,t)).filter(Boolean);ey.set(e,i)}var l=ex.get(e);l&&ex.set(e,eB(e,l,t));var s=eE.get(e);if(null!=s&&s.at){var c=v.bR.isPoint(null==s?void 0:s.at)?eT(e,s.at,t):eB(e,s.at,t);eE.set(e,c?e_(e_({},s),{},{at:c}):null)}switch(t.type){case"insert_text":case"remove_text":case"set_node":case"split_node":r.push(...eL(e,t.path));break;case"set_selection":null===(d=ev.get(e))||void 0===d||d.unref(),ev.delete(e);break;case"insert_node":case"remove_node":r.push(...eL(e,v.wA.parent(t.path)));break;case"merge_node":r.push(...eL(e,v.wA.previous(t.path)));break;case"move_node":r.push(...eL(e,v.wA.common(v.wA.parent(t.path),v.wA.parent(t.newPath)))),v.wA.isBefore(t.path,t.newPath)?(r.push(...eL(e,v.wA.parent(t.path))),u=t.newPath):(r.push(...eL(e,v.wA.parent(t.newPath))),u=t.path);var d,u,f=v.bP.get(e,v.wA.parent(u)),p=ek.findKey(e,f);a.push([v.KE.pathRef(e,v.wA.parent(u)),p])}switch(n(t),t.type){case"insert_node":case"remove_node":case"merge_node":case"move_node":case"split_node":case"insert_text":case"remove_text":case"set_selection":en.set(e,!0)}for(var[g,h]of r){var[m]=v.KE.node(e,g);ed.set(m,h)}for(var[b,w]of a){if(b.current){var[y]=v.KE.node(e,b.current);ed.set(y,w)}b.unref()}},e.setFragmentData=n=>{var{selection:r}=e;if(r){var[a,o]=v.Q6.edges(r),i=v.KE.void(e,{at:a.path}),l=v.KE.void(e,{at:o.path});if(!v.Q6.isCollapsed(r)||i){var s=ek.toDOMRange(e,r),c=s.cloneContents(),d=c.childNodes[0];if(c.childNodes.forEach(e=>{e.textContent&&""!==e.textContent.trim()&&(d=e)}),l){var[u]=l,f=s.cloneRange(),p=ek.toDOMNode(e,u);f.setEndAfter(p),c=f.cloneContents()}if(i&&(d=c.querySelector("[data-slate-spacer]")),Array.from(c.querySelectorAll("[data-slate-zero-width]")).forEach(e=>{var t="n"===e.getAttribute("data-slate-zero-width");e.textContent=t?"\n":""}),C(d)){var g=d.ownerDocument.createElement("span");g.style.whiteSpace="pre",g.appendChild(d),c.appendChild(g),d=g}var h=JSON.stringify(e.getFragment()),m=window.btoa(encodeURIComponent(h));d.setAttribute("data-slate-fragment",m),n.setData("application/".concat(t),m);var b=c.ownerDocument.createElement("div");return b.appendChild(c),b.setAttribute("hidden","true"),c.ownerDocument.body.appendChild(b),n.setData("text/html",b.innerHTML),n.setData("text/plain",M(b)),c.ownerDocument.body.removeChild(b),n}}},e.insertData=t=>{e.insertFragmentData(t)||e.insertTextData(t)},e.insertFragmentData=n=>{var r=n.getData("application/".concat(t))||B(n);if(r){var a=JSON.parse(decodeURIComponent(window.atob(r)));return e.insertFragment(a),!0}return!1},e.insertTextData=t=>{var n=t.getData("text/plain");if(n){var r=n.split(/\r\n|\r|\n/),a=!1;for(var o of r)a&&v.gB.splitNodes(e,{always:!0}),e.insertText(o),a=!0;return!0}return!1},e.onChange=t=>{var n=eh.get(e);n&&n(t),r(t)},e},eL=(e,t)=>{var n=[];for(var[r,a]of v.KE.levels(e,{at:t})){var o=ek.findKey(e,r);n.push([a,o])}return n},eI={bold:"mod+b",compose:["down","left","right","up","backspace","enter"],moveBackward:"left",moveForward:"right",moveWordBackward:"ctrl+left",moveWordForward:"ctrl+right",deleteBackward:"shift?+backspace",deleteForward:"shift?+delete",extendBackward:"shift+left",extendForward:"shift+right",italic:"mod+i",insertSoftBreak:"shift+enter",splitBlock:"enter",undo:"mod+z"},eK={moveLineBackward:"opt+up",moveLineForward:"opt+down",moveWordBackward:"opt+left",moveWordForward:"opt+right",deleteBackward:["ctrl+backspace","ctrl+h"],deleteForward:["ctrl+delete","ctrl+d"],deleteLineBackward:"cmd+shift?+backspace",deleteLineForward:["cmd+shift?+delete","ctrl+k"],deleteWordBackward:"opt+shift?+backspace",deleteWordForward:"opt+shift?+delete",extendLineBackward:"opt+shift+up",extendLineForward:"opt+shift+down",redo:"cmd+shift+z",transposeCharacter:"ctrl+t"},eW={deleteWordBackward:"ctrl+shift?+backspace",deleteWordForward:"ctrl+shift?+delete",redo:["ctrl+y","ctrl+shift+z"]},eH=e=>{var t=eI[e],n=eK[e],r=eW[e],a=t&&(0,h.v_)(t),o=n&&(0,h.v_)(n),i=r&&(0,h.v_)(r);return e=>!!(a&&a(e)||L&&o&&o(e)||!L&&i&&i(e))},eX={isBold:eH("bold"),isCompose:eH("compose"),isMoveBackward:eH("moveBackward"),isMoveForward:eH("moveForward"),isDeleteBackward:eH("deleteBackward"),isDeleteForward:eH("deleteForward"),isDeleteLineBackward:eH("deleteLineBackward"),isDeleteLineForward:eH("deleteLineForward"),isDeleteWordBackward:eH("deleteWordBackward"),isDeleteWordForward:eH("deleteWordForward"),isExtendBackward:eH("extendBackward"),isExtendForward:eH("extendForward"),isExtendLineBackward:eH("extendLineBackward"),isExtendLineForward:eH("extendLineForward"),isItalic:eH("italic"),isMoveLineBackward:eH("moveLineBackward"),isMoveLineForward:eH("moveLineForward"),isMoveWordBackward:eH("moveWordBackward"),isMoveWordForward:eH("moveWordForward"),isRedo:eH("redo"),isSoftBreak:eH("insertSoftBreak"),isSplitBlock:eH("splitBlock"),isTransposeCharacter:eH("transposeCharacter"),isUndo:eH("undo")};function eY(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var eQ=["anchor","focus"],eq=["anchor","focus"],eV=(e,t)=>Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(n=>t.hasOwnProperty(n)&&e[n]===t[n]),eU=(e,t)=>{var n=eY(e,eQ),r=eY(t,eq);return e[eC]===t[eC]&&eV(n,r)},eG=(e,t)=>{if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var r=e[n],a=t[n];if(!v.Q6.equals(r,a)||!eU(r,a))return!1}return!0},eZ=(e,t)=>{if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var r=e[n],a=t[n];if(r.anchor.offset!==a.anchor.offset||r.focus.offset!==a.focus.offset||!eU(r,a))return!1}return!0},eJ=[],e$="ResizeObserver loop completed with undelivered notifications.",e0=function(){var e;"function"==typeof ErrorEvent?e=new ErrorEvent("error",{message:e$}):((e=document.createEvent("Event")).initEvent("error",!1,!1),e.message=e$),window.dispatchEvent(e)};!function(e){e.BORDER_BOX="border-box",e.CONTENT_BOX="content-box",e.DEVICE_PIXEL_CONTENT_BOX="device-pixel-content-box"}(o||(o={}));var e1=function(e){return Object.freeze(e)},e2=function(e,t){this.inlineSize=e,this.blockSize=t,e1(this)},e4=function(){function e(e,t,n,r){return this.x=e,this.y=t,this.width=n,this.height=r,this.top=this.y,this.left=this.x,this.bottom=this.top+this.height,this.right=this.left+this.width,e1(this)}return e.prototype.toJSON=function(){return{x:this.x,y:this.y,top:this.top,right:this.right,bottom:this.bottom,left:this.left,width:this.width,height:this.height}},e.fromRect=function(t){return new e(t.x,t.y,t.width,t.height)},e}(),e3=function(e){return e instanceof SVGElement&&"getBBox"in e},e6=function(e){if(e3(e)){var t=e.getBBox(),n=t.width,r=t.height;return!n&&!r}var a=e.offsetWidth,o=e.offsetHeight;return!(a||o||e.getClientRects().length)},e8=function(e){if(e instanceof Element)return!0;var t,n=null===(t=null==e?void 0:e.ownerDocument)||void 0===t?void 0:t.defaultView;return!!(n&&e instanceof n.Element)},e5=function(e){switch(e.tagName){case"INPUT":if("image"!==e.type)break;case"VIDEO":case"AUDIO":case"EMBED":case"OBJECT":case"CANVAS":case"IFRAME":case"IMG":return!0}return!1},e9="undefined"!=typeof window?window:{},e7=new WeakMap,te=/auto|scroll/,tt=/^tb|vertical/,tn=/msie|trident/i.test(e9.navigator&&e9.navigator.userAgent),tr=function(e){return parseFloat(e||"0")},ta=function(e,t,n){return void 0===e&&(e=0),void 0===t&&(t=0),void 0===n&&(n=!1),new e2((n?t:e)||0,(n?e:t)||0)},to=e1({devicePixelContentBoxSize:ta(),borderBoxSize:ta(),contentBoxSize:ta(),contentRect:new e4(0,0,0,0)}),ti=function(e,t){if(void 0===t&&(t=!1),e7.has(e)&&!t)return e7.get(e);if(e6(e))return e7.set(e,to),to;var n=getComputedStyle(e),r=e3(e)&&e.ownerSVGElement&&e.getBBox(),a=!tn&&"border-box"===n.boxSizing,o=tt.test(n.writingMode||""),i=!r&&te.test(n.overflowY||""),l=!r&&te.test(n.overflowX||""),s=r?0:tr(n.paddingTop),c=r?0:tr(n.paddingRight),d=r?0:tr(n.paddingBottom),u=r?0:tr(n.paddingLeft),f=r?0:tr(n.borderTopWidth),p=r?0:tr(n.borderRightWidth),g=r?0:tr(n.borderBottomWidth),v=r?0:tr(n.borderLeftWidth),h=u+c,m=s+d,b=v+p,w=f+g,y=l?e.offsetHeight-w-e.clientHeight:0,E=i?e.offsetWidth-b-e.clientWidth:0,x=r?r.width:tr(n.width)-(a?h+b:0)-E,S=r?r.height:tr(n.height)-(a?m+w:0)-y,C=x+h+E+b,O=S+m+y+w,k=e1({devicePixelContentBoxSize:ta(Math.round(x*devicePixelRatio),Math.round(S*devicePixelRatio),o),borderBoxSize:ta(C,O,o),contentBoxSize:ta(x,S,o),contentRect:new e4(u,s,x,S)});return e7.set(e,k),k},tl=function(e,t,n){var r=ti(e,n),a=r.borderBoxSize,i=r.contentBoxSize,l=r.devicePixelContentBoxSize;switch(t){case o.DEVICE_PIXEL_CONTENT_BOX:return l;case o.BORDER_BOX:return a;default:return i}},ts=function(e){var t=ti(e);this.target=e,this.contentRect=t.contentRect,this.borderBoxSize=e1([t.borderBoxSize]),this.contentBoxSize=e1([t.contentBoxSize]),this.devicePixelContentBoxSize=e1([t.devicePixelContentBoxSize])},tc=function(e){if(e6(e))return 1/0;for(var t=0,n=e.parentNode;n;)t+=1,n=n.parentNode;return t},td=function(){var e=1/0,t=[];eJ.forEach(function(n){if(0!==n.activeTargets.length){var r=[];n.activeTargets.forEach(function(t){var n=new ts(t.target),a=tc(t.target);r.push(n),t.lastReportedSize=tl(t.target,t.observedBox),a<e&&(e=a)}),t.push(function(){n.callback.call(n.observer,r,n.observer)}),n.activeTargets.splice(0,n.activeTargets.length)}});for(var n=0;n<t.length;n++)(0,t[n])();return e},tu=function(e){eJ.forEach(function(t){t.activeTargets.splice(0,t.activeTargets.length),t.skippedTargets.splice(0,t.skippedTargets.length),t.observationTargets.forEach(function(n){n.isActive()&&(tc(n.target)>e?t.activeTargets.push(n):t.skippedTargets.push(n))})})},tf=function(){var e=0;for(tu(0);eJ.some(function(e){return e.activeTargets.length>0});)tu(e=td());return eJ.some(function(e){return e.skippedTargets.length>0})&&e0(),e>0},tp=[],tg=function(e){if(!i){var t=0,n=document.createTextNode("");new MutationObserver(function(){return tp.splice(0).forEach(function(e){return e()})}).observe(n,{characterData:!0}),i=function(){n.textContent="".concat(t?t--:t++)}}tp.push(e),i()},tv=function(e){tg(function(){requestAnimationFrame(e)})},th=0,tm={attributes:!0,characterData:!0,childList:!0,subtree:!0},tb=["resize","load","transitionend","animationend","animationstart","animationiteration","keyup","keydown","mouseup","mousedown","mouseover","mouseout","blur","focus"],tw=function(e){return void 0===e&&(e=0),Date.now()+e},ty=!1,tE=new(function(){function e(){var e=this;this.stopped=!0,this.listener=function(){return e.schedule()}}return e.prototype.run=function(e){var t=this;if(void 0===e&&(e=250),!ty){ty=!0;var n=tw(e);tv(function(){var r=!1;try{r=tf()}finally{if(ty=!1,e=n-tw(),!th)return;r?t.run(1e3):e>0?t.run(e):t.start()}})}},e.prototype.schedule=function(){this.stop(),this.run()},e.prototype.observe=function(){var e=this,t=function(){return e.observer&&e.observer.observe(document.body,tm)};document.body?t():e9.addEventListener("DOMContentLoaded",t)},e.prototype.start=function(){var e=this;this.stopped&&(this.stopped=!1,this.observer=new MutationObserver(this.listener),this.observe(),tb.forEach(function(t){return e9.addEventListener(t,e.listener,!0)}))},e.prototype.stop=function(){var e=this;this.stopped||(this.observer&&this.observer.disconnect(),tb.forEach(function(t){return e9.removeEventListener(t,e.listener,!0)}),this.stopped=!0)},e}()),tx=function(e){!th&&e>0&&tE.start(),(th+=e)||tE.stop()},tS=function(){function e(e,t){this.target=e,this.observedBox=t||o.CONTENT_BOX,this.lastReportedSize={inlineSize:0,blockSize:0}}return e.prototype.isActive=function(){var e,t=tl(this.target,this.observedBox,!0);return!e3(e=this.target)&&!e5(e)&&"inline"===getComputedStyle(e).display&&(this.lastReportedSize=t),this.lastReportedSize.inlineSize!==t.inlineSize||this.lastReportedSize.blockSize!==t.blockSize},e}(),tC=function(e,t){this.activeTargets=[],this.skippedTargets=[],this.observationTargets=[],this.observer=e,this.callback=t},tO=new WeakMap,tk=function(e,t){for(var n=0;n<e.length;n+=1)if(e[n].target===t)return n;return -1},tA=function(){function e(){}return e.connect=function(e,t){var n=new tC(e,t);tO.set(e,n)},e.observe=function(e,t,n){var r=tO.get(e),a=0===r.observationTargets.length;0>tk(r.observationTargets,t)&&(a&&eJ.push(r),r.observationTargets.push(new tS(t,n&&n.box)),tx(1),tE.schedule())},e.unobserve=function(e,t){var n=tO.get(e),r=tk(n.observationTargets,t),a=1===n.observationTargets.length;r>=0&&(a&&eJ.splice(eJ.indexOf(n),1),n.observationTargets.splice(r,1),tx(-1))},e.disconnect=function(e){var t=this,n=tO.get(e);n.observationTargets.slice().forEach(function(n){return t.unobserve(e,n.target)}),n.activeTargets.splice(0,n.activeTargets.length)},e}(),tP=function(){function e(e){if(0==arguments.length)throw TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");if("function"!=typeof e)throw TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");tA.connect(this,e)}return e.prototype.observe=function(e,t){if(0==arguments.length)throw TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!e8(e))throw TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");tA.observe(this,e,t)},e.prototype.unobserve=function(e){if(0==arguments.length)throw TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!e8(e))throw TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");tA.unobserve(this,e)},e.prototype.disconnect=function(){tA.disconnect(this)},e.toString=function(){return"function ResizeObserver () { [polyfill code] }"},e}(),tD=n(47993);function tM(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function tT(e){return(tT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tB(e,t,n){var r;return r=function(e,t){if("object"!==tT(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==tT(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===tT(r)?r:String(r))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}console.log("");var tN=(0,p.createContext)(null),tR=()=>{var e=(0,p.useContext)(tN);if(!e)throw Error("The `useSlateStatic` hook must be used inside the <Slate> component's context.");return e},tz=ek;function tj(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function t_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tj(Object(n),!0).forEach(function(t){tB(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tj(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var tF=function(){},tL=e=>(null==e?void 0:e.constructor.name)==="DataTransfer",tI=G?p.useLayoutEffect:p.useEffect,tK=["node"];function tW(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}var tH={subtree:!0,childList:!0,characterData:!0},tX=I?e=>{var{node:t}=e,n=tM(e,tK);if(!I)return null;var r=tR(),a=function(){var e=(0,p.useRef)(!1);return(0,p.useEffect)(()=>(e.current=!0,()=>{e.current=!1}),[]),e.current}(),[o]=(0,p.useState)(()=>(function(e){var{editor:t,scheduleOnDOMSelectionChange:n,onDOMSelectionChange:r}=e,a=!1,o=null,i=null,l=null,s=0,c=!1,d=()=>{var e=ex.get(t);if(ex.delete(t),e){var{selection:n}=t,r=eM(t,e);!r||n&&v.Q6.equals(r,n)||v.gB.select(t,r)}},u=()=>{var e=eE.get(t);if(eE.delete(t),e){if(e.at){var n=v.bR.isPoint(e.at)?eD(t,e.at):eM(t,e.at);if(!n)return;var r=v.KE.range(t,n);t.selection&&v.Q6.equals(t.selection,r)||v.gB.select(t,n)}e.run()}},f=()=>{if(i&&(clearTimeout(i),i=null),l&&(clearTimeout(l),l=null),!b()&&!m()){d();return}a||(a=!0,setTimeout(()=>a=!1)),m()&&(a="action");var e=t.selection&&v.KE.rangeRef(t,t.selection,{affinity:"forward"});ew.set(t,t.marks),tF("flush",eE.get(t),ey.get(t));for(var o=b();s=null===(f=ey.get(t))||void 0===f?void 0:f[0];){var s,f,p,g=eb.get(t);void 0!==g&&(eb.delete(t),t.marks=g),g&&!1===c&&(c=null);var h=function(e){var{path:t,diff:n}=e;return{anchor:{path:t,offset:n.start},focus:{path:t,offset:n.end}}}(s);t.selection&&v.Q6.equals(t.selection,h)||v.gB.select(t,h),s.diff.text?v.KE.insertText(t,s.diff.text):v.KE.deleteFragment(t),ey.set(t,null===(p=ey.get(t))||void 0===p?void 0:p.filter(e=>{var{id:t}=e;return t!==s.id})),!function(e,t){var{path:n,diff:r}=t;if(!v.KE.hasPath(e,n))return!1;var a=v.bP.get(e,n);if(!v.EY.isText(a))return!1;if(r.start!==a.text.length||0===r.text.length)return a.text.slice(r.start,r.start+r.text.length)===r.text;var o=v.wA.next(n);if(!v.KE.hasPath(e,o))return!1;var i=v.bP.get(e,o);return v.EY.isText(i)&&i.text.startsWith(r.text)}(t,s)&&(o=!1,eE.delete(t),ew.delete(t),a="action",ex.delete(t),n.cancel(),r.cancel(),null==e||e.unref())}var w=null==e?void 0:e.unref();if(!w||ex.get(t)||t.selection&&v.Q6.equals(w,t.selection)||v.gB.select(t,w),m()){u();return}o&&n(),n.flush(),r.flush(),d();var y=ew.get(t);ew.delete(t),void 0!==y&&(t.marks=y,t.onChange())},p=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=el.get(t);if(n){if(b()||e){n.style.display="none";return}n.style.removeProperty("display")}},g=(e,n)=>{var r,a,o,i,l,c,d,u,f=null!==(u=ey.get(t))&&void 0!==u?u:[];ey.set(t,f);var g=v.bP.leaf(t,e),h=f.findIndex(t=>v.wA.equals(t.path,e));if(h<0){eP(g.text,n)&&f.push({path:e,diff:n,id:s++}),p();return}var m=(r=g.text,o=Math.min((a=f[h].diff).start,n.start),i=Math.max(0,Math.min(a.start+a.text.length,n.end)-n.start),l=eA(r,a,n),c=Math.max(n.start+n.text.length,a.start+a.text.length+(a.start+a.text.length>n.start?n.text.length:0)-i),d=l.slice(o,c),eP(r,{start:o,end:Math.max(a.end,n.end-a.text.length+(a.end-a.start)),text:d}));if(!m){f.splice(h,1),p();return}f[h]=t_(t_({},f[h]),{},{diff:m})},h=function(e){var{at:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};c=!1,ex.delete(t),n.cancel(),r.cancel(),m()&&f(),eE.set(t,{at:a,run:e}),l=setTimeout(f)},m=()=>!!eE.get(t),b=()=>{var e;return!!(null!==(e=ey.get(t))&&void 0!==e&&e.length)},w=e=>{ex.set(t,e),i&&(clearTimeout(i),i=null);var{selection:n}=t;if(e){var r=!n||!v.wA.equals(n.anchor.path,e.anchor.path),a=!n||!v.wA.equals(n.anchor.path.slice(0,-1),e.anchor.path.slice(0,-1));(r&&c||a)&&(c=!1),(r||b())&&(i=setTimeout(f,200))}},y=()=>{m()||(l=setTimeout(f))};return{flush:f,scheduleFlush:y,hasPendingDiffs:b,hasPendingAction:m,hasPendingChanges:()=>m()||b(),isFlushing:()=>a,handleUserSelect:w,handleCompositionEnd:e=>{o&&clearTimeout(o),o=setTimeout(()=>{eg.set(t,!1),f()},25)},handleCompositionStart:e=>{eg.set(t,!0),o&&(clearTimeout(o),o=null)},handleDOMBeforeInput:e=>{if(i&&(clearTimeout(i),i=null),!en.get(t)){var{inputType:n}=e,r=null,a=e.dataTransfer||e.data||void 0;!1!==c&&"insertText"!==n&&"insertCompositionText"!==n&&(c=!1);var[o]=e.getTargetRanges();o&&(r=tz.toSlateRange(t,o,{exactMatch:!1,suppressThrow:!0}));var l=tz.getWindow(t).getSelection();if(!r&&l&&(o=l,r=tz.toSlateRange(t,l,{exactMatch:!1,suppressThrow:!0})),r=null!==(T=r)&&void 0!==T?T:t.selection){var s=!0;if(n.startsWith("delete")){if(v.Q6.isExpanded(r)){var[d,u]=v.Q6.edges(r);if(v.bP.leaf(t,d.path).text.length===d.offset&&0===u.offset){var f=v.KE.next(t,{at:d.path,match:v.EY.isText});f&&v.wA.equals(f[1],u.path)&&(r={anchor:u,focus:u})}}var p=n.endsWith("Backward")?"backward":"forward",[m,b]=v.Q6.edges(r),[E,x]=v.KE.leaf(t,m.path),C={text:"",start:m.offset,end:b.offset},O=ey.get(t),k=null==O?void 0:O.find(e=>v.wA.equals(e.path,x)),A=k?[k.diff,C]:[C];if(0===eA(E.text,...A).length&&(s=!1),v.Q6.isExpanded(r)){if(s&&v.wA.equals(r.anchor.path,r.focus.path)){var P={path:r.anchor.path,offset:m.offset};return w(v.KE.range(t,P,P)),g(r.anchor.path,{text:"",end:b.offset,start:m.offset})}return h(()=>v.KE.deleteFragment(t,{direction:p}),{at:r})}}switch(n){case"deleteByComposition":case"deleteByCut":case"deleteByDrag":return h(()=>v.KE.deleteFragment(t),{at:r});case"deleteContent":case"deleteContentForward":var{anchor:D}=r;if(s&&v.Q6.isCollapsed(r)){var M=v.bP.leaf(t,D.path);if(D.offset<M.text.length)return g(D.path,{text:"",start:D.offset,end:D.offset+1})}return h(()=>v.KE.deleteForward(t),{at:r});case"deleteContentBackward":var T,B,{anchor:N}=r,R=S(o)?o.isCollapsed:!!(null!==(B=o)&&void 0!==B&&B.collapsed);if(s&&R&&v.Q6.isCollapsed(r)&&N.offset>0)return g(N.path,{text:"",start:N.offset-1,end:N.offset});return h(()=>v.KE.deleteBackward(t),{at:r});case"deleteEntireSoftLine":return h(()=>{v.KE.deleteBackward(t,{unit:"line"}),v.KE.deleteForward(t,{unit:"line"})},{at:r});case"deleteHardLineBackward":return h(()=>v.KE.deleteBackward(t,{unit:"block"}),{at:r});case"deleteSoftLineBackward":return h(()=>v.KE.deleteBackward(t,{unit:"line"}),{at:r});case"deleteHardLineForward":return h(()=>v.KE.deleteForward(t,{unit:"block"}),{at:r});case"deleteSoftLineForward":return h(()=>v.KE.deleteForward(t,{unit:"line"}),{at:r});case"deleteWordBackward":return h(()=>v.KE.deleteBackward(t,{unit:"word"}),{at:r});case"deleteWordForward":return h(()=>v.KE.deleteForward(t,{unit:"word"}),{at:r});case"insertLineBreak":return h(()=>v.KE.insertSoftBreak(t),{at:r});case"insertParagraph":return h(()=>v.KE.insertBreak(t),{at:r});case"insertCompositionText":case"deleteCompositionText":case"insertFromComposition":case"insertFromDrop":case"insertFromPaste":case"insertFromYank":case"insertReplacementText":case"insertText":if(tL(a))return h(()=>tz.insertData(t,a),{at:r});var z=null!=a?a:"";if(eb.get(t)&&(z=z.replace("\uFEFF","")),"insertText"===n&&/.*\n.*\n$/.test(z)&&(z=z.slice(0,-1)),z.includes("\n"))return h(()=>{var e=z.split("\n");e.forEach((n,r)=>{n&&v.KE.insertText(t,n),r!==e.length-1&&v.KE.insertSoftBreak(t)})},{at:r});if(v.wA.equals(r.anchor.path,r.focus.path)){var[j,_]=v.Q6.edges(r),F={start:j.offset,end:_.offset,text:z};if(z&&c&&"insertCompositionText"===n){var L=c.start+c.text.search(/\S|$/);F.start+F.text.search(/\S|$/)===L+1&&F.end===c.start+c.text.length?(F.start-=1,c=null,y()):c=!1}else c="insertText"===n&&(null===c?F:!!(c&&v.Q6.isCollapsed(r))&&c.end+c.text.length===j.offset&&t_(t_({},c),{},{text:c.text+z}));if(s){g(j.path,F);return}}return h(()=>v.KE.insertText(t,z),{at:r})}}}},handleKeyDown:e=>{b()||(p(!0),setTimeout(p))},handleDomMutations:e=>{if(!(b()||m())&&e.some(n=>R(t,n,e))){var n;null===(n=eS.get(t))||void 0===n||n()}},handleInput:()=>{(m()||!b())&&f()}}})(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tW(Object(n),!0).forEach(function(t){tB(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tW(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({editor:r},n)));return!function(e,t,n){var[r]=(0,p.useState)(()=>new MutationObserver(t));tI(()=>{r.takeRecords()}),(0,p.useEffect)(()=>{if(!e.current)throw Error("Failed to attach MutationObserver, `node` is undefined");return r.observe(e.current,n),()=>r.disconnect()},[r,e,n])}(t,o.handleDomMutations,tH),em.set(r,o.scheduleFlush),a&&o.flush(),o}:()=>null;function tY(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}var tQ=e=>{var{isLast:t,leaf:n,parent:r,text:a}=e,o=tR(),i=tz.findPath(o,a),l=v.wA.parent(i),s=!!n[eO];return o.isVoid(r)?p.createElement(tU,{length:v.bP.string(r).length}):""!==n.text||r.children[r.children.length-1]!==a||o.isInline(r)||""!==v.KE.string(o,l)?""===n.text?p.createElement(tU,{isMarkPlaceholder:s}):t&&"\n"===n.text.slice(-1)?p.createElement(tq,{isTrailing:!0,text:n.text}):p.createElement(tq,{text:n.text}):p.createElement(tU,{isLineBreak:!0,isMarkPlaceholder:s})},tq=e=>{var{text:t,isTrailing:n=!1}=e,r=(0,p.useRef)(null),a=()=>"".concat(null!=t?t:"").concat(n?"\n":""),[o]=(0,p.useState)(a);return tI(()=>{var e=a();r.current&&r.current.textContent!==e&&(r.current.textContent=e)}),p.createElement(tV,{ref:r},o)},tV=(0,p.memo)((0,p.forwardRef)((e,t)=>p.createElement("span",{"data-slate-string":!0,ref:t},e.children))),tU=e=>{var{length:t=0,isLineBreak:n=!1,isMarkPlaceholder:r=!1}=e,a={"data-slate-zero-width":n?"n":"z","data-slate-length":t};return r&&(a["data-slate-mark-placeholder"]=!0),p.createElement("span",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tY(Object(n),!0).forEach(function(t){tB(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tY(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({},a),"\uFEFF",n?p.createElement("br",null):null)};function tG(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function tZ(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tG(Object(n),!0).forEach(function(t){tB(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tG(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var tJ=300*!!I;function t$(e){e.current&&(clearTimeout(e.current),e.current=null)}var t0=p.memo(e=>{var{leaf:t,isLast:n,text:r,parent:a,renderPlaceholder:o,renderLeaf:i=e=>p.createElement(t1,tZ({},e))}=e,l=tR(),s=(0,p.useRef)(null),c=(0,p.useRef)(null),[d,u]=(0,p.useState)(!1),f=(0,p.useRef)(null),g=(0,p.useCallback)(e=>{if(!function(e,t){e.current&&(e.current.disconnect(),t&&(e.current=null))}(s,null==e),null==e){var n;el.delete(l),null===(n=t.onPlaceholderResize)||void 0===n||n.call(t,null)}else el.set(l,e),s.current||(s.current=new(window.ResizeObserver||tP)(()=>{var n;null===(n=t.onPlaceholderResize)||void 0===n||n.call(t,e)})),s.current.observe(e),c.current=e},[c,t,l]),v=p.createElement(tQ,{isLast:n,leaf:t,parent:a,text:r}),h=!!t[eC];if((0,p.useEffect)(()=>(h?f.current||(f.current=setTimeout(()=>{u(!0),f.current=null},tJ)):(t$(f),u(!1)),()=>t$(f)),[h,u]),h&&d){var m={children:t.placeholder,attributes:{"data-slate-placeholder":!0,style:{position:"absolute",top:0,pointerEvents:"none",width:"100%",maxWidth:"100%",display:"block",opacity:"0.333",userSelect:"none",textDecoration:"none",WebkitUserModify:W?"inherit":void 0},contentEditable:!1,ref:g}};v=p.createElement(p.Fragment,null,o(m),v)}return i({attributes:{"data-slate-leaf":!0},children:v,leaf:t,text:r})},(e,t)=>t.parent===e.parent&&t.isLast===e.isLast&&t.renderLeaf===e.renderLeaf&&t.renderPlaceholder===e.renderPlaceholder&&t.text===e.text&&v.EY.equals(t.leaf,e.leaf)&&t.leaf[eC]===e.leaf[eC]),t1=e=>{var{attributes:t,children:n}=e;return p.createElement("span",tZ({},t),n)},t2=p.memo(e=>{for(var{decorations:t,isLast:n,parent:r,renderPlaceholder:a,renderLeaf:o,text:i}=e,l=tR(),s=(0,p.useRef)(null),c=v.EY.decorations(i,t),d=tz.findKey(l,i),u=[],f=0;f<c.length;f++){var g=c[f];u.push(p.createElement(t0,{isLast:n&&f===c.length-1,key:"".concat(d.id,"-").concat(f),renderPlaceholder:a,leaf:g,text:i,parent:r,renderLeaf:o}))}var h=(0,p.useCallback)(e=>{var t=eu.get(l);e?(null==t||t.set(d,e),ec.set(i,e),es.set(e,i)):(null==t||t.delete(d),ec.delete(i),s.current&&es.delete(s.current)),s.current=e},[s,l,d,i]);return p.createElement("span",{"data-slate-node":"text",ref:h},u)},(e,t)=>t.parent===e.parent&&t.isLast===e.isLast&&t.renderLeaf===e.renderLeaf&&t.renderPlaceholder===e.renderPlaceholder&&t.text===e.text&&eZ(t.decorations,e.decorations));function t4(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function t3(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?t4(Object(n),!0).forEach(function(t){tB(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t4(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var t6=p.memo(e=>{var{decorations:t,element:n,renderElement:r=e=>p.createElement(t8,t3({},e)),renderPlaceholder:a,renderLeaf:o,selection:i}=e,l=tR(),c=nn(),d=l.isInline(n),u=tz.findKey(l,n),f=(0,p.useCallback)(e=>{var t=eu.get(l);e?(null==t||t.set(u,e),ec.set(n,e),es.set(e,n)):(null==t||t.delete(u),ec.delete(n))},[l,u,n]),g=ne({decorations:t,node:n,renderElement:r,renderPlaceholder:a,renderLeaf:o,selection:i}),h={"data-slate-node":"element",ref:f};if(d&&(h["data-slate-inline"]=!0),!d&&v.KE.hasInlines(l,n)){var m=v.bP.string(n),b=s()(m);"rtl"===b&&(h.dir=b)}if(v.KE.isVoid(l,n)){h["data-slate-void"]=!0,!c&&d&&(h.contentEditable=!1);var[[w]]=v.bP.texts(n);g=p.createElement(d?"span":"div",{"data-slate-spacer":!0,style:{height:"0",color:"transparent",outline:"none",position:"absolute"}},p.createElement(t2,{renderPlaceholder:a,decorations:[],isLast:!1,parent:n,text:w})),er.set(w,0),ea.set(w,n)}return r({attributes:h,children:g,element:n})},(e,t)=>e.element===t.element&&e.renderElement===t.renderElement&&e.renderLeaf===t.renderLeaf&&e.renderPlaceholder===t.renderPlaceholder&&eG(e.decorations,t.decorations)&&(e.selection===t.selection||!!e.selection&&!!t.selection&&v.Q6.equals(e.selection,t.selection))),t8=e=>{var{attributes:t,children:n,element:r}=e,a=tR().isInline(r)?"span":"div";return p.createElement(a,t3(t3({},t),{},{style:{position:"relative"}}),n)},t5=(0,p.createContext)(()=>[]),t9=()=>(0,p.useContext)(t5),t7=(0,p.createContext)(!1),ne=e=>{var{decorations:t,node:n,renderElement:r,renderPlaceholder:a,renderLeaf:o,selection:i}=e,l=t9(),s=tR();en.set(s,!1);for(var c=tz.findPath(s,n),d=[],u=v.Hg.isElement(n)&&!s.isInline(n)&&v.KE.hasInlines(s,n),f=0;f<n.children.length;f++){var g=c.concat(f),h=n.children[f],m=tz.findKey(s,h),b=v.KE.range(s,g),w=i&&v.Q6.intersection(b,i),y=l([h,g]);for(var E of t){var x=v.Q6.intersection(E,b);x&&y.push(x)}v.Hg.isElement(h)?d.push(p.createElement(t7.Provider,{key:"provider-".concat(m.id),value:!!w},p.createElement(t6,{decorations:y,element:h,key:m.id,renderElement:r,renderPlaceholder:a,renderLeaf:o,selection:w}))):d.push(p.createElement(t2,{decorations:y,key:m.id,isLast:u&&f===n.children.length-1,parent:n,renderPlaceholder:a,renderLeaf:o,text:h})),er.set(h,f),ea.set(h,n)}return d},nt=(0,p.createContext)(!1),nn=()=>(0,p.useContext)(nt),nr=(0,p.createContext)(null),na=()=>{var e=(0,p.useContext)(nr);if(!e)throw Error("The `useSlate` hook must be used inside the <Slate> component's context.");var{editor:t}=e;return t},no=(e,t)=>{var n=[],r=()=>{n=[]};return{registerMutations:r=>{if(t.current){var a=r.filter(t=>R(e,t,r));n.push(...a)}},restoreDOM:function(){n.length>0&&(n.reverse().forEach(e=>{"characterData"!==e.type&&(e.removedNodes.forEach(t=>{e.target.insertBefore(t,e.nextSibling)}),e.addedNodes.forEach(t=>{e.target.removeChild(t)}))}),r())},clear:r}},ni={subtree:!0,childList:!0,characterData:!0,characterDataOldValue:!0};class nl extends p.Component{constructor(){super(...arguments),tB(this,"context",null),tB(this,"manager",null),tB(this,"mutationObserver",null)}observe(){var e,{node:t}=this.props;if(!t.current)throw Error("Failed to attach MutationObserver, `node` is undefined");null===(e=this.mutationObserver)||void 0===e||e.observe(t.current,ni)}componentDidMount(){var{receivedUserInput:e}=this.props,t=this.context;this.manager=no(t,e),this.mutationObserver=new MutationObserver(this.manager.registerMutations),this.observe()}getSnapshotBeforeUpdate(){var e,t,n,r,a=null===(e=this.mutationObserver)||void 0===e?void 0:e.takeRecords();return null!=a&&a.length&&(null===(r=this.manager)||void 0===r||r.registerMutations(a)),null===(t=this.mutationObserver)||void 0===t||t.disconnect(),null===(n=this.manager)||void 0===n||n.restoreDOM(),null}componentDidUpdate(){var e;null===(e=this.manager)||void 0===e||e.clear(),this.observe()}componentWillUnmount(){var e;null===(e=this.mutationObserver)||void 0===e||e.disconnect()}render(){return this.props.children}}tB(nl,"contextType",tN);var ns=I?nl:e=>{var{children:t}=e;return p.createElement(p.Fragment,null,t)},nc=(0,p.createContext)(!1),nd=["autoFocus","decorate","onDOMBeforeInput","placeholder","readOnly","renderElement","renderLeaf","renderPlaceholder","scrollSelectionIntoView","style","as","disableDefaultStyles"],nu=["text"];function nf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function np(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?nf(Object(n),!0).forEach(function(t){tB(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nf(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var ng=e=>p.createElement(p.Fragment,null,ne(e)),nv=(0,p.forwardRef)((e,t)=>{var n=(0,p.useCallback)(e=>p.createElement(nh,np({},e)),[]),{autoFocus:r,decorate:a=nm,onDOMBeforeInput:o,placeholder:i,readOnly:l=!1,renderElement:c,renderLeaf:u,renderPlaceholder:g=n,scrollSelectionIntoView:h=nb,style:m={},as:b="div",disableDefaultStyles:y=!1}=e,S=tM(e,nd),C=na(),[k,A]=(0,p.useState)(!1),P=(0,p.useRef)(null),D=(0,p.useRef)([]),[M,T]=(0,p.useState)(),B=(0,p.useRef)(!1),{onUserInput:R,receivedUserInput:j}=function(){var e=tR(),t=(0,p.useRef)(!1),n=(0,p.useRef)(0),r=(0,p.useCallback)(()=>{if(!t.current){t.current=!0;var r=tz.getWindow(e);r.cancelAnimationFrame(n.current),n.current=r.requestAnimationFrame(()=>{t.current=!1})}},[e]);return(0,p.useEffect)(()=>()=>cancelAnimationFrame(n.current),[]),{receivedUserInput:t,onUserInput:r}}(),[,_]=(0,p.useReducer)(e=>e+1,0);eS.set(C,_),ef.set(C,l);var L=(0,p.useMemo)(()=>({isDraggingInternally:!1,isUpdatingSelection:!1,latestElement:null,hasMarkPlaceholder:!1}),[]);(0,p.useEffect)(()=>{P.current&&r&&P.current.focus()},[r]);var H=(0,p.useRef)(),Y=(0,p.useMemo)(()=>f()(()=>{if(en.get(C)){Y();return}var e=tz.toDOMNode(C,C).getRootNode();if(!B.current&&W&&e instanceof ShadowRoot){B.current=!0,z()?document.execCommand("indent"):v.gB.deselect(C),B.current=!1;return}var t=H.current;if((I||!tz.isComposing(C))&&(!L.isUpdatingSelection||null!=t&&t.isFlushing())&&!L.isDraggingInternally){var n=tz.findDocumentOrShadowRoot(C),{activeElement:r}=n,a=tz.toDOMNode(C,C),o=N(n);if(r===a?(L.latestElement=r,ep.set(C,!0)):ep.delete(C),!o)return v.gB.deselect(C);var{anchorNode:i,focusNode:s}=o,c=tz.hasEditableTarget(C,i)||tz.isTargetInsideNonReadonlyVoid(C,i),d=tz.hasTarget(C,s);if(c&&d){var u=tz.toSlateRange(C,o,{exactMatch:!1,suppressThrow:!0});u&&(tz.isComposing(C)||null!=t&&t.hasPendingChanges()||null!=t&&t.isFlushing()?null==t||t.handleUserSelect(u):v.gB.select(C,u))}!l||c&&d||v.gB.deselect(C)}},100),[C,l,L]),Q=(0,p.useMemo)(()=>d()(Y,0),[Y]);H.current=tX({node:P,onDOMSelectionChange:Y,scheduleOnDOMSelectionChange:Q}),tI(()=>{P.current&&(n=w(P.current))?(eo.set(C,n),ei.set(C,P.current),ec.set(C,P.current),es.set(P.current,C)):ec.delete(C);var e,t,n,{selection:r}=C,a=N(tz.findDocumentOrShadowRoot(C));if(!(!a||!tz.isFocused(C)||null!==(e=H.current)&&void 0!==e&&e.hasPendingAction())){var o=e=>{var t="None"!==a.type;if(r||t){var n=a.focusNode;if(K&&a.rangeCount>1){var o=a.getRangeAt(0),i=a.getRangeAt(a.rangeCount-1);c=o.startContainer===n?i.endContainer:o.startContainer}else c=a.anchorNode;var l=ei.get(C),s=!1;if(l.contains(c)&&l.contains(n)&&(s=!0),t&&s&&r&&!e){var c,d,u=tz.toSlateRange(C,a,{exactMatch:!0,suppressThrow:!0});if(u&&v.Q6.equals(u,r)&&(!L.hasMarkPlaceholder||null!==(d=c)&&void 0!==d&&null!==(d=d.parentElement)&&void 0!==d&&d.hasAttribute("data-slate-mark-placeholder")))return}if(r&&!tz.hasRange(C,r)){C.selection=tz.toSlateRange(C,a,{exactMatch:!1,suppressThrow:!0});return}L.isUpdatingSelection=!0;var f=null;try{f=r&&tz.toDOMRange(C,r)}catch(e){}return f?(tz.isComposing(C)&&!I?a.collapseToEnd():v.Q6.isBackward(r)?a.setBaseAndExtent(f.endContainer,f.endOffset,f.startContainer,f.startOffset):a.setBaseAndExtent(f.startContainer,f.startOffset,f.endContainer,f.endOffset),h(C,f)):a.removeAllRanges(),f}};a.rangeCount<=1&&o();var i=(null===(t=H.current)||void 0===t?void 0:t.isFlushing())==="action";if(!I||!i){setTimeout(()=>{L.isUpdatingSelection=!1});return}var l=null,s=requestAnimationFrame(()=>{if(i){var e=e=>{try{tz.toDOMNode(C,C).focus(),o(e)}catch(e){}};e(),l=setTimeout(()=>{e(!0),L.isUpdatingSelection=!1})}});return()=>{cancelAnimationFrame(s),l&&clearTimeout(l)}}});var J=(0,p.useCallback)(e=>{var t=tz.toDOMNode(C,C).getRootNode();if(null!=B&&B.current&&W&&t instanceof ShadowRoot){var n=e.getTargetRanges()[0],r=new window.Range;r.setStart(n.startContainer,n.startOffset),r.setEnd(n.endContainer,n.endOffset);var a=tz.toSlateRange(C,r,{exactMatch:!1,suppressThrow:!1});v.gB.select(C,a),e.preventDefault(),e.stopImmediatePropagation();return}if(R(),!l&&tz.hasEditableTarget(C,e.target)&&!nE(e,o)){if(H.current)return H.current.handleDOMBeforeInput(e);Q.flush(),Y.flush();var{selection:i}=C,{inputType:s}=e,c=e.dataTransfer||e.data||void 0,d="insertCompositionText"===s||"deleteCompositionText"===s;if(d&&tz.isComposing(C))return;var u=!1;if("insertText"===s&&i&&v.Q6.isCollapsed(i)&&e.data&&1===e.data.length&&/[a-z ]/i.test(e.data)&&0!==i.anchor.offset&&(u=!0,C.marks&&(u=!1),!en.get(C))){var{anchor:f}=i,[p,g]=tz.toDOMPoint(C,f),h=null===(w=p.parentElement)||void 0===w?void 0:w.closest("a"),m=tz.getWindow(C);if(u&&h&&tz.hasDOMNode(C,h)){var b,w,y,E,x=null==m?void 0:m.document.createTreeWalker(h,NodeFilter.SHOW_TEXT).lastChild();x===p&&(null===(E=x.textContent)||void 0===E?void 0:E.length)===g&&(u=!1)}if(u&&p.parentElement&&(null==m||null===(y=m.getComputedStyle(p.parentElement))||void 0===y?void 0:y.whiteSpace)==="pre"){var S=v.KE.above(C,{at:f.path,match:e=>v.Hg.isElement(e)&&v.KE.isBlock(C,e)});S&&v.bP.string(S[0]).includes("	")&&(u=!1)}}if((!s.startsWith("delete")||s.startsWith("deleteBy"))&&!en.get(C)){var[O]=e.getTargetRanges();if(O){var k=tz.toSlateRange(C,O,{exactMatch:!1,suppressThrow:!1});if(!i||!v.Q6.equals(i,k)){u=!1;var P=!d&&C.selection&&v.KE.rangeRef(C,C.selection);v.gB.select(C,k),P&&ev.set(C,P)}}}if(d)return;if(u||e.preventDefault(),i&&v.Q6.isExpanded(i)&&s.startsWith("delete")){var M=s.endsWith("Backward")?"backward":"forward";v.KE.deleteFragment(C,{direction:M});return}switch(s){case"deleteByComposition":case"deleteByCut":case"deleteByDrag":v.KE.deleteFragment(C);break;case"deleteContent":case"deleteContentForward":v.KE.deleteForward(C);break;case"deleteContentBackward":v.KE.deleteBackward(C);break;case"deleteEntireSoftLine":v.KE.deleteBackward(C,{unit:"line"}),v.KE.deleteForward(C,{unit:"line"});break;case"deleteHardLineBackward":v.KE.deleteBackward(C,{unit:"block"});break;case"deleteSoftLineBackward":v.KE.deleteBackward(C,{unit:"line"});break;case"deleteHardLineForward":v.KE.deleteForward(C,{unit:"block"});break;case"deleteSoftLineForward":v.KE.deleteForward(C,{unit:"line"});break;case"deleteWordBackward":v.KE.deleteBackward(C,{unit:"word"});break;case"deleteWordForward":v.KE.deleteForward(C,{unit:"word"});break;case"insertLineBreak":v.KE.insertSoftBreak(C);break;case"insertParagraph":v.KE.insertBreak(C);break;case"insertFromComposition":case"insertFromDrop":case"insertFromPaste":case"insertFromYank":case"insertReplacementText":case"insertText":"insertFromComposition"===s&&tz.isComposing(C)&&(A(!1),eg.set(C,!1)),(null==c?void 0:c.constructor.name)==="DataTransfer"?tz.insertData(C,c):"string"==typeof c&&(u?D.current.push(()=>v.KE.insertText(C,c)):v.KE.insertText(C,c))}var T=null===(b=ev.get(C))||void 0===b?void 0:b.unref();ev.delete(C),!T||C.selection&&v.Q6.equals(C.selection,T)||v.gB.select(C,T)}},[C,Y,R,o,l,Q]),$=(0,p.useCallback)(e=>{null==e?(Y.cancel(),Q.cancel(),ei.delete(C),ec.delete(C),P.current&&Z&&P.current.removeEventListener("beforeinput",J)):Z&&e.addEventListener("beforeinput",J),P.current=e,"function"==typeof t?t(e):t&&(t.current=e)},[Y,Q,C,J,t]);tI(()=>{var e=tz.getWindow(C),t=e=>{var{target:t}=e,n=t instanceof HTMLElement?t:null,r=null==n?void 0:n.tagName;"INPUT"!==r&&"TEXTAREA"!==r&&Q()};e.document.addEventListener("selectionchange",t);var n=()=>{L.isDraggingInternally=!1};return e.document.addEventListener("dragend",n),e.document.addEventListener("drop",n),()=>{e.document.removeEventListener("selectionchange",t),e.document.removeEventListener("dragend",n),e.document.removeEventListener("drop",n)}},[Q,L]);var ee=a([C,[]]),et=i&&1===C.children.length&&1===Array.from(v.bP.texts(C)).length&&""===v.bP.string(C)&&!k,er=(0,p.useCallback)(e=>{if(e&&et){var t;T(null===(t=e.getBoundingClientRect())||void 0===t?void 0:t.height)}else T(void 0)},[et]);if(et){var ea=v.KE.start(C,[]);ee.push({[eC]:!0,placeholder:i,onPlaceholderResize:er,anchor:ea,focus:ea})}var{marks:el}=C;if(L.hasMarkPlaceholder=!1,C.selection&&v.Q6.isCollapsed(C.selection)&&el){var{anchor:ed}=C.selection,eu=v.bP.leaf(C,ed.path),eh=tM(eu,nu);if(!v.EY.equals(eu,el,{loose:!0})){L.hasMarkPlaceholder=!0;var em=Object.fromEntries(Object.keys(eh).map(e=>[e,null]));ee.push(np(np(np({[eO]:!0},em),el),{},{anchor:ed,focus:ed}))}}return(0,p.useEffect)(()=>{setTimeout(()=>{var{selection:e}=C;if(e){var{anchor:t}=e,n=v.bP.leaf(C,t.path);if(el&&!v.EY.equals(n,el,{loose:!0})){eb.set(C,el);return}}eb.delete(C)})}),p.createElement(nt.Provider,{value:l},p.createElement(nc.Provider,{value:k},p.createElement(t5.Provider,{value:a},p.createElement(ns,{node:P,receivedUserInput:j},p.createElement(b,np(np({role:l?void 0:"textbox","aria-multiline":!l||void 0},S),{},{spellCheck:(!!Z||!G)&&S.spellCheck,autoCorrect:Z||!G?S.autoCorrect:"false",autoCapitalize:Z||!G?S.autoCapitalize:"false","data-slate-editor":!0,"data-slate-node":"value",contentEditable:!l,zindex:-1,suppressContentEditableWarning:!0,ref:$,style:np(np({},y?{}:np({position:"relative",whiteSpace:"pre-wrap",wordWrap:"break-word"},M?{minHeight:M}:{})),m),onBeforeInput:(0,p.useCallback)(e=>{if(!Z&&!l&&!nw(e,S.onBeforeInput)&&tz.hasSelectableTarget(C,e.target)&&(e.preventDefault(),!tz.isComposing(C))){var t=e.data;v.KE.insertText(C,t)}},[S.onBeforeInput,C,l]),onInput:(0,p.useCallback)(e=>{if(!nw(e,S.onInput)){if(H.current){H.current.handleInput();return}for(var t of D.current)t();if(D.current=[],!tz.isFocused(C)){var n=e.nativeEvent;if("historyUndo"===n.inputType&&"function"==typeof C.undo){C.undo();return}if("historyRedo"===n.inputType&&"function"==typeof C.redo){C.redo();return}}}},[S.onInput,C]),onBlur:(0,p.useCallback)(e=>{if(!(l||L.isUpdatingSelection||!tz.hasSelectableTarget(C,e.target)||nw(e,S.onBlur))){var t=tz.findDocumentOrShadowRoot(C);if(L.latestElement!==t.activeElement){var{relatedTarget:n}=e;if(!(n===tz.toDOMNode(C,C)||E(n)&&n.hasAttribute("data-slate-spacer"))){if(null!=n&&x(n)&&tz.hasDOMNode(C,n)){var r=tz.toSlateNode(C,n);if(v.Hg.isElement(r)&&!C.isVoid(r))return}if(W){var a=N(t);null==a||a.removeAllRanges()}ep.delete(C)}}}},[l,L.isUpdatingSelection,L.latestElement,C,S.onBlur]),onClick:(0,p.useCallback)(e=>{if(tz.hasTarget(C,e.target)&&!nw(e,S.onClick)&&x(e.target)){var t=tz.toSlateNode(C,e.target),n=tz.findPath(C,t);if(v.KE.hasPath(C,n)&&v.bP.get(C,n)===t){if(3===e.detail&&n.length>=1){var r=n;if(!(v.Hg.isElement(t)&&v.KE.isBlock(C,t))){var a,o=v.KE.above(C,{match:e=>v.Hg.isElement(e)&&v.KE.isBlock(C,e),at:n});r=null!==(a=null==o?void 0:o[1])&&void 0!==a?a:n.slice(0,1)}var i=v.KE.range(C,r);v.gB.select(C,i);return}if(!l){var s=v.KE.start(C,n),c=v.KE.end(C,n),d=v.KE.void(C,{at:s}),u=v.KE.void(C,{at:c});if(d&&u&&v.wA.equals(d[1],u[1])){var f=v.KE.range(C,s);v.gB.select(C,f)}}}}},[C,S.onClick,l]),onCompositionEnd:(0,p.useCallback)(e=>{if(tz.hasSelectableTarget(C,e.target)){var t;if(tz.isComposing(C)&&Promise.resolve().then(()=>{A(!1),eg.set(C,!1)}),null===(t=H.current)||void 0===t||t.handleCompositionEnd(e),!nw(e,S.onCompositionEnd)&&!I&&!W&&!q&&!F&&!U&&!V&&e.data){var n=eb.get(C);eb.delete(C),void 0!==n&&(ew.set(C,C.marks),C.marks=n),v.KE.insertText(C,e.data);var r=ew.get(C);ew.delete(C),void 0!==r&&(C.marks=r)}}},[S.onCompositionEnd,C]),onCompositionUpdate:(0,p.useCallback)(e=>{!tz.hasSelectableTarget(C,e.target)||nw(e,S.onCompositionUpdate)||tz.isComposing(C)||(A(!0),eg.set(C,!0))},[S.onCompositionUpdate,C]),onCompositionStart:(0,p.useCallback)(e=>{if(tz.hasSelectableTarget(C,e.target)&&(null===(t=H.current)||void 0===t||t.handleCompositionStart(e),!nw(e,S.onCompositionStart)&&!I)){A(!0);var t,{selection:n}=C;if(n&&v.Q6.isExpanded(n)){v.KE.deleteFragment(C);return}}},[S.onCompositionStart,C]),onCopy:(0,p.useCallback)(e=>{!tz.hasSelectableTarget(C,e.target)||nw(e,S.onCopy)||ny(e)||(e.preventDefault(),tz.setFragmentData(C,e.clipboardData,"copy"))},[S.onCopy,C]),onCut:(0,p.useCallback)(e=>{if(!l&&tz.hasSelectableTarget(C,e.target)&&!nw(e,S.onCut)&&!ny(e)){e.preventDefault(),tz.setFragmentData(C,e.clipboardData,"cut");var{selection:t}=C;if(t){if(v.Q6.isExpanded(t))v.KE.deleteFragment(C);else{var n=v.bP.parent(C,t.anchor.path);v.KE.isVoid(C,n)&&v.gB.delete(C)}}}},[l,C,S.onCut]),onDragOver:(0,p.useCallback)(e=>{if(tz.hasTarget(C,e.target)&&!nw(e,S.onDragOver)){var t=tz.toSlateNode(C,e.target);v.Hg.isElement(t)&&v.KE.isVoid(C,t)&&e.preventDefault()}},[S.onDragOver,C]),onDragStart:(0,p.useCallback)(e=>{if(!l&&tz.hasTarget(C,e.target)&&!nw(e,S.onDragStart)){var t=tz.toSlateNode(C,e.target),n=tz.findPath(C,t);if(v.Hg.isElement(t)&&v.KE.isVoid(C,t)||v.KE.void(C,{at:n,voids:!0})){var r=v.KE.range(C,n);v.gB.select(C,r)}L.isDraggingInternally=!0,tz.setFragmentData(C,e.dataTransfer,"drag")}},[l,C,S.onDragStart,L]),onDrop:(0,p.useCallback)(e=>{if(!l&&tz.hasTarget(C,e.target)&&!nw(e,S.onDrop)){e.preventDefault();var t=C.selection,n=tz.findEventRange(C,e),r=e.dataTransfer;v.gB.select(C,n),L.isDraggingInternally&&t&&!v.Q6.equals(t,n)&&!v.KE.void(C,{at:n,voids:!0})&&v.gB.delete(C,{at:t}),tz.insertData(C,r),tz.isFocused(C)||tz.focus(C)}},[l,C,S.onDrop,L]),onDragEnd:(0,p.useCallback)(e=>{!l&&L.isDraggingInternally&&S.onDragEnd&&tz.hasTarget(C,e.target)&&S.onDragEnd(e)},[l,L,S,C]),onFocus:(0,p.useCallback)(e=>{if(!l&&!L.isUpdatingSelection&&tz.hasEditableTarget(C,e.target)&&!nw(e,S.onFocus)){var t=tz.toDOMNode(C,C);if(L.latestElement=tz.findDocumentOrShadowRoot(C).activeElement,K&&e.target!==t){t.focus();return}ep.set(C,!0)}},[l,L,C,S.onFocus]),onKeyDown:(0,p.useCallback)(e=>{if(!l&&tz.hasEditableTarget(C,e.target)){null===(t=H.current)||void 0===t||t.handleKeyDown(e);var t,{nativeEvent:n}=e;if(tz.isComposing(C)&&!1===n.isComposing&&(eg.set(C,!1),A(!1)),!(nw(e,S.onKeyDown)||tz.isComposing(C))){var{selection:r}=C,a=C.children[null!==r?r.focus.path[0]:0],o="rtl"===s()(v.bP.string(a));if(eX.isRedo(n)){e.preventDefault(),"function"==typeof C.redo&&C.redo();return}if(eX.isUndo(n)){e.preventDefault(),"function"==typeof C.undo&&C.undo();return}if(eX.isMoveLineBackward(n)){e.preventDefault(),v.gB.move(C,{unit:"line",reverse:!0});return}if(eX.isMoveLineForward(n)){e.preventDefault(),v.gB.move(C,{unit:"line"});return}if(eX.isExtendLineBackward(n)){e.preventDefault(),v.gB.move(C,{unit:"line",edge:"focus",reverse:!0});return}if(eX.isExtendLineForward(n)){e.preventDefault(),v.gB.move(C,{unit:"line",edge:"focus"});return}if(eX.isMoveBackward(n)){e.preventDefault(),r&&v.Q6.isCollapsed(r)?v.gB.move(C,{reverse:!o}):v.gB.collapse(C,{edge:o?"end":"start"});return}if(eX.isMoveForward(n)){e.preventDefault(),r&&v.Q6.isCollapsed(r)?v.gB.move(C,{reverse:o}):v.gB.collapse(C,{edge:o?"start":"end"});return}if(eX.isMoveWordBackward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)&&v.gB.collapse(C,{edge:"focus"}),v.gB.move(C,{unit:"word",reverse:!o});return}if(eX.isMoveWordForward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)&&v.gB.collapse(C,{edge:"focus"}),v.gB.move(C,{unit:"word",reverse:o});return}if(Z){if((X||W)&&r&&(eX.isDeleteBackward(n)||eX.isDeleteForward(n))&&v.Q6.isCollapsed(r)){var i=v.bP.parent(C,r.anchor.path);if(v.Hg.isElement(i)&&v.KE.isVoid(C,i)&&(v.KE.isInline(C,i)||v.KE.isBlock(C,i))){e.preventDefault(),v.KE.deleteBackward(C,{unit:"block"});return}}}else{if(eX.isBold(n)||eX.isItalic(n)||eX.isTransposeCharacter(n)){e.preventDefault();return}if(eX.isSoftBreak(n)){e.preventDefault(),v.KE.insertSoftBreak(C);return}if(eX.isSplitBlock(n)){e.preventDefault(),v.KE.insertBreak(C);return}if(eX.isDeleteBackward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)?v.KE.deleteFragment(C,{direction:"backward"}):v.KE.deleteBackward(C);return}if(eX.isDeleteForward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)?v.KE.deleteFragment(C,{direction:"forward"}):v.KE.deleteForward(C);return}if(eX.isDeleteLineBackward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)?v.KE.deleteFragment(C,{direction:"backward"}):v.KE.deleteBackward(C,{unit:"line"});return}if(eX.isDeleteLineForward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)?v.KE.deleteFragment(C,{direction:"forward"}):v.KE.deleteForward(C,{unit:"line"});return}if(eX.isDeleteWordBackward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)?v.KE.deleteFragment(C,{direction:"backward"}):v.KE.deleteBackward(C,{unit:"word"});return}if(eX.isDeleteWordForward(n)){e.preventDefault(),r&&v.Q6.isExpanded(r)?v.KE.deleteFragment(C,{direction:"forward"}):v.KE.deleteForward(C,{unit:"word"});return}}}}},[l,C,S.onKeyDown]),onPaste:(0,p.useCallback)(e=>{!l&&tz.hasEditableTarget(C,e.target)&&!nw(e,S.onPaste)&&(!Z||O(e.nativeEvent)||W)&&(e.preventDefault(),tz.insertData(C,e.clipboardData))},[l,C,S.onPaste])}),p.createElement(ng,{decorations:ee,node:C,renderElement:c,renderPlaceholder:g,renderLeaf:u,selection:C.selection}))))))}),nh=e=>{var{attributes:t,children:n}=e;return p.createElement("span",np({},t),n,I&&p.createElement("br",null))},nm=()=>[],nb=(e,t)=>{if(t.getBoundingClientRect&&(!e.selection||e.selection&&v.Q6.isCollapsed(e.selection))){var n=t.startContainer.parentElement;n.getBoundingClientRect=t.getBoundingClientRect.bind(t),(0,g.A)(n,{scrollMode:"if-needed"}),delete n.getBoundingClientRect}},nw=(e,t)=>{if(!t)return!1;var n=t(e);return null!=n?n:e.isDefaultPrevented()||e.isPropagationStopped()},ny=e=>x(e.target)&&(e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement),nE=(e,t)=>{if(!t)return!1;var n=t(e);return null!=n?n:e.defaultPrevented},nx=(0,p.createContext)(!1),nS=(0,p.createContext)({}),nC=parseInt(p.version.split(".")[0],10),nO=["editor","children","onChange","onSelectionChange","onValueChange","initialValue"],nk=e=>{var{editor:t,children:n,onChange:r,onSelectionChange:a,onValueChange:o,initialValue:i}=e,l=tM(e,nO),[s,c]=p.useState(()=>{if(!v.bP.isNodeList(i))throw Error("[Slate] initialValue is invalid! Expected a list of elements but got: ".concat(v.h6.stringify(i)));if(!v.KE.isEditor(t))throw Error("[Slate] editor is invalid! You passed: ".concat(v.h6.stringify(t)));return t.children=i,Object.assign(t,l),{v:0,editor:t}}),{selectorContext:d,onChange:u}=function(e){var t=(0,p.useRef)([]).current,n=(0,p.useRef)({editor:e}).current,r=(0,p.useCallback)(e=>{n.editor=e,t.forEach(t=>t(e))},[t,n]);return{selectorContext:(0,p.useMemo)(()=>({getSlate:()=>n.editor,addEventListener:e=>(t.push(e),()=>{t.splice(t.indexOf(e),1)})}),[t,n]),onChange:r}}(t),f=(0,p.useCallback)(e=>{var n;(r&&r(t.children),(null==e||null===(n=e.operation)||void 0===n?void 0:n.type)==="set_selection")?null==a||a(t.selection):null==o||o(t.children),c(e=>({v:e.v+1,editor:t})),u(t)},[t,u,r,a,o]);(0,p.useEffect)(()=>(eh.set(t,f),()=>{eh.set(t,()=>{})}),[t,f]);var[g,h]=(0,p.useState)(tz.isFocused(t));return(0,p.useEffect)(()=>{h(tz.isFocused(t))},[t]),tI(()=>{var e=()=>h(tz.isFocused(t));return nC>=17?(document.addEventListener("focusin",e),document.addEventListener("focusout",e),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",e)}):(document.addEventListener("focus",e,!0),document.addEventListener("blur",e,!0),()=>{document.removeEventListener("focus",e,!0),document.removeEventListener("blur",e,!0)})},[]),p.createElement(nS.Provider,{value:d},p.createElement(nr.Provider,{value:s},p.createElement(tN.Provider,{value:s.editor},p.createElement(nx.Provider,{value:g},n))))},nA=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"x-slate-fragment",n=e,{onChange:r}=n=eF(n,t);return n.onChange=e=>{(nC<18?tD.unstable_batchedUpdates:e=>e())(()=>{r(e)})},n}},37177:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(35726),a=n(21462);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"};var i=n(29236);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,r.A)({},e,{ref:t,icon:o}))})},39510:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(35726),a=n(21462);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"};var i=n(29236);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,r.A)({},e,{ref:t,icon:o}))})},47109:()=>{},48772:(e,t,n)=>{"use strict";n.d(t,{A:()=>B});var r=n(12694),a=n(53172),o=n(21462),i=n(46001),l=n.n(i),s=n(14084),c=n(29694),d=n(77312),u=n(40667),f=n(80382),p=n(70869),g=n(42248),v=n(32866),h=n(67621),m=o.createContext({});m.Consumer;var b=n(49357),w=n(37985),y=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},E=o.forwardRef(function(e,t){var n,r=e.prefixCls,a=e.children,i=e.actions,s=e.extra,c=e.styles,u=e.className,f=e.classNames,p=e.colStyle,g=y(e,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),v=(0,o.useContext)(m),h=v.grid,E=v.itemLayout,x=(0,o.useContext)(d.QO),S=x.getPrefixCls,C=x.list,O=function(e){var t,n;return l()(null===(n=null===(t=null==C?void 0:C.item)||void 0===t?void 0:t.classNames)||void 0===n?void 0:n[e],null==f?void 0:f[e])},k=function(e){var t,n;return Object.assign(Object.assign({},null===(n=null===(t=null==C?void 0:C.item)||void 0===t?void 0:t.styles)||void 0===n?void 0:n[e]),null==c?void 0:c[e])},A=S("list",r),P=i&&i.length>0&&o.createElement("ul",{className:l()("".concat(A,"-item-action"),O("actions")),key:"actions",style:k("actions")},i.map(function(e,t){return o.createElement("li",{key:"".concat(A,"-item-action-").concat(t)},e,t!==i.length-1&&o.createElement("em",{className:"".concat(A,"-item-action-split")}))})),D=o.createElement(h?"div":"li",Object.assign({},g,h?{}:{ref:t},{className:l()("".concat(A,"-item"),{["".concat(A,"-item-no-flex")]:!("vertical"===E?!!s:(n=!1,o.Children.forEach(a,function(e){"string"==typeof e&&(n=!0)}),!(n&&o.Children.count(a)>1)))},u)}),"vertical"===E&&s?[o.createElement("div",{className:"".concat(A,"-item-main"),key:"content"},a,P),o.createElement("div",{className:l()("".concat(A,"-item-extra"),O("extra")),key:"extra",style:k("extra")},s)]:[a,P,(0,b.Ob)(s,{key:"extra"})]);return h?o.createElement(w.A,{ref:t,flex:1,style:p},D):D});E.Meta=function(e){var t=e.prefixCls,n=e.className,r=e.avatar,a=e.title,i=e.description,s=y(e,["prefixCls","className","avatar","title","description"]),c=(0,(0,o.useContext)(d.QO).getPrefixCls)("list",t),u=l()("".concat(c,"-item-meta"),n),f=o.createElement("div",{className:"".concat(c,"-item-meta-content")},a&&o.createElement("h4",{className:"".concat(c,"-item-meta-title")},a),i&&o.createElement("div",{className:"".concat(c,"-item-meta-description")},i));return o.createElement("div",Object.assign({},s,{className:u}),r&&o.createElement("div",{className:"".concat(c,"-item-meta-avatar")},r),(a||i)&&f)};var x=n(64467),S=n(5214),C=n(68197),O=n(13440),k=function(e){var t=e.listBorderedCls,n=e.componentCls,r=e.paddingLG,a=e.margin,o=e.itemPaddingSM,i=e.itemPaddingLG,l=e.marginLG,s=e.borderRadiusLG;return{[t]:{border:"".concat((0,x.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:s,["".concat(n,"-header,").concat(n,"-footer,").concat(n,"-item")]:{paddingInline:r},["".concat(n,"-pagination")]:{margin:"".concat((0,x.zA)(a)," ").concat((0,x.zA)(l))}},["".concat(t).concat(n,"-sm")]:{["".concat(n,"-item,").concat(n,"-header,").concat(n,"-footer")]:{padding:o}},["".concat(t).concat(n,"-lg")]:{["".concat(n,"-item,").concat(n,"-header,").concat(n,"-footer")]:{padding:i}}}},A=function(e){var t=e.componentCls,n=e.screenSM,r=e.screenMD,a=e.marginLG,o=e.marginSM,i=e.margin;return{["@media screen and (max-width:".concat(r,"px)")]:{[t]:{["".concat(t,"-item")]:{["".concat(t,"-item-action")]:{marginInlineStart:a}}},["".concat(t,"-vertical")]:{["".concat(t,"-item")]:{["".concat(t,"-item-extra")]:{marginInlineStart:a}}}},["@media screen and (max-width: ".concat(n,"px)")]:{[t]:{["".concat(t,"-item")]:{flexWrap:"wrap",["".concat(t,"-action")]:{marginInlineStart:o}}},["".concat(t,"-vertical")]:{["".concat(t,"-item")]:{flexWrap:"wrap-reverse",["".concat(t,"-item-main")]:{minWidth:e.contentWidth},["".concat(t,"-item-extra")]:{margin:"auto auto ".concat((0,x.zA)(i))}}}}}},P=function(e){var t=e.componentCls,n=e.antCls,r=e.controlHeight,a=e.minHeight,o=e.paddingSM,i=e.marginLG,l=e.padding,s=e.itemPadding,c=e.colorPrimary,d=e.itemPaddingSM,u=e.itemPaddingLG,f=e.paddingXS,p=e.margin,g=e.colorText,v=e.colorTextDescription,h=e.motionDurationSlow,m=e.lineWidth,b=e.headerBg,w=e.footerBg,y=e.emptyTextPadding,E=e.metaMarginBottom,C=e.avatarMarginRight,O=e.titleMarginBottom,k=e.descriptionFontSize;return{[t]:Object.assign(Object.assign({},(0,S.dF)(e)),{position:"relative","*":{outline:"none"},["".concat(t,"-header")]:{background:b},["".concat(t,"-footer")]:{background:w},["".concat(t,"-header, ").concat(t,"-footer")]:{paddingBlock:o},["".concat(t,"-pagination")]:{marginBlockStart:i,["".concat(n,"-pagination-options")]:{textAlign:"start"}},["".concat(t,"-spin")]:{minHeight:a,textAlign:"center"},["".concat(t,"-items")]:{margin:0,padding:0,listStyle:"none"},["".concat(t,"-item")]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:s,color:g,["".concat(t,"-item-meta")]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",["".concat(t,"-item-meta-avatar")]:{marginInlineEnd:C},["".concat(t,"-item-meta-content")]:{flex:"1 0",width:0,color:g},["".concat(t,"-item-meta-title")]:{margin:"0 0 ".concat((0,x.zA)(e.marginXXS)," 0"),color:g,fontSize:e.fontSize,lineHeight:e.lineHeight,"> a":{color:g,transition:"all ".concat(h),"&:hover":{color:c}}},["".concat(t,"-item-meta-description")]:{color:v,fontSize:k,lineHeight:e.lineHeight}},["".concat(t,"-item-action")]:{flex:"0 0 auto",marginInlineStart:e.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:"0 ".concat((0,x.zA)(f)),color:v,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},["".concat(t,"-item-action-split")]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:m,height:e.calc(e.fontHeight).sub(e.calc(e.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:e.colorSplit}}},["".concat(t,"-empty")]:{padding:"".concat((0,x.zA)(l)," 0"),color:v,fontSize:e.fontSizeSM,textAlign:"center"},["".concat(t,"-empty-text")]:{padding:y,color:e.colorTextDisabled,fontSize:e.fontSize,textAlign:"center"},["".concat(t,"-item-no-flex")]:{display:"block"}}),["".concat(t,"-grid ").concat(n,"-col > ").concat(t,"-item")]:{display:"block",maxWidth:"100%",marginBlockEnd:p,paddingBlock:0,borderBlockEnd:"none"},["".concat(t,"-vertical ").concat(t,"-item")]:{alignItems:"initial",["".concat(t,"-item-main")]:{display:"block",flex:1},["".concat(t,"-item-extra")]:{marginInlineStart:i},["".concat(t,"-item-meta")]:{marginBlockEnd:E,["".concat(t,"-item-meta-title")]:{marginBlockStart:0,marginBlockEnd:O,color:g,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}},["".concat(t,"-item-action")]:{marginBlockStart:l,marginInlineStart:"auto","> li":{padding:"0 ".concat((0,x.zA)(l)),"&:first-child":{paddingInlineStart:0}}}},["".concat(t,"-split ").concat(t,"-item")]:{borderBlockEnd:"".concat((0,x.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderBlockEnd:"none"}},["".concat(t,"-split ").concat(t,"-header")]:{borderBlockEnd:"".concat((0,x.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit)},["".concat(t,"-split").concat(t,"-empty ").concat(t,"-footer")]:{borderTop:"".concat((0,x.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit)},["".concat(t,"-loading ").concat(t,"-spin-nested-loading")]:{minHeight:r},["".concat(t,"-split").concat(t,"-something-after-last-item ").concat(n,"-spin-container > ").concat(t,"-items > ").concat(t,"-item:last-child")]:{borderBlockEnd:"".concat((0,x.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit)},["".concat(t,"-lg ").concat(t,"-item")]:{padding:u},["".concat(t,"-sm ").concat(t,"-item")]:{padding:d},["".concat(t,":not(").concat(t,"-vertical)")]:{["".concat(t,"-item-no-flex")]:{["".concat(t,"-item-action")]:{float:"right"}}}}};let D=(0,C.OF)("List",function(e){var t=(0,O.oX)(e,{listBorderedCls:"".concat(e.componentCls,"-bordered"),minHeight:e.controlHeightLG});return[P(t),k(t),A(t)]},function(e){return{contentWidth:220,itemPadding:"".concat((0,x.zA)(e.paddingContentVertical)," 0"),itemPaddingSM:"".concat((0,x.zA)(e.paddingContentVerticalSM)," ").concat((0,x.zA)(e.paddingContentHorizontal)),itemPaddingLG:"".concat((0,x.zA)(e.paddingContentVerticalLG)," ").concat((0,x.zA)(e.paddingContentHorizontalLG)),headerBg:"transparent",footerBg:"transparent",emptyTextPadding:e.padding,metaMarginBottom:e.padding,avatarMarginRight:e.padding,titleMarginBottom:e.paddingSM,descriptionFontSize:e.fontSize}});var M=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},T=o.forwardRef(function(e,t){var n=e.pagination,i=void 0!==n&&n,b=e.prefixCls,w=e.bordered,y=e.split,E=e.className,x=e.rootClassName,S=e.style,C=e.children,O=e.itemLayout,k=e.loadMore,A=e.grid,P=e.dataSource,T=void 0===P?[]:P,B=e.size,N=e.header,R=e.footer,z=e.loading,j=e.rowKey,_=e.renderItem,F=e.locale,L=M(e,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),I=i&&"object"==typeof i?i:{},K=(0,r._)(o.useState(I.defaultCurrent||1),2),W=K[0],H=K[1],X=(0,r._)(o.useState(I.defaultPageSize||10),2),Y=X[0],Q=X[1],q=(0,d.TP)("list"),V=q.getPrefixCls,U=q.direction,G=q.className,Z=q.style,J=o.useContext(d.QO).renderEmpty,$=function(e){return function(t,n){var r;H(t),Q(n),i&&(null===(r=null==i?void 0:i[e])||void 0===r||r.call(i,t,n))}},ee=$("onChange"),et=$("onShowSizeChange"),en=function(e,t){var n;return _?((n="function"==typeof j?j(e):j?e[j]:e.key)||(n="list-item-".concat(t)),o.createElement(o.Fragment,{key:n},_(e,t))):null},er=V("list",b),ea=(0,r._)(D(er),3),eo=ea[0],ei=ea[1],el=ea[2],es=void 0!==z&&z;"boolean"==typeof es&&(es={spinning:es});var ec=!!(null==es?void 0:es.spinning),ed=(0,f.A)(B),eu="";switch(ed){case"large":eu="lg";break;case"small":eu="sm"}var ef=l()(er,{["".concat(er,"-vertical")]:"vertical"===O,["".concat(er,"-").concat(eu)]:eu,["".concat(er,"-split")]:void 0===y||y,["".concat(er,"-bordered")]:void 0!==w&&w,["".concat(er,"-loading")]:ec,["".concat(er,"-grid")]:!!A,["".concat(er,"-something-after-last-item")]:!!(k||i||R),["".concat(er,"-rtl")]:"rtl"===U},G,E,x,ei,el),ep=(0,s.A)({current:1,total:0},{total:T.length,current:W,pageSize:Y},i||{}),eg=Math.ceil(ep.total/ep.pageSize);ep.current>eg&&(ep.current=eg);var ev=i&&o.createElement("div",{className:l()("".concat(er,"-pagination"))},o.createElement(v.A,Object.assign({align:"end"},ep,{onChange:ee,onShowSizeChange:et}))),eh=(0,a.A)(T);i&&T.length>(ep.current-1)*ep.pageSize&&(eh=(0,a.A)(T).splice((ep.current-1)*ep.pageSize,ep.pageSize));var em=Object.keys(A||{}).some(function(e){return["xs","sm","md","lg","xl","xxl"].includes(e)}),eb=(0,g.A)(em),ew=o.useMemo(function(){for(var e=0;e<c.ye.length;e+=1){var t=c.ye[e];if(eb[t])return t}},[eb]),ey=o.useMemo(function(){if(A){var e=ew&&A[ew]?A[ew]:A.column;if(e)return{width:"".concat(100/e,"%"),maxWidth:"".concat(100/e,"%")}}},[JSON.stringify(A),ew]),eE=ec&&o.createElement("div",{style:{minHeight:53}});if(eh.length>0){var ex=eh.map(function(e,t){return en(e,t)});eE=A?o.createElement(p.A,{gutter:A.gutter},o.Children.map(ex,function(e){return o.createElement("div",{key:null==e?void 0:e.key,style:ey},e)})):o.createElement("ul",{className:"".concat(er,"-items")},ex)}else C||ec||(eE=o.createElement("div",{className:"".concat(er,"-empty-text")},(null==F?void 0:F.emptyText)||(null==J?void 0:J("List"))||o.createElement(u.A,{componentName:"List"})));var eS=ep.position||"bottom",eC=o.useMemo(function(){return{grid:A,itemLayout:O}},[JSON.stringify(A),O]);return eo(o.createElement(m.Provider,{value:eC},o.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},Z),S),className:ef},L),("top"===eS||"both"===eS)&&ev,N&&o.createElement("div",{className:"".concat(er,"-header")},N),o.createElement(h.A,Object.assign({},es),eE,C),R&&o.createElement("div",{className:"".concat(er,"-footer")},R),k||("bottom"===eS||"both"===eS)&&ev)))});T.Item=E;let B=T},54695:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(35726),a=n(21462),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},i=n(29236);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,r.A)({},e,{ref:t,icon:o}))})},57008:(e,t,n)=>{"use strict";n.d(t,{Qx:()=>c,jM:()=>H,mq:()=>X,vD:()=>Y});var r,a=Symbol.for("immer-nothing"),o=Symbol.for("immer-draftable"),i=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var s=Object.getPrototypeOf;function c(e){return!!e&&!!e[i]}function d(e){return!!e&&(f(e)||Array.isArray(e)||!!e[o]||!!e.constructor?.[o]||m(e)||b(e))}var u=Object.prototype.constructor.toString();function f(e){if(!e||"object"!=typeof e)return!1;let t=s(e);if(null===t)return!0;let n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===u}function p(e,t){0===g(e)?Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)}):e.forEach((n,r)=>t(r,n,e))}function g(e){let t=e[i];return t?t.type_:Array.isArray(e)?1:m(e)?2:3*!!b(e)}function v(e,t){return 2===g(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function h(e,t,n){let r=g(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function m(e){return e instanceof Map}function b(e){return e instanceof Set}function w(e){return e.copy_||e.base_}function y(e,t){if(m(e))return new Map(e);if(b(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let n=f(e);if(!0!==t&&("class_only"!==t||n)){let t=s(e);return null!==t&&n?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[i];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){let a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(s(e),t)}}function E(e,t=!1){return S(e)||c(e)||!d(e)||(g(e)>1&&(e.set=e.add=e.clear=e.delete=x),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>E(t,!0))),e}function x(){l(2)}function S(e){return Object.isFrozen(e)}var C={};function O(e){let t=C[e];return t||l(0,e),t}function k(e,t){t&&(O("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function A(e){P(e),e.drafts_.forEach(M),e.drafts_=null}function P(e){e===r&&(r=e.parent_)}function D(e){return r={drafts_:[],parent_:r,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function M(e){let t=e[i];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let n=t.drafts_[0];return void 0!==e&&e!==n?(n[i].modified_&&(A(t),l(4)),d(e)&&(e=B(t,e),t.parent_||R(t,e)),t.patches_&&O("Patches").generateReplacementPatches_(n[i].base_,e,t.patches_,t.inversePatches_)):e=B(t,n,[]),A(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==a?e:void 0}function B(e,t,n){if(S(t))return t;let r=t[i];if(!r)return p(t,(a,o)=>N(e,r,t,a,o,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return R(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;let t=r.copy_,a=t,o=!1;3===r.type_&&(a=new Set(t),t.clear(),o=!0),p(a,(a,i)=>N(e,r,t,a,i,n,o)),R(e,t,!1),n&&e.patches_&&O("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function N(e,t,n,r,a,o,i){if(c(a)){let i=B(e,a,o&&t&&3!==t.type_&&!v(t.assigned_,r)?o.concat(r):void 0);if(h(n,r,i),!c(i))return;e.canAutoFreeze_=!1}else i&&n.add(a);if(d(a)&&!S(a)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;B(e,a),(!t||!t.scope_.parent_)&&"symbol"!=typeof r&&Object.prototype.propertyIsEnumerable.call(n,r)&&R(e,a)}}function R(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&E(t,n)}var z={get(e,t){if(t===i)return e;let n=w(e);if(!v(n,t))return function(e,t,n){let r=F(t,n);return r?"value"in r?r.value:r.get?.call(e.draft_):void 0}(e,n,t);let r=n[t];return e.finalized_||!d(r)?r:r===_(e.base_,t)?(I(e),e.copy_[t]=K(r,e)):r},has:(e,t)=>t in w(e),ownKeys:e=>Reflect.ownKeys(w(e)),set(e,t,n){let r=F(w(e),t);if(r?.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){let r=_(w(e),t),a=r?.[i];if(a&&a.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if((n===r?0!==n||1/n==1/r:n!=n&&r!=r)&&(void 0!==n||v(e.base_,t)))return!0;I(e),L(e)}return!!(e.copy_[t]===n&&(void 0!==n||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=n,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==_(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,I(e),L(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let n=w(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty(){l(11)},getPrototypeOf:e=>s(e.base_),setPrototypeOf(){l(12)}},j={};function _(e,t){let n=e[i];return(n?w(n):e)[t]}function F(e,t){if(!(t in e))return;let n=s(e);for(;n;){let e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=s(n)}}function L(e){!e.modified_&&(e.modified_=!0,e.parent_&&L(e.parent_))}function I(e){e.copy_||(e.copy_=y(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function K(e,t){let n=m(e)?O("MapSet").proxyMap_(e,t):b(e)?O("MapSet").proxySet_(e,t):function(e,t){let n=Array.isArray(e),a={type_:+!!n,scope_:t?t.scope_:r,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},o=a,i=z;n&&(o=[a],i=j);let{revoke:l,proxy:s}=Proxy.revocable(o,i);return a.draft_=s,a.revoke_=l,s}(e,t);return(t?t.scope_:r).drafts_.push(n),n}p(z,(e,t)=>{j[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),j.deleteProperty=function(e,t){return j.set.call(this,e,t,void 0)},j.set=function(e,t,n){return z.set.call(this,e[0],t,n,e[0])};var W=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,n)=>{let r;if("function"==typeof e&&"function"!=typeof t){let n=t;t=e;let r=this;return function(e=n,...a){return r.produce(e,e=>t.call(this,e,...a))}}if("function"!=typeof t&&l(6),void 0!==n&&"function"!=typeof n&&l(7),d(e)){let a=D(this),o=K(e,void 0),i=!0;try{r=t(o),i=!1}finally{i?A(a):P(a)}return k(a,n),T(r,a)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(r=t(e))&&(r=e),r===a&&(r=void 0),this.autoFreeze_&&E(r,!0),n){let t=[],a=[];O("Patches").generateReplacementPatches_(e,r,t,a),n(t,a)}return r}},this.produceWithPatches=(e,t)=>{let n,r;return"function"==typeof e?(t,...n)=>this.produceWithPatches(t,t=>e(t,...n)):[this.produce(e,t,(e,t)=>{n=e,r=t}),n,r]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;d(e)||l(8),c(e)&&(c(t=e)||l(10,t),e=function e(t){let n;if(!d(t)||S(t))return t;let r=t[i];if(r){if(!r.modified_)return r.base_;r.finalized_=!0,n=y(t,r.scope_.immer_.useStrictShallowCopy_)}else n=y(t,!0);return p(n,(t,r)=>{h(n,t,e(r))}),r&&(r.finalized_=!1),n}(t));let n=D(this),r=K(e,void 0);return r[i].isManual_=!0,P(n),r}finishDraft(e,t){let n=e&&e[i];n&&n.isManual_||l(9);let{scope_:r}=n;return k(r,t),T(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){let r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));let r=O("Patches").applyPatches_;return c(e)?r(e,t):this.produce(e,e=>r(e,t))}},H=W.produce;W.produceWithPatches.bind(W),W.setAutoFreeze.bind(W),W.setUseStrictShallowCopy.bind(W),W.applyPatches.bind(W);var X=W.createDraft.bind(W),Y=W.finishDraft.bind(W)},58307:(e,t,n)=>{"use strict";n.d(t,{$k:()=>d});var r=n(8727),a=n(33665),o={isHistory:e=>(0,r.Q)(e)&&Array.isArray(e.redos)&&Array.isArray(e.undos)&&(0===e.redos.length||a.I.isOperationList(e.redos[0].operations))&&(0===e.undos.length||a.I.isOperationList(e.undos[0].operations))};new WeakMap;var i=new WeakMap,l=new WeakMap,s=new WeakMap,c={isHistoryEditor:e=>o.isHistory(e.history)&&a.KE.isEditor(e),isMerging:e=>l.get(e),isSplittingOnce:e=>s.get(e),setSplittingOnce(e,t){s.set(e,t)},isSaving:e=>i.get(e),redo(e){e.redo()},undo(e){e.undo()},withMerging(e,t){var n=c.isMerging(e);l.set(e,!0),t(),l.set(e,n)},withNewBatch(e,t){var n=c.isMerging(e);l.set(e,!0),s.set(e,!0),t(),l.set(e,n),s.delete(e)},withoutMerging(e,t){var n=c.isMerging(e);l.set(e,!1),t(),l.set(e,n)},withoutSaving(e,t){var n=c.isSaving(e);i.set(e,!1),t(),i.set(e,n)}},d=e=>{var{apply:t}=e;return e.history={undos:[],redos:[]},e.redo=()=>{var{history:t}=e,{redos:n}=t;if(n.length>0){var r=n[n.length-1];r.selectionBefore&&a.gB.setSelection(e,r.selectionBefore),c.withoutSaving(e,()=>{a.KE.withoutNormalizing(e,()=>{for(var t of r.operations)e.apply(t)})}),t.redos.pop(),e.writeHistory("undos",r)}},e.undo=()=>{var{history:t}=e,{undos:n}=t;if(n.length>0){var r=n[n.length-1];c.withoutSaving(e,()=>{a.KE.withoutNormalizing(e,()=>{for(var t of r.operations.map(a.I.inverse).reverse())e.apply(t);r.selectionBefore&&a.gB.setSelection(e,r.selectionBefore)})}),e.writeHistory("redos",r),t.undos.pop()}},e.apply=n=>{var{operations:r,history:a}=e,{undos:o}=a,i=o[o.length-1],l=i&&i.operations[i.operations.length-1],s=c.isSaving(e),d=c.isMerging(e);if(null==s&&(s=f(n)),s){if(null==d&&(d=null!=i&&(0!==r.length||u(n,l))),c.isSplittingOnce(e)&&(d=!1,c.setSplittingOnce(e,void 0)),i&&d)i.operations.push(n);else{var p={operations:[n],selectionBefore:e.selection};e.writeHistory("undos",p)}for(;o.length>100;)o.shift();a.redos=[]}t(n)},e.writeHistory=(t,n)=>{e.history[t].push(n)},e},u=(e,t)=>!!(t&&"insert_text"===e.type&&"insert_text"===t.type&&e.offset===t.offset+t.text.length&&a.wA.equals(e.path,t.path)||t&&"remove_text"===e.type&&"remove_text"===t.type&&e.offset+e.text.length===t.offset&&a.wA.equals(e.path,t.path)),f=(e,t)=>"set_selection"!==e.type},61250:(e,t,n)=>{"use strict";n.d(t,{A:()=>ex});var r=n(12694),a=n(21462),o=n(14453),i=n(46001),l=n.n(i),s=n(35726),c=n(20477),d=n(26975),u=n(60295),f=n(75884),p=n(28750);function g(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}var v=n(39074),h=n(55692),m=n(92415),b=n(15191),w=n(65823),y=n(5206),E=a.createContext(null);let x=function(e){var t=e.visible,n=e.maskTransitionName,r=e.getContainer,o=e.prefixCls,i=e.rootClassName,s=e.icons,u=e.countRender,f=e.showSwitch,p=e.showProgress,g=e.current,v=e.transform,h=e.count,m=e.scale,x=e.minScale,S=e.maxScale,C=e.closeIcon,O=e.onActive,k=e.onClose,A=e.onZoomIn,P=e.onZoomOut,D=e.onRotateRight,M=e.onRotateLeft,T=e.onFlipX,B=e.onFlipY,N=e.onReset,R=e.toolbarRender,z=e.zIndex,j=e.image,_=(0,a.useContext)(E),F=s.rotateLeft,L=s.rotateRight,I=s.zoomIn,K=s.zoomOut,W=s.close,H=s.left,X=s.right,Y=s.flipX,Q=s.flipY,q="".concat(o,"-operations-operation");a.useEffect(function(){var e=function(e){e.keyCode===b.A.ESC&&k()};return t&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}},[t]);var V=function(e,t){e.preventDefault(),e.stopPropagation(),O(t)},U=a.useCallback(function(e){var t=e.type,n=e.disabled,r=e.onClick,i=e.icon;return a.createElement("div",{key:t,className:l()(q,"".concat(o,"-operations-operation-").concat(t),(0,d.A)({},"".concat(o,"-operations-operation-disabled"),!!n)),onClick:r},i)},[q,o]),G=f?U({icon:H,onClick:function(e){return V(e,-1)},type:"prev",disabled:0===g}):void 0,Z=f?U({icon:X,onClick:function(e){return V(e,1)},type:"next",disabled:g===h-1}):void 0,J=U({icon:Q,onClick:B,type:"flipY"}),$=U({icon:Y,onClick:T,type:"flipX"}),ee=U({icon:F,onClick:M,type:"rotateLeft"}),et=U({icon:L,onClick:D,type:"rotateRight"}),en=U({icon:K,onClick:P,type:"zoomOut",disabled:m<=x}),er=U({icon:I,onClick:A,type:"zoomIn",disabled:m===S}),ea=a.createElement("div",{className:"".concat(o,"-operations")},J,$,ee,et,en,er);return a.createElement(y.Ay,{visible:t,motionName:n},function(e){var t=e.className,n=e.style;return a.createElement(w.A,{open:!0,getContainer:null!=r?r:document.body},a.createElement("div",{className:l()("".concat(o,"-operations-wrapper"),t,i),style:(0,c.A)((0,c.A)({},n),{},{zIndex:z})},null===C?null:a.createElement("button",{className:"".concat(o,"-close"),onClick:k},C||W),f&&a.createElement(a.Fragment,null,a.createElement("div",{className:l()("".concat(o,"-switch-left"),(0,d.A)({},"".concat(o,"-switch-left-disabled"),0===g)),onClick:function(e){return V(e,-1)}},H),a.createElement("div",{className:l()("".concat(o,"-switch-right"),(0,d.A)({},"".concat(o,"-switch-right-disabled"),g===h-1)),onClick:function(e){return V(e,1)}},X)),a.createElement("div",{className:"".concat(o,"-footer")},p&&a.createElement("div",{className:"".concat(o,"-progress")},u?u(g+1,h):"".concat(g+1," / ").concat(h)),R?R(ea,(0,c.A)((0,c.A)({icons:{prevIcon:G,nextIcon:Z,flipYIcon:J,flipXIcon:$,rotateLeftIcon:ee,rotateRightIcon:et,zoomOutIcon:en,zoomInIcon:er},actions:{onActive:O,onFlipY:B,onFlipX:T,onRotateLeft:M,onRotateRight:D,onZoomOut:P,onZoomIn:A,onReset:N,onClose:k},transform:v},_?{current:g,total:h}:{}),{},{image:j})):ea)))})};var S=n(74365),C=n(35884),O={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1},k=n(97789);function A(e,t,n,r){var a=t+n,o=(n-r)/2;if(n>r){if(t>0)return(0,d.A)({},e,o);if(t<0&&a<r)return(0,d.A)({},e,-o)}else if(t<0||a>r)return(0,d.A)({},e,t<0?o:-o);return{}}function P(e,t,n,r){var a=g(),o=a.width,i=a.height,l=null;return e<=o&&t<=i?l={x:0,y:0}:(e>o||t>i)&&(l=(0,c.A)((0,c.A)({},A("x",n,e,o)),A("y",r,t,i))),l}function D(e){var t=e.src,n=e.isCustomPlaceholder,r=e.fallback,o=(0,a.useState)(n?"loading":"normal"),i=(0,u.A)(o,2),l=i[0],s=i[1],c=(0,a.useRef)(!1),d="error"===l;(0,a.useEffect)(function(){var e=!0;return new Promise(function(e){if(!t){e(!1);return}var n=document.createElement("img");n.onerror=function(){return e(!1)},n.onload=function(){return e(!0)},n.src=t}).then(function(t){!t&&e&&s("error")}),function(){e=!1}},[t]),(0,a.useEffect)(function(){n&&!c.current?s("loading"):d&&s("normal")},[t]);var f=function(){s("normal")};return[function(e){c.current=!1,"loading"===l&&null!=e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(c.current=!0,f())},d&&r?{src:r}:{onLoad:f,src:t},l]}function M(e,t){return Math.hypot(e.x-t.x,e.y-t.y)}var T=["fallback","src","imgRef"],B=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],N=function(e){var t=e.fallback,n=e.src,r=e.imgRef,o=(0,p.A)(e,T),i=D({src:n,fallback:t}),l=(0,u.A)(i,2),c=l[0],d=l[1];return a.createElement("img",(0,s.A)({ref:function(e){r.current=e,c(e)}},o,d))};let R=function(e){var t,n,r,o,i,f,v,w,y,A,D,T,R,z,j,_,F,L,I,K,W,H,X,Y,Q,q,V,U,G=e.prefixCls,Z=e.src,J=e.alt,$=e.imageInfo,ee=e.fallback,et=e.movable,en=void 0===et||et,er=e.onClose,ea=e.visible,eo=e.icons,ei=e.rootClassName,el=e.closeIcon,es=e.getContainer,ec=e.current,ed=void 0===ec?0:ec,eu=e.count,ef=void 0===eu?1:eu,ep=e.countRender,eg=e.scaleStep,ev=void 0===eg?.5:eg,eh=e.minScale,em=void 0===eh?1:eh,eb=e.maxScale,ew=void 0===eb?50:eb,ey=e.transitionName,eE=e.maskTransitionName,ex=void 0===eE?"fade":eE,eS=e.imageRender,eC=e.imgCommonProps,eO=e.toolbarRender,ek=e.onTransform,eA=e.onChange,eP=(0,p.A)(e,B),eD=(0,a.useRef)(),eM=(0,a.useContext)(E),eT=eM&&ef>1,eB=eM&&ef>=1,eN=(0,a.useState)(!0),eR=(0,u.A)(eN,2),ez=eR[0],ej=eR[1],e_=(t=(0,a.useRef)(null),n=(0,a.useRef)([]),r=(0,a.useState)(O),i=(o=(0,u.A)(r,2))[0],f=o[1],v=function(e,r){null===t.current&&(n.current=[],t.current=(0,C.A)(function(){f(function(e){var a=e;return n.current.forEach(function(e){a=(0,c.A)((0,c.A)({},a),e)}),t.current=null,null==ek||ek({transform:a,action:r}),a})})),n.current.push((0,c.A)((0,c.A)({},i),e))},{transform:i,resetTransform:function(e){f(O),(0,S.A)(O,i)||null==ek||ek({transform:O,action:e})},updateTransform:v,dispatchZoomChange:function(e,t,n,r,a){var o=eD.current,l=o.width,s=o.height,c=o.offsetWidth,d=o.offsetHeight,u=o.offsetLeft,f=o.offsetTop,p=e,h=i.scale*e;h>ew?(h=ew,p=ew/i.scale):h<em&&(p=(h=a?h:em)/i.scale);var m=null!=r?r:innerHeight/2,b=p-1,w=b*((null!=n?n:innerWidth/2)-i.x-u),y=b*(m-i.y-f),E=i.x-(w-b*l*.5),x=i.y-(y-b*s*.5);if(e<1&&1===h){var S=c*h,C=d*h,O=g(),k=O.width,A=O.height;S<=k&&C<=A&&(E=0,x=0)}v({x:E,y:x,scale:h},t)}}),eF=e_.transform,eL=e_.resetTransform,eI=e_.updateTransform,eK=e_.dispatchZoomChange,eW=(w=eF.rotate,y=eF.scale,A=eF.x,D=eF.y,T=(0,a.useState)(!1),z=(R=(0,u.A)(T,2))[0],j=R[1],_=(0,a.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),F=function(e){ea&&z&&eI({x:e.pageX-_.current.diffX,y:e.pageY-_.current.diffY},"move")},L=function(){if(ea&&z){j(!1);var e=_.current,t=e.transformX,n=e.transformY;if(A!==t&&D!==n){var r=eD.current.offsetWidth*y,a=eD.current.offsetHeight*y,o=eD.current.getBoundingClientRect(),i=o.left,l=o.top,s=w%180!=0,d=P(s?a:r,s?r:a,i,l);d&&eI((0,c.A)({},d),"dragRebound")}}},(0,a.useEffect)(function(){var e,t,n,r;if(en){n=(0,m.A)(window,"mouseup",L,!1),r=(0,m.A)(window,"mousemove",F,!1);try{window.top!==window.self&&(e=(0,m.A)(window.top,"mouseup",L,!1),t=(0,m.A)(window.top,"mousemove",F,!1))}catch(e){(0,k.$e)(!1,"[rc-image] ".concat(e))}}return function(){var a,o,i,l;null===(a=n)||void 0===a||a.remove(),null===(o=r)||void 0===o||o.remove(),null===(i=e)||void 0===i||i.remove(),null===(l=t)||void 0===l||l.remove()}},[ea,z,A,D,w,en]),{isMoving:z,onMouseDown:function(e){en&&0===e.button&&(e.preventDefault(),e.stopPropagation(),_.current={diffX:e.pageX-A,diffY:e.pageY-D,transformX:A,transformY:D},j(!0))},onMouseMove:F,onMouseUp:L,onWheel:function(e){if(ea&&0!=e.deltaY){var t=1+Math.min(Math.abs(e.deltaY/100),1)*ev;e.deltaY>0&&(t=1/t),eK(t,"wheel",e.clientX,e.clientY)}}}),eH=eW.isMoving,eX=eW.onMouseDown,eY=eW.onWheel,eQ=(I=eF.rotate,K=eF.scale,W=eF.x,H=eF.y,X=(0,a.useState)(!1),Q=(Y=(0,u.A)(X,2))[0],q=Y[1],V=(0,a.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),U=function(e){V.current=(0,c.A)((0,c.A)({},V.current),e)},(0,a.useEffect)(function(){var e;return ea&&en&&(e=(0,m.A)(window,"touchmove",function(e){return e.preventDefault()},{passive:!1})),function(){var t;null===(t=e)||void 0===t||t.remove()}},[ea,en]),{isTouching:Q,onTouchStart:function(e){if(en){e.stopPropagation(),q(!0);var t=e.touches,n=void 0===t?[]:t;n.length>1?U({point1:{x:n[0].clientX,y:n[0].clientY},point2:{x:n[1].clientX,y:n[1].clientY},eventType:"touchZoom"}):U({point1:{x:n[0].clientX-W,y:n[0].clientY-H},eventType:"move"})}},onTouchMove:function(e){var t=e.touches,n=void 0===t?[]:t,r=V.current,a=r.point1,o=r.point2,i=r.eventType;if(n.length>1&&"touchZoom"===i){var l={x:n[0].clientX,y:n[0].clientY},s={x:n[1].clientX,y:n[1].clientY},c=function(e,t,n,r){var a=M(e,n),o=M(t,r);if(0===a&&0===o)return[e.x,e.y];var i=a/(a+o);return[e.x+i*(t.x-e.x),e.y+i*(t.y-e.y)]}(a,o,l,s),d=(0,u.A)(c,2),f=d[0],p=d[1];eK(M(l,s)/M(a,o),"touchZoom",f,p,!0),U({point1:l,point2:s,eventType:"touchZoom"})}else"move"===i&&(eI({x:n[0].clientX-a.x,y:n[0].clientY-a.y},"move"),U({eventType:"move"}))},onTouchEnd:function(){if(ea){if(Q&&q(!1),U({eventType:"none"}),em>K)return eI({x:0,y:0,scale:em},"touchZoom");var e=eD.current.offsetWidth*K,t=eD.current.offsetHeight*K,n=eD.current.getBoundingClientRect(),r=n.left,a=n.top,o=I%180!=0,i=P(o?t:e,o?e:t,r,a);i&&eI((0,c.A)({},i),"dragRebound")}}}),eq=eQ.isTouching,eV=eQ.onTouchStart,eU=eQ.onTouchMove,eG=eQ.onTouchEnd,eZ=eF.rotate,eJ=eF.scale,e$=l()((0,d.A)({},"".concat(G,"-moving"),eH));(0,a.useEffect)(function(){ez||ej(!0)},[ez]);var e0=function(e){var t=ed+e;Number.isInteger(t)&&!(t<0)&&!(t>ef-1)&&(ej(!1),eL(e<0?"prev":"next"),null==eA||eA(t,ed))},e1=function(e){ea&&eT&&(e.keyCode===b.A.LEFT?e0(-1):e.keyCode===b.A.RIGHT&&e0(1))};(0,a.useEffect)(function(){var e=(0,m.A)(window,"keydown",e1,!1);return function(){e.remove()}},[ea,eT,ed]);var e2=a.createElement(N,(0,s.A)({},eC,{width:e.width,height:e.height,imgRef:eD,className:"".concat(G,"-img"),alt:J,style:{transform:"translate3d(".concat(eF.x,"px, ").concat(eF.y,"px, 0) scale3d(").concat(eF.flipX?"-":"").concat(eJ,", ").concat(eF.flipY?"-":"").concat(eJ,", 1) rotate(").concat(eZ,"deg)"),transitionDuration:(!ez||eq)&&"0s"},fallback:ee,src:Z,onWheel:eY,onMouseDown:eX,onDoubleClick:function(e){ea&&(1!==eJ?eI({x:0,y:0,scale:1},"doubleClick"):eK(1+ev,"doubleClick",e.clientX,e.clientY))},onTouchStart:eV,onTouchMove:eU,onTouchEnd:eG,onTouchCancel:eG})),e4=(0,c.A)({url:Z,alt:J},$);return a.createElement(a.Fragment,null,a.createElement(h.A,(0,s.A)({transitionName:void 0===ey?"zoom":ey,maskTransitionName:ex,closable:!1,keyboard:!0,prefixCls:G,onClose:er,visible:ea,classNames:{wrapper:e$},rootClassName:ei,getContainer:es},eP,{afterClose:function(){eL("close")}}),a.createElement("div",{className:"".concat(G,"-img-wrapper")},eS?eS(e2,(0,c.A)({transform:eF,image:e4},eM?{current:ed}:{})):e2)),a.createElement(x,{visible:ea,transform:eF,maskTransitionName:ex,closeIcon:el,getContainer:es,prefixCls:G,rootClassName:ei,icons:void 0===eo?{}:eo,countRender:ep,showSwitch:eT,showProgress:eB,current:ed,count:ef,scale:eJ,minScale:em,maxScale:ew,toolbarRender:eO,onActive:e0,onZoomIn:function(){eK(1+ev,"zoomIn")},onZoomOut:function(){eK(1/(1+ev),"zoomOut")},onRotateRight:function(){eI({rotate:eZ+90},"rotateRight")},onRotateLeft:function(){eI({rotate:eZ-90},"rotateLeft")},onFlipX:function(){eI({flipX:!eF.flipX},"flipX")},onFlipY:function(){eI({flipY:!eF.flipY},"flipY")},onClose:er,onReset:function(){eL("reset")},zIndex:void 0!==eP.zIndex?eP.zIndex+1:void 0,image:e4}))};var z=n(53172),j=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"],_=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],F=["src"],L=0,I=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],K=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],W=function(e){var t,n,r,o,i=e.src,g=e.alt,h=e.onPreviewClose,m=e.prefixCls,b=void 0===m?"rc-image":m,w=e.previewPrefixCls,y=void 0===w?"".concat(b,"-preview"):w,x=e.placeholder,S=e.fallback,C=e.width,O=e.height,k=e.style,A=e.preview,P=void 0===A||A,M=e.className,T=e.onClick,B=e.onError,N=e.wrapperClassName,z=e.wrapperStyle,_=e.rootClassName,F=(0,p.A)(e,I),W="object"===(0,f.A)(P)?P:{},H=W.src,X=W.visible,Y=void 0===X?void 0:X,Q=W.onVisibleChange,q=W.getContainer,V=W.mask,U=W.maskClassName,G=W.movable,Z=W.icons,J=W.scaleStep,$=W.minScale,ee=W.maxScale,et=W.imageRender,en=W.toolbarRender,er=(0,p.A)(W,K),ea=null!=H?H:i,eo=(0,v.A)(!!Y,{value:Y,onChange:void 0===Q?h:Q}),ei=(0,u.A)(eo,2),el=ei[0],es=ei[1],ec=D({src:i,isCustomPlaceholder:x&&!0!==x,fallback:S}),ed=(0,u.A)(ec,3),eu=ed[0],ef=ed[1],ep=ed[2],eg=(0,a.useState)(null),ev=(0,u.A)(eg,2),eh=ev[0],em=ev[1],eb=(0,a.useContext)(E),ew=!!P,ey=l()(b,N,_,(0,d.A)({},"".concat(b,"-error"),"error"===ep)),eE=(0,a.useMemo)(function(){var t={};return j.forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),t},j.map(function(t){return e[t]})),ex=(0,a.useMemo)(function(){return(0,c.A)((0,c.A)({},eE),{},{src:ea})},[ea,eE]),eS=(t=a.useState(function(){return String(L+=1)}),n=(0,u.A)(t,1)[0],r=a.useContext(E),o={data:ex,canPreview:ew},a.useEffect(function(){if(r)return r.register(n,o)},[]),a.useEffect(function(){r&&r.register(n,o)},[ew,ex]),n);return a.createElement(a.Fragment,null,a.createElement("div",(0,s.A)({},F,{className:ey,onClick:ew?function(e){var t,n,r=(t=e.target.getBoundingClientRect(),n=document.documentElement,{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}),a=r.left,o=r.top;eb?eb.onPreview(eS,ea,a,o):(em({x:a,y:o}),es(!0)),null==T||T(e)}:T,style:(0,c.A)({width:C,height:O},z)}),a.createElement("img",(0,s.A)({},eE,{className:l()("".concat(b,"-img"),(0,d.A)({},"".concat(b,"-img-placeholder"),!0===x),M),style:(0,c.A)({height:O},k),ref:eu},ef,{width:C,height:O,onError:B})),"loading"===ep&&a.createElement("div",{"aria-hidden":"true",className:"".concat(b,"-placeholder")},x),V&&ew&&a.createElement("div",{className:l()("".concat(b,"-mask"),U),style:{display:(null==k?void 0:k.display)==="none"?"none":void 0}},V)),!eb&&ew&&a.createElement(R,(0,s.A)({"aria-hidden":!el,visible:el,prefixCls:y,onClose:function(){es(!1),em(null)},mousePosition:eh,src:ea,alt:g,imageInfo:{width:C,height:O},fallback:S,getContainer:void 0===q?void 0:q,icons:Z,movable:G,scaleStep:J,minScale:$,maxScale:ee,rootClassName:_,imageRender:et,imgCommonProps:eE,toolbarRender:en},er)))};W.PreviewGroup=function(e){var t,n,r,o,i,l,g=e.previewPrefixCls,h=e.children,m=e.icons,b=e.items,w=e.preview,y=e.fallback,x="object"===(0,f.A)(w)?w:{},S=x.visible,C=x.onVisibleChange,O=x.getContainer,k=x.current,A=x.movable,P=x.minScale,D=x.maxScale,M=x.countRender,T=x.closeIcon,B=x.onChange,N=x.onTransform,L=x.toolbarRender,I=x.imageRender,K=(0,p.A)(x,_),W=(t=a.useState({}),r=(n=(0,u.A)(t,2))[0],o=n[1],i=a.useCallback(function(e,t){return o(function(n){return(0,c.A)((0,c.A)({},n),{},(0,d.A)({},e,t))}),function(){o(function(t){var n=(0,c.A)({},t);return delete n[e],n})}},[]),[a.useMemo(function(){return b?b.map(function(e){if("string"==typeof e)return{data:{src:e}};var t={};return Object.keys(e).forEach(function(n){["src"].concat((0,z.A)(j)).includes(n)&&(t[n]=e[n])}),{data:t}}):Object.keys(r).reduce(function(e,t){var n=r[t],a=n.canPreview,o=n.data;return a&&e.push({data:o,id:t}),e},[])},[b,r]),i,!!b]),H=(0,u.A)(W,3),X=H[0],Y=H[1],Q=H[2],q=(0,v.A)(0,{value:k}),V=(0,u.A)(q,2),U=V[0],G=V[1],Z=(0,a.useState)(!1),J=(0,u.A)(Z,2),$=J[0],ee=J[1],et=(null===(l=X[U])||void 0===l?void 0:l.data)||{},en=et.src,er=(0,p.A)(et,F),ea=(0,v.A)(!!S,{value:S,onChange:function(e,t){null==C||C(e,t,U)}}),eo=(0,u.A)(ea,2),ei=eo[0],el=eo[1],es=(0,a.useState)(null),ec=(0,u.A)(es,2),ed=ec[0],eu=ec[1],ef=a.useCallback(function(e,t,n,r){var a=Q?X.findIndex(function(e){return e.data.src===t}):X.findIndex(function(t){return t.id===e});G(a<0?0:a),el(!0),eu({x:n,y:r}),ee(!0)},[X,Q]);a.useEffect(function(){ei?$||G(0):ee(!1)},[ei]);var ep=a.useMemo(function(){return{register:Y,onPreview:ef}},[Y,ef]);return a.createElement(E.Provider,{value:ep},h,a.createElement(R,(0,s.A)({"aria-hidden":!ei,movable:A,visible:ei,prefixCls:void 0===g?"rc-image-preview":g,closeIcon:T,onClose:function(){el(!1),eu(null)},mousePosition:ed,imgCommonProps:er,src:en,fallback:y,icons:void 0===m?{}:m,minScale:P,maxScale:D,getContainer:O,current:U,count:X.length,countRender:M,onTransform:N,toolbarRender:L,imageRender:I,onChange:function(e,t){G(e),null==B||B(e,t)}},K)))};var H=n(13e3),X=n(33984),Y=n(77312),Q=n(50945),q=n(89132),V=n(8324),U=n(31259),G=n(73208),Z=n(20078),J=n(54695),$=n(11338),ee=n(39510),et=n(37177),en=n(64467),er=n(16700),ea=n(83320),eo=n(5214),ei=n(10186),el=n(54259),es=n(68197),ec=n(13440),ed=function(e){return{position:e||"absolute",inset:0}},eu=function(e){var t=e.iconCls,n=e.motionDurationSlow,r=e.paddingXXS,a=e.marginXXS,o=e.prefixCls;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:e.colorTextLightSolid,background:new er.Y("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:"opacity ".concat(n),[".".concat(o,"-mask-info")]:Object.assign(Object.assign({},eo.L9),{padding:"0 ".concat((0,en.zA)(r)),[t]:{marginInlineEnd:a,svg:{verticalAlign:"baseline"}}})}},ef=function(e){var t=e.previewCls,n=e.modalMaskBg,r=e.paddingSM,a=e.marginXL,o=e.margin,i=e.paddingLG,l=e.previewOperationColorDisabled,s=e.previewOperationHoverColor,c=e.motionDurationSlow,d=e.iconCls,u=e.colorTextLightSolid,f=new er.Y(n).setA(.1),p=f.clone().setA(.2);return{["".concat(t,"-footer")]:{position:"fixed",bottom:a,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},["".concat(t,"-progress")]:{marginBottom:o},["".concat(t,"-close")]:{position:"fixed",top:a,right:{_skip_check_:!0,value:a},display:"flex",color:u,backgroundColor:f.toRgbString(),borderRadius:"50%",padding:r,outline:0,border:0,cursor:"pointer",transition:"all ".concat(c),"&:hover":{backgroundColor:p.toRgbString()},["& > ".concat(d)]:{fontSize:e.previewOperationSize}},["".concat(t,"-operations")]:{display:"flex",alignItems:"center",padding:"0 ".concat((0,en.zA)(i)),backgroundColor:f.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:r,padding:r,cursor:"pointer",transition:"all ".concat(c),userSelect:"none",["&:not(".concat(t,"-operations-operation-disabled):hover > ").concat(d)]:{color:s},"&-disabled":{color:l,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},["& > ".concat(d)]:{fontSize:e.previewOperationSize}}}}},ep=function(e){var t=e.modalMaskBg,n=e.iconCls,r=e.previewOperationColorDisabled,a=e.previewCls,o=e.zIndexPopup,i=e.motionDurationSlow,l=new er.Y(t).setA(.1),s=l.clone().setA(.2);return{["".concat(a,"-switch-left, ").concat(a,"-switch-right")]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(o).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:l.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:"all ".concat(i),userSelect:"none","&:hover":{background:s.toRgbString()},"&-disabled":{"&, &:hover":{color:r,background:"transparent",cursor:"not-allowed",["> ".concat(n)]:{cursor:"not-allowed"}}},["> ".concat(n)]:{fontSize:e.previewOperationSize}},["".concat(a,"-switch-left")]:{insetInlineStart:e.marginSM},["".concat(a,"-switch-right")]:{insetInlineEnd:e.marginSM}}},eg=function(e){var t=e.motionEaseOut,n=e.previewCls,r=e.motionDurationSlow,a=e.componentCls;return[{["".concat(a,"-preview-root")]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},["".concat(n,"-body")]:Object.assign(Object.assign({},ed()),{overflow:"hidden"}),["".concat(n,"-img")]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:"transform ".concat(r," ").concat(t," 0s"),userSelect:"none","&-wrapper":Object.assign(Object.assign({},ed()),{transition:"transform ".concat(r," ").concat(t," 0s"),display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},["".concat(n,"-moving")]:{["".concat(n,"-preview-img")]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{["".concat(a,"-preview-root")]:{["".concat(n,"-wrap")]:{zIndex:e.zIndexPopup}}},{["".concat(a,"-preview-operations-wrapper")]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[ef(e),ep(e)]}]},ev=function(e){var t=e.componentCls;return{[t]:{position:"relative",display:"inline-block",["".concat(t,"-img")]:{width:"100%",height:"auto",verticalAlign:"middle"},["".concat(t,"-img-placeholder")]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},["".concat(t,"-mask")]:Object.assign({},eu(e)),["".concat(t,"-mask:hover")]:{opacity:1},["".concat(t,"-placeholder")]:Object.assign({},ed())}}},eh=function(e){var t=e.previewCls;return{["".concat(t,"-root")]:(0,ei.aB)(e,"zoom"),"&":(0,el.p9)(e,!0)}};let em=(0,es.OF)("Image",function(e){var t="".concat(e.componentCls,"-preview"),n=(0,ec.oX)(e,{previewCls:t,modalMaskBg:new er.Y("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[ev(n),eg(n),(0,ea.Dk)((0,ec.oX)(n,{componentCls:t})),eh(n)]},function(e){return{zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new er.Y(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new er.Y(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new er.Y(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon}});var eb=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},ew={rotateLeft:a.createElement(Z.A,null),rotateRight:a.createElement(J.A,null),zoomIn:a.createElement(ee.A,null),zoomOut:a.createElement(et.A,null),close:a.createElement(V.A,null),left:a.createElement(U.A,null),right:a.createElement(G.A,null),flipX:a.createElement($.A,null),flipY:a.createElement($.A,{rotate:90})},ey=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},eE=function(e){var t=e.prefixCls,n=e.preview,i=e.className,s=e.rootClassName,c=e.style,d=ey(e,["prefixCls","preview","className","rootClassName","style"]),u=(0,Y.TP)("image"),f=u.getPrefixCls,p=u.getPopupContainer,g=u.className,v=u.style,h=u.preview,m=(0,r._)((0,q.A)("Image"),1)[0],b=f("image",t),w=f(),y=(0,Q.A)(b),E=(0,r._)(em(b,y),3),x=E[0],S=E[1],C=E[2],O=l()(s,S,C,y),k=l()(i,S,g),A=(0,r._)((0,H.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),1)[0],P=a.useMemo(function(){if(!1===n)return n;var e="object"==typeof n?n:{},t=e.getContainer,r=e.closeIcon,i=e.rootClassName,s=ey(e,["getContainer","closeIcon","rootClassName"]);return Object.assign(Object.assign({mask:a.createElement("div",{className:"".concat(b,"-mask-info")},a.createElement(o.A,null),null==m?void 0:m.preview),icons:ew},s),{rootClassName:l()(O,i),getContainer:null!=t?t:p,transitionName:(0,X.b)(w,"zoom",e.transitionName),maskTransitionName:(0,X.b)(w,"fade",e.maskTransitionName),zIndex:A,closeIcon:null!=r?r:null==h?void 0:h.closeIcon})},[n,m,null==h?void 0:h.closeIcon]),D=Object.assign(Object.assign({},v),c);return x(a.createElement(W,Object.assign({prefixCls:b,preview:P,rootClassName:O,className:k,style:D},d)))};eE.PreviewGroup=function(e){var t=e.previewPrefixCls,n=e.preview,o=eb(e,["previewPrefixCls","preview"]),i=a.useContext(Y.QO).getPrefixCls,s=i("image",t),c="".concat(s,"-preview"),d=i(),u=(0,Q.A)(s),f=(0,r._)(em(s,u),3),p=f[0],g=f[1],v=f[2],h=(0,r._)((0,H.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),1)[0],m=a.useMemo(function(){if(!1===n)return n;var e,t="object"==typeof n?n:{},r=l()(g,v,u,null!==(e=t.rootClassName)&&void 0!==e?e:"");return Object.assign(Object.assign({},t),{transitionName:(0,X.b)(d,"zoom",t.transitionName),maskTransitionName:(0,X.b)(d,"fade",t.maskTransitionName),rootClassName:r,zIndex:h})},[n]);return p(a.createElement(W.PreviewGroup,Object.assign({preview:m,previewPrefixCls:c,icons:ew},o)))};let ex=eE},95495:(e,t,n)=>{"use strict";n.d(t,{A:()=>P});var r=n(12694),a=n(21462),o=n(46001),i=n.n(o),l=n(32e3),s=n(59744),c=n(29694),d=n(77312),u=n(50945),f=n(80382),p=n(42248),g=a.createContext({}),v=n(64467),h=n(5214),m=n(68197),b=n(13440),w=function(e){var t=e.antCls,n=e.componentCls,r=e.iconCls,a=e.avatarBg,o=e.avatarColor,i=e.containerSize,l=e.containerSizeLG,s=e.containerSizeSM,c=e.textFontSize,d=e.textFontSizeLG,u=e.textFontSizeSM,f=e.borderRadius,p=e.borderRadiusLG,g=e.borderRadiusSM,m=e.lineWidth,b=e.lineType,w=function(e,t,a){return{width:e,height:e,borderRadius:"50%",["&".concat(n,"-square")]:{borderRadius:a},["&".concat(n,"-icon")]:{fontSize:t,["> ".concat(r)]:{margin:0}}}};return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,h.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:o,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:a,border:"".concat((0,v.zA)(m)," ").concat(b," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),w(i,c,f)),{"&-lg":Object.assign({},w(l,d,p)),"&-sm":Object.assign({},w(s,u,g)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=function(e){var t=e.componentCls,n=e.groupBorderColor,r=e.groupOverlapping,a=e.groupSpace;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:a}}}};let E=(0,m.OF)("Avatar",function(e){var t=e.colorTextLightSolid,n=e.colorTextPlaceholder,r=(0,b.oX)(e,{avatarBg:n,avatarColor:t});return[w(r),y(r)]},function(e){var t=e.controlHeight,n=e.controlHeightLG,r=e.controlHeightSM,a=e.fontSize,o=e.fontSizeLG,i=e.fontSizeXL,l=e.fontSizeHeading3,s=e.marginXS;return{containerSize:t,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((o+i)/2),textFontSizeLG:l,textFontSizeSM:a,groupSpace:e.marginXXS,groupOverlapping:-s,groupBorderColor:e.colorBorderBg}});var x=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},S=a.forwardRef(function(e,t){var n,o=(0,r._)(a.useState(1),2),v=o[0],h=o[1],m=(0,r._)(a.useState(!1),2),b=m[0],w=m[1],y=(0,r._)(a.useState(!0),2),S=y[0],C=y[1],O=a.useRef(null),k=a.useRef(null),A=(0,s.K4)(t,O),P=a.useContext(d.QO),D=P.getPrefixCls,M=P.avatar,T=a.useContext(g),B=function(){if(k.current&&O.current){var t=k.current.offsetWidth,n=O.current.offsetWidth;if(0!==t&&0!==n){var r=e.gap,a=void 0===r?4:r;2*a<n&&h(n-2*a<t?(n-2*a)/t:1)}}};a.useEffect(function(){w(!0)},[]),a.useEffect(function(){C(!0),h(1)},[e.src]),a.useEffect(B,[e.gap]);var N=e.prefixCls,R=e.shape,z=e.size,j=e.src,_=e.srcSet,F=e.icon,L=e.className,I=e.rootClassName,K=e.alt,W=e.draggable,H=e.children,X=e.crossOrigin,Y=x(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),Q=(0,f.A)(function(e){var t,n;return null!==(n=null!==(t=null!=z?z:null==T?void 0:T.size)&&void 0!==t?t:e)&&void 0!==n?n:"default"}),q=Object.keys("object"==typeof Q&&Q||{}).some(function(e){return["xs","sm","md","lg","xl","xxl"].includes(e)}),V=(0,p.A)(q),U=a.useMemo(function(){if("object"!=typeof Q)return{};var e=Q[c.ye.find(function(e){return V[e]})];return e?{width:e,height:e,fontSize:e&&(F||H)?e/2:18}:{}},[V,Q]),G=D("avatar",N),Z=(0,u.A)(G),J=(0,r._)(E(G,Z),3),$=J[0],ee=J[1],et=J[2],en=i()({["".concat(G,"-lg")]:"large"===Q,["".concat(G,"-sm")]:"small"===Q}),er=a.isValidElement(j),ea=R||(null==T?void 0:T.shape)||"circle",eo=i()(G,en,null==M?void 0:M.className,"".concat(G,"-").concat(ea),{["".concat(G,"-image")]:er||j&&S,["".concat(G,"-icon")]:!!F},et,Z,L,I,ee),ei="number"==typeof Q?{width:Q,height:Q,fontSize:F?Q/2:18}:{};if("string"==typeof j&&S)n=a.createElement("img",{src:j,draggable:W,srcSet:_,onError:function(){var t=e.onError;!1!==(null==t?void 0:t())&&C(!1)},alt:K,crossOrigin:X});else if(er)n=j;else if(F)n=F;else if(b||1!==v){var el="scale(".concat(v,")");n=a.createElement(l.A,{onResize:B},a.createElement("span",{className:"".concat(G,"-string"),ref:k,style:Object.assign({},{msTransform:el,WebkitTransform:el,transform:el})},H))}else n=a.createElement("span",{className:"".concat(G,"-string"),style:{opacity:0},ref:k},H);return delete Y.onError,delete Y.gap,$(a.createElement("span",Object.assign({},Y,{style:Object.assign(Object.assign(Object.assign(Object.assign({},ei),U),null==M?void 0:M.style),Y.style),className:eo,ref:A}),n))}),C=n(721),O=n(49357),k=n(41209),A=function(e){var t=a.useContext(g),n=t.size,r=t.shape,o=a.useMemo(function(){return{size:e.size||n,shape:e.shape||r}},[e.size,e.shape,n,r]);return a.createElement(g.Provider,{value:o},e.children)};S.Group=function(e){var t,n,o,l,s=a.useContext(d.QO),c=s.getPrefixCls,f=s.direction,p=e.prefixCls,g=e.className,v=e.rootClassName,h=e.style,m=e.maxCount,b=e.maxStyle,w=e.size,y=e.shape,x=e.maxPopoverPlacement,P=e.maxPopoverTrigger,D=e.children,M=e.max,T=c("avatar",p),B="".concat(T,"-group"),N=(0,u.A)(T),R=(0,r._)(E(T,N),3),z=R[0],j=R[1],_=R[2],F=i()(B,{["".concat(B,"-rtl")]:"rtl"===f},_,N,g,v,j),L=(0,C.A)(D).map(function(e,t){return(0,O.Ob)(e,{key:"avatar-key-".concat(t)})}),I=(null==M?void 0:M.count)||m,K=L.length;if(I&&I<K){var W=L.slice(0,I),H=L.slice(I,K),X=(null==M?void 0:M.style)||b,Y=(null===(t=null==M?void 0:M.popover)||void 0===t?void 0:t.trigger)||P||"hover",Q=(null===(n=null==M?void 0:M.popover)||void 0===n?void 0:n.placement)||x||"top",q=Object.assign(Object.assign({content:H},null==M?void 0:M.popover),{classNames:{root:i()("".concat(B,"-popover"),null===(l=null===(o=null==M?void 0:M.popover)||void 0===o?void 0:o.classNames)||void 0===l?void 0:l.root)},placement:Q,trigger:Y});return W.push(a.createElement(k.A,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},q),a.createElement(S,{style:X},"+".concat(K-I)))),z(a.createElement(A,{shape:y,size:w},a.createElement("div",{className:F,style:h},W)))}return z(a.createElement(A,{shape:y,size:w},a.createElement("div",{className:F,style:h},L)))};let P=S},99962:(e,t,n)=>{var r=n(60929),a=n(54557);e.exports=function(e,t,n){var o=!0,i=!0;if("function"!=typeof e)throw TypeError("Expected a function");return a(n)&&(o="leading"in n?!!n.leading:o,i="trailing"in n?!!n.trailing:i),r(e,t,{leading:o,maxWait:t,trailing:i})}}}]);