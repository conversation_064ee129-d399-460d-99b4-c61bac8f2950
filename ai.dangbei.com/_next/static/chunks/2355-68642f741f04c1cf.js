"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2355],{41209:(t,e,n)=>{n.d(e,{A:()=>N});var o=n(12694),r=n(21462),a=n(46001),i=n.n(a),l=n(39074),c=n(15191),d=function(t){return t?"function"==typeof t?t():t:null},s=n(33984),p=n(49357),g=n(39652),u=n(84975),f=n(77312),m=n(5214),b=n(10186),v=n(33542),h=n(54044),y=n(937),x=n(68197),O=n(13440),w=function(t){var e=t.componentCls,n=t.popoverColor,o=t.titleMinWidth,r=t.fontWeightStrong,a=t.innerPadding,i=t.boxShadowSecondary,l=t.colorTextHeading,c=t.borderRadiusLG,d=t.zIndexPopup,s=t.titleMarginBottom,p=t.colorBgElevated,g=t.popoverBg,u=t.titleBorderBottom,f=t.innerContentPadding,b=t.titlePadding;return[{[e]:Object.assign(Object.assign({},(0,m.dF)(t)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":p,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},["".concat(e,"-content")]:{position:"relative"},["".concat(e,"-inner")]:{backgroundColor:g,backgroundClip:"padding-box",borderRadius:c,boxShadow:i,padding:a},["".concat(e,"-title")]:{minWidth:o,marginBottom:s,color:l,fontWeight:r,borderBottom:u,padding:b},["".concat(e,"-inner-content")]:{color:n,padding:f}})},(0,v.Ay)(t,"var(--antd-arrow-background-color)"),{["".concat(e,"-pure")]:{position:"relative",maxWidth:"none",margin:t.sizePopupArrow,display:"inline-block",["".concat(e,"-content")]:{display:"inline-block"}}}]},C=function(t){var e=t.componentCls;return{[e]:y.s.map(function(n){var o=t["".concat(n,"6")];return{["&".concat(e,"-").concat(n)]:{"--antd-arrow-background-color":o,["".concat(e,"-inner")]:{backgroundColor:o},["".concat(e,"-arrow")]:{background:"transparent"}}}})}};let S=(0,x.OF)("Popover",function(t){var e=t.colorBgElevated,n=t.colorText,o=(0,O.oX)(t,{popoverBg:e,popoverColor:n});return[w(o),C(o),(0,b.aB)(o,"zoom-big")]},function(t){var e=t.lineWidth,n=t.controlHeight,o=t.fontHeight,r=t.padding,a=t.wireframe,i=t.zIndexPopupBase,l=t.borderRadiusLG,c=t.marginXS,d=t.lineType,s=t.colorSplit,p=t.paddingSM,g=n-o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:i+30},(0,h.n)(t)),(0,v.Ke)({contentRadius:l,limitVerticalRadius:!0})),{innerPadding:12*!a,titleMarginBottom:a?0:c,titlePadding:a?"".concat(g/2,"px ").concat(r,"px ").concat(g/2-e,"px"):0,titleBorderBottom:a?"".concat(e,"px ").concat(d," ").concat(s):"none",innerContentPadding:a?"".concat(p,"px ").concat(r,"px"):0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var z=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>e.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n},E=function(t){var e=t.title,n=t.content,o=t.prefixCls;return e||n?r.createElement(r.Fragment,null,e&&r.createElement("div",{className:"".concat(o,"-title")},e),n&&r.createElement("div",{className:"".concat(o,"-inner-content")},n)):null},j=function(t){var e=t.hashId,n=t.prefixCls,o=t.className,a=t.style,l=t.placement,c=t.title,s=t.content,p=t.children,g=d(c),f=d(s),m=i()(e,n,"".concat(n,"-pure"),"".concat(n,"-placement-").concat(void 0===l?"top":l),o);return r.createElement("div",{className:m,style:a},r.createElement("div",{className:"".concat(n,"-arrow")}),r.createElement(u.z,Object.assign({},t,{className:e,prefixCls:n}),p||r.createElement(E,{prefixCls:n,title:g,content:f})))},k=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>e.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n},P=r.forwardRef(function(t,e){var n,a,u=t.prefixCls,m=t.title,b=t.content,v=t.overlayClassName,h=t.placement,y=t.trigger,x=t.children,O=t.mouseEnterDelay,w=t.mouseLeaveDelay,C=t.onOpenChange,z=t.overlayStyle,j=t.styles,P=t.classNames,N=k(t,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),B=(0,f.TP)("popover"),I=B.getPrefixCls,W=B.className,M=B.style,A=B.classNames,_=B.styles,T=I("popover",u),D=(0,o._)(S(T),3),H=D[0],G=D[1],L=D[2],F=I(),R=i()(v,G,L,W,A.root,null==P?void 0:P.root),V=i()(A.body,null==P?void 0:P.body),X=(0,o._)((0,l.A)(!1,{value:null!==(n=t.open)&&void 0!==n?n:t.visible,defaultValue:null!==(a=t.defaultOpen)&&void 0!==a?a:t.defaultVisible}),2),K=X[0],Y=X[1],Q=function(t,e){Y(t,!0),null==C||C(t,e)},U=function(t){t.keyCode===c.A.ESC&&Q(!1,t)},$=d(m),q=d(b);return H(r.createElement(g.A,Object.assign({placement:void 0===h?"top":h,trigger:void 0===y?"hover":y,mouseEnterDelay:void 0===O?.1:O,mouseLeaveDelay:void 0===w?.1:w},N,{prefixCls:T,classNames:{root:R,body:V},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},_.root),M),void 0===z?{}:z),null==j?void 0:j.root),body:Object.assign(Object.assign({},_.body),null==j?void 0:j.body)},ref:e,open:K,onOpenChange:function(t){Q(t)},overlay:$||q?r.createElement(E,{prefixCls:T,title:$,content:q}):null,transitionName:(0,s.b)(F,"zoom-big",N.transitionName),"data-popover-inject":!0}),(0,p.Ob)(x,{onKeyDown:function(t){var e,n;r.isValidElement(x)&&(null===(n=null==x?void 0:(e=x.props).onKeyDown)||void 0===n||n.call(e,t)),U(t)}})))});P._InternalPanelDoNotUseOrYouWillBeFired=function(t){var e=t.prefixCls,n=t.className,a=z(t,["prefixCls","className"]),l=(0,r.useContext(f.QO).getPrefixCls)("popover",e),c=(0,o._)(S(l),3),d=c[0],s=c[1],p=c[2];return d(r.createElement(j,Object.assign({},a,{prefixCls:l,hashId:s,className:i()(n,p)})))};let N=P},49539:(t,e,n)=>{n.d(e,{A:()=>m});var o=n(12694),r=n(21462),a=n(46001),i=n.n(a),l=n(77312),c=n(64467),d=n(5214),s=n(68197),p=n(13440),g=function(t){var e=t.componentCls,n=t.sizePaddingEdgeHorizontal,o=t.colorSplit,r=t.lineWidth,a=t.textPaddingInline,i=t.orientationMargin,l=t.verticalMarginInline;return{[e]:Object.assign(Object.assign({},(0,d.dF)(t)),{borderBlockStart:"".concat((0,c.zA)(r)," solid ").concat(o),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,c.zA)(r)," solid ").concat(o)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,c.zA)(t.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(e,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,c.zA)(t.dividerHorizontalWithTextGutterMargin)," 0"),color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(o),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,c.zA)(r)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(e,"-with-text-start")]:{"&::before":{width:"calc(".concat(i," * 100%)")},"&::after":{width:"calc(100% - ".concat(i," * 100%)")}},["&-horizontal".concat(e,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(i," * 100%)")},"&::after":{width:"calc(".concat(i," * 100%)")}},["".concat(e,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:a},"&-dashed":{background:"none",borderColor:o,borderStyle:"dashed",borderWidth:"".concat((0,c.zA)(r)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(e,"-dashed")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:o,borderStyle:"dotted",borderWidth:"".concat((0,c.zA)(r)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(e,"-dotted")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(e,"-with-text")]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},["&-horizontal".concat(e,"-with-text-start").concat(e,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(e,"-inner-text")]:{paddingInlineStart:n}},["&-horizontal".concat(e,"-with-text-end").concat(e,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(e,"-inner-text")]:{paddingInlineEnd:n}}})}};let u=(0,s.OF)("Divider",function(t){return[g((0,p.oX)(t,{dividerHorizontalWithTextGutterMargin:t.margin,dividerHorizontalGutterMargin:t.marginLG,sizePaddingEdgeHorizontal:0}))]},function(t){return{textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}},{unitless:{orientationMargin:!0}});var f=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>e.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n};let m=function(t){var e=(0,l.TP)("divider"),n=e.getPrefixCls,a=e.direction,c=e.className,d=e.style,s=t.prefixCls,p=t.type,g=void 0===p?"horizontal":p,m=t.orientation,b=void 0===m?"center":m,v=t.orientationMargin,h=t.className,y=t.rootClassName,x=t.children,O=t.dashed,w=t.variant,C=void 0===w?"solid":w,S=t.plain,z=t.style,E=f(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),j=n("divider",s),k=(0,o._)(u(j),3),P=k[0],N=k[1],B=k[2],I=!!x,W=r.useMemo(function(){return"left"===b?"rtl"===a?"end":"start":"right"===b?"rtl"===a?"start":"end":b},[a,b]),M="start"===W&&null!=v,A="end"===W&&null!=v,_=i()(j,c,N,B,"".concat(j,"-").concat(g),{["".concat(j,"-with-text")]:I,["".concat(j,"-with-text-").concat(W)]:I,["".concat(j,"-dashed")]:!!O,["".concat(j,"-").concat(C)]:"solid"!==C,["".concat(j,"-plain")]:!!S,["".concat(j,"-rtl")]:"rtl"===a,["".concat(j,"-no-default-orientation-margin-start")]:M,["".concat(j,"-no-default-orientation-margin-end")]:A},h,y),T=r.useMemo(function(){return"number"==typeof v?v:/^\d+$/.test(v)?Number(v):v},[v]);return P(r.createElement("div",Object.assign({className:_,style:Object.assign(Object.assign({},d),z)},E,{role:"separator"}),x&&"vertical"!==g&&r.createElement("span",{className:"".concat(j,"-inner-text"),style:{marginInlineStart:M?T:void 0,marginInlineEnd:A?T:void 0}},x)))}}}]);