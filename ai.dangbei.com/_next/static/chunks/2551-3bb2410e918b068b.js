(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2551],{72551:e=>{e.exports=function(e,n){var i,t,o,r,a,c,s,d,l,u,p,f,m,g,h,S,y,I,v,_,w,k;if(!e){console.warn("can't use weixin-js-sdk in server side");return}if(!e.jWeixin)return i={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},t=function(){var e,n={};for(e in i)n[i[e]]=e;return n}(),r=(o=e.document).title,a=navigator.userAgent.toLowerCase(),c=!(!(f=navigator.platform.toLowerCase()).match("mac")&&!f.match("win")),s=-1!=a.indexOf("wxdebugger"),d=-1!=a.indexOf("micromessenger"),l=-1!=a.indexOf("android"),u=-1!=a.indexOf("iphone")||-1!=a.indexOf("ipad"),p=(f=a.match(/micromessenger\/(\d+\.\d+\.\d+)/)||a.match(/micromessenger\/(\d+\.\d+)/))?f[1]:"",m={initStartTime:L(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},g={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:u?1:l?2:-1,clientVersion:p,url:encodeURIComponent(location.href)},h={},S={_completes:[]},y={state:0,data:{}},N(function(){m.initEndTime=L()}),I=!1,v=[],_={config:function(n){C("config",h=n);var t=!1!==h.check;N(function(){if(t)T(i.config,{verifyJsApiList:A(h.jsApiList),verifyOpenTagList:A(h.openTagList)},(S._complete=function(e){m.preVerifyEndTime=L(),y.state=1,y.data=e},S.success=function(e){g.isPreVerifyOk=0},S.fail=function(e){S._fail?S._fail(e):y.state=-1},(e=S._completes).push(function(){B()}),S.complete=function(n){for(var i=0,t=e.length;i<t;++i)e[i]();S._completes=[]},S)),m.preVerifyStartTime=L();else{y.state=1;for(var e,n=S._completes,o=0,r=n.length;o<r;++o)n[o]();S._completes=[]}}),_.invoke||(_.invoke=function(n,i,t){e.WeixinJSBridge&&WeixinJSBridge.invoke(n,P(i),t)},_.on=function(n,i){e.WeixinJSBridge&&WeixinJSBridge.on(n,i)})},ready:function(e){(0!=y.state||(S._completes.push(e),!d&&h.debug))&&e()},error:function(e){p<"6.0.2"||(-1==y.state?e(y.data):S._fail=e)},checkJsApi:function(e){T("checkJsApi",{jsApiList:A(e.jsApiList)},(e._complete=function(e){l&&(i=e.checkResult)&&(e.checkResult=JSON.parse(i));var n,i=e,o=i.checkResult;for(n in o){var r=t[n];r&&(o[r]=o[n],delete o[n])}},e))},onMenuShareTimeline:function(e){M(i.onMenuShareTimeline,{complete:function(){T("shareTimeline",{title:e.title||r,desc:e.title||r,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){M(i.onMenuShareAppMessage,{complete:function(n){"favorite"===n.scene?T("sendAppMessage",{title:e.title||r,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):T("sendAppMessage",{title:e.title||r,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){M(i.onMenuShareQQ,{complete:function(){T("shareQQ",{title:e.title||r,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){M(i.onMenuShareWeibo,{complete:function(){T("shareWeiboApp",{title:e.title||r,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){M(i.onMenuShareQZone,{complete:function(){T("shareQZone",{title:e.title||r,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){T("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){T("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){T("startRecord",{},e)},stopRecord:function(e){T("stopRecord",{},e)},onVoiceRecordEnd:function(e){M("onVoiceRecordEnd",e)},playVoice:function(e){T("playVoice",{localId:e.localId},e)},pauseVoice:function(e){T("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){T("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){M("onVoicePlayEnd",e)},uploadVoice:function(e){T("uploadVoice",{localId:e.localId,isShowProgressTips:+(0!=e.isShowProgressTips)},e)},downloadVoice:function(e){T("downloadVoice",{serverId:e.serverId,isShowProgressTips:+(0!=e.isShowProgressTips)},e)},translateVoice:function(e){T("translateVoice",{localId:e.localId,isShowProgressTips:+(0!=e.isShowProgressTips)},e)},chooseImage:function(e){T("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(l){var n=e.localIds;try{n&&(e.localIds=JSON.parse(n))}catch(e){}}},e))},getLocation:function(e){e=e||{},T(i.getLocation,{type:e.type||"wgs84"},(e._complete=function(e){delete e.type},e))},previewImage:function(e){T(i.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){T("uploadImage",{localId:e.localId,isShowProgressTips:+(0!=e.isShowProgressTips)},e)},downloadImage:function(e){T("downloadImage",{serverId:e.serverId,isShowProgressTips:+(0!=e.isShowProgressTips)},e)},getLocalImgData:function(e){!1===I?(I=!0,T("getLocalImgData",{localId:e.localId},(e._complete=function(e){var n;I=!1,0<v.length&&(n=v.shift(),wx.getLocalImgData(n))},e))):v.push(e)},getNetworkType:function(e){T("getNetworkType",{},(e._complete=function(e){var n=e,e=n.errMsg,i=(n.errMsg="getNetworkType:ok",n.subtype);if(delete n.subtype,i)n.networkType=i;else{var i=e.indexOf(":"),t=e.substring(i+1);switch(t){case"wifi":case"edge":case"wwan":n.networkType=t;break;default:n.errMsg="getNetworkType:fail"}}},e))},openLocation:function(e){T("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)},hideOptionMenu:function(e){T("hideOptionMenu",{},e)},showOptionMenu:function(e){T("showOptionMenu",{},e)},closeWindow:function(e){T("closeWindow",{},e=e||{})},hideMenuItems:function(e){T("hideMenuItems",{menuList:e.menuList},e)},showMenuItems:function(e){T("showMenuItems",{menuList:e.menuList},e)},hideAllNonBaseMenuItem:function(e){T("hideAllNonBaseMenuItem",{},e)},showAllNonBaseMenuItem:function(e){T("showAllNonBaseMenuItem",{},e)},scanQRCode:function(e){T("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){var n;u&&(n=e.resultStr)&&(e.resultStr=(n=JSON.parse(n))&&n.scan_code&&n.scan_code.scan_result)},e))},openAddress:function(e){T(i.openAddress,{},(e._complete=function(e){e.postalCode=e.addressPostalCode,delete e.addressPostalCode,e.provinceName=e.proviceFirstStageName,delete e.proviceFirstStageName,e.cityName=e.addressCitySecondStageName,delete e.addressCitySecondStageName,e.countryName=e.addressCountiesThirdStageName,delete e.addressCountiesThirdStageName,e.detailInfo=e.addressDetailInfo,delete e.addressDetailInfo},e))},openProductSpecificView:function(e){T(i.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)},addCard:function(e){for(var n=e.cardList,t=[],o=0,r=n.length;o<r;++o){var a=n[o],a={card_id:a.cardId,card_ext:a.cardExt};t.push(a)}T(i.addCard,{card_list:t},(e._complete=function(e){if(n=e.card_list){for(var n,i=0,t=(n=JSON.parse(n)).length;i<t;++i){var o=n[i];o.cardId=o.card_id,o.cardExt=o.card_ext,o.isSuccess=!!o.is_succ,delete o.card_id,delete o.card_ext,delete o.is_succ}e.cardList=n,delete e.card_list}},e))},chooseCard:function(e){T("chooseCard",{app_id:h.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))},openCard:function(e){for(var n=e.cardList,t=[],o=0,r=n.length;o<r;++o){var a=n[o],a={card_id:a.cardId,code:a.code};t.push(a)}T(i.openCard,{card_list:t},e)},consumeAndShareCard:function(e){T(i.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)},chooseWXPay:function(e){T(i.chooseWXPay,x(e),e),B({jsApiName:"chooseWXPay"})},openEnterpriseRedPacket:function(e){T(i.openEnterpriseRedPacket,x(e),e)},startSearchBeacons:function(e){T(i.startSearchBeacons,{ticket:e.ticket},e)},stopSearchBeacons:function(e){T(i.stopSearchBeacons,{},e)},onSearchBeacons:function(e){M(i.onSearchBeacons,e)},openEnterpriseChat:function(e){T("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)},launchMiniProgram:function(e){T("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){var n;if("string"==typeof e&&0<e.length)return n=e.split("?")[0]+".html",void 0!==(e=e.split("?")[1])?n+"?"+e:n}(e.path),envVersion:e.envVersion},e)},openBusinessView:function(e){T("openBusinessView",{businessType:e.businessType,queryString:e.queryString||"",envVersion:e.envVersion},(e._complete=function(e){if(l){var n=e.extraData;if(n)try{e.extraData=JSON.parse(n)}catch(n){e.extraData={}}}},e))},miniProgram:{navigateBack:function(e){e=e||{},N(function(){T("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)})},navigateTo:function(e){N(function(){T("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)})},redirectTo:function(e){N(function(){T("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)})},switchTab:function(e){N(function(){T("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)})},reLaunch:function(e){N(function(){T("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)})},postMessage:function(e){N(function(){T("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)})},getEnv:function(n){N(function(){n({miniprogram:"miniprogram"===e.__wxjs_environment})})}}},w=1,k={},o.addEventListener("error",function(e){var n,i,t;l||(t=(n=e.target).tagName,i=n.src,"IMG"!=t&&"VIDEO"!=t&&"AUDIO"!=t&&"SOURCE"!=t)||-1!=i.indexOf("wxlocalresource://")&&(e.preventDefault(),e.stopPropagation(),(t=n["wx-id"])||(t=w++,n["wx-id"]=t),k[t]||(k[t]=!0,wx.ready(function(){wx.getLocalImgData({localId:i,success:function(e){n.src=e.localData}})})))},!0),o.addEventListener("load",function(e){var n;l||(n=(e=e.target).tagName,e.src,"IMG"!=n&&"VIDEO"!=n&&"AUDIO"!=n&&"SOURCE"!=n)||(n=e["wx-id"])&&(k[n]=!1)},!0),_;return e.jWeixin;function T(n,i,t){e.WeixinJSBridge?WeixinJSBridge.invoke(n,P(i),function(e){V(n,e,t)}):C(n,t)}function M(n,i,t){e.WeixinJSBridge?WeixinJSBridge.on(n,function(e){t&&t.trigger&&t.trigger(e),V(n,e,i)}):C(n,t||i)}function P(e){return(e=e||{}).appId=h.appId,e.verifyAppId=h.appId,e.verifySignType="sha1",e.verifyTimestamp=h.timestamp+"",e.verifyNonceStr=h.nonceStr,e.verifySignature=h.signature,e}function x(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function V(e,n,i){"openEnterpriseChat"!=e&&"openBusinessView"!==e||(n.errCode=n.err_code),delete n.err_code,delete n.err_desc,delete n.err_detail;var o,r,a,c,s=n.errMsg,e=(s||(s=n.err_msg,delete n.err_msg,o=e,r=s,(c=t[o])&&(o=c),c="ok",r&&(a=r.indexOf(":"),"access denied"!=(c=(c=(c=-1!=(c=-1!=(c="failed"==(c="confirm"==(c=r.substring(a+1))?"ok":c)?"fail":c).indexOf("failed_")?c.substring(7):c).indexOf("fail_")?c.substring(5):c).replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=c||(c="permission denied"),""==(c="config"==o&&"function not exist"==c?"ok":c))&&(c="fail"),n.errMsg=s=r=o+":"+c),(i=i||{})._complete&&(i._complete(n),delete i._complete),s=n.errMsg||"",h.debug&&!i.isInnerInvoke&&alert(JSON.stringify(n)),s.indexOf(":"));switch(s.substring(e+1)){case"ok":i.success&&i.success(n);break;case"cancel":i.cancel&&i.cancel(n);break;default:i.fail&&i.fail(n)}i.complete&&i.complete(n)}function A(e){if(e){for(var n=0,t=e.length;n<t;++n){var o=e[n],o=i[o];o&&(e[n]=o)}return e}}function C(e,n){var i;!h.debug||n&&n.isInnerInvoke||((i=t[e])&&(e=i),n&&n._complete&&delete n._complete,console.log('"'+e+'",',n||""))}function B(e){var n;c||s||h.debug||p<"6.0.2"||g.systemType<0||(n=new Image,g.appId=h.appId,g.initTime=m.initEndTime-m.initStartTime,g.preVerifyTime=m.preVerifyEndTime-m.preVerifyStartTime,_.getNetworkType({isInnerInvoke:!0,success:function(i){g.networkType=i.networkType,n.src=i="https://open.weixin.qq.com/sdk/report?v="+g.version+"&o="+g.isPreVerifyOk+"&s="+g.systemType+"&c="+g.clientVersion+"&a="+g.appId+"&n="+g.networkType+"&i="+g.initTime+"&p="+g.preVerifyTime+"&u="+g.url+"&jsapi_name="+(e?e.jsApiName:"")}}))}function L(){return new Date().getTime()}function N(n){d&&(e.WeixinJSBridge?n():o.addEventListener&&o.addEventListener("WeixinJSBridgeReady",n,!1))}}("object"==typeof window&&window)}}]);