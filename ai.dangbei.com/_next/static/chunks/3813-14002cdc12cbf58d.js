"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3813],{83813:(n,e,c)=>{c.d(e,{A:()=>H});var a=c(12694),t=c(21462),i=c(92168),o=c(46001),l=c.n(o),r=c(35726),d=c(26975),s=c(60295),h=c(28750),u=c(39074),g=c(15191),m=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],k=t.forwardRef(function(n,e){var c,a=n.prefixCls,i=void 0===a?"rc-switch":a,o=n.className,k=n.checked,p=n.defaultChecked,S=n.disabled,b=n.loadingIcon,I=n.checkedChildren,f=n.unCheckedChildren,v=n.onClick,w=n.onChange,C=n.onKeyDown,E=(0,h.A)(n,m),M=(0,u.A)(!1,{value:k,defaultValue:p}),y=(0,s.A)(M,2),A=y[0],z=y[1];function x(n,e){var c=A;return S||(z(c=n),null==w||w(c,e)),c}var q=l()(i,o,(c={},(0,d.A)(c,"".concat(i,"-checked"),A),(0,d.A)(c,"".concat(i,"-disabled"),S),c));return t.createElement("button",(0,r.A)({},E,{type:"button",role:"switch","aria-checked":A,disabled:S,className:q,ref:e,onKeyDown:function(n){n.which===g.A.LEFT?x(!1,n):n.which===g.A.RIGHT&&x(!0,n),null==C||C(n)},onClick:function(n){var e=x(!A,n);null==v||v(e,n)}}),b,t.createElement("span",{className:"".concat(i,"-inner")},t.createElement("span",{className:"".concat(i,"-inner-checked")},I),t.createElement("span",{className:"".concat(i,"-inner-unchecked")},f)))});k.displayName="Switch";var p=c(95418),S=c(77312),b=c(13872),I=c(80382),f=c(64467),v=c(16700),w=c(5214),C=c(68197),E=c(13440),M=function(n){var e=n.componentCls,c=n.trackHeightSM,a=n.trackPadding,t=n.trackMinWidthSM,i=n.innerMinMarginSM,o=n.innerMaxMarginSM,l=n.handleSizeSM,r=n.calc,d="".concat(e,"-inner"),s=(0,f.zA)(r(l).add(r(a).mul(2)).equal()),h=(0,f.zA)(r(o).mul(2).equal());return{[e]:{["&".concat(e,"-small")]:{minWidth:t,height:c,lineHeight:(0,f.zA)(c),["".concat(e,"-inner")]:{paddingInlineStart:o,paddingInlineEnd:i,["".concat(d,"-checked, ").concat(d,"-unchecked")]:{minHeight:c},["".concat(d,"-checked")]:{marginInlineStart:"calc(-100% + ".concat(s," - ").concat(h,")"),marginInlineEnd:"calc(100% - ".concat(s," + ").concat(h,")")},["".concat(d,"-unchecked")]:{marginTop:r(c).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},["".concat(e,"-handle")]:{width:l,height:l},["".concat(e,"-loading-icon")]:{top:r(r(l).sub(n.switchLoadingIconSize)).div(2).equal(),fontSize:n.switchLoadingIconSize},["&".concat(e,"-checked")]:{["".concat(e,"-inner")]:{paddingInlineStart:i,paddingInlineEnd:o,["".concat(d,"-checked")]:{marginInlineStart:0,marginInlineEnd:0},["".concat(d,"-unchecked")]:{marginInlineStart:"calc(100% - ".concat(s," + ").concat(h,")"),marginInlineEnd:"calc(-100% + ".concat(s," - ").concat(h,")")}},["".concat(e,"-handle")]:{insetInlineStart:"calc(100% - ".concat((0,f.zA)(r(l).add(a).equal()),")")}},["&:not(".concat(e,"-disabled):active")]:{["&:not(".concat(e,"-checked) ").concat(d)]:{["".concat(d,"-unchecked")]:{marginInlineStart:r(n.marginXXS).div(2).equal(),marginInlineEnd:r(n.marginXXS).mul(-1).div(2).equal()}},["&".concat(e,"-checked ").concat(d)]:{["".concat(d,"-checked")]:{marginInlineStart:r(n.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:r(n.marginXXS).div(2).equal()}}}}}}},y=function(n){var e=n.componentCls,c=n.handleSize,a=n.calc;return{[e]:{["".concat(e,"-loading-icon").concat(n.iconCls)]:{position:"relative",top:a(a(c).sub(n.fontSize)).div(2).equal(),color:n.switchLoadingIconColor,verticalAlign:"top"},["&".concat(e,"-checked ").concat(e,"-loading-icon")]:{color:n.switchColor}}}},A=function(n){var e=n.componentCls,c=n.trackPadding,a=n.handleBg,t=n.handleShadow,i=n.handleSize,o=n.calc,l="".concat(e,"-handle");return{[e]:{[l]:{position:"absolute",top:c,insetInlineStart:c,width:i,height:i,transition:"all ".concat(n.switchDuration," ease-in-out"),"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:o(i).div(2).equal(),boxShadow:t,transition:"all ".concat(n.switchDuration," ease-in-out"),content:'""'}},["&".concat(e,"-checked ").concat(l)]:{insetInlineStart:"calc(100% - ".concat((0,f.zA)(o(i).add(c).equal()),")")},["&:not(".concat(e,"-disabled):active")]:{["".concat(l,"::before")]:{insetInlineEnd:n.switchHandleActiveInset,insetInlineStart:0},["&".concat(e,"-checked ").concat(l,"::before")]:{insetInlineEnd:0,insetInlineStart:n.switchHandleActiveInset}}}}},z=function(n){var e=n.componentCls,c=n.trackHeight,a=n.trackPadding,t=n.innerMinMargin,i=n.innerMaxMargin,o=n.handleSize,l=n.calc,r="".concat(e,"-inner"),d=(0,f.zA)(l(o).add(l(a).mul(2)).equal()),s=(0,f.zA)(l(i).mul(2).equal());return{[e]:{[r]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:i,paddingInlineEnd:t,transition:"padding-inline-start ".concat(n.switchDuration," ease-in-out, padding-inline-end ").concat(n.switchDuration," ease-in-out"),["".concat(r,"-checked, ").concat(r,"-unchecked")]:{display:"block",color:n.colorTextLightSolid,fontSize:n.fontSizeSM,transition:"margin-inline-start ".concat(n.switchDuration," ease-in-out, margin-inline-end ").concat(n.switchDuration," ease-in-out"),pointerEvents:"none",minHeight:c},["".concat(r,"-checked")]:{marginInlineStart:"calc(-100% + ".concat(d," - ").concat(s,")"),marginInlineEnd:"calc(100% - ".concat(d," + ").concat(s,")")},["".concat(r,"-unchecked")]:{marginTop:l(c).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},["&".concat(e,"-checked ").concat(r)]:{paddingInlineStart:t,paddingInlineEnd:i,["".concat(r,"-checked")]:{marginInlineStart:0,marginInlineEnd:0},["".concat(r,"-unchecked")]:{marginInlineStart:"calc(100% - ".concat(d," + ").concat(s,")"),marginInlineEnd:"calc(-100% + ".concat(d," - ").concat(s,")")}},["&:not(".concat(e,"-disabled):active")]:{["&:not(".concat(e,"-checked) ").concat(r)]:{["".concat(r,"-unchecked")]:{marginInlineStart:l(a).mul(2).equal(),marginInlineEnd:l(a).mul(-1).mul(2).equal()}},["&".concat(e,"-checked ").concat(r)]:{["".concat(r,"-checked")]:{marginInlineStart:l(a).mul(-1).mul(2).equal(),marginInlineEnd:l(a).mul(2).equal()}}}}}},x=function(n){var e=n.componentCls,c=n.trackHeight,a=n.trackMinWidth;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,w.dF)(n)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:c,lineHeight:(0,f.zA)(c),verticalAlign:"middle",background:n.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:"all ".concat(n.motionDurationMid),userSelect:"none",["&:hover:not(".concat(e,"-disabled)")]:{background:n.colorTextTertiary}}),(0,w.K8)(n)),{["&".concat(e,"-checked")]:{background:n.switchColor,["&:hover:not(".concat(e,"-disabled)")]:{background:n.colorPrimaryHover}},["&".concat(e,"-loading, &").concat(e,"-disabled")]:{cursor:"not-allowed",opacity:n.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},["&".concat(e,"-rtl")]:{direction:"rtl"}})}};let q=(0,C.OF)("Switch",function(n){var e=(0,E.oX)(n,{switchDuration:n.motionDurationMid,switchColor:n.colorPrimary,switchDisabledOpacity:n.opacityLoading,switchLoadingIconSize:n.calc(n.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:"rgba(0, 0, 0, ".concat(n.opacityLoading,")"),switchHandleActiveInset:"-30%"});return[x(e),z(e),A(e),y(e),M(e)]},function(n){var e=n.fontSize,c=n.lineHeight,a=n.controlHeight,t=n.colorWhite,i=e*c,o=a/2,l=i-4,r=o-4;return{trackHeight:i,trackHeightSM:o,trackMinWidth:2*l+8,trackMinWidthSM:2*r+4,trackPadding:2,handleBg:t,handleSize:l,handleSizeSM:r,handleShadow:"0 2px 4px 0 ".concat(new v.Y("#00230b").setA(.2).toRgbString()),innerMinMargin:l/2,innerMaxMargin:l+2+4,innerMinMarginSM:r/2,innerMaxMarginSM:r+2+4}});var O=function(n,e){var c={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&0>e.indexOf(a)&&(c[a]=n[a]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,a=Object.getOwnPropertySymbols(n);t<a.length;t++)0>e.indexOf(a[t])&&Object.prototype.propertyIsEnumerable.call(n,a[t])&&(c[a[t]]=n[a[t]]);return c},N=t.forwardRef(function(n,e){var c=n.prefixCls,o=n.size,r=n.disabled,d=n.loading,s=n.className,h=n.rootClassName,g=n.style,m=n.checked,f=n.value,v=n.defaultChecked,w=n.defaultValue,C=n.onChange,E=O(n,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),M=(0,a._)((0,u.A)(!1,{value:null!=m?m:f,defaultValue:null!=v?v:w}),2),y=M[0],A=M[1],z=t.useContext(S.QO),x=z.getPrefixCls,N=z.direction,H=z.switch,D=t.useContext(b.A),j=(null!=r?r:D)||d,P=x("switch",c),T=t.createElement("div",{className:"".concat(P,"-handle")},d&&t.createElement(i.A,{className:"".concat(P,"-loading-icon")})),L=(0,a._)(q(P),3),X=L[0],_=L[1],W=L[2],R=(0,I.A)(o),K=l()(null==H?void 0:H.className,{["".concat(P,"-small")]:"small"===R,["".concat(P,"-loading")]:d,["".concat(P,"-rtl")]:"rtl"===N},s,h,_,W),V=Object.assign(Object.assign({},null==H?void 0:H.style),g);return X(t.createElement(p.A,{component:"Switch"},t.createElement(k,Object.assign({},E,{checked:y,onChange:function(){A(arguments.length<=0?void 0:arguments[0]),null==C||C.apply(void 0,arguments)},prefixCls:P,className:K,style:V,disabled:j,ref:e,loadingIcon:T}))))});N.__ANT_SWITCH=!0;let H=N}}]);