"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4838],{5587:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(35726),r=n(21462);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};var i=n(29236);let c=r.forwardRef(function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:a}))})},8035:(e,t,n)=>{n.d(t,{A:()=>l,h:()=>u});var o=n(21462),r=n(59744),a=n(33414),i=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n},c=o.createContext(null),l=o.forwardRef(function(e,t){var n=e.children,l=i(e,["children"]),u=o.useContext(c),s=o.useMemo(function(){return Object.assign(Object.assign({},u),l)},[u,l.prefixCls,l.mode,l.selectable,l.rootClassName]),d=(0,r.H3)(n),m=(0,r.xK)(t,d?(0,r.A9)(n):null);return o.createElement(c.Provider,{value:s},o.createElement(a.A,{space:!0},d?o.cloneElement(n,{ref:m}):n))});let u=c},12300:(e,t,n)=>{n.d(t,{A:()=>b});var o=n(12694),r=n(21462),a=n(46001),i=n.n(a),c=n(721),l=n(3547),u=n(77312),s=n(77708),d=r.createContext({latestIndex:0}),m=d.Provider;let p=function(e){var t=e.className,n=e.index,o=e.children,a=e.split,i=e.style,c=r.useContext(d).latestIndex;return null==o?null:r.createElement(r.Fragment,null,r.createElement("div",{className:t,style:i},o),n<c&&a&&r.createElement("span",{className:"".concat(t,"-split")},a))};var f=n(48240),v=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n},g=r.forwardRef(function(e,t){var n,a=(0,u.TP)("space"),s=a.getPrefixCls,d=a.direction,g=a.size,b=a.className,h=a.style,y=a.classNames,C=a.styles,A=e.size,I=void 0===A?null!=g?g:"small":A,w=e.align,S=e.className,x=e.rootClassName,E=e.children,O=e.direction,k=void 0===O?"horizontal":O,B=e.prefixCls,N=e.split,P=e.style,z=e.wrap,R=e.classNames,M=e.styles,j=v(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),T=(0,o._)(Array.isArray(I)?I:[I,I],2),D=T[0],H=T[1],L=(0,l.X)(H),K=(0,l.X)(D),_=(0,l.m)(H),W=(0,l.m)(D),X=(0,c.A)(E,{keepEmpty:!0}),F=void 0===w&&"horizontal"===k?"center":w,V=s("space",B),q=(0,o._)((0,f.A)(V),3),G=q[0],Y=q[1],Q=q[2],U=i()(V,b,Y,"".concat(V,"-").concat(k),{["".concat(V,"-rtl")]:"rtl"===d,["".concat(V,"-align-").concat(F)]:F,["".concat(V,"-gap-row-").concat(H)]:L,["".concat(V,"-gap-col-").concat(D)]:K},S,x,Q),J=i()("".concat(V,"-item"),null!==(n=null==R?void 0:R.item)&&void 0!==n?n:y.item),Z=0,$=X.map(function(e,t){null!=e&&(Z=t);var n,o=(null==e?void 0:e.key)||"".concat(J,"-").concat(t);return r.createElement(p,{className:J,key:o,index:t,split:N,style:null!==(n=null==M?void 0:M.item)&&void 0!==n?n:C.item},e)}),ee=r.useMemo(function(){return{latestIndex:Z}},[Z]);if(0===X.length)return null;var et={};return void 0!==z&&z&&(et.flexWrap="wrap"),!K&&W&&(et.columnGap=D),!L&&_&&(et.rowGap=H),G(r.createElement("div",Object.assign({ref:t,className:U,style:Object.assign(Object.assign(Object.assign({},et),h),P)},j),r.createElement(m,{value:ee},$)))});g.Compact=s.Ay;let b=g},54017:(e,t,n)=>{n.d(t,{A:()=>A});var o=n(35726),r=n(26975),a=n(60295),i=n(28750),c=n(24735),l=n(46001),u=n.n(l),s=n(59744),d=n(21462),m=n(15191),p=n(35884),f=m.A.ESC,v=m.A.TAB,g=(0,d.forwardRef)(function(e,t){var n=e.overlay,o=e.arrow,r=e.prefixCls,a=(0,d.useMemo)(function(){var e;return"function"==typeof n?n():n},[n]),i=(0,s.K4)(t,(0,s.A9)(a));return d.createElement(d.Fragment,null,o&&d.createElement("div",{className:"".concat(r,"-arrow")}),d.cloneElement(a,{ref:(0,s.f3)(a)?i:void 0}))}),b={adjustX:1,adjustY:1},h=[0,0];let y={topLeft:{points:["bl","tl"],overflow:b,offset:[0,-4],targetOffset:h},top:{points:["bc","tc"],overflow:b,offset:[0,-4],targetOffset:h},topRight:{points:["br","tr"],overflow:b,offset:[0,-4],targetOffset:h},bottomLeft:{points:["tl","bl"],overflow:b,offset:[0,4],targetOffset:h},bottom:{points:["tc","bc"],overflow:b,offset:[0,4],targetOffset:h},bottomRight:{points:["tr","br"],overflow:b,offset:[0,4],targetOffset:h}};var C=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];let A=d.forwardRef(function(e,t){var n,l,m,b,h,A,I,w,S,x,E,O,k,B,N=e.arrow,P=void 0!==N&&N,z=e.prefixCls,R=void 0===z?"rc-dropdown":z,M=e.transitionName,j=e.animation,T=e.align,D=e.placement,H=e.placements,L=e.getPopupContainer,K=e.showAction,_=e.hideAction,W=e.overlayClassName,X=e.overlayStyle,F=e.visible,V=e.trigger,q=void 0===V?["hover"]:V,G=e.autoFocus,Y=e.overlay,Q=e.children,U=e.onVisibleChange,J=(0,i.A)(e,C),Z=d.useState(),$=(0,a.A)(Z,2),ee=$[0],et=$[1],en="visible"in e?F:ee,eo=d.useRef(null),er=d.useRef(null),ea=d.useRef(null);d.useImperativeHandle(t,function(){return eo.current});var ei=function(e){et(e),null==U||U(e)};l=(n={visible:en,triggerRef:ea,onVisibleChange:ei,autoFocus:G,overlayRef:er}).visible,m=n.triggerRef,b=n.onVisibleChange,h=n.autoFocus,A=n.overlayRef,I=d.useRef(!1),w=function(){if(l){var e,t;null===(e=m.current)||void 0===e||null===(t=e.focus)||void 0===t||t.call(e),null==b||b(!1)}},S=function(){var e;return null!==(e=A.current)&&void 0!==e&&!!e.focus&&(A.current.focus(),I.current=!0,!0)},x=function(e){switch(e.keyCode){case f:w();break;case v:var t=!1;I.current||(t=S()),t?e.preventDefault():w()}},d.useEffect(function(){return l?(window.addEventListener("keydown",x),h&&(0,p.A)(S,3),function(){window.removeEventListener("keydown",x),I.current=!1}):function(){I.current=!1}},[l]);var ec=function(){return d.createElement(g,{ref:er,overlay:Y,prefixCls:R,arrow:P})},el=d.cloneElement(Q,{className:u()(null===(B=Q.props)||void 0===B?void 0:B.className,en&&(void 0!==(E=e.openClassName)?E:"".concat(R,"-open"))),ref:(0,s.f3)(Q)?(0,s.K4)(ea,(0,s.A9)(Q)):void 0}),eu=_;return eu||-1===q.indexOf("contextMenu")||(eu=["click"]),d.createElement(c.A,(0,o.A)({builtinPlacements:void 0===H?y:H},J,{prefixCls:R,ref:eo,popupClassName:u()(W,(0,r.A)({},"".concat(R,"-show-arrow"),P)),popupStyle:X,action:q,showAction:K,hideAction:eu,popupPlacement:void 0===D?"bottomLeft":D,popupAlign:T,popupTransitionName:M,popupAnimation:j,popupVisible:en,stretch:(O=e.minOverlayWidthMatchTrigger,k=e.alignPoint,"minOverlayWidthMatchTrigger"in e?O:!k)?"minWidth":"",popup:"function"==typeof Y?ec:ec(),onPopupVisibleChange:ei,onPopupClick:function(t){var n=e.onOverlayClick;et(!1),n&&n(t)},getPopupContainer:L}),el)})},58815:(e,t,n)=>{n.d(t,{A:()=>Y});var o=n(21462),r=n(77707),a=n(9730),i=n(12694),c=n(5587),l=n(46001),u=n.n(l),s=n(17763),d=n(81698),m=n(33984),p=n(49357),f=n(77312),v=n(50945),g=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1}),b=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let h=function(e){var t=e.prefixCls,n=e.className,a=e.dashed,i=b(e,["prefixCls","className","dashed"]),c=(0,o.useContext(f.QO).getPrefixCls)("menu",t),l=u()({["".concat(c,"-item-divider-dashed")]:!!a},n);return o.createElement(r.cG,Object.assign({className:l},i))};var y=n(721),C=n(39652);let A=function(e){var t,n,i,c=e.className,l=e.children,s=e.icon,m=e.title,f=e.danger,v=e.extra,b=o.useContext(g),h=b.prefixCls,A=b.firstLevel,I=b.direction,w=b.disableMenuItemTitleTooltip,S=b.inlineCollapsed,x=o.useContext(a.P).siderCollapsed,E=m;void 0===m?E=A?l:"":!1===m&&(E="");var O={title:E};x||S||(O.title=null,O.open=!1);var k=(0,y.A)(l).length,B=o.createElement(r.q7,Object.assign({},(0,d.A)(e,["title","icon","danger"]),{className:u()({["".concat(h,"-item-danger")]:f,["".concat(h,"-item-only-child")]:(s?k+1:k)===1},c),title:"string"==typeof m?m:void 0}),(0,p.Ob)(s,{className:u()(o.isValidElement(s)?null===(i=s.props)||void 0===i?void 0:i.className:"","".concat(h,"-item-icon"))}),(t=null==l?void 0:l[0],n=o.createElement("span",{className:u()("".concat(h,"-title-content"),{["".concat(h,"-title-content-with-extra")]:!!v||0===v})},l),(!s||o.isValidElement(l)&&"span"===l.type)&&l&&S&&A&&"string"==typeof t?o.createElement("div",{className:"".concat(h,"-inline-collapsed-noicon")},t.charAt(0)):n));return w||(B=o.createElement(C.A,Object.assign({},O,{placement:"rtl"===I?"left":"right",classNames:{root:"".concat(h,"-inline-collapsed-tooltip")}}),B)),B};var I=n(8035),w=n(64467),S=n(16700),x=n(5214),E=n(82934),O=n(80610),k=n(10186),B=n(68197),N=n(13440);let P=function(e){var t=e.componentCls,n=e.motionDurationSlow,o=e.horizontalLineHeight,r=e.colorSplit,a=e.lineWidth,i=e.lineType,c=e.itemPaddingInline;return{["".concat(t,"-horizontal")]:{lineHeight:o,border:0,borderBottom:"".concat((0,w.zA)(a)," ").concat(i," ").concat(r),boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},["".concat(t,"-item, ").concat(t,"-submenu")]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:c},["> ".concat(t,"-item:hover,\n        > ").concat(t,"-item-active,\n        > ").concat(t,"-submenu ").concat(t,"-submenu-title:hover")]:{backgroundColor:"transparent"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{transition:["border-color ".concat(n),"background ".concat(n)].join(",")},["".concat(t,"-submenu-arrow")]:{display:"none"}}}},z=function(e){var t=e.componentCls,n=e.menuArrowOffset,o=e.calc;return{["".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-submenu-rtl")]:{transformOrigin:"100% 0"},["".concat(t,"-rtl").concat(t,"-vertical,\n    ").concat(t,"-submenu-rtl ").concat(t,"-vertical")]:{["".concat(t,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateY(".concat((0,w.zA)(o(n).mul(-1).equal()),")")},"&::after":{transform:"rotate(45deg) translateY(".concat((0,w.zA)(n),")")}}}}};var R=function(e){return Object.assign({},(0,x.jk)(e))};let M=function(e,t){var n=e.componentCls,o=e.itemColor,r=e.itemSelectedColor,a=e.subMenuItemSelectedColor,i=e.groupTitleColor,c=e.itemBg,l=e.subMenuItemBg,u=e.itemSelectedBg,s=e.activeBarHeight,d=e.activeBarWidth,m=e.activeBarBorderWidth,p=e.motionDurationSlow,f=e.motionEaseInOut,v=e.motionEaseOut,g=e.itemPaddingInline,b=e.motionDurationMid,h=e.itemHoverColor,y=e.lineType,C=e.colorSplit,A=e.itemDisabledColor,I=e.dangerItemColor,S=e.dangerItemHoverColor,x=e.dangerItemSelectedColor,E=e.dangerItemActiveBg,O=e.dangerItemSelectedBg,k=e.popupBg,B=e.itemHoverBg,N=e.itemActiveBg,P=e.menuSubMenuBg,z=e.horizontalItemSelectedColor,M=e.horizontalItemSelectedBg,j=e.horizontalItemBorderRadius,T=e.horizontalItemHoverBg;return{["".concat(n,"-").concat(t,", ").concat(n,"-").concat(t," > ").concat(n)]:{color:o,background:c,["&".concat(n,"-root:focus-visible")]:Object.assign({},R(e)),["".concat(n,"-item")]:{"&-group-title, &-extra":{color:i}},["".concat(n,"-submenu-selected > ").concat(n,"-submenu-title")]:{color:a},["".concat(n,"-item, ").concat(n,"-submenu-title")]:{color:o,["&:not(".concat(n,"-item-disabled):focus-visible")]:Object.assign({},R(e))},["".concat(n,"-item-disabled, ").concat(n,"-submenu-disabled")]:{color:"".concat(A," !important")},["".concat(n,"-item:not(").concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{["&:hover, > ".concat(n,"-submenu-title:hover")]:{color:h}},["&:not(".concat(n,"-horizontal)")]:{["".concat(n,"-item:not(").concat(n,"-item-selected)")]:{"&:hover":{backgroundColor:B},"&:active":{backgroundColor:N}},["".concat(n,"-submenu-title")]:{"&:hover":{backgroundColor:B},"&:active":{backgroundColor:N}}},["".concat(n,"-item-danger")]:{color:I,["&".concat(n,"-item:hover")]:{["&:not(".concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{color:S}},["&".concat(n,"-item:active")]:{background:E}},["".concat(n,"-item a")]:{"&, &:hover":{color:"inherit"}},["".concat(n,"-item-selected")]:{color:r,["&".concat(n,"-item-danger")]:{color:x},"a, a:hover":{color:"inherit"}},["& ".concat(n,"-item-selected")]:{backgroundColor:u,["&".concat(n,"-item-danger")]:{backgroundColor:O}},["&".concat(n,"-submenu > ").concat(n)]:{backgroundColor:P},["&".concat(n,"-popup > ").concat(n)]:{backgroundColor:k},["&".concat(n,"-submenu-popup > ").concat(n)]:{backgroundColor:k},["&".concat(n,"-horizontal")]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{["> ".concat(n,"-item, > ").concat(n,"-submenu")]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:j,"&::after":{position:"absolute",insetInline:g,bottom:0,borderBottom:"".concat((0,w.zA)(s)," solid transparent"),transition:"border-color ".concat(p," ").concat(f),content:'""'},"&:hover, &-active, &-open":{background:T,"&::after":{borderBottomWidth:s,borderBottomColor:z}},"&-selected":{color:z,backgroundColor:M,"&:hover":{backgroundColor:M},"&::after":{borderBottomWidth:s,borderBottomColor:z}}}}),["&".concat(n,"-root")]:{["&".concat(n,"-inline, &").concat(n,"-vertical")]:{borderInlineEnd:"".concat((0,w.zA)(m)," ").concat(y," ").concat(C)}},["&".concat(n,"-inline")]:{["".concat(n,"-sub").concat(n,"-inline")]:{background:l},["".concat(n,"-item")]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:"".concat((0,w.zA)(d)," solid ").concat(r),transform:"scaleY(0.0001)",opacity:0,transition:["transform ".concat(b," ").concat(v),"opacity ".concat(b," ").concat(v)].join(","),content:'""'},["&".concat(n,"-item-danger")]:{"&::after":{borderInlineEndColor:x}}},["".concat(n,"-selected, ").concat(n,"-item-selected")]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:["transform ".concat(b," ").concat(f),"opacity ".concat(b," ").concat(f)].join(",")}}}}}};var j=function(e){var t=e.componentCls,n=e.itemHeight,o=e.itemMarginInline,r=e.padding,a=e.menuArrowSize,i=e.marginXS,c=e.itemMarginBlock,l=e.itemWidth,u=e.itemPaddingInline,s=e.calc(a).add(r).add(i).equal();return{["".concat(t,"-item")]:{position:"relative",overflow:"hidden"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,w.zA)(n),paddingInline:u,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:c,width:l},["> ".concat(t,"-item,\n            > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,w.zA)(n)},["".concat(t,"-item-group-list ").concat(t,"-submenu-title,\n            ").concat(t,"-submenu-title")]:{paddingInlineEnd:s}}};let T=function(e){var t=e.componentCls,n=e.iconCls,o=e.itemHeight,r=e.colorTextLightSolid,a=e.dropdownWidth,i=e.controlHeightLG,c=e.motionEaseOut,l=e.paddingXL,u=e.itemMarginInline,s=e.fontSizeLG,d=e.motionDurationFast,m=e.motionDurationSlow,p=e.paddingXS,f=e.boxShadowSecondary,v=e.collapsedWidth,g=e.collapsedIconSize,b={height:o,lineHeight:(0,w.zA)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({["&".concat(t,"-root")]:{boxShadow:"none"}},j(e))},["".concat(t,"-submenu-popup")]:{["".concat(t,"-vertical")]:Object.assign(Object.assign({},j(e)),{boxShadow:f})}},{["".concat(t,"-submenu-popup ").concat(t,"-vertical").concat(t,"-sub")]:{minWidth:a,maxHeight:"calc(100vh - ".concat((0,w.zA)(e.calc(i).mul(2.5).equal()),")"),padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{["".concat(t,"-inline")]:{width:"100%",["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{display:"flex",alignItems:"center",transition:["border-color ".concat(m),"background ".concat(m),"padding ".concat(d," ").concat(c)].join(","),["> ".concat(t,"-title-content")]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},["".concat(t,"-sub").concat(t,"-inline")]:{padding:0,border:0,borderRadius:0,boxShadow:"none",["& > ".concat(t,"-submenu > ").concat(t,"-submenu-title")]:b,["& ".concat(t,"-item-group-title")]:{paddingInlineStart:l}},["".concat(t,"-item")]:b}},{["".concat(t,"-inline-collapsed")]:{width:v,["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu ").concat(t,"-submenu-title")]:{["> ".concat(t,"-inline-collapsed-noicon")]:{fontSize:s,textAlign:"center"}}},["> ".concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-submenu > ").concat(t,"-submenu-title,\n          > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{insetInlineStart:0,paddingInline:"calc(50% - ".concat((0,w.zA)(e.calc(g).div(2).equal())," - ").concat((0,w.zA)(u),")"),textOverflow:"clip",["\n            ".concat(t,"-submenu-arrow,\n            ").concat(t,"-submenu-expand-icon\n          ")]:{opacity:0},["".concat(t,"-item-icon, ").concat(n)]:{margin:0,fontSize:g,lineHeight:(0,w.zA)(o),"+ span":{display:"inline-block",opacity:0}}},["".concat(t,"-item-icon, ").concat(n)]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",["".concat(t,"-item-icon, ").concat(n)]:{display:"none"},"a, a:hover":{color:r}},["".concat(t,"-item-group-title")]:Object.assign(Object.assign({},x.L9),{paddingInline:p})}}]};var D=function(e){var t=e.componentCls,n=e.motionDurationSlow,o=e.motionDurationMid,r=e.motionEaseInOut,a=e.motionEaseOut,i=e.iconCls,c=e.iconSize,l=e.iconMarginInlineEnd;return{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:["border-color ".concat(n),"background ".concat(n),"padding calc(".concat(n," + 0.1s) ").concat(r)].join(","),["".concat(t,"-item-icon, ").concat(i)]:{minWidth:c,fontSize:c,transition:["font-size ".concat(o," ").concat(a),"margin ".concat(n," ").concat(r),"color ".concat(n)].join(","),"+ span":{marginInlineStart:l,opacity:1,transition:["opacity ".concat(n," ").concat(r),"margin ".concat(n),"color ".concat(n)].join(",")}},["".concat(t,"-item-icon")]:Object.assign({},(0,x.Nk)()),["&".concat(t,"-item-only-child")]:{["> ".concat(i,", > ").concat(t,"-item-icon")]:{marginInlineEnd:0}}},["".concat(t,"-item-disabled, ").concat(t,"-submenu-disabled")]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},["> ".concat(t,"-submenu-title")]:{color:"inherit !important",cursor:"not-allowed"}}}},H=function(e){var t=e.componentCls,n=e.motionDurationSlow,o=e.motionEaseInOut,r=e.borderRadius,a=e.menuArrowSize,i=e.menuArrowOffset;return{["".concat(t,"-submenu")]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:a,color:"currentcolor",transform:"translateY(-50%)",transition:"transform ".concat(n," ").concat(o,", opacity ").concat(n)},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(a).mul(.6).equal(),height:e.calc(a).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:["background ".concat(n," ").concat(o),"transform ".concat(n," ").concat(o),"top ".concat(n," ").concat(o),"color ".concat(n," ").concat(o)].join(","),content:'""'},"&::before":{transform:"rotate(45deg) translateY(".concat((0,w.zA)(e.calc(i).mul(-1).equal()),")")},"&::after":{transform:"rotate(-45deg) translateY(".concat((0,w.zA)(i),")")}}}}},L=function(e){var t=e.antCls,n=e.componentCls,o=e.fontSize,r=e.motionDurationSlow,a=e.motionDurationMid,i=e.motionEaseInOut,c=e.paddingXS,l=e.padding,u=e.colorSplit,s=e.lineWidth,d=e.zIndexPopup,m=e.borderRadiusLG,p=e.subMenuItemBorderRadius,f=e.menuArrowSize,v=e.menuArrowOffset,g=e.lineType,b=e.groupTitleLineHeight,h=e.groupTitleFontSize;return[{"":{[n]:Object.assign(Object.assign({},(0,x.t6)()),{"&-hidden":{display:"none"}})},["".concat(n,"-submenu-hidden")]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,x.dF)(e)),(0,x.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:"width ".concat(r," cubic-bezier(0.2, 0, 0, 1) 0s"),"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",["".concat(n,"-item")]:{flex:"none"}},["".concat(n,"-item, ").concat(n,"-submenu, ").concat(n,"-submenu-title")]:{borderRadius:e.itemBorderRadius},["".concat(n,"-item-group-title")]:{padding:"".concat((0,w.zA)(c)," ").concat((0,w.zA)(l)),fontSize:h,lineHeight:b,transition:"all ".concat(r)},["&-horizontal ".concat(n,"-submenu")]:{transition:["border-color ".concat(r," ").concat(i),"background ".concat(r," ").concat(i)].join(",")},["".concat(n,"-submenu, ").concat(n,"-submenu-inline")]:{transition:["border-color ".concat(r," ").concat(i),"background ".concat(r," ").concat(i),"padding ".concat(a," ").concat(i)].join(",")},["".concat(n,"-submenu ").concat(n,"-sub")]:{cursor:"initial",transition:["background ".concat(r," ").concat(i),"padding ".concat(r," ").concat(i)].join(",")},["".concat(n,"-title-content")]:{transition:"color ".concat(r),"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},["> ".concat(t,"-typography-ellipsis-single-line")]:{display:"inline",verticalAlign:"unset"},["".concat(n,"-item-extra")]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},["".concat(n,"-item a")]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},["".concat(n,"-item-divider")]:{overflow:"hidden",lineHeight:0,borderColor:u,borderStyle:g,borderWidth:0,borderTopWidth:s,marginBlock:s,padding:0,"&-dashed":{borderStyle:"dashed"}}}),D(e)),{["".concat(n,"-item-group")]:{["".concat(n,"-item-group-list")]:{margin:0,padding:0,["".concat(n,"-item, ").concat(n,"-submenu-title")]:{paddingInline:"".concat((0,w.zA)(e.calc(o).mul(2).equal())," ").concat((0,w.zA)(l))}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:d,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",["&".concat(n,"-submenu")]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},["> ".concat(n)]:Object.assign(Object.assign(Object.assign({borderRadius:m},D(e)),H(e)),{["".concat(n,"-item, ").concat(n,"-submenu > ").concat(n,"-submenu-title")]:{borderRadius:p},["".concat(n,"-submenu-title::after")]:{transition:"transform ".concat(r," ").concat(i)}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),H(e)),{["&-inline-collapsed ".concat(n,"-submenu-arrow,\n        &-inline ").concat(n,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateX(".concat((0,w.zA)(v),")")},"&::after":{transform:"rotate(45deg) translateX(".concat((0,w.zA)(e.calc(v).mul(-1).equal()),")")}},["".concat(n,"-submenu-open").concat(n,"-submenu-inline > ").concat(n,"-submenu-title > ").concat(n,"-submenu-arrow")]:{transform:"translateY(".concat((0,w.zA)(e.calc(f).mul(.2).mul(-1).equal()),")"),"&::after":{transform:"rotate(-45deg) translateX(".concat((0,w.zA)(e.calc(v).mul(-1).equal()),")")},"&::before":{transform:"rotate(45deg) translateX(".concat((0,w.zA)(v),")")}}})},{["".concat(t,"-layout-header")]:{[n]:{lineHeight:"inherit"}}}]},K=function(e){var t,n,o,r=e.colorPrimary,a=e.colorError,i=e.colorTextDisabled,c=e.colorErrorBg,l=e.colorText,u=e.colorTextDescription,s=e.colorBgContainer,d=e.colorFillAlter,m=e.colorFillContent,p=e.lineWidth,f=e.lineWidthBold,v=e.controlItemBgActive,g=e.colorBgTextHover,b=e.controlHeightLG,h=e.lineHeight,y=e.colorBgElevated,C=e.marginXXS,A=e.padding,I=e.fontSize,w=e.controlHeightSM,x=e.fontSizeLG,E=e.colorTextLightSolid,O=e.colorErrorHover,k=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,B=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,N=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,P=new S.Y(E).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:l,itemColor:l,colorItemTextHover:l,itemHoverColor:l,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:u,groupTitleColor:u,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:s,itemBg:s,colorItemBgHover:g,itemHoverBg:g,colorItemBgActive:m,itemActiveBg:v,colorSubItemBg:d,subMenuItemBg:d,colorItemBgSelected:v,itemSelectedBg:v,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:k,colorActiveBarHeight:f,activeBarHeight:f,colorActiveBarBorderSize:p,activeBarBorderWidth:B,colorItemTextDisabled:i,itemDisabledColor:i,colorDangerItemText:a,dangerItemColor:a,colorDangerItemTextHover:a,dangerItemHoverColor:a,colorDangerItemTextSelected:a,dangerItemSelectedColor:a,colorDangerItemBgActive:c,dangerItemActiveBg:c,colorDangerItemBgSelected:c,dangerItemSelectedBg:c,itemMarginInline:N,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:b,groupTitleLineHeight:h,collapsedWidth:2*b,popupBg:y,itemMarginBlock:C,itemPaddingInline:A,horizontalLineHeight:"".concat(1.15*b,"px"),iconSize:I,iconMarginInlineEnd:w-I,collapsedIconSize:x,groupTitleFontSize:I,darkItemDisabledColor:new S.Y(E).setA(.25).toRgbString(),darkItemColor:P,darkDangerItemColor:a,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:E,darkItemSelectedBg:r,darkDangerItemSelectedBg:a,darkItemHoverBg:"transparent",darkGroupTitleColor:P,darkItemHoverColor:E,darkDangerItemHoverColor:O,darkDangerItemSelectedColor:E,darkDangerItemActiveBg:a,itemWidth:k?"calc(100% + ".concat(B,"px)"):"calc(100% - ".concat(2*N,"px)")}},_=n(13e3);let W=function(e){var t,n,a=e.popupClassName,c=e.icon,l=e.title,s=e.theme,m=o.useContext(g),f=m.prefixCls,v=m.inlineCollapsed,b=m.theme,h=(0,r.Wj)();if(c){var y=o.isValidElement(l)&&"span"===l.type;n=o.createElement(o.Fragment,null,(0,p.Ob)(c,{className:u()(o.isValidElement(c)?null===(t=c.props)||void 0===t?void 0:t.className:"","".concat(f,"-item-icon"))}),y?l:o.createElement("span",{className:"".concat(f,"-title-content")},l))}else n=v&&!h.length&&l&&"string"==typeof l?o.createElement("div",{className:"".concat(f,"-inline-collapsed-noicon")},l.charAt(0)):o.createElement("span",{className:"".concat(f,"-title-content")},l);var C=o.useMemo(function(){return Object.assign(Object.assign({},m),{firstLevel:!1})},[m]),A=(0,i._)((0,_.YK)("Menu"),1)[0];return o.createElement(g.Provider,{value:C},o.createElement(r.g8,Object.assign({},(0,d.A)(e,["icon"]),{title:n,popupClassName:u()(f,a,"".concat(f,"-").concat(s||b)),popupStyle:Object.assign({zIndex:A},e.popupStyle)})))};var X=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function F(e){return null===e||!1===e}var V={item:A,submenu:W,divider:h},q=(0,o.forwardRef)(function(e,t){var n,a=o.useContext(I.h),l=a||{},b=o.useContext(f.QO),h=b.getPrefixCls,y=b.getPopupContainer,C=b.direction,A=b.menu,w=h(),S=e.prefixCls,x=e.className,R=e.style,j=e.theme,D=void 0===j?"light":j,H=e.expandIcon,_=e._internalDisableMenuItemTitleTooltip,W=e.inlineCollapsed,q=e.siderCollapsed,G=e.rootClassName,Y=e.mode,Q=e.selectable,U=e.onClick,J=e.overflowedIndicatorPopupClassName,Z=X(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),$=(0,d.A)(Z,["collapsedWidth"]);null===(n=l.validator)||void 0===n||n.call(l,{mode:Y});var ee=(0,s.A)(function(){var e;null==U||U.apply(void 0,arguments),null===(e=l.onClick)||void 0===e||e.call(l)}),et=l.mode||Y,en=null!=Q?Q:l.selectable,eo=null!=W?W:q,er={horizontal:{motionName:"".concat(w,"-slide-up")},inline:(0,m.A)(w),other:{motionName:"".concat(w,"-zoom-big")}},ea=h("menu",S||l.prefixCls),ei=(0,v.A)(ea),ec=(0,i._)(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,B.OF)("Menu",function(e){var t=e.colorBgElevated,n=e.controlHeightLG,o=e.fontSize,r=e.darkItemColor,a=e.darkDangerItemColor,i=e.darkItemBg,c=e.darkSubMenuItemBg,l=e.darkItemSelectedColor,u=e.darkItemSelectedBg,s=e.darkDangerItemSelectedBg,d=e.darkItemHoverBg,m=e.darkGroupTitleColor,p=e.darkItemHoverColor,f=e.darkItemDisabledColor,v=e.darkDangerItemHoverColor,g=e.darkDangerItemSelectedColor,b=e.darkDangerItemActiveBg,h=e.popupBg,y=e.darkPopupBg,C=e.calc(o).div(7).mul(5).equal(),A=(0,N.oX)(e,{menuArrowSize:C,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(C).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:h}),I=(0,N.oX)(A,{itemColor:r,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:l,subMenuItemSelectedColor:l,itemBg:i,popupBg:y,subMenuItemBg:c,itemActiveBg:"transparent",itemSelectedBg:u,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:d,itemDisabledColor:f,dangerItemColor:a,dangerItemHoverColor:v,dangerItemSelectedColor:g,dangerItemActiveBg:b,dangerItemSelectedBg:s,menuSubMenuBg:c,horizontalItemSelectedColor:l,horizontalItemSelectedBg:u});return[L(A),P(A),T(A),M(A,"light"),M(I,"dark"),z(A),(0,E.A)(A),(0,O._j)(A,"slide-up"),(0,O._j)(A,"slide-down"),(0,k.aB)(A,"zoom-big")]},K,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)}(ea,ei,!a),3),el=ec[0],eu=ec[1],es=ec[2],ed=u()("".concat(ea,"-").concat(D),null==A?void 0:A.className,x),em=o.useMemo(function(){if("function"==typeof H||F(H))return H||null;if("function"==typeof l.expandIcon||F(l.expandIcon))return l.expandIcon||null;if("function"==typeof(null==A?void 0:A.expandIcon)||F(null==A?void 0:A.expandIcon))return(null==A?void 0:A.expandIcon)||null;var e,t,n=null!==(e=null!=H?H:null==l?void 0:l.expandIcon)&&void 0!==e?e:null==A?void 0:A.expandIcon;return(0,p.Ob)(n,{className:u()("".concat(ea,"-submenu-expand-icon"),o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})},[H,null==l?void 0:l.expandIcon,null==A?void 0:A.expandIcon,ea]),ep=o.useMemo(function(){return{prefixCls:ea,inlineCollapsed:eo||!1,direction:C,firstLevel:!0,theme:D,mode:et,disableMenuItemTitleTooltip:_}},[ea,eo,C,_,D]);return el(o.createElement(I.h.Provider,{value:null},o.createElement(g.Provider,{value:ep},o.createElement(r.Ay,Object.assign({getPopupContainer:y,overflowedIndicator:o.createElement(c.A,null),overflowedIndicatorPopupClassName:u()(ea,"".concat(ea,"-").concat(D),J),mode:et,selectable:en,onClick:ee},$,{inlineCollapsed:eo,style:Object.assign(Object.assign({},null==A?void 0:A.style),R),className:ed,prefixCls:ea,direction:C,defaultMotions:er,expandIcon:em,ref:t,rootClassName:u()(G,eu,l.rootClassName,es,ei),_internalComponents:V})))))}),G=(0,o.forwardRef)(function(e,t){var n=(0,o.useRef)(null),r=o.useContext(a.P);return(0,o.useImperativeHandle)(t,function(){return{menu:n.current,focus:function(e){var t;null===(t=n.current)||void 0===t||t.focus(e)}}}),o.createElement(q,Object.assign({ref:n},e,r))});G.Item=A,G.SubMenu=W,G.Divider=h,G.ItemGroup=r.te;let Y=G},74838:(e,t,n)=>{n.d(t,{A:()=>g});var o=n(75101),r=n(12694),a=n(21462),i=n(5587),c=n(46001),l=n.n(c),u=n(67657),s=n(77312),d=n(12300),m=n(77708),p=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n},f=function(e){var t=a.useContext(s.QO),n=t.getPopupContainer,c=t.getPrefixCls,f=t.direction,v=e.prefixCls,g=e.type,b=void 0===g?"default":g,h=e.danger,y=e.disabled,C=e.loading,A=e.onClick,I=e.htmlType,w=e.children,S=e.className,x=e.menu,E=e.arrow,O=e.autoFocus,k=e.overlay,B=e.trigger,N=e.align,P=e.open,z=e.onOpenChange,R=e.placement,M=e.getPopupContainer,j=e.href,T=e.icon,D=void 0===T?a.createElement(i.A,null):T,H=e.title,L=e.buttonsRender,K=e.mouseEnterDelay,_=e.mouseLeaveDelay,W=e.overlayClassName,X=e.overlayStyle,F=e.destroyPopupOnHide,V=e.dropdownRender,q=p(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),G=c("dropdown",v),Y={menu:x,arrow:E,autoFocus:O,align:N,disabled:y,trigger:y?[]:B,onOpenChange:z,getPopupContainer:M||n,mouseEnterDelay:K,mouseLeaveDelay:_,overlayClassName:W,overlayStyle:X,destroyPopupOnHide:F,dropdownRender:V},Q=(0,m.RQ)(G,f),U=Q.compactSize,J=Q.compactItemClassnames,Z=l()("".concat(G,"-button"),J,S);"overlay"in e&&(Y.overlay=k),"open"in e&&(Y.open=P),"placement"in e?Y.placement=R:Y.placement="rtl"===f?"bottomLeft":"bottomRight";var $=a.createElement(u.Ay,{type:b,danger:h,disabled:y,loading:C,onClick:A,htmlType:I,href:j,title:H},w),ee=a.createElement(u.Ay,{type:b,danger:h,icon:D}),et=(0,r._)((void 0===L?function(e){return e}:L)([$,ee]),2),en=et[0],eo=et[1];return a.createElement(d.A.Compact,Object.assign({className:Z,size:U,block:!0},q),en,a.createElement(o.A,Object.assign({},Y),eo))};f.__ANT_BUTTON=!0;var v=o.A;v.Button=f;let g=v},75101:(e,t,n)=>{n.d(t,{A:()=>H});var o=n(12694),r=n(21462),a=n(31259),i=n(73208),c=n(46001),l=n.n(c),u=n(54017),s=n(17763),d=n(39074),m=n(81698),p=n(13e3),f=n(33234),v=n(76442),g=n(49357),b=n(5282),h=n(72955),y=n(77312),C=n(50945),A=n(58815),I=n(8035),w=n(44867),S=n(64467),x=n(5214),E=n(80610),O=n(61904),k=n(10186),B=n(33542),N=n(54044),P=n(68197),z=n(13440);let R=function(e){var t=e.componentCls,n=e.menuCls,o=e.colorError,r=e.colorTextLightSolid,a="".concat(n,"-item");return{["".concat(t,", ").concat(t,"-menu-submenu")]:{["".concat(n," ").concat(a)]:{["&".concat(a,"-danger:not(").concat(a,"-disabled)")]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}};var M=function(e){var t=e.componentCls,n=e.menuCls,o=e.zIndexPopup,r=e.dropdownArrowDistance,a=e.sizePopupArrow,i=e.antCls,c=e.iconCls,l=e.motionDurationMid,u=e.paddingBlock,s=e.fontSize,d=e.dropdownEdgeChildPadding,m=e.colorTextDisabled,p=e.fontSizeIcon,f=e.controlPaddingHorizontal,v=e.colorBgElevated;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(a).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},["&-trigger".concat(i,"-btn")]:{["& > ".concat(c,"-down, & > ").concat(i,"-btn-icon > ").concat(c,"-down")]:{fontSize:p}},["".concat(t,"-wrap")]:{position:"relative",["".concat(i,"-btn > ").concat(c,"-down")]:{fontSize:p},["".concat(c,"-down::before")]:{transition:"transform ".concat(l)}},["".concat(t,"-wrap-open")]:{["".concat(c,"-down::before")]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},["&".concat(i,"-slide-down-enter").concat(i,"-slide-down-enter-active").concat(t,"-placement-bottomLeft,\n          &").concat(i,"-slide-down-appear").concat(i,"-slide-down-appear-active").concat(t,"-placement-bottomLeft,\n          &").concat(i,"-slide-down-enter").concat(i,"-slide-down-enter-active").concat(t,"-placement-bottom,\n          &").concat(i,"-slide-down-appear").concat(i,"-slide-down-appear-active").concat(t,"-placement-bottom,\n          &").concat(i,"-slide-down-enter").concat(i,"-slide-down-enter-active").concat(t,"-placement-bottomRight,\n          &").concat(i,"-slide-down-appear").concat(i,"-slide-down-appear-active").concat(t,"-placement-bottomRight")]:{animationName:E.ox},["&".concat(i,"-slide-up-enter").concat(i,"-slide-up-enter-active").concat(t,"-placement-topLeft,\n          &").concat(i,"-slide-up-appear").concat(i,"-slide-up-appear-active").concat(t,"-placement-topLeft,\n          &").concat(i,"-slide-up-enter").concat(i,"-slide-up-enter-active").concat(t,"-placement-top,\n          &").concat(i,"-slide-up-appear").concat(i,"-slide-up-appear-active").concat(t,"-placement-top,\n          &").concat(i,"-slide-up-enter").concat(i,"-slide-up-enter-active").concat(t,"-placement-topRight,\n          &").concat(i,"-slide-up-appear").concat(i,"-slide-up-appear-active").concat(t,"-placement-topRight")]:{animationName:E.nP},["&".concat(i,"-slide-down-leave").concat(i,"-slide-down-leave-active").concat(t,"-placement-bottomLeft,\n          &").concat(i,"-slide-down-leave").concat(i,"-slide-down-leave-active").concat(t,"-placement-bottom,\n          &").concat(i,"-slide-down-leave").concat(i,"-slide-down-leave-active").concat(t,"-placement-bottomRight")]:{animationName:E.vR},["&".concat(i,"-slide-up-leave").concat(i,"-slide-up-leave-active").concat(t,"-placement-topLeft,\n          &").concat(i,"-slide-up-leave").concat(i,"-slide-up-leave-active").concat(t,"-placement-top,\n          &").concat(i,"-slide-up-leave").concat(i,"-slide-up-leave-active").concat(t,"-placement-topRight")]:{animationName:E.YU}}},(0,B.Ay)(e,v,{arrowPlacement:{top:!0,bottom:!0}}),{["".concat(t," ").concat(n)]:{position:"relative",margin:0},["".concat(n,"-submenu-popup")]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},["".concat(t,", ").concat(t,"-menu-submenu")]:Object.assign(Object.assign({},(0,x.dF)(e)),{[n]:Object.assign(Object.assign({padding:d,listStyleType:"none",backgroundColor:v,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,x.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},["".concat(n,"-item-group-title")]:{padding:"".concat((0,S.zA)(u)," ").concat((0,S.zA)(f)),color:e.colorTextDescription,transition:"all ".concat(l)},["".concat(n,"-item")]:{position:"relative",display:"flex",alignItems:"center"},["".concat(n,"-item-icon")]:{minWidth:s,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},["".concat(n,"-title-content")]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:"all ".concat(l),"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},["".concat(n,"-item-extra")]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},["".concat(n,"-item, ").concat(n,"-submenu-title")]:Object.assign(Object.assign({display:"flex",margin:0,padding:"".concat((0,S.zA)(u)," ").concat((0,S.zA)(f)),color:e.colorText,fontWeight:"normal",fontSize:s,lineHeight:e.lineHeight,cursor:"pointer",transition:"all ".concat(l),borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,x.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:v,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:"".concat((0,S.zA)(e.marginXXS)," 0"),overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},["".concat(t,"-menu-submenu-expand-icon")]:{position:"absolute",insetInlineEnd:e.paddingXS,["".concat(t,"-menu-submenu-arrow-icon")]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:p,fontStyle:"normal"}}}),["".concat(n,"-item-group-list")]:{margin:"0 ".concat((0,S.zA)(e.marginXS)),padding:0,listStyle:"none"},["".concat(n,"-submenu-title")]:{paddingInlineEnd:e.calc(f).add(e.fontSizeSM).equal()},["".concat(n,"-submenu-vertical")]:{position:"relative"},["".concat(n,"-submenu").concat(n,"-submenu-disabled ").concat(t,"-menu-submenu-title")]:{["&, ".concat(t,"-menu-submenu-arrow-icon")]:{color:m,backgroundColor:v,cursor:"not-allowed"}},["".concat(n,"-submenu-selected ").concat(t,"-menu-submenu-title")]:{color:e.colorPrimary}})})},[(0,E._j)(e,"slide-up"),(0,E._j)(e,"slide-down"),(0,O.Mh)(e,"move-up"),(0,O.Mh)(e,"move-down"),(0,k.aB)(e,"zoom-big")]]};let j=(0,P.OF)("Dropdown",function(e){var t=e.marginXXS,n=e.sizePopupArrow,o=e.paddingXXS,r=e.componentCls,a=(0,z.oX)(e,{menuCls:"".concat(r,"-menu"),dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[M(a),R(a)]},function(e){return Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,B.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,N.n)(e))},{resetStyle:!1});var T=function(e){var t,n=e.menu,c=e.arrow,v=e.prefixCls,S=e.children,x=e.trigger,E=e.disabled,O=e.dropdownRender,k=e.getPopupContainer,B=e.overlayClassName,N=e.rootClassName,P=e.overlayStyle,z=e.open,R=e.onOpenChange,M=e.visible,T=e.onVisibleChange,D=e.mouseEnterDelay,H=e.mouseLeaveDelay,L=e.autoAdjustOverflow,K=e.placement,_=void 0===K?"":K,W=e.overlay,X=e.transitionName,F=r.useContext(y.QO),V=F.getPopupContainer,q=F.getPrefixCls,G=F.direction,Y=F.dropdown;(0,b.rJ)("Dropdown");var Q=r.useMemo(function(){var e=q();return void 0!==X?X:_.includes("top")?"".concat(e,"-slide-down"):"".concat(e,"-slide-up")},[q,_,X]),U=r.useMemo(function(){return _?_.includes("Center")?_.slice(0,_.indexOf("Center")):_:"rtl"===G?"bottomRight":"bottomLeft"},[_,G]),J=q("dropdown",v),Z=(0,C.A)(J),$=(0,o._)(j(J,Z),3),ee=$[0],et=$[1],en=$[2],eo=(0,o._)((0,w.Ay)(),2)[1],er=r.Children.only("object"!=typeof S&&"function"!=typeof S||null===S?r.createElement("span",null,S):S),ea=(0,g.Ob)(er,{className:l()("".concat(J,"-trigger"),{["".concat(J,"-rtl")]:"rtl"===G},er.props.className),disabled:null!==(t=er.props.disabled)&&void 0!==t?t:E}),ei=E?[]:x,ec=!!(null==ei?void 0:ei.includes("contextMenu")),el=(0,o._)((0,d.A)(!1,{value:null!=z?z:M}),2),eu=el[0],es=el[1],ed=(0,s.A)(function(e){null==R||R(e,{source:"trigger"}),null==T||T(e),es(e)}),em=l()(B,N,et,en,Z,null==Y?void 0:Y.className,{["".concat(J,"-rtl")]:"rtl"===G}),ep=(0,f.A)({arrowPointAtCenter:"object"==typeof c&&c.pointAtCenter,autoAdjustOverflow:void 0===L||L,offset:eo.marginXXS,arrowWidth:c?eo.sizePopupArrow:0,borderRadius:eo.borderRadius}),ef=r.useCallback(function(){(null==n||!n.selectable||null==n||!n.multiple)&&(null==R||R(!1,{source:"menu"}),es(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),ev=(0,o._)((0,p.YK)("Dropdown",null==P?void 0:P.zIndex),2),eg=ev[0],eb=ev[1],eh=r.createElement(u.A,Object.assign({alignPoint:ec},(0,m.A)(e,["rootClassName"]),{mouseEnterDelay:void 0===D?.15:D,mouseLeaveDelay:void 0===H?.1:H,visible:eu,builtinPlacements:ep,arrow:!!c,overlayClassName:em,prefixCls:J,getPopupContainer:k||V,transitionName:Q,trigger:ei,overlay:function(){var e;return e=(null==n?void 0:n.items)?r.createElement(A.A,Object.assign({},n)):"function"==typeof W?W():W,O&&(e=O(e)),e=r.Children.only("string"==typeof e?r.createElement("span",null,e):e),r.createElement(I.A,{prefixCls:"".concat(J,"-menu"),rootClassName:l()(en,Z),expandIcon:r.createElement("span",{className:"".concat(J,"-menu-submenu-arrow")},"rtl"===G?r.createElement(a.A,{className:"".concat(J,"-menu-submenu-arrow-icon")}):r.createElement(i.A,{className:"".concat(J,"-menu-submenu-arrow-icon")})),mode:"vertical",selectable:!1,onClick:ef,validator:function(e){e.mode}},e)},placement:U,onVisibleChange:ed,overlayStyle:Object.assign(Object.assign(Object.assign({},null==Y?void 0:Y.style),P),{zIndex:eg})}),ea);return eg&&(eh=r.createElement(h.A.Provider,{value:eb},eh)),ee(eh)},D=(0,v.A)(T,"align",void 0,"dropdown",function(e){return e});T._InternalPanelDoNotUseOrYouWillBeFired=function(e){return r.createElement(D,Object.assign({},e),r.createElement("span",null))};let H=T},77707:(e,t,n)=>{n.d(t,{cG:()=>eR,q7:()=>ef,te:()=>eT,Dr:()=>ef,g8:()=>eP,Ay:()=>eW,Wj:()=>O});var o=n(35726),r=n(26975),a=n(20477),i=n(53172),c=n(60295),l=n(28750),u=n(46001),s=n.n(u),d=n(82393),m=n(39074),p=n(74365),f=n(97789),v=n(21462),g=n(47993),b=v.createContext(null);function h(e,t){return void 0===e?null:"".concat(e,"-").concat(t)}function y(e){return h(v.useContext(b),e)}var C=n(8207),A=["children","locked"],I=v.createContext(null);function w(e){var t=e.children,n=e.locked,o=(0,l.A)(e,A),r=v.useContext(I),i=(0,C.A)(function(){var e;return e=(0,a.A)({},r),Object.keys(o).forEach(function(t){var n=o[t];void 0!==n&&(e[t]=n)}),e},[r,o],function(e,t){return!n&&(e[0]!==t[0]||!(0,p.A)(e[1],t[1],!0))});return v.createElement(I.Provider,{value:i},t)}var S=v.createContext(null);function x(){return v.useContext(S)}var E=v.createContext([]);function O(e){var t=v.useContext(E);return v.useMemo(function(){return void 0!==e?[].concat((0,i.A)(t),[e]):t},[t,e])}var k=v.createContext(null),B=v.createContext({}),N=n(88212);function P(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,N.A)(e)){var n=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(n)||e.isContentEditable||"a"===n&&!!e.getAttribute("href"),r=e.getAttribute("tabindex"),a=Number(r),i=null;return r&&!Number.isNaN(a)?i=a:o&&null===i&&(i=0),o&&e.disabled&&(i=null),null!==i&&(i>=0||t&&i<0)}return!1}var z=n(15191),R=n(35884),M=z.A.LEFT,j=z.A.RIGHT,T=z.A.UP,D=z.A.DOWN,H=z.A.ENTER,L=z.A.ESC,K=z.A.HOME,_=z.A.END,W=[T,D,M,j];function X(e,t){return(function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=(0,i.A)(e.querySelectorAll("*")).filter(function(e){return P(e,t)});return P(e,t)&&n.unshift(e),n})(e,!0).filter(function(e){return t.has(e)})}function F(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var r=X(e,t),a=r.length,i=r.findIndex(function(e){return n===e});return o<0?-1===i?i=a-1:i-=1:o>0&&(i+=1),r[i=(i+a)%a]}var V=function(e,t){var n=new Set,o=new Map,r=new Map;return e.forEach(function(e){var a=document.querySelector("[data-menu-id='".concat(h(t,e),"']"));a&&(n.add(a),r.set(a,e),o.set(e,a))}),{elements:n,key2element:o,element2key:r}},q="__RC_UTIL_PATH_SPLIT__",G=function(e){return e.join(q)},Y="rc-menu-more";function Q(e){var t=v.useRef(e);t.current=e;var n=v.useCallback(function(){for(var e,n=arguments.length,o=Array(n),r=0;r<n;r++)o[r]=arguments[r];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(o))},[]);return e?n:void 0}var U=Math.random().toFixed(5).toString().slice(2),J=0,Z=n(74729),$=n(9987),ee=n(20577),et=n(50188),en=n(81698),eo=n(59744);function er(e,t,n,o){var r=v.useContext(I),a=r.activeKey,i=r.onActive,c=r.onInactive,l={active:a===e};return t||(l.onMouseEnter=function(t){null==n||n({key:e,domEvent:t}),i(e)},l.onMouseLeave=function(t){null==o||o({key:e,domEvent:t}),c(e)}),l}function ea(e){var t=v.useContext(I),n=t.mode,o=t.rtl,r=t.inlineIndent;return"inline"!==n?null:o?{paddingRight:e*r}:{paddingLeft:e*r}}function ei(e){var t,n=e.icon,o=e.props,r=e.children;return null===n||!1===n?null:("function"==typeof n?t=v.createElement(n,(0,a.A)({},o)):"boolean"!=typeof n&&(t=n),t||r||null)}var ec=["item"];function el(e){var t=e.item,n=(0,l.A)(e,ec);return Object.defineProperty(n,"item",{get:function(){return(0,f.Ay)(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var eu=["title","attribute","elementRef"],es=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],ed=["active"],em=function(e){(0,ee.A)(n,e);var t=(0,et.A)(n);function n(){return(0,Z.A)(this,n),t.apply(this,arguments)}return(0,$.A)(n,[{key:"render",value:function(){var e=this.props,t=e.title,n=e.attribute,r=e.elementRef,a=(0,l.A)(e,eu),i=(0,en.A)(a,["eventKey","popupClassName","popupOffset","onTitleClick"]);return(0,f.Ay)(!n,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),v.createElement(d.A.Item,(0,o.A)({},n,{title:"string"==typeof t?t:void 0},i,{ref:r}))}}]),n}(v.Component),ep=v.forwardRef(function(e,t){var n=e.style,c=e.className,u=e.eventKey,d=(e.warnKey,e.disabled),m=e.itemIcon,p=e.children,f=e.role,g=e.onMouseEnter,b=e.onMouseLeave,h=e.onClick,C=e.onKeyDown,A=e.onFocus,w=(0,l.A)(e,es),S=y(u),x=v.useContext(I),E=x.prefixCls,k=x.onItemClick,N=x.disabled,P=x.overflowDisabled,R=x.itemIcon,M=x.selectedKeys,j=x.onActive,T=v.useContext(B)._internalRenderMenuItem,D="".concat(E,"-item"),H=v.useRef(),L=v.useRef(),K=N||d,_=(0,eo.xK)(t,L),W=O(u),X=function(e){return{key:u,keyPath:(0,i.A)(W).reverse(),item:H.current,domEvent:e}},F=er(u,K,g,b),V=F.active,q=(0,l.A)(F,ed),G=M.includes(u),Y=ea(W.length),Q={};"option"===e.role&&(Q["aria-selected"]=G);var U=v.createElement(em,(0,o.A)({ref:H,elementRef:_,role:null===f?"none":f||"menuitem",tabIndex:d?null:-1,"data-menu-id":P&&S?null:S},(0,en.A)(w,["extra"]),q,Q,{component:"li","aria-disabled":d,style:(0,a.A)((0,a.A)({},Y),n),className:s()(D,(0,r.A)((0,r.A)((0,r.A)({},"".concat(D,"-active"),V),"".concat(D,"-selected"),G),"".concat(D,"-disabled"),K),c),onClick:function(e){if(!K){var t=X(e);null==h||h(el(t)),k(t)}},onKeyDown:function(e){if(null==C||C(e),e.which===z.A.ENTER){var t=X(e);null==h||h(el(t)),k(t)}},onFocus:function(e){j(u),null==A||A(e)}}),p,v.createElement(ei,{props:(0,a.A)((0,a.A)({},e),{},{isSelected:G}),icon:m||R}));return T&&(U=T(U,e,{selected:G})),U});let ef=v.forwardRef(function(e,t){var n=e.eventKey,r=x(),a=O(n);return(v.useEffect(function(){if(r)return r.registerPath(n,a),function(){r.unregisterPath(n,a)}},[a]),r)?null:v.createElement(ep,(0,o.A)({},e,{ref:t}))});var ev=["className","children"],eg=v.forwardRef(function(e,t){var n=e.className,r=e.children,a=(0,l.A)(e,ev),i=v.useContext(I),c=i.prefixCls,u=i.mode,d=i.rtl;return v.createElement("ul",(0,o.A)({className:s()(c,d&&"".concat(c,"-rtl"),"".concat(c,"-sub"),"".concat(c,"-").concat("inline"===u?"inline":"vertical"),n),role:"menu"},a,{"data-menu-list":!0,ref:t}),r)});eg.displayName="SubMenuList";var eb=n(721);function eh(e,t){return(0,eb.A)(e).map(function(e,n){if(v.isValidElement(e)){var o,r,a=e.key,c=null!==(o=null===(r=e.props)||void 0===r?void 0:r.eventKey)&&void 0!==o?o:a;null==c&&(c="tmp_key-".concat([].concat((0,i.A)(t),[n]).join("-")));var l={key:c,eventKey:c};return v.cloneElement(e,l)}return e})}var ey=n(24735),eC={adjustX:1,adjustY:1},eA={topLeft:{points:["bl","tl"],overflow:eC},topRight:{points:["br","tr"],overflow:eC},bottomLeft:{points:["tl","bl"],overflow:eC},bottomRight:{points:["tr","br"],overflow:eC},leftTop:{points:["tr","tl"],overflow:eC},leftBottom:{points:["br","bl"],overflow:eC},rightTop:{points:["tl","tr"],overflow:eC},rightBottom:{points:["bl","br"],overflow:eC}},eI={topLeft:{points:["bl","tl"],overflow:eC},topRight:{points:["br","tr"],overflow:eC},bottomLeft:{points:["tl","bl"],overflow:eC},bottomRight:{points:["tr","br"],overflow:eC},rightTop:{points:["tr","tl"],overflow:eC},rightBottom:{points:["br","bl"],overflow:eC},leftTop:{points:["tl","tr"],overflow:eC},leftBottom:{points:["bl","br"],overflow:eC}};function ew(e,t,n){return t||(n?n[e]||n.other:void 0)}var eS={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function ex(e){var t=e.prefixCls,n=e.visible,o=e.children,i=e.popup,l=e.popupStyle,u=e.popupClassName,d=e.popupOffset,m=e.disabled,p=e.mode,f=e.onVisibleChange,g=v.useContext(I),b=g.getPopupContainer,h=g.rtl,y=g.subMenuOpenDelay,C=g.subMenuCloseDelay,A=g.builtinPlacements,w=g.triggerSubMenuAction,S=g.forceSubMenuRender,x=g.rootClassName,E=g.motion,O=g.defaultMotions,k=v.useState(!1),B=(0,c.A)(k,2),N=B[0],P=B[1],z=h?(0,a.A)((0,a.A)({},eI),A):(0,a.A)((0,a.A)({},eA),A),M=eS[p],j=ew(p,E,O),T=v.useRef(j);"inline"!==p&&(T.current=j);var D=(0,a.A)((0,a.A)({},T.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),H=v.useRef();return v.useEffect(function(){return H.current=(0,R.A)(function(){P(n)}),function(){R.A.cancel(H.current)}},[n]),v.createElement(ey.A,{prefixCls:t,popupClassName:s()("".concat(t,"-popup"),(0,r.A)({},"".concat(t,"-rtl"),h),u,x),stretch:"horizontal"===p?"minWidth":null,getPopupContainer:b,builtinPlacements:z,popupPlacement:M,popupVisible:N,popup:i,popupStyle:l,popupAlign:d&&{offset:d},action:m?[]:[w],mouseEnterDelay:y,mouseLeaveDelay:C,onPopupVisibleChange:f,forceRender:S,popupMotion:D,fresh:!0},o)}var eE=n(5206);function eO(e){var t=e.id,n=e.open,r=e.keyPath,i=e.children,l="inline",u=v.useContext(I),s=u.prefixCls,d=u.forceSubMenuRender,m=u.motion,p=u.defaultMotions,f=u.mode,g=v.useRef(!1);g.current=f===l;var b=v.useState(!g.current),h=(0,c.A)(b,2),y=h[0],C=h[1],A=!!g.current&&n;v.useEffect(function(){g.current&&C(!1)},[f]);var S=(0,a.A)({},ew(l,m,p));r.length>1&&(S.motionAppear=!1);var x=S.onVisibleChanged;return(S.onVisibleChanged=function(e){return g.current||e||C(!0),null==x?void 0:x(e)},y)?null:v.createElement(w,{mode:l,locked:!g.current},v.createElement(eE.Ay,(0,o.A)({visible:A},S,{forceRender:d,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(e){var n=e.className,o=e.style;return v.createElement(eg,{id:t,className:n,style:o},i)}))}var ek=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],eB=["active"],eN=v.forwardRef(function(e,t){var n=e.style,i=e.className,u=e.title,m=e.eventKey,p=(e.warnKey,e.disabled),f=e.internalPopupClose,g=e.children,b=e.itemIcon,h=e.expandIcon,C=e.popupClassName,A=e.popupOffset,S=e.popupStyle,x=e.onClick,E=e.onMouseEnter,N=e.onMouseLeave,P=e.onTitleClick,z=e.onTitleMouseEnter,R=e.onTitleMouseLeave,M=(0,l.A)(e,ek),j=y(m),T=v.useContext(I),D=T.prefixCls,H=T.mode,L=T.openKeys,K=T.disabled,_=T.overflowDisabled,W=T.activeKey,X=T.selectedKeys,F=T.itemIcon,V=T.expandIcon,q=T.onItemClick,G=T.onOpenChange,Y=T.onActive,U=v.useContext(B)._internalRenderSubMenuItem,J=v.useContext(k).isSubPathKey,Z=O(),$="".concat(D,"-submenu"),ee=K||p,et=v.useRef(),en=v.useRef(),eo=null!=h?h:V,ec=L.includes(m),eu=!_&&ec,es=J(X,m),ed=er(m,ee,z,R),em=ed.active,ep=(0,l.A)(ed,eB),ef=v.useState(!1),ev=(0,c.A)(ef,2),eb=ev[0],eh=ev[1],ey=function(e){ee||eh(e)},eC=v.useMemo(function(){return em||"inline"!==H&&(eb||J([W],m))},[H,em,W,eb,m,J]),eA=ea(Z.length),eI=Q(function(e){null==x||x(el(e)),q(e)}),ew=j&&"".concat(j,"-popup"),eS=v.useMemo(function(){return v.createElement(ei,{icon:"horizontal"!==H?eo:void 0,props:(0,a.A)((0,a.A)({},e),{},{isOpen:eu,isSubMenu:!0})},v.createElement("i",{className:"".concat($,"-arrow")}))},[H,eo,e,eu,$]),eE=v.createElement("div",(0,o.A)({role:"menuitem",style:eA,className:"".concat($,"-title"),tabIndex:ee?null:-1,ref:et,title:"string"==typeof u?u:null,"data-menu-id":_&&j?null:j,"aria-expanded":eu,"aria-haspopup":!0,"aria-controls":ew,"aria-disabled":ee,onClick:function(e){!ee&&(null==P||P({key:m,domEvent:e}),"inline"===H&&G(m,!ec))},onFocus:function(){Y(m)}},ep),u,eS),eN=v.useRef(H);if("inline"!==H&&Z.length>1?eN.current="vertical":eN.current=H,!_){var eP=eN.current;eE=v.createElement(ex,{mode:eP,prefixCls:$,visible:!f&&eu&&"inline"!==H,popupClassName:C,popupOffset:A,popupStyle:S,popup:v.createElement(w,{mode:"horizontal"===eP?"vertical":eP},v.createElement(eg,{id:ew,ref:en},g)),disabled:ee,onVisibleChange:function(e){"inline"!==H&&G(m,e)}},eE)}var ez=v.createElement(d.A.Item,(0,o.A)({ref:t,role:"none"},M,{component:"li",style:n,className:s()($,"".concat($,"-").concat(H),i,(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},"".concat($,"-open"),eu),"".concat($,"-active"),eC),"".concat($,"-selected"),es),"".concat($,"-disabled"),ee)),onMouseEnter:function(e){ey(!0),null==E||E({key:m,domEvent:e})},onMouseLeave:function(e){ey(!1),null==N||N({key:m,domEvent:e})}}),eE,!_&&v.createElement(eO,{id:ew,open:eu,keyPath:Z},g));return U&&(ez=U(ez,e,{selected:es,active:eC,open:eu,disabled:ee})),v.createElement(w,{onItemClick:eI,mode:"horizontal"===H?"vertical":H,itemIcon:null!=b?b:F,expandIcon:eo},ez)});let eP=v.forwardRef(function(e,t){var n,r=e.eventKey,a=e.children,i=O(r),c=eh(a,i),l=x();return v.useEffect(function(){if(l)return l.registerPath(r,i),function(){l.unregisterPath(r,i)}},[i]),n=l?c:v.createElement(eN,(0,o.A)({ref:t},e),c),v.createElement(E.Provider,{value:i},n)});var ez=n(75884);function eR(e){var t=e.className,n=e.style,o=v.useContext(I).prefixCls;return x()?null:v.createElement("li",{role:"separator",className:s()("".concat(o,"-item-divider"),t),style:n})}var eM=["className","title","eventKey","children"],ej=v.forwardRef(function(e,t){var n=e.className,r=e.title,a=(e.eventKey,e.children),i=(0,l.A)(e,eM),c=v.useContext(I).prefixCls,u="".concat(c,"-item-group");return v.createElement("li",(0,o.A)({ref:t,role:"presentation"},i,{onClick:function(e){return e.stopPropagation()},className:s()(u,n)}),v.createElement("div",{role:"presentation",className:"".concat(u,"-title"),title:"string"==typeof r?r:void 0},r),v.createElement("ul",{role:"group",className:"".concat(u,"-list")},a))});let eT=v.forwardRef(function(e,t){var n=e.eventKey,r=eh(e.children,O(n));return x()?r:v.createElement(ej,(0,o.A)({ref:t},(0,en.A)(e,["warnKey"])),r)});var eD=["label","children","key","type","extra"];function eH(e,t,n,r,i){var c=e,u=(0,a.A)({divider:eR,item:ef,group:eT,submenu:eP},r);return t&&(c=function e(t,n,r){var a=n.item,i=n.group,c=n.submenu,u=n.divider;return(t||[]).map(function(t,s){if(t&&"object"===(0,ez.A)(t)){var d=t.label,m=t.children,p=t.key,f=t.type,g=t.extra,b=(0,l.A)(t,eD),h=null!=p?p:"tmp-".concat(s);return m||"group"===f?"group"===f?v.createElement(i,(0,o.A)({key:h},b,{title:d}),e(m,n,r)):v.createElement(c,(0,o.A)({key:h},b,{title:d}),e(m,n,r)):"divider"===f?v.createElement(u,(0,o.A)({key:h},b)):v.createElement(a,(0,o.A)({key:h},b,{extra:g}),d,(!!g||0===g)&&v.createElement("span",{className:"".concat(r,"-item-extra")},g))}return null}).filter(function(e){return e})}(t,u,i)),eh(c,n)}var eL=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],eK=[],e_=v.forwardRef(function(e,t){var n,u,f,h,y,C,A,I,x,E,O,N,P,z,Z,$,ee,et,en,eo,er,ea,ei,ec,eu,es,ed=e.prefixCls,em=void 0===ed?"rc-menu":ed,ep=e.rootClassName,ev=e.style,eg=e.className,eb=e.tabIndex,eh=e.items,ey=e.children,eC=e.direction,eA=e.id,eI=e.mode,ew=void 0===eI?"vertical":eI,eS=e.inlineCollapsed,ex=e.disabled,eE=e.disabledOverflow,eO=e.subMenuOpenDelay,ek=e.subMenuCloseDelay,eB=e.forceSubMenuRender,eN=e.defaultOpenKeys,ez=e.openKeys,eR=e.activeKey,eM=e.defaultActiveFirst,ej=e.selectable,eT=void 0===ej||ej,eD=e.multiple,e_=void 0!==eD&&eD,eW=e.defaultSelectedKeys,eX=e.selectedKeys,eF=e.onSelect,eV=e.onDeselect,eq=e.inlineIndent,eG=e.motion,eY=e.defaultMotions,eQ=e.triggerSubMenuAction,eU=e.builtinPlacements,eJ=e.itemIcon,eZ=e.expandIcon,e$=e.overflowedIndicator,e0=void 0===e$?"...":e$,e1=e.overflowedIndicatorPopupClassName,e2=e.getPopupContainer,e5=e.onClick,e4=e.onOpenChange,e7=e.onKeyDown,e6=(e.openAnimation,e.openTransitionName,e._internalRenderMenuItem),e3=e._internalRenderSubMenuItem,e9=e._internalComponents,e8=(0,l.A)(e,eL),te=v.useMemo(function(){return[eH(ey,eh,eK,e9,em),eH(ey,eh,eK,{},em)]},[ey,eh,e9]),tt=(0,c.A)(te,2),tn=tt[0],to=tt[1],tr=v.useState(!1),ta=(0,c.A)(tr,2),ti=ta[0],tc=ta[1],tl=v.useRef(),tu=(n=(0,m.A)(eA,{value:eA}),f=(u=(0,c.A)(n,2))[0],h=u[1],v.useEffect(function(){J+=1;var e="".concat(U,"-").concat(J);h("rc-menu-uuid-".concat(e))},[]),f),ts="rtl"===eC,td=(0,m.A)(eN,{value:ez,postState:function(e){return e||eK}}),tm=(0,c.A)(td,2),tp=tm[0],tf=tm[1],tv=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function n(){tf(e),null==e4||e4(e)}t?(0,g.flushSync)(n):n()},tg=v.useState(tp),tb=(0,c.A)(tg,2),th=tb[0],ty=tb[1],tC=v.useRef(!1),tA=v.useMemo(function(){return("inline"===ew||"vertical"===ew)&&eS?["vertical",eS]:[ew,!1]},[ew,eS]),tI=(0,c.A)(tA,2),tw=tI[0],tS=tI[1],tx="inline"===tw,tE=v.useState(tw),tO=(0,c.A)(tE,2),tk=tO[0],tB=tO[1],tN=v.useState(tS),tP=(0,c.A)(tN,2),tz=tP[0],tR=tP[1];v.useEffect(function(){tB(tw),tR(tS),tC.current&&(tx?tf(th):tv(eK))},[tw,tS]);var tM=v.useState(0),tj=(0,c.A)(tM,2),tT=tj[0],tD=tj[1],tH=tT>=tn.length-1||"horizontal"!==tk||eE;v.useEffect(function(){tx&&ty(tp)},[tp]),v.useEffect(function(){return tC.current=!0,function(){tC.current=!1}},[]);var tL=(y=v.useState({}),C=(0,c.A)(y,2)[1],A=(0,v.useRef)(new Map),I=(0,v.useRef)(new Map),x=v.useState([]),O=(E=(0,c.A)(x,2))[0],N=E[1],P=(0,v.useRef)(0),z=(0,v.useRef)(!1),Z=function(){z.current||C({})},$=(0,v.useCallback)(function(e,t){var n=G(t);I.current.set(n,e),A.current.set(e,n),P.current+=1;var o=P.current;Promise.resolve().then(function(){o===P.current&&Z()})},[]),ee=(0,v.useCallback)(function(e,t){var n=G(t);I.current.delete(n),A.current.delete(e)},[]),et=(0,v.useCallback)(function(e){N(e)},[]),en=(0,v.useCallback)(function(e,t){var n=(A.current.get(e)||"").split(q);return t&&O.includes(n[0])&&n.unshift(Y),n},[O]),eo=(0,v.useCallback)(function(e,t){return e.filter(function(e){return void 0!==e}).some(function(e){return en(e,!0).includes(t)})},[en]),er=(0,v.useCallback)(function(e){var t="".concat(A.current.get(e)).concat(q),n=new Set;return(0,i.A)(I.current.keys()).forEach(function(e){e.startsWith(t)&&n.add(I.current.get(e))}),n},[]),v.useEffect(function(){return function(){z.current=!0}},[]),{registerPath:$,unregisterPath:ee,refreshOverflowKeys:et,isSubPathKey:eo,getKeyPath:en,getKeys:function(){var e=(0,i.A)(A.current.keys());return O.length&&e.push(Y),e},getSubPathKeys:er}),tK=tL.registerPath,t_=tL.unregisterPath,tW=tL.refreshOverflowKeys,tX=tL.isSubPathKey,tF=tL.getKeyPath,tV=tL.getKeys,tq=tL.getSubPathKeys,tG=v.useMemo(function(){return{registerPath:tK,unregisterPath:t_}},[tK,t_]),tY=v.useMemo(function(){return{isSubPathKey:tX}},[tX]);v.useEffect(function(){tW(tH?eK:tn.slice(tT+1).map(function(e){return e.key}))},[tT,tH]);var tQ=(0,m.A)(eR||eM&&(null===(es=tn[0])||void 0===es?void 0:es.key),{value:eR}),tU=(0,c.A)(tQ,2),tJ=tU[0],tZ=tU[1],t$=Q(function(e){tZ(e)}),t0=Q(function(){tZ(void 0)});(0,v.useImperativeHandle)(t,function(){return{list:tl.current,focus:function(e){var t,n,o=V(tV(),tu),r=o.elements,a=o.key2element,i=o.element2key,c=X(tl.current,r),l=null!=tJ?tJ:c[0]?i.get(c[0]):null===(t=tn.find(function(e){return!e.props.disabled}))||void 0===t?void 0:t.key,u=a.get(l);l&&u&&(null==u||null===(n=u.focus)||void 0===n||n.call(u,e))}}});var t1=(0,m.A)(eW||[],{value:eX,postState:function(e){return Array.isArray(e)?e:null==e?eK:[e]}}),t2=(0,c.A)(t1,2),t5=t2[0],t4=t2[1],t7=function(e){if(eT){var t,n=e.key,o=t5.includes(n);t4(t=e_?o?t5.filter(function(e){return e!==n}):[].concat((0,i.A)(t5),[n]):[n]);var r=(0,a.A)((0,a.A)({},e),{},{selectedKeys:t});o?null==eV||eV(r):null==eF||eF(r)}!e_&&tp.length&&"inline"!==tk&&tv(eK)},t6=Q(function(e){null==e5||e5(el(e)),t7(e)}),t3=Q(function(e,t){var n=tp.filter(function(t){return t!==e});if(t)n.push(e);else if("inline"!==tk){var o=tq(e);n=n.filter(function(e){return!o.has(e)})}(0,p.A)(tp,n,!0)||tv(n,!0)}),t9=(ea=function(e,t){var n=null!=t?t:!tp.includes(e);t3(e,n)},ei=v.useRef(),(ec=v.useRef()).current=tJ,eu=function(){R.A.cancel(ei.current)},v.useEffect(function(){return function(){eu()}},[]),function(e){var t=e.which;if([].concat(W,[H,L,K,_]).includes(t)){var n=tV(),o=V(n,tu),a=o,i=a.elements,c=a.key2element,l=a.element2key,u=function(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}(c.get(tJ),i),s=l.get(u),d=function(e,t,n,o){var a,i="prev",c="next",l="children",u="parent";if("inline"===e&&o===H)return{inlineTrigger:!0};var s=(0,r.A)((0,r.A)({},T,i),D,c),d=(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},M,n?c:i),j,n?i:c),D,l),H,l),m=(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({},T,i),D,c),H,l),L,u),M,n?l:u),j,n?u:l);switch(null===(a=({inline:s,horizontal:d,vertical:m,inlineSub:s,horizontalSub:m,verticalSub:m})["".concat(e).concat(t?"":"Sub")])||void 0===a?void 0:a[o]){case i:return{offset:-1,sibling:!0};case c:return{offset:1,sibling:!0};case u:return{offset:-1,sibling:!1};case l:return{offset:1,sibling:!1};default:return null}}(tk,1===tF(s,!0).length,ts,t);if(!d&&t!==K&&t!==_)return;(W.includes(t)||[K,_].includes(t))&&e.preventDefault();var m=function(e){if(e){var t=e,n=e.querySelector("a");null!=n&&n.getAttribute("href")&&(t=n);var o=l.get(e);tZ(o),eu(),ei.current=(0,R.A)(function(){ec.current===o&&t.focus()})}};if([K,_].includes(t)||d.sibling||!u){var p,f=u&&"inline"!==tk?function(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}(u):tl.current,v=X(f,i);m(t===K?v[0]:t===_?v[v.length-1]:F(f,i,u,d.offset))}else if(d.inlineTrigger)ea(s);else if(d.offset>0)ea(s,!0),eu(),ei.current=(0,R.A)(function(){o=V(n,tu);var e=u.getAttribute("aria-controls");m(F(document.getElementById(e),o.elements))},5);else if(d.offset<0){var g=tF(s,!0),b=g[g.length-2],h=c.get(b);ea(b,!1),m(h)}}null==e7||e7(e)});v.useEffect(function(){tc(!0)},[]);var t8=v.useMemo(function(){return{_internalRenderMenuItem:e6,_internalRenderSubMenuItem:e3}},[e6,e3]),ne="horizontal"!==tk||eE?tn:tn.map(function(e,t){return v.createElement(w,{key:e.key,overflowDisabled:t>tT},e)}),nt=v.createElement(d.A,(0,o.A)({id:eA,ref:tl,prefixCls:"".concat(em,"-overflow"),component:"ul",itemComponent:ef,className:s()(em,"".concat(em,"-root"),"".concat(em,"-").concat(tk),eg,(0,r.A)((0,r.A)({},"".concat(em,"-inline-collapsed"),tz),"".concat(em,"-rtl"),ts),ep),dir:eC,style:ev,role:"menu",tabIndex:void 0===eb?0:eb,data:ne,renderRawItem:function(e){return e},renderRawRest:function(e){var t=e.length,n=t?tn.slice(-t):null;return v.createElement(eP,{eventKey:Y,title:e0,disabled:tH,internalPopupClose:0===t,popupClassName:e1},n)},maxCount:"horizontal"!==tk||eE?d.A.INVALIDATE:d.A.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){tD(e)},onKeyDown:t9},e8));return v.createElement(B.Provider,{value:t8},v.createElement(b.Provider,{value:tu},v.createElement(w,{prefixCls:em,rootClassName:ep,mode:tk,openKeys:tp,rtl:ts,disabled:ex,motion:ti?eG:null,defaultMotions:ti?eY:null,activeKey:tJ,onActive:t$,onInactive:t0,selectedKeys:t5,inlineIndent:void 0===eq?24:eq,subMenuOpenDelay:void 0===eO?.1:eO,subMenuCloseDelay:void 0===ek?.1:ek,forceSubMenuRender:eB,builtinPlacements:eU,triggerSubMenuAction:void 0===eQ?"hover":eQ,getPopupContainer:e2,itemIcon:eJ,expandIcon:eZ,onItemClick:t6,onOpenChange:t3},v.createElement(k.Provider,{value:tY},nt),v.createElement("div",{style:{display:"none"},"aria-hidden":!0},v.createElement(S.Provider,{value:tG},to)))))});e_.Item=ef,e_.SubMenu=eP,e_.ItemGroup=eT,e_.Divider=eR;let eW=e_}}]);