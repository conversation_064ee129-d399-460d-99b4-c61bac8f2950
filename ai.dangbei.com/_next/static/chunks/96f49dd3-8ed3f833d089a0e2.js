"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8156],{33665:(u,e,r)=>{r.d(e,{EY:()=>K,Hg:()=>P,I:()=>T,KE:()=>I,Q6:()=>O,bP:()=>N,bR:()=>L,gB:()=>ub,h6:()=>Q,ie:()=>e5,wA:()=>A});var t,n=r(8727),o=r(57008),a={transform(u,e){var{current:r,affinity:t}=u;if(null!=r){var n=A.transform(r,e,{affinity:t});u.current=n,null==n&&u.unref()}}},i={transform(u,e){var{current:r,affinity:t}=u;if(null!=r){var n=L.transform(r,e,{affinity:t});u.current=n,null==n&&u.unref()}}},D={transform(u,e){var{current:r,affinity:t}=u;if(null!=r){var n=O.transform(r,e,{affinity:t});u.current=n,null==n&&u.unref()}}},s=new WeakMap,l=new WeakMap,C=new WeakMap,f=new WeakMap,c=new WeakMap,B=new WeakMap,h=new WeakMap,A={ancestors(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{reverse:r=!1}=e,t=A.levels(u,e);return r?t.slice(1):t.slice(0,-1)},common(u,e){for(var r=[],t=0;t<u.length&&t<e.length;t++){var n=u[t];if(n!==e[t])break;r.push(n)}return r},compare(u,e){for(var r=Math.min(u.length,e.length),t=0;t<r;t++){if(u[t]<e[t])return -1;if(u[t]>e[t])return 1}return 0},endsAfter(u,e){var r=u.length-1,t=u.slice(0,r),n=e.slice(0,r),o=u[r],a=e[r];return A.equals(t,n)&&o>a},endsAt(u,e){var r=u.length,t=u.slice(0,r),n=e.slice(0,r);return A.equals(t,n)},endsBefore(u,e){var r=u.length-1,t=u.slice(0,r),n=e.slice(0,r),o=u[r],a=e[r];return A.equals(t,n)&&o<a},equals:(u,e)=>u.length===e.length&&u.every((u,r)=>u===e[r]),hasPrevious:u=>u[u.length-1]>0,isAfter:(u,e)=>1===A.compare(u,e),isAncestor:(u,e)=>u.length<e.length&&0===A.compare(u,e),isBefore:(u,e)=>-1===A.compare(u,e),isChild:(u,e)=>u.length===e.length+1&&0===A.compare(u,e),isCommon:(u,e)=>u.length<=e.length&&0===A.compare(u,e),isDescendant:(u,e)=>u.length>e.length&&0===A.compare(u,e),isParent:(u,e)=>u.length+1===e.length&&0===A.compare(u,e),isPath:u=>Array.isArray(u)&&(0===u.length||"number"==typeof u[0]),isSibling(u,e){if(u.length!==e.length)return!1;var r=u.slice(0,-1),t=e.slice(0,-1);return u[u.length-1]!==e[e.length-1]&&A.equals(r,t)},levels(u){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{reverse:r=!1}=e,t=[],n=0;n<=u.length;n++)t.push(u.slice(0,n));return r&&t.reverse(),t},next(u){if(0===u.length)throw Error("Cannot get the next path of a root path [".concat(u,"], because it has no next index."));var e=u[u.length-1];return u.slice(0,-1).concat(e+1)},operationCanTransformPath(u){switch(u.type){case"insert_node":case"remove_node":case"merge_node":case"split_node":case"move_node":return!0;default:return!1}},parent(u){if(0===u.length)throw Error("Cannot get the parent path of the root path [".concat(u,"]."));return u.slice(0,-1)},previous(u){if(0===u.length)throw Error("Cannot get the previous path of a root path [".concat(u,"], because it has no previous index."));var e=u[u.length-1];if(e<=0)throw Error("Cannot get the previous path of a first child path [".concat(u,"] because it would result in a negative index."));return u.slice(0,-1).concat(e-1)},relative(u,e){if(!A.isAncestor(e,u)&&!A.equals(u,e))throw Error("Cannot get the relative path of [".concat(u,"] inside ancestor [").concat(e,"], because it is not above or equal to the path."));return u.slice(e.length)},transform(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!u)return null;var t=[...u],{affinity:n="forward"}=r;if(0===u.length)return t;switch(e.type){case"insert_node":var{path:o}=e;(A.equals(o,t)||A.endsBefore(o,t)||A.isAncestor(o,t))&&(t[o.length-1]+=1);break;case"remove_node":var{path:a}=e;if(A.equals(a,t)||A.isAncestor(a,t))return null;A.endsBefore(a,t)&&(t[a.length-1]-=1);break;case"merge_node":var{path:i,position:D}=e;A.equals(i,t)||A.endsBefore(i,t)?t[i.length-1]-=1:A.isAncestor(i,t)&&(t[i.length-1]-=1,t[i.length]+=D);break;case"split_node":var{path:s,position:l}=e;if(A.equals(s,t)){if("forward"===n)t[t.length-1]+=1;else if("backward"!==n)return null}else A.endsBefore(s,t)?t[s.length-1]+=1:A.isAncestor(s,t)&&u[s.length]>=l&&(t[s.length-1]+=1,t[s.length]-=l);break;case"move_node":var{path:C,newPath:f}=e;if(A.equals(C,f))break;if(A.isAncestor(C,t)||A.equals(C,t)){var c=f.slice();return A.endsBefore(C,f)&&C.length<f.length&&(c[C.length-1]-=1),c.concat(t.slice(C.length))}A.isSibling(C,f)&&(A.isAncestor(f,t)||A.equals(f,t))?A.endsBefore(C,t)?t[C.length-1]-=1:t[C.length-1]+=1:A.endsBefore(f,t)||A.equals(f,t)||A.isAncestor(f,t)?(A.endsBefore(C,t)&&(t[C.length-1]-=1),t[f.length-1]+=1):A.endsBefore(C,t)&&(A.equals(f,t)&&(t[f.length-1]+=1),t[C.length-1]-=1)}return t}};function v(u){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(u){return typeof u}:function(u){return u&&"function"==typeof Symbol&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u})(u)}function d(u,e,r){var t;return t=function(u,e){if("object"!==v(u)||null===u)return u;var r=u[Symbol.toPrimitive];if(void 0!==r){var t=r.call(u,e||"default");if("object"!==v(t))return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(u)}(e,"string"),(e="symbol"===v(t)?t:String(t))in u?Object.defineProperty(u,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):u[e]=r,u}function p(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function F(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var E=(u,e,r)=>{switch(r.type){case"insert_node":var{path:t,node:n}=r,o=N.parent(u,t),a=t[t.length-1];if(a>o.children.length)throw Error('Cannot apply an "insert_node" operation at path ['.concat(t,"] because the destination is past the end of the node."));if(o.children.splice(a,0,n),e)for(var[i,D]of O.points(e))e[D]=L.transform(i,r);break;case"insert_text":var{path:s,offset:l,text:C}=r;if(0===C.length)break;var f=N.leaf(u,s),c=f.text.slice(0,l),B=f.text.slice(l);if(f.text=c+C+B,e)for(var[h,v]of O.points(e))e[v]=L.transform(h,r);break;case"merge_node":var{path:d}=r,p=N.get(u,d),E=A.previous(d),g=N.get(u,E),y=N.parent(u,d),m=d[d.length-1];if(K.isText(p)&&K.isText(g))g.text+=p.text;else if(K.isText(p)||K.isText(g))throw Error('Cannot apply a "merge_node" operation at path ['.concat(d,"] to nodes of different interfaces: ").concat(Q.stringify(p)," ").concat(Q.stringify(g)));else g.children.push(...p.children);if(y.children.splice(m,1),e)for(var[b,w]of O.points(e))e[w]=L.transform(b,r);break;case"move_node":var{path:P,newPath:x}=r;if(A.isAncestor(P,x))throw Error("Cannot move a path [".concat(P,"] to new path [").concat(x,"] because the destination is inside itself."));var j=N.get(u,P),k=N.parent(u,P),R=P[P.length-1];k.children.splice(R,1);var S=A.transform(P,r),T=N.get(u,A.parent(S)),_=S[S.length-1];if(T.children.splice(_,0,j),e)for(var[q,I]of O.points(e))e[I]=L.transform(q,r);break;case"remove_node":var{path:z}=r,V=z[z.length-1];if(N.parent(u,z).children.splice(V,1),e)for(var[M,W]of O.points(e)){var $=L.transform(M,r);if(null!=e&&null!=$)e[W]=$;else{var J=void 0,Z=void 0;for(var[H,Y]of N.texts(u))if(-1===A.compare(Y,z))J=[H,Y];else{Z=[H,Y];break}var G=!1;J&&Z&&(G=A.equals(Z[1],z)?!A.hasPrevious(Z[1]):A.common(J[1],z).length<A.common(Z[1],z).length),J&&!G?(M.path=J[1],M.offset=J[0].text.length):Z?(M.path=Z[1],M.offset=0):e=null}}break;case"remove_text":var{path:U,offset:X,text:uu}=r;if(0===uu.length)break;var ue=N.leaf(u,U),ur=ue.text.slice(0,X),ut=ue.text.slice(X+uu.length);if(ue.text=ur+ut,e)for(var[un,uo]of O.points(e))e[uo]=L.transform(un,r);break;case"set_node":var{path:ua,properties:ui,newProperties:uD}=r;if(0===ua.length)throw Error("Cannot set properties on the root node!");var us=N.get(u,ua);for(var ul in uD){if("children"===ul||"text"===ul)throw Error('Cannot set the "'.concat(ul,'" property of nodes!'));var uC=uD[ul];null==uC?delete us[ul]:us[ul]=uC}for(var uf in ui)uD.hasOwnProperty(uf)||delete us[uf];break;case"set_selection":var{newProperties:uc}=r;if(null==uc)e=uc;else{if(null==e){if(!O.isRange(uc))throw Error('Cannot apply an incomplete "set_selection" operation properties '.concat(Q.stringify(uc)," when there is no current selection."));e=F({},uc)}for(var uB in uc){var uh=uc[uB];if(null==uh){if("anchor"===uB||"focus"===uB)throw Error('Cannot remove the "'.concat(uB,'" selection property'));delete e[uB]}else e[uB]=uh}}break;case"split_node":var uA,{path:uv,position:ud,properties:up}=r;if(0===uv.length)throw Error('Cannot apply a "split_node" operation at path ['.concat(uv,"] because the root node cannot be split."));var uF=N.get(u,uv),uE=N.parent(u,uv),ug=uv[uv.length-1];if(K.isText(uF)){var uy=uF.text.slice(0,ud),um=uF.text.slice(ud);uF.text=uy,uA=F(F({},up),{},{text:um})}else{var ub=uF.children.slice(0,ud),uO=uF.children.slice(ud);uF.children=ub,uA=F(F({},up),{},{children:uO})}if(uE.children.splice(ug+1,0,uA),e)for(var[uw,uP]of O.points(e))e[uP]=L.transform(uw,r)}return e},g=(u,e)=>{for(var r in u){var t=u[r],o=e[r];if((0,n.Q)(t)&&(0,n.Q)(o)){if(!g(t,o))return!1}else if(Array.isArray(t)&&Array.isArray(o)){if(t.length!==o.length)return!1;for(var a=0;a<t.length;a++)if(t[a]!==o[a])return!1}else if(t!==o)return!1}for(var i in e)if(void 0===u[i]&&void 0!==e[i])return!1;return!0};function y(u,e){if(null==u)return{};var r,t,n=function(u,e){if(null==u)return{};var r,t,n={},o=Object.keys(u);for(t=0;t<o.length;t++)r=o[t],e.indexOf(r)>=0||(n[r]=u[r]);return n}(u,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(u);for(t=0;t<o.length;t++)r=o[t],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(u,r)&&(n[r]=u[r])}return n}var m=["anchor","focus"];function b(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}var O={edges(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{reverse:r=!1}=e,{anchor:t,focus:n}=u;return O.isBackward(u)===r?[t,n]:[n,t]},end(u){var[,e]=O.edges(u);return e},equals:(u,e)=>L.equals(u.anchor,e.anchor)&&L.equals(u.focus,e.focus),surrounds(u,e){var r=O.intersection(u,e);return!!r&&O.equals(r,e)},includes(u,e){if(O.isRange(e)){if(O.includes(u,e.anchor)||O.includes(u,e.focus))return!0;var[r,t]=O.edges(u),[n,o]=O.edges(e);return L.isBefore(r,n)&&L.isAfter(t,o)}var[a,i]=O.edges(u),D=!1,s=!1;return L.isPoint(e)?(D=L.compare(e,a)>=0,s=0>=L.compare(e,i)):(D=A.compare(e,a.path)>=0,s=0>=A.compare(e,i.path)),D&&s},intersection(u,e){var r=y(u,m),[t,n]=O.edges(u),[o,a]=O.edges(e),i=L.isBefore(t,o)?o:t,D=L.isBefore(n,a)?n:a;return L.isBefore(D,i)?null:function(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}({anchor:i,focus:D},r)},isBackward(u){var{anchor:e,focus:r}=u;return L.isAfter(e,r)},isCollapsed(u){var{anchor:e,focus:r}=u;return L.equals(e,r)},isExpanded:u=>!O.isCollapsed(u),isForward:u=>!O.isBackward(u),isRange:u=>(0,n.Q)(u)&&L.isPoint(u.anchor)&&L.isPoint(u.focus),*points(u){yield[u.anchor,"anchor"],yield[u.focus,"focus"]},start(u){var[e]=O.edges(u);return e},transform(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(0,o.jM)(u,u=>{if(null===u)return null;var t,n,{affinity:o="inward"}=r;if("inward"===o){var a=O.isCollapsed(u);O.isForward(u)?(t="forward",n=a?t:"backward"):(t="backward",n=a?t:"forward")}else"outward"===o?O.isForward(u)?(t="backward",n="forward"):(t="forward",n="backward"):(t=o,n=o);var i=L.transform(u.anchor,e,{affinity:t}),D=L.transform(u.focus,e,{affinity:n});if(!i||!D)return null;u.anchor=i,u.focus=D})}},w=u=>(0,n.Q)(u)&&N.isNodeList(u.children)&&!I.isEditor(u),P={isAncestor:u=>(0,n.Q)(u)&&N.isNodeList(u.children),isElement:w,isElementList:u=>Array.isArray(u)&&u.every(u=>P.isElement(u)),isElementProps:u=>void 0!==u.children,isElementType:function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"type";return w(u)&&u[r]===e},matches(u,e){for(var r in e)if("children"!==r&&u[r]!==e[r])return!1;return!0}},x=["children"],j=["text"],k=new WeakMap,N={ancestor(u,e){var r=N.get(u,e);if(K.isText(r))throw Error("Cannot get the ancestor node at path [".concat(e,"] because it refers to a text node instead: ").concat(Q.stringify(r)));return r},ancestors(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function*(){for(var t of A.ancestors(e,r)){var n=[N.ancestor(u,t),t];yield n}}()},child(u,e){if(K.isText(u))throw Error("Cannot get the child of a text node: ".concat(Q.stringify(u)));var r=u.children[e];if(null==r)throw Error("Cannot get child at index `".concat(e,"` in node: ").concat(Q.stringify(u)));return r},children(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function*(){for(var{reverse:t=!1}=r,n=N.ancestor(u,e),{children:o}=n,a=t?o.length-1:0;t?a>=0:a<o.length;){var i=N.child(n,a),D=e.concat(a);yield[i,D],a=t?a-1:a+1}}()},common(u,e,r){var t=A.common(e,r);return[N.get(u,t),t]},descendant(u,e){var r=N.get(u,e);if(I.isEditor(r))throw Error("Cannot get the descendant node at path [".concat(e,"] because it refers to the root editor node instead: ").concat(Q.stringify(r)));return r},descendants(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){for(var[r,t]of N.nodes(u,e))0!==t.length&&(yield[r,t])}()},elements(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){for(var[r,t]of N.nodes(u,e))P.isElement(r)&&(yield[r,t])}()},extractProps(u){if(P.isAncestor(u)){var e=y(u,x);return e}var e=y(u,j);return e},first(u,e){for(var r=e.slice(),t=N.get(u,r);t&&!K.isText(t)&&0!==t.children.length;)t=t.children[0],r.push(0);return[t,r]},fragment(u,e){if(K.isText(u))throw Error("Cannot get a fragment starting from a root text node: ".concat(Q.stringify(u)));return(0,o.jM)({children:u.children},u=>{var[r,t]=O.edges(e);for(var[,n]of N.nodes(u,{reverse:!0,pass:u=>{var[,r]=u;return!O.includes(e,r)}})){if(!O.includes(e,n)){var o=N.parent(u,n),a=n[n.length-1];o.children.splice(a,1)}if(A.equals(n,t.path)){var i=N.leaf(u,n);i.text=i.text.slice(0,t.offset)}if(A.equals(n,r.path)){var D=N.leaf(u,n);D.text=D.text.slice(r.offset)}}I.isEditor(u)&&(u.selection=null)}).children},get(u,e){var r=N.getIf(u,e);if(void 0===r)throw Error("Cannot find a descendant at path [".concat(e,"] in node: ").concat(Q.stringify(u)));return r},getIf(u,e){for(var r=u,t=0;t<e.length;t++){var n=e[t];if(K.isText(r)||!r.children[n])return;r=r.children[n]}return r},has(u,e){for(var r=u,t=0;t<e.length;t++){var n=e[t];if(K.isText(r)||!r.children[n])return!1;r=r.children[n]}return!0},isNode:u=>K.isText(u)||P.isElement(u)||I.isEditor(u),isNodeList(u){if(!Array.isArray(u))return!1;var e=k.get(u);if(void 0!==e)return e;var r=u.every(u=>N.isNode(u));return k.set(u,r),r},last(u,e){for(var r=e.slice(),t=N.get(u,r);t&&!K.isText(t)&&0!==t.children.length;){var n=t.children.length-1;t=t.children[n],r.push(n)}return[t,r]},leaf(u,e){var r=N.get(u,e);if(!K.isText(r))throw Error("Cannot get the leaf node at path [".concat(e,"] because it refers to a non-leaf node: ").concat(Q.stringify(r)));return r},levels(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function*(){for(var t of A.levels(e,r)){var n=N.get(u,t);yield[n,t]}}()},matches:(u,e)=>P.isElement(u)&&P.isElementProps(e)&&P.matches(u,e)||K.isText(u)&&K.isTextProps(e)&&K.matches(u,e),nodes(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){for(var{pass:r,reverse:t=!1}=e,{from:n=[],to:o}=e,a=new Set,i=[],D=u;!(o&&(t?A.isBefore(i,o):A.isAfter(i,o)));){if(a.has(D)||(yield[D,i]),!a.has(D)&&!K.isText(D)&&0!==D.children.length&&(null==r||!1===r([D,i]))){a.add(D);var s=t?D.children.length-1:0;A.isAncestor(i,n)&&(s=n[i.length]),i=i.concat(s),D=N.get(u,i);continue}if(0===i.length)break;if(!t){var l=A.next(i);if(N.has(u,l)){i=l,D=N.get(u,i);continue}}if(t&&0!==i[i.length-1]){i=A.previous(i),D=N.get(u,i);continue}i=A.parent(i),D=N.get(u,i),a.add(D)}}()},parent(u,e){var r=A.parent(e),t=N.get(u,r);if(K.isText(t))throw Error("Cannot get the parent of path [".concat(e,"] because it does not exist in the root."));return t},string:u=>K.isText(u)?u.text:u?u.children.map(N.string).join(""):void 0,texts(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){for(var[r,t]of N.nodes(u,e))K.isText(r)&&(yield[r,t])}()}};function R(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function S(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var T={isNodeOperation:u=>T.isOperation(u)&&u.type.endsWith("_node"),isOperation(u){if(!(0,n.Q)(u))return!1;switch(u.type){case"insert_node":case"remove_node":return A.isPath(u.path)&&N.isNode(u.node);case"insert_text":case"remove_text":return"number"==typeof u.offset&&"string"==typeof u.text&&A.isPath(u.path);case"merge_node":return"number"==typeof u.position&&A.isPath(u.path)&&(0,n.Q)(u.properties);case"move_node":return A.isPath(u.path)&&A.isPath(u.newPath);case"set_node":return A.isPath(u.path)&&(0,n.Q)(u.properties)&&(0,n.Q)(u.newProperties);case"set_selection":return null===u.properties&&O.isRange(u.newProperties)||null===u.newProperties&&O.isRange(u.properties)||(0,n.Q)(u.properties)&&(0,n.Q)(u.newProperties);case"split_node":return A.isPath(u.path)&&"number"==typeof u.position&&(0,n.Q)(u.properties);default:return!1}},isOperationList:u=>Array.isArray(u)&&u.every(u=>T.isOperation(u)),isSelectionOperation:u=>T.isOperation(u)&&u.type.endsWith("_selection"),isTextOperation:u=>T.isOperation(u)&&u.type.endsWith("_text"),inverse(u){switch(u.type){case"insert_node":return S(S({},u),{},{type:"remove_node"});case"insert_text":return S(S({},u),{},{type:"remove_text"});case"merge_node":return S(S({},u),{},{type:"split_node",path:A.previous(u.path)});case"move_node":var{newPath:e,path:r}=u;if(A.equals(e,r))return u;if(A.isSibling(r,e))return S(S({},u),{},{path:e,newPath:r});var t=A.transform(r,u),n=A.transform(A.next(r),u);return S(S({},u),{},{path:t,newPath:n});case"remove_node":return S(S({},u),{},{type:"insert_node"});case"remove_text":return S(S({},u),{},{type:"insert_text"});case"set_node":var{properties:o,newProperties:a}=u;return S(S({},u),{},{properties:a,newProperties:o});case"set_selection":var{properties:i,newProperties:D}=u;if(null==i)return S(S({},u),{},{properties:D,newProperties:null});if(null==D)return S(S({},u),{},{properties:null,newProperties:i});return S(S({},u),{},{properties:D,newProperties:i});case"split_node":return S(S({},u),{},{type:"merge_node",path:A.next(u.path)})}}},_=new WeakMap,q=u=>{var e=_.get(u);if(void 0!==e)return e;if(!(0,n.Q)(u))return!1;var r="function"==typeof u.addMark&&"function"==typeof u.apply&&"function"==typeof u.deleteFragment&&"function"==typeof u.insertBreak&&"function"==typeof u.insertSoftBreak&&"function"==typeof u.insertFragment&&"function"==typeof u.insertNode&&"function"==typeof u.insertText&&"function"==typeof u.isElementReadOnly&&"function"==typeof u.isInline&&"function"==typeof u.isSelectable&&"function"==typeof u.isVoid&&"function"==typeof u.normalizeNode&&"function"==typeof u.onChange&&"function"==typeof u.removeMark&&"function"==typeof u.getDirtyPaths&&(null===u.marks||(0,n.Q)(u.marks))&&(null===u.selection||O.isRange(u.selection))&&N.isNodeList(u.children)&&T.isOperationList(u.operations);return _.set(u,r),r},I={above:(u,e)=>u.above(e),addMark(u,e,r){u.addMark(e,r)},after:(u,e,r)=>u.after(e,r),before:(u,e,r)=>u.before(e,r),deleteBackward(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{unit:r="character"}=e;u.deleteBackward(r)},deleteForward(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{unit:r="character"}=e;u.deleteForward(r)},deleteFragment(u,e){u.deleteFragment(e)},edges:(u,e)=>u.edges(e),elementReadOnly(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return u.elementReadOnly(e)},end:(u,e)=>u.end(e),first:(u,e)=>u.first(e),fragment:(u,e)=>u.fragment(e),hasBlocks:(u,e)=>u.hasBlocks(e),hasInlines:(u,e)=>u.hasInlines(e),hasPath:(u,e)=>u.hasPath(e),hasTexts:(u,e)=>u.hasTexts(e),insertBreak(u){u.insertBreak()},insertFragment(u,e,r){u.insertFragment(e,r)},insertNode(u,e){u.insertNode(e)},insertSoftBreak(u){u.insertSoftBreak()},insertText(u,e){u.insertText(e)},isBlock:(u,e)=>u.isBlock(e),isEdge:(u,e,r)=>u.isEdge(e,r),isEditor:u=>q(u),isElementReadOnly:(u,e)=>u.isElementReadOnly(e),isEmpty:(u,e)=>u.isEmpty(e),isEnd:(u,e,r)=>u.isEnd(e,r),isInline:(u,e)=>u.isInline(e),isNormalizing:u=>u.isNormalizing(),isSelectable:(u,e)=>u.isSelectable(e),isStart:(u,e,r)=>u.isStart(e,r),isVoid:(u,e)=>u.isVoid(e),last:(u,e)=>u.last(e),leaf:(u,e,r)=>u.leaf(e,r),levels:(u,e)=>u.levels(e),marks:u=>u.getMarks(),next:(u,e)=>u.next(e),node:(u,e,r)=>u.node(e,r),nodes:(u,e)=>u.nodes(e),normalize(u,e){u.normalize(e)},parent:(u,e,r)=>u.parent(e,r),path:(u,e,r)=>u.path(e,r),pathRef:(u,e,r)=>u.pathRef(e,r),pathRefs:u=>u.pathRefs(),point:(u,e,r)=>u.point(e,r),pointRef:(u,e,r)=>u.pointRef(e,r),pointRefs:u=>u.pointRefs(),positions:(u,e)=>u.positions(e),previous:(u,e)=>u.previous(e),range:(u,e,r)=>u.range(e,r),rangeRef:(u,e,r)=>u.rangeRef(e,r),rangeRefs:u=>u.rangeRefs(),removeMark(u,e){u.removeMark(e)},setNormalizing(u,e){u.setNormalizing(e)},start:(u,e)=>u.start(e),string:(u,e,r)=>u.string(e,r),unhangRange:(u,e,r)=>u.unhangRange(e,r),void:(u,e)=>u.void(e),withoutNormalizing(u,e){u.withoutNormalizing(e)},shouldMergeNodesRemovePrevNode:(u,e,r)=>u.shouldMergeNodesRemovePrevNode(e,r)},z={isSpan:u=>Array.isArray(u)&&2===u.length&&u.every(A.isPath)};function V(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function M(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?V(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var L={compare(u,e){var r=A.compare(u.path,e.path);return 0===r?u.offset<e.offset?-1:+(u.offset>e.offset):r},isAfter:(u,e)=>1===L.compare(u,e),isBefore:(u,e)=>-1===L.compare(u,e),equals:(u,e)=>u.offset===e.offset&&A.equals(u.path,e.path),isPoint:u=>(0,n.Q)(u)&&"number"==typeof u.offset&&A.isPath(u.path),transform(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(0,o.jM)(u,u=>{if(null===u)return null;var{affinity:t="forward"}=r,{path:n,offset:o}=u;switch(e.type){case"insert_node":case"move_node":u.path=A.transform(n,e,r);break;case"insert_text":A.equals(e.path,n)&&(e.offset<o||e.offset===o&&"forward"===t)&&(u.offset+=e.text.length);break;case"merge_node":A.equals(e.path,n)&&(u.offset+=e.position),u.path=A.transform(n,e,r);break;case"remove_text":A.equals(e.path,n)&&e.offset<=o&&(u.offset-=Math.min(o-e.offset,e.text.length));break;case"remove_node":if(A.equals(e.path,n)||A.isAncestor(e.path,n))return null;u.path=A.transform(n,e,r);break;case"split_node":if(A.equals(e.path,n)){if(e.position===o&&null==t)return null;(e.position<o||e.position===o&&"forward"===t)&&(u.offset-=e.position,u.path=A.transform(n,e,M(M({},r),{},{affinity:"forward"})))}else u.path=A.transform(n,e,r)}})}},W=void 0,Q={setScrubber(u){W=u},stringify:u=>JSON.stringify(u,W)},$=["text"],J=["anchor","focus","merge"];function Z(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function H(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Z(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var K={equals(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{loose:t=!1}=r;return g(t?y(u,$):u,t?y(e,$):e)},isText:u=>(0,n.Q)(u)&&"string"==typeof u.text,isTextList:u=>Array.isArray(u)&&u.every(u=>K.isText(u)),isTextProps:u=>void 0!==u.text,matches(u,e){for(var r in e)if("text"!==r&&(!u.hasOwnProperty(r)||u[r]!==e[r]))return!1;return!0},decorations(u,e){var r=[H({},u)];for(var t of e){var{anchor:n,focus:o,merge:a}=t,i=y(t,J),[D,s]=O.edges(t),l=[],C=0,f=D.offset,c=s.offset,B=null!=a?a:Object.assign;for(var h of r){var{length:A}=h.text,v=C;if(C+=A,f<=v&&C<=c){B(h,i),l.push(h);continue}if(f!==c&&(f===C||c===v)||f>C||c<v||c===v&&0!==v){l.push(h);continue}var d=h,p=void 0,F=void 0;if(c<C){var E=c-v;F=H(H({},d),{},{text:d.text.slice(E)}),d=H(H({},d),{},{text:d.text.slice(0,E)})}if(f>v){var g=f-v;p=H(H({},d),{},{text:d.text.slice(0,g)}),d=H(H({},d),{},{text:d.text.slice(g)})}B(d,i),p&&l.push(p),l.push(d),F&&l.push(F)}r=l}return r}},Y=u=>u.selection?u.selection:u.children.length>0?I.end(u,[]):[0],G=(u,e)=>{var[r]=I.node(u,e);return u=>u===r},U=function(u){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=!e,n=e?uo(u):u,o=t.None,a=t.None,i=0,D=null,s=null;for(var l of n){var C=l.codePointAt(0);if(!C)break;var f=uv(l,C);if([o,a]=r?[a,f]:[f,o],(o&t.ZWJ)!=0&&(a&t.ExtPict)!=0&&!(r?uF(u.substring(0,i)):uF(u.substring(0,u.length-i)))||(o&t.RI)!=0&&(a&t.RI)!=0&&!(s=null!==s?!s:!!r||ug(u.substring(0,u.length-i)))||o!==t.None&&a!==t.None&&function(u,e){return -1===ud.findIndex(r=>(u&r[0])!=0&&(e&r[1])!=0)}(o,a))break;i+=l.length}return i||1},X=/\s/,uu=/[\u002B\u0021-\u0023\u0025-\u002A\u002C-\u002F\u003A\u003B\u003F\u0040\u005B-\u005D\u005F\u007B\u007D\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E3B\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/,ue=/['\u2018\u2019]/,ur=function(u){for(var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=0,t=!1;u.length>0;){var n=U(u,e),[o,a]=ut(u,n,e);if(un(o,a,e))t=!0,r+=n;else if(t)break;else r+=n;u=a}return r},ut=(u,e,r)=>{if(r){var t=u.length-e;return[u.slice(t,u.length),u.slice(0,t)]}return[u.slice(0,e),u.slice(e)]},un=function u(e,r){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(X.test(e))return!1;if(ue.test(e)){var n=U(r,t),[o,a]=ut(r,n,t);if(u(o,a,t))return!0}return!uu.test(e)},uo=function*(u){for(var e=u.length-1,r=0;r<u.length;r++){var t=u.charAt(e-r);if(ui(t.charCodeAt(0))){var n=u.charAt(e-r-1);if(ua(n.charCodeAt(0))){yield n+t,r++;continue}}yield t}},ua=u=>u>=55296&&u<=56319,ui=u=>u>=56320&&u<=57343;!function(u){u[u.None=0]="None",u[u.Extend=1]="Extend",u[u.ZWJ=2]="ZWJ",u[u.RI=4]="RI",u[u.Prepend=8]="Prepend",u[u.SpacingMark=16]="SpacingMark",u[u.L=32]="L",u[u.V=64]="V",u[u.T=128]="T",u[u.LV=256]="LV",u[u.LVT=512]="LVT",u[u.ExtPict=1024]="ExtPict",u[u.Any=2048]="Any"}(t||(t={}));var uD=/^(?:[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0898-\u089F\u08CA-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09BE\u09C1-\u09C4\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3E\u0B3F\u0B41-\u0B44\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B82\u0BBE\u0BC0\u0BCD\u0BD7\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CBF\u0CC2\u0CC6\u0CCC\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D3E\u0D41-\u0D44\u0D4D\u0D57\u0D62\u0D63\u0D81\u0DCA\u0DCF\u0DD2-\u0DD4\u0DD6\u0DDF\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u200C\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFF9E\uFF9F]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD803[\uDD24-\uDD27\uDEAB\uDEAC\uDEFD-\uDEFF\uDF46-\uDF50\uDF82-\uDF85]|\uD804[\uDC01\uDC38-\uDC46\uDC70\uDC73\uDC74\uDC7F-\uDC81\uDCB3-\uDCB6\uDCB9\uDCBA\uDCC2\uDD00-\uDD02\uDD27-\uDD2B\uDD2D-\uDD34\uDD73\uDD80\uDD81\uDDB6-\uDDBE\uDDC9-\uDDCC\uDDCF\uDE2F-\uDE31\uDE34\uDE36\uDE37\uDE3E\uDE41\uDEDF\uDEE3-\uDEEA\uDF00\uDF01\uDF3B\uDF3C\uDF3E\uDF40\uDF57\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC38-\uDC3F\uDC42-\uDC44\uDC46\uDC5E\uDCB0\uDCB3-\uDCB8\uDCBA\uDCBD\uDCBF\uDCC0\uDCC2\uDCC3\uDDAF\uDDB2-\uDDB5\uDDBC\uDDBD\uDDBF\uDDC0\uDDDC\uDDDD\uDE33-\uDE3A\uDE3D\uDE3F\uDE40\uDEAB\uDEAD\uDEB0-\uDEB5\uDEB7\uDF1D-\uDF1F\uDF22-\uDF25\uDF27-\uDF2B]|\uD806[\uDC2F-\uDC37\uDC39\uDC3A\uDD30\uDD3B\uDD3C\uDD3E\uDD43\uDDD4-\uDDD7\uDDDA\uDDDB\uDDE0\uDE01-\uDE0A\uDE33-\uDE38\uDE3B-\uDE3E\uDE47\uDE51-\uDE56\uDE59-\uDE5B\uDE8A-\uDE96\uDE98\uDE99]|\uD807[\uDC30-\uDC36\uDC38-\uDC3D\uDC3F\uDC92-\uDCA7\uDCAA-\uDCB0\uDCB2\uDCB3\uDCB5\uDCB6\uDD31-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD45\uDD47\uDD90\uDD91\uDD95\uDD97\uDEF3\uDEF4\uDF00\uDF01\uDF36-\uDF3A\uDF40\uDF42]|\uD80D[\uDC40\uDC47-\uDC55]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF4F\uDF8F-\uDF92\uDFE4]|\uD82F[\uDC9D\uDC9E]|\uD833[\uDF00-\uDF2D\uDF30-\uDF46]|\uD834[\uDD65\uDD67-\uDD69\uDD6E-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDC8F\uDD30-\uDD36\uDEAE\uDEEC-\uDEEF]|\uD839[\uDCEC-\uDCEF]|\uD83A[\uDCD0-\uDCD6\uDD44-\uDD4A]|\uD83C[\uDFFB-\uDFFF]|\uDB40[\uDC20-\uDC7F\uDD00-\uDDEF])$/,us=/^(?:[\u0600-\u0605\u06DD\u070F\u0890\u0891\u08E2\u0D4E]|\uD804[\uDCBD\uDCCD\uDDC2\uDDC3]|\uD806[\uDD3F\uDD41\uDE3A\uDE84-\uDE89]|\uD807\uDD46)$/,ul=/^(?:[\u0903\u093B\u093E-\u0940\u0949-\u094C\u094E\u094F\u0982\u0983\u09BF\u09C0\u09C7\u09C8\u09CB\u09CC\u0A03\u0A3E-\u0A40\u0A83\u0ABE-\u0AC0\u0AC9\u0ACB\u0ACC\u0B02\u0B03\u0B40\u0B47\u0B48\u0B4B\u0B4C\u0BBF\u0BC1\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCC\u0C01-\u0C03\u0C41-\u0C44\u0C82\u0C83\u0CBE\u0CC0\u0CC1\u0CC3\u0CC4\u0CC7\u0CC8\u0CCA\u0CCB\u0D02\u0D03\u0D3F\u0D40\u0D46-\u0D48\u0D4A-\u0D4C\u0D82\u0D83\u0DD0\u0DD1\u0DD8-\u0DDE\u0DF2\u0DF3\u0E33\u0EB3\u0F3E\u0F3F\u0F7F\u1031\u103B\u103C\u1056\u1057\u1084\u1715\u1734\u17B6\u17BE-\u17C5\u17C7\u17C8\u1923-\u1926\u1929-\u192B\u1930\u1931\u1933-\u1938\u1A19\u1A1A\u1A55\u1A57\u1A6D-\u1A72\u1B04\u1B3B\u1B3D-\u1B41\u1B43\u1B44\u1B82\u1BA1\u1BA6\u1BA7\u1BAA\u1BE7\u1BEA-\u1BEC\u1BEE\u1BF2\u1BF3\u1C24-\u1C2B\u1C34\u1C35\u1CE1\u1CF7\uA823\uA824\uA827\uA880\uA881\uA8B4-\uA8C3\uA952\uA953\uA983\uA9B4\uA9B5\uA9BA\uA9BB\uA9BE-\uA9C0\uAA2F\uAA30\uAA33\uAA34\uAA4D\uAAEB\uAAEE\uAAEF\uAAF5\uABE3\uABE4\uABE6\uABE7\uABE9\uABEA\uABEC]|\uD804[\uDC00\uDC02\uDC82\uDCB0-\uDCB2\uDCB7\uDCB8\uDD2C\uDD45\uDD46\uDD82\uDDB3-\uDDB5\uDDBF\uDDC0\uDDCE\uDE2C-\uDE2E\uDE32\uDE33\uDE35\uDEE0-\uDEE2\uDF02\uDF03\uDF3F\uDF41-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF62\uDF63]|\uD805[\uDC35-\uDC37\uDC40\uDC41\uDC45\uDCB1\uDCB2\uDCB9\uDCBB\uDCBC\uDCBE\uDCC1\uDDB0\uDDB1\uDDB8-\uDDBB\uDDBE\uDE30-\uDE32\uDE3B\uDE3C\uDE3E\uDEAC\uDEAE\uDEAF\uDEB6\uDF26]|\uD806[\uDC2C-\uDC2E\uDC38\uDD31-\uDD35\uDD37\uDD38\uDD3D\uDD40\uDD42\uDDD1-\uDDD3\uDDDC-\uDDDF\uDDE4\uDE39\uDE57\uDE58\uDE97]|\uD807[\uDC2F\uDC3E\uDCA9\uDCB1\uDCB4\uDD8A-\uDD8E\uDD93\uDD94\uDD96\uDEF5\uDEF6]|\uD81B[\uDF51-\uDF87\uDFF0\uDFF1]|\uD834[\uDD66\uDD6D])$/,uC=/^[\u1100-\u115F\uA960-\uA97C]$/,uf=/^[\u1160-\u11A7\uD7B0-\uD7C6]$/,uc=/^[\u11A8-\u11FF\uD7CB-\uD7FB]$/,uB=/^[\uAC00\uAC1C\uAC38\uAC54\uAC70\uAC8C\uACA8\uACC4\uACE0\uACFC\uAD18\uAD34\uAD50\uAD6C\uAD88\uADA4\uADC0\uADDC\uADF8\uAE14\uAE30\uAE4C\uAE68\uAE84\uAEA0\uAEBC\uAED8\uAEF4\uAF10\uAF2C\uAF48\uAF64\uAF80\uAF9C\uAFB8\uAFD4\uAFF0\uB00C\uB028\uB044\uB060\uB07C\uB098\uB0B4\uB0D0\uB0EC\uB108\uB124\uB140\uB15C\uB178\uB194\uB1B0\uB1CC\uB1E8\uB204\uB220\uB23C\uB258\uB274\uB290\uB2AC\uB2C8\uB2E4\uB300\uB31C\uB338\uB354\uB370\uB38C\uB3A8\uB3C4\uB3E0\uB3FC\uB418\uB434\uB450\uB46C\uB488\uB4A4\uB4C0\uB4DC\uB4F8\uB514\uB530\uB54C\uB568\uB584\uB5A0\uB5BC\uB5D8\uB5F4\uB610\uB62C\uB648\uB664\uB680\uB69C\uB6B8\uB6D4\uB6F0\uB70C\uB728\uB744\uB760\uB77C\uB798\uB7B4\uB7D0\uB7EC\uB808\uB824\uB840\uB85C\uB878\uB894\uB8B0\uB8CC\uB8E8\uB904\uB920\uB93C\uB958\uB974\uB990\uB9AC\uB9C8\uB9E4\uBA00\uBA1C\uBA38\uBA54\uBA70\uBA8C\uBAA8\uBAC4\uBAE0\uBAFC\uBB18\uBB34\uBB50\uBB6C\uBB88\uBBA4\uBBC0\uBBDC\uBBF8\uBC14\uBC30\uBC4C\uBC68\uBC84\uBCA0\uBCBC\uBCD8\uBCF4\uBD10\uBD2C\uBD48\uBD64\uBD80\uBD9C\uBDB8\uBDD4\uBDF0\uBE0C\uBE28\uBE44\uBE60\uBE7C\uBE98\uBEB4\uBED0\uBEEC\uBF08\uBF24\uBF40\uBF5C\uBF78\uBF94\uBFB0\uBFCC\uBFE8\uC004\uC020\uC03C\uC058\uC074\uC090\uC0AC\uC0C8\uC0E4\uC100\uC11C\uC138\uC154\uC170\uC18C\uC1A8\uC1C4\uC1E0\uC1FC\uC218\uC234\uC250\uC26C\uC288\uC2A4\uC2C0\uC2DC\uC2F8\uC314\uC330\uC34C\uC368\uC384\uC3A0\uC3BC\uC3D8\uC3F4\uC410\uC42C\uC448\uC464\uC480\uC49C\uC4B8\uC4D4\uC4F0\uC50C\uC528\uC544\uC560\uC57C\uC598\uC5B4\uC5D0\uC5EC\uC608\uC624\uC640\uC65C\uC678\uC694\uC6B0\uC6CC\uC6E8\uC704\uC720\uC73C\uC758\uC774\uC790\uC7AC\uC7C8\uC7E4\uC800\uC81C\uC838\uC854\uC870\uC88C\uC8A8\uC8C4\uC8E0\uC8FC\uC918\uC934\uC950\uC96C\uC988\uC9A4\uC9C0\uC9DC\uC9F8\uCA14\uCA30\uCA4C\uCA68\uCA84\uCAA0\uCABC\uCAD8\uCAF4\uCB10\uCB2C\uCB48\uCB64\uCB80\uCB9C\uCBB8\uCBD4\uCBF0\uCC0C\uCC28\uCC44\uCC60\uCC7C\uCC98\uCCB4\uCCD0\uCCEC\uCD08\uCD24\uCD40\uCD5C\uCD78\uCD94\uCDB0\uCDCC\uCDE8\uCE04\uCE20\uCE3C\uCE58\uCE74\uCE90\uCEAC\uCEC8\uCEE4\uCF00\uCF1C\uCF38\uCF54\uCF70\uCF8C\uCFA8\uCFC4\uCFE0\uCFFC\uD018\uD034\uD050\uD06C\uD088\uD0A4\uD0C0\uD0DC\uD0F8\uD114\uD130\uD14C\uD168\uD184\uD1A0\uD1BC\uD1D8\uD1F4\uD210\uD22C\uD248\uD264\uD280\uD29C\uD2B8\uD2D4\uD2F0\uD30C\uD328\uD344\uD360\uD37C\uD398\uD3B4\uD3D0\uD3EC\uD408\uD424\uD440\uD45C\uD478\uD494\uD4B0\uD4CC\uD4E8\uD504\uD520\uD53C\uD558\uD574\uD590\uD5AC\uD5C8\uD5E4\uD600\uD61C\uD638\uD654\uD670\uD68C\uD6A8\uD6C4\uD6E0\uD6FC\uD718\uD734\uD750\uD76C\uD788]$/,uh=/^[\uAC01-\uAC1B\uAC1D-\uAC37\uAC39-\uAC53\uAC55-\uAC6F\uAC71-\uAC8B\uAC8D-\uACA7\uACA9-\uACC3\uACC5-\uACDF\uACE1-\uACFB\uACFD-\uAD17\uAD19-\uAD33\uAD35-\uAD4F\uAD51-\uAD6B\uAD6D-\uAD87\uAD89-\uADA3\uADA5-\uADBF\uADC1-\uADDB\uADDD-\uADF7\uADF9-\uAE13\uAE15-\uAE2F\uAE31-\uAE4B\uAE4D-\uAE67\uAE69-\uAE83\uAE85-\uAE9F\uAEA1-\uAEBB\uAEBD-\uAED7\uAED9-\uAEF3\uAEF5-\uAF0F\uAF11-\uAF2B\uAF2D-\uAF47\uAF49-\uAF63\uAF65-\uAF7F\uAF81-\uAF9B\uAF9D-\uAFB7\uAFB9-\uAFD3\uAFD5-\uAFEF\uAFF1-\uB00B\uB00D-\uB027\uB029-\uB043\uB045-\uB05F\uB061-\uB07B\uB07D-\uB097\uB099-\uB0B3\uB0B5-\uB0CF\uB0D1-\uB0EB\uB0ED-\uB107\uB109-\uB123\uB125-\uB13F\uB141-\uB15B\uB15D-\uB177\uB179-\uB193\uB195-\uB1AF\uB1B1-\uB1CB\uB1CD-\uB1E7\uB1E9-\uB203\uB205-\uB21F\uB221-\uB23B\uB23D-\uB257\uB259-\uB273\uB275-\uB28F\uB291-\uB2AB\uB2AD-\uB2C7\uB2C9-\uB2E3\uB2E5-\uB2FF\uB301-\uB31B\uB31D-\uB337\uB339-\uB353\uB355-\uB36F\uB371-\uB38B\uB38D-\uB3A7\uB3A9-\uB3C3\uB3C5-\uB3DF\uB3E1-\uB3FB\uB3FD-\uB417\uB419-\uB433\uB435-\uB44F\uB451-\uB46B\uB46D-\uB487\uB489-\uB4A3\uB4A5-\uB4BF\uB4C1-\uB4DB\uB4DD-\uB4F7\uB4F9-\uB513\uB515-\uB52F\uB531-\uB54B\uB54D-\uB567\uB569-\uB583\uB585-\uB59F\uB5A1-\uB5BB\uB5BD-\uB5D7\uB5D9-\uB5F3\uB5F5-\uB60F\uB611-\uB62B\uB62D-\uB647\uB649-\uB663\uB665-\uB67F\uB681-\uB69B\uB69D-\uB6B7\uB6B9-\uB6D3\uB6D5-\uB6EF\uB6F1-\uB70B\uB70D-\uB727\uB729-\uB743\uB745-\uB75F\uB761-\uB77B\uB77D-\uB797\uB799-\uB7B3\uB7B5-\uB7CF\uB7D1-\uB7EB\uB7ED-\uB807\uB809-\uB823\uB825-\uB83F\uB841-\uB85B\uB85D-\uB877\uB879-\uB893\uB895-\uB8AF\uB8B1-\uB8CB\uB8CD-\uB8E7\uB8E9-\uB903\uB905-\uB91F\uB921-\uB93B\uB93D-\uB957\uB959-\uB973\uB975-\uB98F\uB991-\uB9AB\uB9AD-\uB9C7\uB9C9-\uB9E3\uB9E5-\uB9FF\uBA01-\uBA1B\uBA1D-\uBA37\uBA39-\uBA53\uBA55-\uBA6F\uBA71-\uBA8B\uBA8D-\uBAA7\uBAA9-\uBAC3\uBAC5-\uBADF\uBAE1-\uBAFB\uBAFD-\uBB17\uBB19-\uBB33\uBB35-\uBB4F\uBB51-\uBB6B\uBB6D-\uBB87\uBB89-\uBBA3\uBBA5-\uBBBF\uBBC1-\uBBDB\uBBDD-\uBBF7\uBBF9-\uBC13\uBC15-\uBC2F\uBC31-\uBC4B\uBC4D-\uBC67\uBC69-\uBC83\uBC85-\uBC9F\uBCA1-\uBCBB\uBCBD-\uBCD7\uBCD9-\uBCF3\uBCF5-\uBD0F\uBD11-\uBD2B\uBD2D-\uBD47\uBD49-\uBD63\uBD65-\uBD7F\uBD81-\uBD9B\uBD9D-\uBDB7\uBDB9-\uBDD3\uBDD5-\uBDEF\uBDF1-\uBE0B\uBE0D-\uBE27\uBE29-\uBE43\uBE45-\uBE5F\uBE61-\uBE7B\uBE7D-\uBE97\uBE99-\uBEB3\uBEB5-\uBECF\uBED1-\uBEEB\uBEED-\uBF07\uBF09-\uBF23\uBF25-\uBF3F\uBF41-\uBF5B\uBF5D-\uBF77\uBF79-\uBF93\uBF95-\uBFAF\uBFB1-\uBFCB\uBFCD-\uBFE7\uBFE9-\uC003\uC005-\uC01F\uC021-\uC03B\uC03D-\uC057\uC059-\uC073\uC075-\uC08F\uC091-\uC0AB\uC0AD-\uC0C7\uC0C9-\uC0E3\uC0E5-\uC0FF\uC101-\uC11B\uC11D-\uC137\uC139-\uC153\uC155-\uC16F\uC171-\uC18B\uC18D-\uC1A7\uC1A9-\uC1C3\uC1C5-\uC1DF\uC1E1-\uC1FB\uC1FD-\uC217\uC219-\uC233\uC235-\uC24F\uC251-\uC26B\uC26D-\uC287\uC289-\uC2A3\uC2A5-\uC2BF\uC2C1-\uC2DB\uC2DD-\uC2F7\uC2F9-\uC313\uC315-\uC32F\uC331-\uC34B\uC34D-\uC367\uC369-\uC383\uC385-\uC39F\uC3A1-\uC3BB\uC3BD-\uC3D7\uC3D9-\uC3F3\uC3F5-\uC40F\uC411-\uC42B\uC42D-\uC447\uC449-\uC463\uC465-\uC47F\uC481-\uC49B\uC49D-\uC4B7\uC4B9-\uC4D3\uC4D5-\uC4EF\uC4F1-\uC50B\uC50D-\uC527\uC529-\uC543\uC545-\uC55F\uC561-\uC57B\uC57D-\uC597\uC599-\uC5B3\uC5B5-\uC5CF\uC5D1-\uC5EB\uC5ED-\uC607\uC609-\uC623\uC625-\uC63F\uC641-\uC65B\uC65D-\uC677\uC679-\uC693\uC695-\uC6AF\uC6B1-\uC6CB\uC6CD-\uC6E7\uC6E9-\uC703\uC705-\uC71F\uC721-\uC73B\uC73D-\uC757\uC759-\uC773\uC775-\uC78F\uC791-\uC7AB\uC7AD-\uC7C7\uC7C9-\uC7E3\uC7E5-\uC7FF\uC801-\uC81B\uC81D-\uC837\uC839-\uC853\uC855-\uC86F\uC871-\uC88B\uC88D-\uC8A7\uC8A9-\uC8C3\uC8C5-\uC8DF\uC8E1-\uC8FB\uC8FD-\uC917\uC919-\uC933\uC935-\uC94F\uC951-\uC96B\uC96D-\uC987\uC989-\uC9A3\uC9A5-\uC9BF\uC9C1-\uC9DB\uC9DD-\uC9F7\uC9F9-\uCA13\uCA15-\uCA2F\uCA31-\uCA4B\uCA4D-\uCA67\uCA69-\uCA83\uCA85-\uCA9F\uCAA1-\uCABB\uCABD-\uCAD7\uCAD9-\uCAF3\uCAF5-\uCB0F\uCB11-\uCB2B\uCB2D-\uCB47\uCB49-\uCB63\uCB65-\uCB7F\uCB81-\uCB9B\uCB9D-\uCBB7\uCBB9-\uCBD3\uCBD5-\uCBEF\uCBF1-\uCC0B\uCC0D-\uCC27\uCC29-\uCC43\uCC45-\uCC5F\uCC61-\uCC7B\uCC7D-\uCC97\uCC99-\uCCB3\uCCB5-\uCCCF\uCCD1-\uCCEB\uCCED-\uCD07\uCD09-\uCD23\uCD25-\uCD3F\uCD41-\uCD5B\uCD5D-\uCD77\uCD79-\uCD93\uCD95-\uCDAF\uCDB1-\uCDCB\uCDCD-\uCDE7\uCDE9-\uCE03\uCE05-\uCE1F\uCE21-\uCE3B\uCE3D-\uCE57\uCE59-\uCE73\uCE75-\uCE8F\uCE91-\uCEAB\uCEAD-\uCEC7\uCEC9-\uCEE3\uCEE5-\uCEFF\uCF01-\uCF1B\uCF1D-\uCF37\uCF39-\uCF53\uCF55-\uCF6F\uCF71-\uCF8B\uCF8D-\uCFA7\uCFA9-\uCFC3\uCFC5-\uCFDF\uCFE1-\uCFFB\uCFFD-\uD017\uD019-\uD033\uD035-\uD04F\uD051-\uD06B\uD06D-\uD087\uD089-\uD0A3\uD0A5-\uD0BF\uD0C1-\uD0DB\uD0DD-\uD0F7\uD0F9-\uD113\uD115-\uD12F\uD131-\uD14B\uD14D-\uD167\uD169-\uD183\uD185-\uD19F\uD1A1-\uD1BB\uD1BD-\uD1D7\uD1D9-\uD1F3\uD1F5-\uD20F\uD211-\uD22B\uD22D-\uD247\uD249-\uD263\uD265-\uD27F\uD281-\uD29B\uD29D-\uD2B7\uD2B9-\uD2D3\uD2D5-\uD2EF\uD2F1-\uD30B\uD30D-\uD327\uD329-\uD343\uD345-\uD35F\uD361-\uD37B\uD37D-\uD397\uD399-\uD3B3\uD3B5-\uD3CF\uD3D1-\uD3EB\uD3ED-\uD407\uD409-\uD423\uD425-\uD43F\uD441-\uD45B\uD45D-\uD477\uD479-\uD493\uD495-\uD4AF\uD4B1-\uD4CB\uD4CD-\uD4E7\uD4E9-\uD503\uD505-\uD51F\uD521-\uD53B\uD53D-\uD557\uD559-\uD573\uD575-\uD58F\uD591-\uD5AB\uD5AD-\uD5C7\uD5C9-\uD5E3\uD5E5-\uD5FF\uD601-\uD61B\uD61D-\uD637\uD639-\uD653\uD655-\uD66F\uD671-\uD68B\uD68D-\uD6A7\uD6A9-\uD6C3\uD6C5-\uD6DF\uD6E1-\uD6FB\uD6FD-\uD717\uD719-\uD733\uD735-\uD74F\uD751-\uD76B\uD76D-\uD787\uD789-\uD7A3]$/,uA=/^(?:[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u2388\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2605\u2607-\u2612\u2614-\u2685\u2690-\u2705\u2708-\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763-\u2767\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC00-\uDCFF\uDD0D-\uDD0F\uDD2F\uDD6C-\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDAD-\uDDE5\uDE01-\uDE0F\uDE1A\uDE2F\uDE32-\uDE3A\uDE3C-\uDE3F\uDE49-\uDFFA]|\uD83D[\uDC00-\uDD3D\uDD46-\uDE4F\uDE80-\uDEFF\uDF74-\uDF7F\uDFD5-\uDFFF]|\uD83E[\uDC0C-\uDC0F\uDC48-\uDC4F\uDC5A-\uDC5F\uDC88-\uDC8F\uDCAE-\uDCFF\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDEFF]|\uD83F[\uDC00-\uDFFD])$/,uv=(u,e)=>{var r=t.Any;return -1!==u.search(uD)&&(r|=t.Extend),8205===e&&(r|=t.ZWJ),e>=127462&&e<=127487&&(r|=t.RI),-1!==u.search(us)&&(r|=t.Prepend),-1!==u.search(ul)&&(r|=t.SpacingMark),-1!==u.search(uC)&&(r|=t.L),-1!==u.search(uf)&&(r|=t.V),-1!==u.search(uc)&&(r|=t.T),-1!==u.search(uB)&&(r|=t.LV),-1!==u.search(uh)&&(r|=t.LVT),-1!==u.search(uA)&&(r|=t.ExtPict),r},ud=[[t.L,t.L|t.V|t.LV|t.LVT],[t.LV|t.V,t.V|t.T],[t.LVT|t.T,t.T],[t.Any,t.Extend|t.ZWJ],[t.Any,t.SpacingMark],[t.Prepend,t.Any],[t.ZWJ,t.ExtPict],[t.RI,t.RI]],up=/(?:[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u2388\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2605\u2607-\u2612\u2614-\u2685\u2690-\u2705\u2708-\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763-\u2767\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC00-\uDCFF\uDD0D-\uDD0F\uDD2F\uDD6C-\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDAD-\uDDE5\uDE01-\uDE0F\uDE1A\uDE2F\uDE32-\uDE3A\uDE3C-\uDE3F\uDE49-\uDFFA]|\uD83D[\uDC00-\uDD3D\uDD46-\uDE4F\uDE80-\uDEFF\uDF74-\uDF7F\uDFD5-\uDFFF]|\uD83E[\uDC0C-\uDC0F\uDC48-\uDC4F\uDC5A-\uDC5F\uDC88-\uDC8F\uDCAE-\uDCFF\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDEFF]|\uD83F[\uDC00-\uDFFD])(?:[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0898-\u089F\u08CA-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09BE\u09C1-\u09C4\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3E\u0B3F\u0B41-\u0B44\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B82\u0BBE\u0BC0\u0BCD\u0BD7\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CBF\u0CC2\u0CC6\u0CCC\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D3E\u0D41-\u0D44\u0D4D\u0D57\u0D62\u0D63\u0D81\u0DCA\u0DCF\u0DD2-\u0DD4\u0DD6\u0DDF\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u200C\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFF9E\uFF9F]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD803[\uDD24-\uDD27\uDEAB\uDEAC\uDEFD-\uDEFF\uDF46-\uDF50\uDF82-\uDF85]|\uD804[\uDC01\uDC38-\uDC46\uDC70\uDC73\uDC74\uDC7F-\uDC81\uDCB3-\uDCB6\uDCB9\uDCBA\uDCC2\uDD00-\uDD02\uDD27-\uDD2B\uDD2D-\uDD34\uDD73\uDD80\uDD81\uDDB6-\uDDBE\uDDC9-\uDDCC\uDDCF\uDE2F-\uDE31\uDE34\uDE36\uDE37\uDE3E\uDE41\uDEDF\uDEE3-\uDEEA\uDF00\uDF01\uDF3B\uDF3C\uDF3E\uDF40\uDF57\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC38-\uDC3F\uDC42-\uDC44\uDC46\uDC5E\uDCB0\uDCB3-\uDCB8\uDCBA\uDCBD\uDCBF\uDCC0\uDCC2\uDCC3\uDDAF\uDDB2-\uDDB5\uDDBC\uDDBD\uDDBF\uDDC0\uDDDC\uDDDD\uDE33-\uDE3A\uDE3D\uDE3F\uDE40\uDEAB\uDEAD\uDEB0-\uDEB5\uDEB7\uDF1D-\uDF1F\uDF22-\uDF25\uDF27-\uDF2B]|\uD806[\uDC2F-\uDC37\uDC39\uDC3A\uDD30\uDD3B\uDD3C\uDD3E\uDD43\uDDD4-\uDDD7\uDDDA\uDDDB\uDDE0\uDE01-\uDE0A\uDE33-\uDE38\uDE3B-\uDE3E\uDE47\uDE51-\uDE56\uDE59-\uDE5B\uDE8A-\uDE96\uDE98\uDE99]|\uD807[\uDC30-\uDC36\uDC38-\uDC3D\uDC3F\uDC92-\uDCA7\uDCAA-\uDCB0\uDCB2\uDCB3\uDCB5\uDCB6\uDD31-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD45\uDD47\uDD90\uDD91\uDD95\uDD97\uDEF3\uDEF4\uDF00\uDF01\uDF36-\uDF3A\uDF40\uDF42]|\uD80D[\uDC40\uDC47-\uDC55]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF4F\uDF8F-\uDF92\uDFE4]|\uD82F[\uDC9D\uDC9E]|\uD833[\uDF00-\uDF2D\uDF30-\uDF46]|\uD834[\uDD65\uDD67-\uDD69\uDD6E-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDC8F\uDD30-\uDD36\uDEAE\uDEEC-\uDEEF]|\uD839[\uDCEC-\uDCEF]|\uD83A[\uDCD0-\uDCD6\uDD44-\uDD4A]|\uD83C[\uDFFB-\uDFFF]|\uDB40[\uDC20-\uDC7F\uDD00-\uDDEF])*\u200D$/,uF=u=>-1!==u.search(up),uE=/(?:\uD83C[\uDDE6-\uDDFF])+$/g,ug=u=>{var e=u.match(uE);return null!==e&&e[0].length/2%2==1};function uy(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function um(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uy(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):uy(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var ub=um(um(um(um({},{transform(u,e){u.children=(0,o.mq)(u.children);var r=u.selection&&(0,o.mq)(u.selection);try{r=E(u,r,e)}finally{u.children=(0,o.vD)(u.children),r?u.selection=(0,o.Qx)(r)?(0,o.vD)(r):r:u.selection=null}}}),{insertNodes(u,e,r){u.insertNodes(e,r)},liftNodes(u,e){u.liftNodes(e)},mergeNodes(u,e){u.mergeNodes(e)},moveNodes(u,e){u.moveNodes(e)},removeNodes(u,e){u.removeNodes(e)},setNodes(u,e,r){u.setNodes(e,r)},splitNodes(u,e){u.splitNodes(e)},unsetNodes(u,e,r){u.unsetNodes(e,r)},unwrapNodes(u,e){u.unwrapNodes(e)},wrapNodes(u,e,r){u.wrapNodes(e,r)}}),{collapse(u,e){u.collapse(e)},deselect(u){u.deselect()},move(u,e){u.move(e)},select(u,e){u.select(e)},setPoint(u,e,r){u.setPoint(e,r)},setSelection(u,e){u.setSelection(e)}}),{delete(u,e){u.delete(e)},insertFragment(u,e,r){u.insertFragment(e,r)},insertText(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};I.withoutNormalizing(u,()=>{var{voids:t=!1}=r,{at:n=Y(u)}=r;if(A.isPath(n)&&(n=I.range(u,n)),O.isRange(n)){if(O.isCollapsed(n))n=n.anchor;else{var o=O.end(n);if(!t&&I.void(u,{at:o}))return;var a=O.start(n),i=I.pointRef(u,a),D=I.pointRef(u,o);ub.delete(u,{at:n,voids:t});var s=i.unref(),l=D.unref();n=s||l,ub.setSelection(u,{anchor:n,focus:n})}}if(!(!t&&I.void(u,{at:n})||I.elementReadOnly(u,{at:n}))){var{path:C,offset:f}=n;e.length>0&&u.apply({type:"insert_text",path:C,offset:f,text:e})}})}}),uO=new WeakMap,uw=u=>uO.get(u)||!1,uP=(u,e,r)=>{var t=uO.get(u)||!1;uO.set(u,!0);try{e(),r()}finally{uO.set(u,t)}};function ux(u,e,r){var t,n,o=s.get(u)||[],a=l.get(u)||new Set,i=u=>{if(u){var e=u.join(",");n.has(e)||(n.add(e),t.push(u))}};if(r)for(var D of(t=[],n=new Set,o))i(r(D));else t=o,n=a;for(var C of e)i(C);s.set(u,t),l.set(u,n)}var uj=(u,e)=>{for(var r of I.pathRefs(u))a.transform(r,e);for(var t of I.pointRefs(u))i.transform(t,e);for(var n of I.rangeRefs(u))D.transform(n,e);if(!uw(u)){var o=A.operationCanTransformPath(e)?u=>A.transform(u,e):void 0;ux(u,u.getDirtyPaths(e),o)}ub.transform(u,e),u.operations.push(e),I.normalize(u,{operation:e}),"set_selection"===e.type&&(u.marks=null),C.get(u)||(C.set(u,!0),Promise.resolve().then(()=>{C.set(u,!1),u.onChange({operation:e}),u.operations=[]}))},uk=(u,e)=>{switch(e.type){case"insert_text":case"remove_text":case"set_node":var{path:r}=e;return A.levels(r);case"insert_node":var{node:t,path:n}=e;return[...A.levels(n),...K.isText(t)?[]:Array.from(N.nodes(t),u=>{var[,e]=u;return n.concat(e)})];case"merge_node":var{path:o}=e;return[...A.ancestors(o),A.previous(o)];case"move_node":var{path:a,newPath:i}=e;if(A.equals(a,i))return[];var D=[],s=[];for(var l of A.ancestors(a)){var C=A.transform(l,e);D.push(C)}for(var f of A.ancestors(i)){var c=A.transform(f,e);s.push(c)}var B=s[s.length-1],h=i[i.length-1];return[...D,...s,B.concat(h)];case"remove_node":var{path:v}=e;return[...A.ancestors(v)];case"split_node":var{path:d}=e;return[...A.levels(d),A.next(d)];default:return[]}},uN=u=>{var{selection:e}=u;return e?N.fragment(u,e):[]},uR=(u,e)=>{var[r,t]=e;if(!K.isText(r)){if(P.isElement(r)&&0===r.children.length){ub.insertNodes(u,{text:""},{at:t.concat(0),voids:!0});return}for(var n=!I.isEditor(r)&&P.isElement(r)&&(u.isInline(r)||0===r.children.length||K.isText(r.children[0])||u.isInline(r.children[0])),o=0,a=0;a<r.children.length;a++,o++){var i=N.get(u,t);if(!K.isText(i)){var D=i.children[o],s=i.children[o-1],l=a===r.children.length-1,C=K.isText(D)||P.isElement(D)&&u.isInline(D);if(C!==n)C?ub.removeNodes(u,{at:t.concat(o),voids:!0}):ub.unwrapNodes(u,{at:t.concat(o),voids:!0}),o--;else if(P.isElement(D)){if(u.isInline(D)){if(null!=s&&K.isText(s)){if(l){var f={text:""};ub.insertNodes(u,f,{at:t.concat(o+1),voids:!0}),o++}}else{var c={text:""};ub.insertNodes(u,c,{at:t.concat(o),voids:!0}),o++}}}else K.isText(D)||"children"in D||(D.children=[]),null!=s&&K.isText(s)&&(K.equals(D,s,{loose:!0})?(ub.mergeNodes(u,{at:t.concat(o),voids:!0}),o--):""===s.text?(ub.removeNodes(u,{at:t.concat(o-1),voids:!0}),o--):""===D.text&&(ub.removeNodes(u,{at:t.concat(o),voids:!0}),o--))}}}},uS=(u,e)=>{var{iteration:r,initialDirtyPathsLength:t}=e,n=42*t;if(r>n)throw Error("Could not completely normalize the editor after ".concat(n," iterations! This is usually due to incorrect normalization logic that leaves a node in an invalid state."));return!0},uT=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{voids:r=!1,mode:t="lowest",at:n=u.selection,match:o}=e;if(n){var a=I.path(u,n);for(var[i,D]of I.levels(u,{at:a,voids:r,match:o,reverse:"lowest"===t}))if(!K.isText(i)){if(O.isRange(n)){if(A.isAncestor(D,n.anchor.path)&&A.isAncestor(D,n.focus.path))return[i,D]}else if(!A.equals(a,D))return[i,D]}}};function u_(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function uq(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u_(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):u_(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var uI=(u,e,r)=>{var{selection:t}=u;if(t){var n=(e,r)=>{if(!K.isText(e))return!1;var[t,n]=I.parent(u,r);return!u.isVoid(t)||u.markableVoid(t)},o=O.isExpanded(t),a=!1;if(!o){var[i,D]=I.node(u,t);if(i&&n(i,D)){var[s]=I.parent(u,D);a=s&&u.markableVoid(s)}}if(o||a)ub.setNodes(u,{[e]:r},{match:n,split:!0,voids:!0});else{var l=uq(uq({},I.marks(u)||{}),{},{[e]:r});u.marks=l,C.get(u)||u.onChange()}}};function uz(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function uV(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uz(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):uz(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var uM=function(u,e){var r,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=I.point(u,e,{edge:"end"}),o=I.end(u,[]),{distance:a=1}=t,i=0;for(var D of I.positions(u,uV(uV({},t),{},{at:{anchor:n,focus:o}}))){if(i>a)break;0!==i&&(r=D),i++}return r};function uL(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function uW(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uL(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):uL(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var uQ=function(u,e){var r,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=I.start(u,[]),o=I.point(u,e,{edge:"start"}),{distance:a=1}=t,i=0;for(var D of I.positions(u,uW(uW({},t),{},{at:{anchor:n,focus:o},reverse:!0}))){if(i>a)break;0!==i&&(r=D),i++}return r},u$=(u,e)=>{var{selection:r}=u;r&&O.isCollapsed(r)&&ub.delete(u,{unit:e,reverse:!0})},uJ=(u,e)=>{var{selection:r}=u;r&&O.isCollapsed(r)&&ub.delete(u,{unit:e})},uZ=function(u){var{direction:e="forward"}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{selection:r}=u;r&&O.isExpanded(r)&&ub.delete(u,{reverse:"backward"===e})},uH=(u,e)=>[I.start(u,e),I.end(u,e)];function uK(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function uY(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uK(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):uK(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var uG=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return I.above(u,uY(uY({},e),{},{match:e=>P.isElement(e)&&I.isElementReadOnly(u,e)}))},uU=(u,e)=>I.point(u,e,{edge:"end"}),uX=(u,e)=>{var r=I.path(u,e,{edge:"start"});return I.node(u,r)},u0=(u,e)=>{var r=I.range(u,e);return N.fragment(u,r)};function u1(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function u3(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u1(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):u1(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var u2=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return I.above(u,u3(u3({},e),{},{match:e=>P.isElement(e)&&I.isVoid(u,e)}))},u7=(u,e)=>e.children.some(e=>P.isElement(e)&&I.isBlock(u,e)),u8=(u,e)=>e.children.some(e=>K.isText(e)||I.isInline(u,e)),u4=(u,e)=>N.has(u,e),u9=(u,e)=>e.children.every(u=>K.isText(u)),u5=u=>{ub.splitNodes(u,{always:!0})},u6=(u,e,r)=>{ub.insertNodes(u,e,r)},eu=u=>{ub.splitNodes(u,{always:!0})};function ee(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}var er=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{selection:t,marks:n}=u;if(t){if(n){var o=function(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}({text:e},n);ub.insertNodes(u,o,{at:r.at,voids:r.voids})}else ub.insertText(u,e,r);u.marks=null}},et=(u,e)=>!u.isInline(e),en=(u,e,r)=>I.isStart(u,e,r)||I.isEnd(u,e,r),eo=(u,e)=>{var{children:r}=e,[t]=r;return 0===r.length||1===r.length&&K.isText(t)&&""===t.text&&!u.isVoid(e)},ea=(u,e,r)=>{var t=I.end(u,r);return L.equals(e,t)},ei=u=>{var e=f.get(u);return void 0===e||e},eD=(u,e,r)=>{if(0!==e.offset)return!1;var t=I.start(u,r);return L.equals(e,t)},es=(u,e)=>{var r=I.path(u,e,{edge:"end"});return I.node(u,r)},el=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=I.path(u,e,r);return[N.leaf(u,t),t]},eC=["text"],ef=["text"],ec=function(u){var{marks:e,selection:r}=u;if(!r)return null;var{anchor:t,focus:n}=r;if(e)return e;if(O.isExpanded(r)){if(O.isBackward(r)&&([n,t]=[t,n]),I.isEnd(u,t,t.path)){var o=I.after(u,t);o&&(t=o)}var[a]=I.nodes(u,{match:K.isText,at:{anchor:t,focus:n}});if(!a)return{};var[i]=a;return y(i,eC)}var{path:D}=t,[s]=I.leaf(u,D);if(0===t.offset){var l=I.previous(u,{at:D,match:K.isText});if(!I.above(u,{match:e=>P.isElement(e)&&I.isVoid(u,e)&&u.markableVoid(e)})){var C=I.above(u,{match:e=>P.isElement(e)&&I.isBlock(u,e)});if(l&&C){var[f,c]=l,[,B]=C;A.isAncestor(B,c)&&(s=f)}}}return y(s,ef)},eB=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{mode:r="lowest",voids:t=!1}=e,{match:n,at:o=u.selection}=e;if(o){var a=I.after(u,o,{voids:t});if(a){var[,i]=I.last(u,[]),D=[a.path,i];if(A.isPath(o)&&0===o.length)throw Error("Cannot get the next node from the root node!");if(null==n){if(A.isPath(o)){var[s]=I.parent(u,o);n=u=>s.children.includes(u)}else n=()=>!0}var[l]=I.nodes(u,{at:D,match:n,mode:r,voids:t});return l}}},eh=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=I.path(u,e,r);return[N.get(u,t),t]},eA=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{force:r=!1,operation:t}=e,n=u=>s.get(u)||[],o=u=>l.get(u)||new Set,a=u=>{var e=n(u).pop(),r=e.join(",");return o(u).delete(r),e};if(I.isNormalizing(u)){if(r){var i=Array.from(N.nodes(u),u=>{var[,e]=u;return e}),D=new Set(i.map(u=>u.join(",")));s.set(u,i),l.set(u,D)}0!==n(u).length&&I.withoutNormalizing(u,()=>{for(var e of n(u))if(N.has(u,e)){var r=I.node(u,e),[o,i]=r;P.isElement(o)&&0===o.children.length&&u.normalizeNode(r,{operation:t})}for(var D=n(u),s=D.length,l=0;0!==D.length;){if(!u.shouldNormalize({dirtyPaths:D,iteration:l,initialDirtyPathsLength:s,operation:t}))return;var C=a(u);if(N.has(u,C)){var f=I.node(u,C);u.normalizeNode(f,{operation:t})}l++,D=n(u)}})}},ev=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=I.path(u,e,r),n=A.parent(t);return I.node(u,n)},ed=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{affinity:t="forward"}=r,n={current:e,affinity:t,unref(){var{current:e}=n;return I.pathRefs(u).delete(n),n.current=null,e}};return I.pathRefs(u).add(n),n},ep=u=>{var e=c.get(u);return e||(e=new Set,c.set(u,e)),e},eF=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{depth:t,edge:n}=r;if(A.isPath(e)){if("start"===n){var[,o]=N.first(u,e);e=o}else if("end"===n){var[,a]=N.last(u,e);e=a}}return O.isRange(e)&&(e="start"===n?O.start(e):"end"===n?O.end(e):A.common(e.anchor.path,e.focus.path)),L.isPoint(e)&&(e=e.path),null!=t&&(e=e.slice(0,t)),e},eE=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{affinity:t="forward"}=r,n={current:e,affinity:t,unref(){var{current:e}=n;return I.pointRefs(u).delete(n),n.current=null,e}};return I.pointRefs(u).add(n),n},eg=u=>{var e=B.get(u);return e||(e=new Set,B.set(u,e)),e},ey=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{edge:t="start"}=r;if(A.isPath(e)){if("end"===t){var n,[,o]=N.last(u,e);n=o}else{var[,a]=N.first(u,e);n=a}var i=N.get(u,n);if(!K.isText(i))throw Error("Cannot get the ".concat(t," point in the node at path [").concat(e,"] because it has no ").concat(t," text node."));return{path:n,offset:"end"===t?i.text.length:0}}if(O.isRange(e)){var[D,s]=O.edges(e);return"start"===t?D:s}return e},em=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{mode:r="lowest",voids:t=!1}=e,{match:n,at:o=u.selection}=e;if(o){var a=I.before(u,o,{voids:t});if(a){var[,i]=I.first(u,[]),D=[a.path,i];if(A.isPath(o)&&0===o.length)throw Error("Cannot get the previous node from the root node!");if(null==n){if(A.isPath(o)){var[s]=I.parent(u,o);n=u=>s.children.includes(u)}else n=()=>!0}var[l]=I.nodes(u,{reverse:!0,at:D,match:n,mode:r,voids:t});return l}}},eb=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{affinity:t="forward"}=r,n={current:e,affinity:t,unref(){var{current:e}=n;return I.rangeRefs(u).delete(n),n.current=null,e}};return I.rangeRefs(u).add(n),n},eO=u=>{var e=h.get(u);return e||(e=new Set,h.set(u,e)),e},ew=(u,e,r)=>O.isRange(e)&&!r?e:{anchor:I.start(u,e),focus:I.end(u,r||e)};function eP(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}var ex=(u,e)=>{var{selection:r}=u;if(r){var t=(e,r)=>{if(!K.isText(e))return!1;var[t,n]=I.parent(u,r);return!u.isVoid(t)||u.markableVoid(t)},n=O.isExpanded(r),o=!1;if(!n){var[a,i]=I.node(u,r);if(a&&t(a,i)){var[D]=I.parent(u,i);o=D&&u.markableVoid(D)}}if(n||o)ub.unsetNodes(u,e,{match:t,split:!0,voids:!0});else{var s=function(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eP(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):eP(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}({},I.marks(u)||{});delete s[e],u.marks=s,C.get(u)||u.onChange()}}},ej=(u,e)=>{f.set(u,e)},ek=(u,e)=>I.point(u,e,{edge:"start"}),eN=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{voids:t=!1}=r,n=I.range(u,e),[o,a]=O.edges(n),i="";for(var[D,s]of I.nodes(u,{at:n,match:K.isText,voids:t})){var l=D.text;A.equals(s,a.path)&&(l=l.slice(0,a.offset)),A.equals(s,o.path)&&(l=l.slice(o.offset)),i+=l}return i},eR=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{voids:t=!1}=r,[n,o]=O.edges(e);if(0!==n.offset||0!==o.offset||O.isCollapsed(e)||A.hasPrevious(o.path))return e;var a=I.above(u,{at:o,match:e=>P.isElement(e)&&I.isBlock(u,e),voids:t}),i=a?a[1]:[],D={anchor:I.start(u,n),focus:o},s=!0;for(var[l,C]of I.nodes(u,{at:D,match:K.isText,reverse:!0,voids:t})){if(s){s=!1;continue}if(""!==l.text||A.isBefore(C,i)){o={path:C,offset:l.text.length};break}}return{anchor:n,focus:o}},eS=(u,e)=>{var r=I.isNormalizing(u);I.setNormalizing(u,!1);try{e()}finally{I.setNormalizing(u,r)}I.normalize(u)},eT=(u,e,r)=>{var[t,n]=e;return P.isElement(t)&&I.isEmpty(u,t)||K.isText(t)&&""===t.text&&0!==n[n.length-1]},e_=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};I.withoutNormalizing(u,()=>{var r,t,n,{reverse:o=!1,unit:a="character",distance:i=1,voids:D=!1}=e,{at:s=u.selection,hanging:l=!1}=e;if(s){var C=!1;if(O.isRange(s)&&O.isCollapsed(s)&&(C=!0,s=s.anchor),L.isPoint(s)){var f=I.void(u,{at:s,mode:"highest"});if(!D&&f){var[,c]=f;s=c}else{var B={unit:a,distance:i},h=o?I.before(u,s,B)||I.start(u,[]):I.after(u,s,B)||I.end(u,[]);s={anchor:s,focus:h},l=!0}}if(A.isPath(s)){ub.removeNodes(u,{at:s,voids:D});return}if(!O.isCollapsed(s)){if(!l){var[,v]=O.edges(s),d=I.end(u,[]);L.equals(v,d)||(s=I.unhangRange(u,s,{voids:D}))}var[p,F]=O.edges(s),E=I.above(u,{match:e=>P.isElement(e)&&I.isBlock(u,e),at:p,voids:D}),g=I.above(u,{match:e=>P.isElement(e)&&I.isBlock(u,e),at:F,voids:D}),y=E&&g&&!A.equals(E[1],g[1]),m=A.equals(p.path,F.path),b=D?null:null!==(r=I.void(u,{at:p,mode:"highest"}))&&void 0!==r?r:I.elementReadOnly(u,{at:p,mode:"highest"}),w=D?null:null!==(t=I.void(u,{at:F,mode:"highest"}))&&void 0!==t?t:I.elementReadOnly(u,{at:F,mode:"highest"});if(b){var x=I.before(u,p);x&&E&&A.isAncestor(E[1],x.path)&&(p=x)}if(w){var j=I.after(u,F);j&&g&&A.isAncestor(g[1],j.path)&&(F=j)}var k=[];for(var N of I.nodes(u,{at:s,voids:D})){var[R,S]=N;(!n||0!==A.compare(S,n))&&(!D&&P.isElement(R)&&(I.isVoid(u,R)||I.isElementReadOnly(u,R))||!A.isCommon(S,p.path)&&!A.isCommon(S,F.path))&&(k.push(N),n=S)}var T=Array.from(k,e=>{var[,r]=e;return I.pathRef(u,r)}),_=I.pointRef(u,p),q=I.pointRef(u,F),z="";if(!m&&!b){var V=_.current,[M]=I.leaf(u,V),{path:W}=V,{offset:Q}=p,$=M.text.slice(Q);$.length>0&&(u.apply({type:"remove_text",path:W,offset:Q,text:$}),z=$)}if(T.reverse().map(u=>u.unref()).filter(u=>null!==u).forEach(e=>ub.removeNodes(u,{at:e,voids:D})),!w){var J=q.current,[Z]=I.leaf(u,J),{path:H}=J,K=m?p.offset:0,Y=Z.text.slice(K,F.offset);Y.length>0&&(u.apply({type:"remove_text",path:H,offset:K,text:Y}),z=Y)}!m&&y&&q.current&&_.current&&ub.mergeNodes(u,{at:q.current,hanging:!0,voids:D}),C&&o&&"character"===a&&z.length>1&&z.match(/[\u0E00-\u0E7F]+/)&&ub.insertText(u,z.slice(0,z.length-i));var G=_.unref(),U=q.unref(),X=o?G||U:U||G;null==e.at&&X&&ub.select(u,X)}}})},eq=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};I.withoutNormalizing(u,()=>{var t,{hanging:n=!1,voids:o=!1}=r,{at:a=Y(u),batchDirty:i=!0}=r;if(e.length){if(O.isRange(a)){if(n||(a=I.unhangRange(u,a,{voids:o})),O.isCollapsed(a))a=a.anchor;else{var[,D]=O.edges(a);if(!o&&I.void(u,{at:D}))return;var s=I.pointRef(u,D);ub.delete(u,{at:a}),a=s.unref()}}else A.isPath(a)&&(a=I.start(u,a));if(!(!o&&I.void(u,{at:a}))){var l=I.above(u,{at:a,match:e=>P.isElement(e)&&I.isInline(u,e),mode:"highest",voids:o});if(l){var[,C]=l;I.isEnd(u,a,C)?a=I.after(u,C):I.isStart(u,a,C)&&(a=I.before(u,C))}var[,f]=I.above(u,{match:e=>P.isElement(e)&&I.isBlock(u,e),at:a,voids:o}),c=I.isStart(u,a,f),B=I.isEnd(u,a,f),h=c&&B,v=!c||c&&B,d=!B,[,p]=N.first({children:e},[]),[,F]=N.last({children:e},[]),E=[],g=e=>{var[r,t]=e;return 0!==t.length&&(!!h||!(v&&A.isAncestor(t,p)&&P.isElement(r)&&!u.isVoid(r)&&!u.isInline(r)||d&&A.isAncestor(t,F)&&P.isElement(r)&&!u.isVoid(r)&&!u.isInline(r)))};for(var y of N.nodes({children:e},{pass:g}))g(y)&&E.push(y);var m=[],b=[],w=[],x=!0,j=!1;for(var[k]of E)P.isElement(k)&&!u.isInline(k)?(x=!1,j=!0,b.push(k)):x?m.push(k):w.push(k);var[R]=I.nodes(u,{at:a,match:e=>K.isText(e)||I.isInline(u,e),mode:"highest",voids:o}),[,S]=R,T=I.isStart(u,a,S),_=I.isEnd(u,a,S),q=I.pathRef(u,B&&!w.length?A.next(f):f),z=I.pathRef(u,_?A.next(S):S);ub.splitNodes(u,{at:a,match:e=>j?P.isElement(e)&&I.isBlock(u,e):K.isText(e)||I.isInline(u,e),mode:j?"lowest":"highest",always:j&&(!c||m.length>0)&&(!B||w.length>0),voids:o});var V=I.pathRef(u,!T||T&&_?A.next(S):S);if(ub.insertNodes(u,m,{at:V.current,match:e=>K.isText(e)||I.isInline(u,e),mode:"highest",voids:o,batchDirty:i}),h&&!m.length&&b.length&&!w.length&&ub.delete(u,{at:f,voids:o}),ub.insertNodes(u,b,{at:q.current,match:e=>P.isElement(e)&&I.isBlock(u,e),mode:"lowest",voids:o,batchDirty:i}),ub.insertNodes(u,w,{at:z.current,match:e=>K.isText(e)||I.isInline(u,e),mode:"highest",voids:o,batchDirty:i}),!r.at&&(w.length>0&&z.current?t=A.previous(z.current):b.length>0&&q.current?t=A.previous(q.current):V.current&&(t=A.previous(V.current)),t)){var M=I.end(u,t);ub.select(u,M)}V.unref(),q.unref(),z.unref()}}})},eI=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{edge:r="anchor"}=e,{selection:t}=u;if(t){if("anchor"===r)ub.select(u,t.anchor);else if("focus"===r)ub.select(u,t.focus);else if("start"===r){var[n]=O.edges(t);ub.select(u,n)}else if("end"===r){var[,o]=O.edges(t);ub.select(u,o)}}},ez=u=>{var{selection:e}=u;e&&u.apply({type:"set_selection",properties:e,newProperties:null})},eV=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{selection:r}=u,{distance:t=1,unit:n="character",reverse:o=!1}=e,{edge:a=null}=e;if(r){"start"===a&&(a=O.isBackward(r)?"focus":"anchor"),"end"===a&&(a=O.isBackward(r)?"anchor":"focus");var{anchor:i,focus:D}=r,s={distance:t,unit:n,ignoreNonSelectable:!0},l={};if(null==a||"anchor"===a){var C=o?I.before(u,i,s):I.after(u,i,s);C&&(l.anchor=C)}if(null==a||"focus"===a){var f=o?I.before(u,D,s):I.after(u,D,s);f&&(l.focus=f)}ub.setSelection(u,l)}},eM=(u,e)=>{var{selection:r}=u;if(e=I.range(u,e),r){ub.setSelection(u,e);return}if(!O.isRange(e))throw Error("When setting the selection and the current selection is `null` you must provide at least an `anchor` and `focus`, but you passed: ".concat(Q.stringify(e)));u.apply({type:"set_selection",properties:r,newProperties:e})};function eL(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function eW(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eL(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):eL(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var eQ=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{selection:t}=u,{edge:n="both"}=r;if(t){"start"===n&&(n=O.isBackward(t)?"focus":"anchor"),"end"===n&&(n=O.isBackward(t)?"anchor":"focus");var{anchor:o,focus:a}=t,i="anchor"===n?o:a;ub.setSelection(u,{["anchor"===n?"anchor":"focus"]:eW(eW({},i),e)})}},e$=(u,e)=>{var{selection:r}=u,t={},n={};if(r){for(var o in e)("anchor"!==o||null==e.anchor||L.equals(e.anchor,r.anchor))&&("focus"!==o||null==e.focus||L.equals(e.focus,r.focus))&&("anchor"===o||"focus"===o||e[o]===r[o])||(t[o]=r[o],n[o]=e[o]);Object.keys(t).length>0&&u.apply({type:"set_selection",properties:t,newProperties:n})}},eJ=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};I.withoutNormalizing(u,()=>{var{hanging:t=!1,voids:n=!1,mode:o="lowest",batchDirty:a=!0}=r,{at:i,match:D,select:s}=r;if(N.isNode(e)&&(e=[e]),0!==e.length){var[l]=e;if(i||(i=Y(u),!1===s||(s=!0)),null==s&&(s=!1),O.isRange(i)){if(t||(i=I.unhangRange(u,i,{voids:n})),O.isCollapsed(i))i=i.anchor;else{var[,C]=O.edges(i),f=I.pointRef(u,C);ub.delete(u,{at:i}),i=f.unref()}}if(L.isPoint(i)){null==D&&(D=K.isText(l)?u=>K.isText(u):u.isInline(l)?e=>K.isText(e)||I.isInline(u,e):e=>P.isElement(e)&&I.isBlock(u,e));var[c]=I.nodes(u,{at:i.path,match:D,mode:o,voids:n});if(!c)return;var[,B]=c,h=I.pathRef(u,B),v=I.isEnd(u,i,B);ub.splitNodes(u,{at:i,match:D,mode:o,voids:n});var d=h.unref();i=v?A.next(d):d}var p=A.parent(i),F=i[i.length-1];if(!(!n&&I.void(u,{at:p}))){if(a){var E=[],g=A.levels(p);uP(u,()=>{for(var r of e)!function(){var e=p.concat(F);F++;var t={type:"insert_node",path:e,node:r};u.apply(t),i=A.next(i),E.push(t),K.isText?g.push(...Array.from(N.nodes(r),u=>{var[,r]=u;return e.concat(r)})):g.push(e)}()},()=>{ux(u,g,u=>{var e=u;for(var r of E)if(A.operationCanTransformPath(r)&&!(e=A.transform(e,r)))return null;return e})})}else for(var y of e){var m=p.concat(F);F++,u.apply({type:"insert_node",path:m,node:y}),i=A.next(i)}if(i=A.previous(i),s){var b=I.end(u,i);b&&ub.select(u,b)}}}})},eZ=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};I.withoutNormalizing(u,()=>{var{at:r=u.selection,mode:t="lowest",voids:n=!1}=e,{match:o}=e;if(null==o&&(o=A.isPath(r)?G(u,r):e=>P.isElement(e)&&I.isBlock(u,e)),r)for(var a of Array.from(I.nodes(u,{at:r,match:o,mode:t,voids:n}),e=>{var[,r]=e;return I.pathRef(u,r)})){var i=a.unref();if(i.length<2)throw Error("Cannot lift node at a path [".concat(i,"] because it has a depth of less than `2`."));var[D,s]=I.node(u,A.parent(i)),l=i[i.length-1],{length:C}=D.children;if(1===C){var f=A.next(s);ub.moveNodes(u,{at:i,to:f,voids:n}),ub.removeNodes(u,{at:s,voids:n})}else if(0===l)ub.moveNodes(u,{at:i,to:s,voids:n});else if(l===C-1){var c=A.next(s);ub.moveNodes(u,{at:i,to:c,voids:n})}else{var B=A.next(i),h=A.next(s);ub.splitNodes(u,{at:B,voids:n}),ub.moveNodes(u,{at:i,to:h,voids:n})}}})},eH=["text"],eK=["children"],eY=(u,e)=>P.isElement(e)?!!I.isVoid(u,e)||1===e.children.length&&eY(u,e.children[0]):!I.isEditor(e),eG=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};I.withoutNormalizing(u,()=>{var r,t,{match:n,at:o=u.selection}=e,{hanging:a=!1,voids:i=!1,mode:D="lowest"}=e;if(!!o){if(null==n){if(A.isPath(o)){var[s]=I.parent(u,o);n=u=>s.children.includes(u)}else n=e=>P.isElement(e)&&I.isBlock(u,e)}if(!a&&O.isRange(o)&&(o=I.unhangRange(u,o,{voids:i})),O.isRange(o)){if(O.isCollapsed(o))o=o.anchor;else{var[,l]=O.edges(o),C=I.pointRef(u,l);ub.delete(u,{at:o}),o=C.unref(),null==e.at&&ub.select(u,o)}}var[f]=I.nodes(u,{at:o,match:n,voids:i,mode:D}),c=I.previous(u,{at:o,match:n,voids:i,mode:D});if(f&&c){var[B,h]=f,[v,d]=c;if(0!==h.length&&0!==d.length){var p=A.next(d),F=A.common(h,d),E=A.isSibling(h,d),g=Array.from(I.levels(u,{at:h}),u=>{var[e]=u;return e}).slice(F.length).slice(0,-1),m=I.above(u,{at:h,mode:"highest",match:e=>g.includes(e)&&eY(u,e)}),b=m&&I.pathRef(u,m[1]);if(K.isText(B)&&K.isText(v)){var w=y(B,eH);t=v.text.length,r=w}else if(P.isElement(B)&&P.isElement(v)){var w=y(B,eK);t=v.children.length,r=w}else throw Error("Cannot merge the node at path [".concat(h,"] with the previous sibling because it is not the same kind: ").concat(Q.stringify(B)," ").concat(Q.stringify(v)));E||ub.moveNodes(u,{at:h,to:p,voids:i}),b&&ub.removeNodes(u,{at:b.current,voids:i}),I.shouldMergeNodesRemovePrevNode(u,c,f)?ub.removeNodes(u,{at:d,voids:i}):u.apply({type:"merge_node",path:p,position:t,properties:r}),b&&b.unref()}}}})},eU=(u,e)=>{I.withoutNormalizing(u,()=>{var{to:r,at:t=u.selection,mode:n="lowest",voids:o=!1}=e,{match:a}=e;if(t){null==a&&(a=A.isPath(t)?G(u,t):e=>P.isElement(e)&&I.isBlock(u,e));var i=I.pathRef(u,r);for(var D of Array.from(I.nodes(u,{at:t,match:a,mode:n,voids:o}),e=>{var[,r]=e;return I.pathRef(u,r)})){var s=D.unref(),l=i.current;0!==s.length&&u.apply({type:"move_node",path:s,newPath:l}),i.current&&A.isSibling(l,s)&&A.isAfter(l,s)&&(i.current=A.next(i.current))}i.unref()}})},eX=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};I.withoutNormalizing(u,()=>{var{hanging:r=!1,voids:t=!1,mode:n="lowest"}=e,{at:o=u.selection,match:a}=e;if(o)for(var i of(null==a&&(a=A.isPath(o)?G(u,o):e=>P.isElement(e)&&I.isBlock(u,e)),!r&&O.isRange(o)&&(o=I.unhangRange(u,o,{voids:t})),Array.from(I.nodes(u,{at:o,match:a,mode:n,voids:t}),e=>{var[,r]=e;return I.pathRef(u,r)}))){var D=i.unref();if(D){var[s]=I.node(u,D);u.apply({type:"remove_node",path:D,node:s})}}})},e0=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};I.withoutNormalizing(u,()=>{var{match:t,at:n=u.selection,compare:o,merge:a}=r,{hanging:i=!1,mode:D="lowest",split:s=!1,voids:l=!1}=r;if(n){if(null==t&&(t=A.isPath(n)?G(u,n):e=>P.isElement(e)&&I.isBlock(u,e)),!i&&O.isRange(n)&&(n=I.unhangRange(u,n,{voids:l})),s&&O.isRange(n)){if(O.isCollapsed(n)&&I.leaf(u,n.anchor)[0].text.length>0)return;var C=I.rangeRef(u,n,{affinity:"inward"}),[f,c]=O.edges(n),B="lowest"===D?"lowest":"highest",h=I.isEnd(u,c,c.path);ub.splitNodes(u,{at:c,match:t,mode:B,voids:l,always:!h});var v=I.isStart(u,f,f.path);ub.splitNodes(u,{at:f,match:t,mode:B,voids:l,always:!v}),n=C.unref(),null==r.at&&ub.select(u,n)}for(var[d,p]of(o||(o=(u,e)=>u!==e),I.nodes(u,{at:n,match:t,mode:D,voids:l}))){var F={},E={};if(0!==p.length){var g=!1;for(var y in e)"children"!==y&&"text"!==y&&o(e[y],d[y])&&(g=!0,d.hasOwnProperty(y)&&(F[y]=d[y]),a?null!=e[y]&&(E[y]=a(d[y],e[y])):null!=e[y]&&(E[y]=e[y]));g&&u.apply({type:"set_node",path:p,properties:F,newProperties:E})}}}})},e1=(u,e)=>{if(O.isCollapsed(e))return e.anchor;var[,r]=O.edges(e),t=I.pointRef(u,r);return ub.delete(u,{at:e}),t.unref()},e3=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};I.withoutNormalizing(u,()=>{var r,t,{mode:n="lowest",voids:o=!1}=e,{match:a,at:i=u.selection,height:D=0,always:s=!1}=e;if(null==a&&(a=e=>P.isElement(e)&&I.isBlock(u,e)),O.isRange(i)&&(i=e1(u,i)),A.isPath(i)){var l=i,C=I.point(u,l),[f]=I.parent(u,l);a=u=>u===f,D=C.path.length-l.length+1,i=C,s=!0}if(i){var c=I.pointRef(u,i,{affinity:"backward"});try{var[B]=I.nodes(u,{at:i,match:a,mode:n,voids:o});if(!B)return;var h=I.void(u,{at:i,mode:"highest"});if(!o&&h){var[v,d]=h;if(P.isElement(v)&&u.isInline(v)){var p=I.after(u,d);if(!p){var F=A.next(d);ub.insertNodes(u,{text:""},{at:F,voids:o}),p=I.point(u,F)}i=p,s=!0}D=i.path.length-d.length+1,s=!0}r=I.pointRef(u,i);var E=i.path.length-D,[,g]=B,y=i.path.slice(0,E),m=0===D?i.offset:i.path[E]+0;for(var[b,w]of I.levels(u,{at:y,reverse:!0,voids:o})){var x=!1;if(w.length<g.length||0===w.length||!o&&P.isElement(b)&&I.isVoid(u,b))break;var j=c.current,k=I.isEnd(u,j,w);if(s||!c||!I.isEdge(u,j,w)){x=!0;var R=N.extractProps(b);u.apply({type:"split_node",path:w,position:m,properties:R})}m=w[w.length-1]+(x||k?1:0)}if(null==e.at){var S=r.current||I.end(u,[]);ub.select(u,S)}}finally{c.unref(),null===(t=r)||void 0===t||t.unref()}}})},e2=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Array.isArray(e)||(e=[e]);var t={};for(var n of e)t[n]=null;ub.setNodes(u,t,r)},e7=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};I.withoutNormalizing(u,()=>{var{mode:r="lowest",split:t=!1,voids:n=!1}=e,{at:o=u.selection,match:a}=e;if(o){null==a&&(a=A.isPath(o)?G(u,o):e=>P.isElement(e)&&I.isBlock(u,e)),A.isPath(o)&&(o=I.range(u,o));var i=O.isRange(o)?I.rangeRef(u,o):null;for(var D of Array.from(I.nodes(u,{at:o,match:a,mode:r,voids:n}),e=>{var[,r]=e;return I.pathRef(u,r)}).reverse())!function(){var e=D.unref(),[r]=I.node(u,e),o=I.range(u,e);t&&i&&(o=O.intersection(i.current,o)),ub.liftNodes(u,{at:o,match:u=>P.isAncestor(r)&&r.children.includes(u),voids:n})}();i&&i.unref()}})};function e8(u,e){var r=Object.keys(u);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(u);e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(u,e).enumerable})),r.push.apply(r,t)}return r}function e4(u){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e8(Object(r),!0).forEach(function(e){d(u,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(r)):e8(Object(r)).forEach(function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(r,e))})}return u}var e9=function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};I.withoutNormalizing(u,()=>{var{mode:t="lowest",split:n=!1,voids:o=!1}=r,{match:a,at:i=u.selection}=r;if(i){if(null==a&&(a=A.isPath(i)?G(u,i):u.isInline(e)?e=>P.isElement(e)&&I.isInline(u,e)||K.isText(e):e=>P.isElement(e)&&I.isBlock(u,e)),n&&O.isRange(i)){var[D,s]=O.edges(i),l=I.rangeRef(u,i,{affinity:"inward"});ub.splitNodes(u,{at:s,match:a,voids:o}),ub.splitNodes(u,{at:D,match:a,voids:o}),i=l.unref(),null==r.at&&ub.select(u,i)}for(var[,C]of Array.from(I.nodes(u,{at:i,match:u.isInline(e)?e=>P.isElement(e)&&I.isBlock(u,e):u=>I.isEditor(u),mode:"lowest",voids:o})))if(0===function(){var r=O.isRange(i)?O.intersection(i,I.range(u,C)):i;if(!r)return 0;var n=Array.from(I.nodes(u,{at:r,match:a,mode:t,voids:o}));if(n.length>0){var[D]=n,s=n[n.length-1],[,l]=D,[,f]=s;if(0===l.length&&0===f.length)return 0;var c=A.equals(l,f)?A.parent(l):A.common(l,f),B=I.range(u,l,f),[h]=I.node(u,c),v=c.length+1,d=A.next(f.slice(0,v)),p=e4(e4({},e),{},{children:[]});ub.insertNodes(u,p,{at:d,voids:o}),ub.moveNodes(u,{at:B,match:u=>P.isAncestor(h)&&h.children.includes(u),to:d.concat(0),voids:o})}}())continue}})},e5=()=>{var u={children:[],operations:[],selection:null,marks:null,isElementReadOnly:()=>!1,isInline:()=>!1,isSelectable:()=>!0,isVoid:()=>!1,markableVoid:()=>!1,onChange:()=>{},apply:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uj(u,...r)},addMark:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uI(u,...r)},deleteBackward:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u$(u,...r)},deleteForward:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uJ(u,...r)},deleteFragment:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uZ(u,...r)},getFragment:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uN(u,...r)},insertBreak:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u5(u,...r)},insertSoftBreak:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eu(u,...r)},insertFragment:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eq(u,...r)},insertNode:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u6(u,...r)},insertText:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return er(u,...r)},normalizeNode:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uR(u,...r)},removeMark:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ex(u,...r)},getDirtyPaths:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uk(u,...r)},shouldNormalize:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uS(u,...r)},above:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uT(u,...r)},after:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uM(u,...r)},before:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uQ(u,...r)},collapse:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eI(u,...r)},delete:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e_(u,...r)},deselect:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ez(u,...r)},edges:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uH(u,...r)},elementReadOnly:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uG(u,...r)},end:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uU(u,...r)},first:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return uX(u,...r)},fragment:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u0(u,...r)},getMarks:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ec(u,...r)},hasBlocks:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u7(u,...r)},hasInlines:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u8(u,...r)},hasPath:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u4(u,...r)},hasTexts:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u9(u,...r)},insertNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eJ(u,...r)},isBlock:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return et(u,...r)},isEdge:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return en(u,...r)},isEmpty:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eo(u,...r)},isEnd:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ea(u,...r)},isNormalizing:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ei(u,...r)},isStart:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eD(u,...r)},last:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return es(u,...r)},leaf:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return el(u,...r)},levels:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){var{at:r=u.selection,reverse:t=!1,voids:n=!1}=e,{match:o}=e;if(null==o&&(o=()=>!0),r){var a=[],i=I.path(u,r);for(var[D,s]of N.levels(u,i))if(o(D,s)&&(a.push([D,s]),!n&&P.isElement(D)&&I.isVoid(u,D)))break;t&&a.reverse(),yield*a}}()}(u,...r)},liftNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eZ(u,...r)},mergeNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eG(u,...r)},move:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eV(u,...r)},moveNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eU(u,...r)},next:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eB(u,...r)},node:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eh(u,...r)},nodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){var r,t,n,{at:o=u.selection,mode:a="all",universal:i=!1,reverse:D=!1,voids:s=!1,ignoreNonSelectable:l=!1}=e,{match:C}=e;if(C||(C=()=>!0),o){if(z.isSpan(o))r=o[0],t=o[1];else{var f=I.path(u,o,{edge:"start"}),c=I.path(u,o,{edge:"end"});r=D?c:f,t=D?f:c}var B=N.nodes(u,{reverse:D,from:r,to:t,pass:e=>{var[r]=e;return!!P.isElement(r)&&(!!(!s&&(I.isVoid(u,r)||I.isElementReadOnly(u,r)))||!!l&&!I.isSelectable(u,r))}}),h=[];for(var[v,d]of B){if(!(l&&P.isElement(v))||I.isSelectable(u,v)){var p=n&&0===A.compare(d,n[1]);if("highest"!==a||!p){if(!C(v,d)){if(i&&!p&&K.isText(v))return;continue}if("lowest"===a&&p){n=[v,d];continue}var F="lowest"===a?n:[v,d];F&&(i?h.push(F):yield F),n=[v,d]}}}"lowest"===a&&n&&(i?h.push(n):yield n),i&&(yield*h)}}()}(u,...r)},normalize:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eA(u,...r)},parent:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ev(u,...r)},path:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eF(u,...r)},pathRef:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ed(u,...r)},pathRefs:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ep(u,...r)},point:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ey(u,...r)},pointRef:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eE(u,...r)},pointRefs:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eg(u,...r)},positions:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){var{at:r=u.selection,unit:t="offset",reverse:n=!1,voids:o=!1,ignoreNonSelectable:a=!1}=e;if(r){var i=I.range(u,r),[D,s]=O.edges(i),l=n?s:D,C=!1,f="",c=0,B=0,h=0;for(var[v,d]of I.nodes(u,{at:r,reverse:n,voids:o,ignoreNonSelectable:a})){if(P.isElement(v)){if(!o&&(u.isVoid(v)||u.isElementReadOnly(v))){yield I.start(u,d);continue}if(u.isInline(v))continue;if(I.hasInlines(u,v)){var p=A.isAncestor(d,s.path)?s:I.end(u,d),F=A.isAncestor(d,D.path)?D:I.start(u,d);f=I.string(u,{anchor:F,focus:p},{voids:o}),C=!0}}if(K.isText(v)){var E,g,y,m=A.equals(d,l.path);for(m?(B=n?l.offset:v.text.length-l.offset,h=l.offset):(B=v.text.length,h=n?B:0),(m||C||"offset"===t)&&(yield{path:d,offset:h},C=!1);;){if(0===c){if(""===f)break;E=f,g=t,y=n,f=ut(f,c="character"===g?U(E,y):"word"===g?ur(E,y):"line"===g||"block"===g?E.length:1,n)[1]}if(h=n?h-c:h+c,(B-=c)<0){c=-B;break}c=0,yield{path:d,offset:h}}}}}}()}(u,...r)},previous:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return em(u,...r)},range:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ew(u,...r)},rangeRef:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eb(u,...r)},rangeRefs:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eO(u,...r)},removeNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eX(u,...r)},select:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eM(u,...r)},setNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e0(u,...r)},setNormalizing:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ej(u,...r)},setPoint:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eQ(u,...r)},setSelection:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e$(u,...r)},splitNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e3(u,...r)},start:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return ek(u,...r)},string:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eN(u,...r)},unhangRange:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eR(u,...r)},unsetNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e2(u,...r)},unwrapNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e7(u,...r)},void:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return u2(u,...r)},withoutNormalizing:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eS(u,...r)},wrapNodes:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e9(u,...r)},shouldMergeNodesRemovePrevNode:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return eT(u,...r)}};return u}}}]);