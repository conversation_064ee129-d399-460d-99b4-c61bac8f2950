(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3742,8555,9229],{15687:e=>{e.exports={"prompt-search":"Prompt_prompt-search__8VBex","prompt-list":"Prompt_prompt-list__9XFQ7","prompt-item":"Prompt_prompt-item__KeueI","prompt-item-title":"Prompt_prompt-item-title___2F2G","prompt-item-content":"Prompt_prompt-item-content__Qu5n4"}},43857:(e,t,r)=>{"use strict";r.d(t,{A:()=>A});var n=r(29935),o=r(17844),i=r(12694),l=r(86105),a=r(82643),c=r(23798),s=r(15687),u=r.n(s),d=r(80667),p=r(48772),f=r(35476),m=r(21462),v=r(1450),h=r(34566),b=r(20448),g=r(39652);let x=function(e){var t,r=e.item,n=(0,b.A)(null!==(t=r.title)&&void 0!==t?t:""),o=n.ref,l=n.isOverflow,a=(0,i._)((0,m.useState)(!1),2),s=a[0],d=a[1],p=(0,c.jsxs)("div",{className:u()["prompt-item"],onMouseEnter:function(){d(!0)},onMouseLeave:function(){return d(!1)},children:[(0,c.jsxs)("span",{className:u()["prompt-item-title"],children:[(0,c.jsx)("span",{className:"inline-block w-[20px]",children:r.emoji}),(0,c.jsx)("span",{ref:o,className:"overflow-hidden whitespace-nowrap text-ellipsis",children:r.title})]}),(0,c.jsx)("span",{className:u()["prompt-item-content"],children:r.showContent})]});return l?(0,c.jsx)(g.A,{open:s,title:r.title,placement:"topLeft",children:p}):p};var _=r(8448),y=r(78176),S=r(92079);let A=m.forwardRef(function(e,t){var r,s=e.className,b=e.editorData,g=e.setSlateValue,A=e.position,L=void 0===A?"bottom":A,w=e.focusRef,N=(0,i._)((0,m.useState)([]),2),j=N[0],E=N[1],k=(0,m.useRef)(null),C=(0,i._)((0,m.useState)(!1),2),I=C[0],T=C[1],F=(0,i._)((0,m.useState)(!0),2),U=F[0],H=F[1],R=(0,i._)((0,m.useState)(1),2),P=R[0],D=R[1],K=(0,v.bA)(),M=(0,y.A)().userInfoData,Y=(0,m.useRef)(null);(0,m.useEffect)(function(){var e=function(e){Y.current&&!Y.current.contains(e.target)&&E([])};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}},[Y]),(0,m.useEffect)(function(){if(!(null==M?void 0:M.userId)||!b){E([]);return}z(1,{title:b})},[b]);var z=(0,m.useCallback)((r=(0,n._)(function(e,t){var r,n,i,c,s,u,d,p,m;return(0,a.YH)(this,function(a){switch(a.label){case 0:if(T(!0),(null==t?void 0:null===(r=t.title)||void 0===r?void 0:r.trim().length)===0)return[2];return t.title=t.title.trim(),[4,(0,f.Trk)({body:(0,o._)({pageIndex:e,pageSize:5,sortType:1},t)})];case 1:if(!(null==(d=a.sent())?void 0:null===(n=d.data)||void 0===n?void 0:n.success))return[2];return 1===e?E((0,l._)((null==d?void 0:null===(m=d.data)||void 0===m?void 0:null===(p=m.data)||void 0===p?void 0:p.records)||[])):E(function(e){var t,r;return(0,l._)(e).concat((0,l._)((null==d?void 0:null===(r=d.data)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.records)||[]))}),D(e),T(!1),H((null===(c=d.data)||void 0===c?void 0:null===(i=c.data)||void 0===i?void 0:i.current)!==(null===(u=d.data)||void 0===u?void 0:null===(s=u.data)||void 0===s?void 0:s.pages)),[2]}})}),function(e,t){return r.apply(this,arguments)}),[]),O=function(e){var t=(0,_.g)(e.content||"",e.metaList||[]);t[t.length-1].children.unshift({text:""}),t[t.length-1].children.push({text:""}),t[t.length-1].children.push({text:""}),w.current={anchor:{path:[t.length-1,t[t.length-1].children.length-1],offset:0},focus:{path:[t.length-1,t[t.length-1].children.length-1],offset:0}},null==g||g(t)},G=(0,S.n)(),V=G.isMobile,X=G.isTabletOrMobile;return V?null:j.length>0&&(0,c.jsxs)("div",{ref:Y,className:(0,v.cx)(s,u()["prompt-search"],"rounded-[24px] border border-solid  border-[var(--border-color)] ","top"===L?"border-b-0 rounded-b-0 rounded-br-0":"border-t-0 rounded-t-0 rounded-tr-0","top"===L?X?"left-[16px]":"left-[20px]":"left-[8px] "),style:{width:"top"===L?X?"calc(100% - 32px)":"calc(100% - 40px)":"calc(100% - 16px)"},children:[(0,c.jsxs)(d.A,{align:"center",justify:"space-between",className:"mb-12",children:[(0,c.jsx)("div",{children:"快捷输入"}),(0,c.jsxs)(d.A,{gap:4,className:"px-8 text-12 text-[var(--text-color-secondary)] cursor-pointer hover:text-[#3F8DFF]",justify:"center",align:"center",onClick:function(){K("/prompt")},children:[(0,c.jsx)(h.A,{type:"icon-EditOutlined",className:"!text-14"}),"管理"]})]}),(0,c.jsx)("div",{ref:k,className:"max-h-[215px] overflow-y-auto scrollbar-hide",onScroll:function(e){var t=e.currentTarget,r=t.scrollTop;t.scrollHeight-r-t.clientHeight<30&&!I&&U&&z(P+1,{title:b})},children:(0,c.jsx)(p.A,{itemLayout:"horizontal",loadMore:U,dataSource:j,className:u()["prompt-list"],renderItem:function(e){return(0,c.jsx)(p.A.Item,{onClick:function(){return O(e)},children:(0,c.jsx)(x,{item:e})},e.id)}})})]})})},98505:(e,t,r)=>{"use strict";r.d(t,{U:()=>d});var n=r(29935),o=r(12694),i=r(82643),l=r(92079),a=r(99962),c=r.n(a),s=r(21462),u=null,d=function(e,t){var r,a=(0,l.n)().isMobile,d=(0,o._)((0,s.useState)(!1),2),p=d[0],f=d[1],m=(0,o._)((0,s.useState)(!1),2),v=m[0],h=m[1],b=function(t){var r=e.current;if(r){var n=r.getBoundingClientRect(),o=r.scrollHeight,i=r.clientHeight,l=Number(i*i/o),a=Number(r.scrollTop/(o-i)*(i-l)),c=r.scrollHeight>r.clientHeight,s=!1;c&&(t.clientX>=n.right-8&&t.clientX<=n.right&&t.clientY>=a+72&&t.clientY<=Number(a+l+72)?(s=!0,u&&clearTimeout(u),f(!0)):p&&f(!1)),h(s)}},g=(0,s.useCallback)((r=(0,n._)(function(e){var r;return(0,i.YH)(this,function(n){return(r=e.target).scrollHeight-Math.abs(r.scrollTop)-r.clientHeight>10&&(f(!0),u&&clearTimeout(u),v||(u=setTimeout(function(){f(!1)},500))),null==t||t(e),[2]})}),function(e){return r.apply(this,arguments)}),[t]),x=(0,s.useMemo)(function(){return c()(g,200)},[g]);return(0,s.useEffect)(function(){if(e.current&&!a){var t=e.current;return t.addEventListener("scroll",x),t.addEventListener("mousemove",b),function(){t&&(null==t||t.removeEventListener("scroll",x),null==t||t.removeEventListener("mousemove",b))}}},[e.current,a,x]),p}},99782:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>x,vV:()=>b});var n=r(29935),o=r(17844),i=r(93629),l=r(12694),a=r(82643),c=r(23798),s=r(21462),u=r(35476),d=r(43073),p=r(99287),f=r(81549),m=r(1450),v=r(54435),h=r(30054),b=function(){var e=(0,s.useContext)(g);if(!e)throw Error("useFileUpload must be used within a FileUploadProvider");return e},g=(0,s.createContext)({fileListData:[],setFileListData:function(){},handleUploadInterrupt:function(){}});let x=function(e){return function(t){var r,b,x=(0,s.useRef)(),_=(0,d.lN)(),y=_.addTask,S=_.tasks,A=_.removeTask,L=(0,l._)((0,s.useState)([]),2),w=L[0],N=L[1],j=(0,l._)((0,s.useState)([]),2),E=j[0],k=j[1],C=(0,s.useRef)([]),I=(0,h.vS)(),T=I.uploadStorageType.attachmentStorageType,F=I.attachmentAcceptsMap,U=I.attachmentAccepts,H=I.setPrivateLibSearch,R=I.setSharedLibSearch,P=I.setAllLibSearch,D=I.setShowFile,K=I.setIsAttachment,M=function(e){(0,p.Ek)({docId:e.docId||"",onComplete:function(e){if(e.processStatus===d.KK.FAILED)f.Ay.error("文件".concat(e.docName,"解析失败"));else{var t,r;null===(r=x.current)||void 0===r||null===(t=r.onUploadComplete)||void 0===t||t.call(r)}C.current=C.current.map(function(t){return t.docId===e.docId?e:t}),N(C.current)},onError:function(e){f.Ay.error(e.message)}})};(0,s.useEffect)(function(){C.current=w,w.forEach(function(e){e.processStatus===d.KK.ANALYSING&&M(e)})},[w]);var Y=(0,s.useRef)(null),z=(0,s.useCallback)(function(){Y.current&&Y.current.abort()},[]),O=(0,s.useCallback)((r=(0,n._)(function(e){var t,r,n,l,c,s,p,m,v;return(0,a.YH)(this,function(a){switch(a.label){case 0:Y.current&&Y.current.abort(),Y.current=new AbortController,t=Y.current.signal,a.label=1;case 1:return a.trys.push([1,6,,7]),[4,(0,u.fIY)({body:{filePath:e.result.name,fileName:e.file.name,fileSize:"".concat(e.file.size),storageType:T},signal:t})];case 2:if(!(null===(r=(n=a.sent()).data)||void 0===r?void 0:r.success))return[3,4];return[4,(0,u.TFE)({body:{docIds:[(null===(l=n.data.data)||void 0===l?void 0:l.docId)||""]}})];case 3:return s=a.sent(),A(e.id),(null===(c=s.data)||void 0===c?void 0:c.success)&&(m=null===(p=s.data.data)||void 0===p?void 0:p[0])&&(N([(0,i._)((0,o._)({},m),{loadingfileUrl:URL.createObjectURL(e.file)})]),m.processStatus===d.KK.ANALYSING&&M(m)),[3,5];case 4:N([]),f.Ay.error("文件上传失败！"),A(e.id),a.label=5;case 5:return[3,7];case 6:return(null==(v=a.sent())?void 0:v.name)==="AbortError"||console.error("文件解析出错:",v),[3,7];case 7:return[2]}})}),function(e){return r.apply(this,arguments)}),[T]),G=(0,s.useCallback)(function(e){var t;N([{docType:null===(t=e.file.name.split(".").pop())||void 0===t?void 0:t.toLowerCase(),docName:e.file.name,isUpdate:!0,progress:e.progress,fileUrl:URL.createObjectURL(e.file)}])},[]),V=(0,s.useCallback)(function(e){},[]),X=(b=(0,n._)(function(e){var t,r,n,o,i,l,c,s,u;return(0,a.YH)(this,function(a){if(0===e.length)return[2];if(e.length>v.t5)return f.Ay.error("一次最多只能上传".concat(v.t5,"个文件")),[2];t=!0,r=!1,n=void 0;try{for(o=e[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){c=F[(l=i.value).name.split(".").pop()||""]||0xa00000;try{if(l.size>c){f.Ay.error("文件 ".concat(l.name," 大小不能超过 ").concat((0,m.KU)(c),"，请重新选择文件"));continue}if(u="."+(null===(s=l.name.split(".").pop())||void 0===s?void 0:s.toLowerCase()),!U.includes(u)){f.Ay.error("文件 ".concat(l.name," 格式不支持，请上传 ").concat(U.join(", ")," 格式的文件"));continue}H(!1),R(!1),P(!1),D(!0),K(!0),k([]),y(l,{onSuccess:O,onProgress:G,onError:V})}catch(e){console.error("添加上传任务失败 ".concat(l.name,":"),e)}}}catch(e){r=!0,n=e}finally{try{t||null==o.return||o.return()}finally{if(r)throw n}}return[2]})}),function(e){return b.apply(this,arguments)});return(0,s.useEffect)(function(){return function(){(0,p.rT)(),Y.current&&Y.current.abort()}},[]),(0,c.jsx)(g.Provider,{value:{fileListData:w,setFileListData:N,handleUploadInterrupt:z},children:(0,c.jsx)(e,(0,i._)((0,o._)({},t),{ref:x,onFilesDrop:X,uploadingFiles:S,fileListData:w,setFileListData:N,knowledgeList:E,setKnowledgeList:k,handleUploadInterrupt:z}))})}}}}]);